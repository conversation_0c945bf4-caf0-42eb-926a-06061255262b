# Image Upload Bug Fixes

## Issue Summary
The catalogue edit page was experiencing an error: "Upload error for image 2: 'key is not defined'" when trying to upload images. This was caused by an undefined variable reference in the upload API.

## Root Cause
In `/src/app/api/upload/route.ts`, line 110 was referencing a variable `key` that was not defined in the current scope:

```javascript
const isOptionalImage = key.includes('imageUrl2') || key.includes('imageUrl3');
```

The variable should have been `s3Key` which was defined earlier in the function.

## Fixes Implemented

### 1. Fixed Undefined Variable Reference
**File:** `src/app/api/upload/route.ts`
**Change:** Replaced `key` with `s3Key` on line 110

```javascript
// Before (causing error)
const isOptionalImage = key.includes('imageUrl2') || key.includes('imageUrl3');

// After (fixed)
const isOptionalImage = s3Key.includes('imageUrl2') || s3Key.includes('imageUrl3') || validCategory === 'optional';
```

### 2. Created Enhanced Image Upload Service
**File:** `src/utils/imageUploadUtils.ts` (new file)
**Purpose:** Comprehensive image upload utility with:
- Robust error handling and retry logic
- Progress tracking capabilities
- Validation for file size and type
- Support for optional vs required images
- Fallback URL handling
- Clean error message formatting

**Key Features:**
- `ImageUploadService.uploadImage()` - Single image upload with retries
- `ImageUploadService.uploadMultipleImages()` - Batch upload with progress tracking
- `ImageUploadError` - Custom error class for better error handling
- Comprehensive validation and timeout handling

### 3. Updated Catalogue Edit Page
**File:** `src/app/admin/catalogue/[id]/edit/page.tsx`
**Changes:**
- Integrated the new `ImageUploadService`
- Improved error handling and user feedback
- Better progress tracking and status messages
- Enhanced validation and retry logic

**Key Improvements:**
- Uses the new upload service for better reliability
- Cleaner error messages for users
- Better handling of optional vs required images
- Improved status message display

## Benefits of the Fixes

### 1. Immediate Bug Resolution
- Fixed the "key is not defined" error that was preventing image uploads
- Users can now successfully upload images in the catalogue edit page

### 2. Enhanced Reliability
- Retry logic for failed uploads
- Better timeout handling
- Comprehensive error handling

### 3. Improved User Experience
- Clear status messages and progress feedback
- Better error messages that are user-friendly
- Graceful handling of optional image failures

### 4. Future-Proof Architecture
- Reusable upload service for other parts of the application
- Consistent error handling across all upload functionality
- Easy to extend with additional features

## Testing Recommendations

1. **Basic Upload Test:**
   - Navigate to catalogue edit page
   - Try uploading 1-3 images
   - Verify successful upload and proper URL generation

2. **Error Handling Test:**
   - Try uploading oversized files (>5MB)
   - Try uploading invalid file types
   - Verify proper error messages are shown

3. **Network Issues Test:**
   - Test with slow/unstable network connection
   - Verify retry logic works properly
   - Check timeout handling

4. **Optional Image Test:**
   - Upload only primary image (should work)
   - Upload primary + secondary images
   - Verify optional images can fail gracefully

## Automated Testing

Two test scripts have been created to verify the upload configuration:

### 1. Storage Configuration Setup
```bash
node scripts/setup-storage-config.js
```
This script:
- Validates environment variables
- Creates/updates default storage configuration in database
- Tests database connectivity

### 2. Upload Configuration Test
```bash
node scripts/test-upload-config.js
```
This script:
- Tests environment variables
- Verifies S3 connection
- Tests upload permissions
- Validates complete upload pipeline

**Test Results (Latest Run):**
- ✅ Environment variables: All required S3 variables present
- ✅ S3 connection: Successfully connected to Linode Object Storage
- ✅ Upload permissions: Test file uploaded successfully
- ✅ Storage configuration: Default config created in database

## Files Modified

1. `src/app/api/upload/route.ts` - Fixed undefined variable
2. `src/utils/imageUploadUtils.ts` - New comprehensive upload service
3. `src/app/admin/catalogue/[id]/edit/page.tsx` - Updated to use new service
4. `src/lib/storageConfig.ts` - Enhanced storage configuration handling
5. `src/utils/s3Utils.ts` - Improved S3 client initialization and error handling

## Files Added

1. `scripts/setup-storage-config.js` - Automated storage configuration setup
2. `scripts/test-upload-config.js` - Comprehensive upload testing script
3. `UPLOAD_BUG_FIXES.md` - This documentation file

## Environment Variables Required

Ensure these S3 configuration variables are set:
- `NEXT_PUBLIC_S3_BUCKET`
- `NEXT_PUBLIC_S3_ACCESS_KEY`
- `NEXT_PUBLIC_S3_SECRET_KEY`
- `NEXT_PUBLIC_S3_ENDPOINT`

## Migration Notes

The new upload service is backward compatible with existing upload functionality. Other upload components can be gradually migrated to use the new service for improved reliability.

## Error Monitoring

The enhanced error handling provides better logging and error categorization:
- `VALIDATION_ERROR` - File validation failures
- `TIMEOUT_ERROR` - Upload timeouts
- `NETWORK_ERROR` - Network connectivity issues
- `S3_UPLOAD_FAILED` - S3 storage issues
- `PARSE_ERROR` - Response parsing failures

This makes it easier to monitor and debug upload issues in production.
