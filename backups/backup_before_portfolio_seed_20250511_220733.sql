--
-- PostgreSQL database dump
--

-- Dumped from database version 16.8 (Ubuntu 16.8-0ubuntu0.24.04.1)
-- Dumped by pg_dump version 16.8 (Ubuntu 16.8-0ubuntu0.24.04.1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: don
--

-- *not* creating schema, since initdb creates it


ALTER SCHEMA public OWNER TO don;

--
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: don
--

COMMENT ON SCHEMA public IS '';


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: don
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO don;

--
-- Name: blog_posts; Type: TABLE; Schema: public; Owner: don
--

CREATE TABLE public.blog_posts (
    id integer NOT NULL,
    title character varying(255) NOT NULL,
    slug character varying(255) NOT NULL,
    content text NOT NULL,
    excerpt text,
    author character varying(100),
    category character varying(100),
    tags text[] DEFAULT ARRAY[]::text[],
    status character varying(20) DEFAULT 'draft'::character varying NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    published_at timestamp(3) without time zone
);


ALTER TABLE public.blog_posts OWNER TO don;

--
-- Name: blog_posts_id_seq; Type: SEQUENCE; Schema: public; Owner: don
--

CREATE SEQUENCE public.blog_posts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.blog_posts_id_seq OWNER TO don;

--
-- Name: blog_posts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: don
--

ALTER SEQUENCE public.blog_posts_id_seq OWNED BY public.blog_posts.id;


--
-- Name: categories; Type: TABLE; Schema: public; Owner: don
--

CREATE TABLE public.categories (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    slug character varying(100) NOT NULL,
    description text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.categories OWNER TO don;

--
-- Name: categories_id_seq; Type: SEQUENCE; Schema: public; Owner: don
--

CREATE SEQUENCE public.categories_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.categories_id_seq OWNER TO don;

--
-- Name: categories_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: don
--

ALTER SEQUENCE public.categories_id_seq OWNED BY public.categories.id;


--
-- Name: company_story; Type: TABLE; Schema: public; Owner: don
--

CREATE TABLE public.company_story (
    id text NOT NULL,
    title character varying(200) NOT NULL,
    subtitle character varying(255) NOT NULL,
    "imageSrc" text NOT NULL,
    quote1 text NOT NULL,
    quote2 text,
    "founderName" character varying(100) NOT NULL,
    "founderRole" character varying(100) NOT NULL,
    "linkedinUrl" character varying(255),
    "twitterUrl" character varying(255),
    "instagramUrl" character varying(255),
    "tiktokUrl" character varying(255),
    "isActive" boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.company_story OWNER TO don;

--
-- Name: pricing; Type: TABLE; Schema: public; Owner: don
--

CREATE TABLE public.pricing (
    id integer NOT NULL,
    service character varying(255) NOT NULL,
    price integer NOT NULL,
    description text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    features text[] DEFAULT ARRAY[]::text[],
    icon character varying(100),
    popular boolean DEFAULT false
);


ALTER TABLE public.pricing OWNER TO don;

--
-- Name: pricing_id_seq; Type: SEQUENCE; Schema: public; Owner: don
--

CREATE SEQUENCE public.pricing_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.pricing_id_seq OWNER TO don;

--
-- Name: pricing_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: don
--

ALTER SEQUENCE public.pricing_id_seq OWNED BY public.pricing.id;


--
-- Name: site_settings; Type: TABLE; Schema: public; Owner: don
--

CREATE TABLE public.site_settings (
    id text NOT NULL,
    "siteName" character varying(100) NOT NULL,
    "siteDescription" character varying(255),
    "contactEmail" character varying(100),
    "phoneNumber" character varying(50),
    address character varying(255),
    "facebookUrl" character varying(255),
    "twitterUrl" character varying(255),
    "instagramUrl" character varying(255),
    "tiktokUrl" character varying(255),
    "linkedinUrl" character varying(255),
    "metaTitle" character varying(255),
    "metaDescription" text,
    "googleAnalyticsId" character varying(50),
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.site_settings OWNER TO don;

--
-- Name: storage_config; Type: TABLE; Schema: public; Owner: don
--

CREATE TABLE public.storage_config (
    id text NOT NULL,
    provider character varying(50) NOT NULL,
    region character varying(100) NOT NULL,
    endpoint character varying(255) NOT NULL,
    "bucketName" character varying(100) NOT NULL,
    "accessKeyId" character varying(255) NOT NULL,
    "secretAccessKey" character varying(255) NOT NULL,
    "isDefault" boolean DEFAULT false NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.storage_config OWNER TO don;

--
-- Name: team_members; Type: TABLE; Schema: public; Owner: don
--

CREATE TABLE public.team_members (
    id text NOT NULL,
    name character varying(100) NOT NULL,
    role character varying(100) NOT NULL,
    bio text NOT NULL,
    "imageSrc" text NOT NULL,
    "order" integer DEFAULT 0 NOT NULL,
    "linkedinUrl" character varying(255),
    "twitterUrl" character varying(255),
    "githubUrl" character varying(255),
    "emailAddress" character varying(100),
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.team_members OWNER TO don;

--
-- Name: technology_items; Type: TABLE; Schema: public; Owner: don
--

CREATE TABLE public.technology_items (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    icon character varying(50) NOT NULL,
    level character varying(20) NOT NULL,
    experience character varying(50) NOT NULL,
    "stackId" integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.technology_items OWNER TO don;

--
-- Name: technology_items_id_seq; Type: SEQUENCE; Schema: public; Owner: don
--

CREATE SEQUENCE public.technology_items_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.technology_items_id_seq OWNER TO don;

--
-- Name: technology_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: don
--

ALTER SEQUENCE public.technology_items_id_seq OWNED BY public.technology_items.id;


--
-- Name: technology_stacks; Type: TABLE; Schema: public; Owner: don
--

CREATE TABLE public.technology_stacks (
    id integer NOT NULL,
    category character varying(100) NOT NULL,
    icon character varying(50) NOT NULL,
    description text NOT NULL,
    "bgColor" character varying(100) NOT NULL,
    "textColor" character varying(100) NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.technology_stacks OWNER TO don;

--
-- Name: technology_stacks_id_seq; Type: SEQUENCE; Schema: public; Owner: don
--

CREATE SEQUENCE public.technology_stacks_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.technology_stacks_id_seq OWNER TO don;

--
-- Name: technology_stacks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: don
--

ALTER SEQUENCE public.technology_stacks_id_seq OWNED BY public.technology_stacks.id;


--
-- Name: website_portfolio; Type: TABLE; Schema: public; Owner: don
--

CREATE TABLE public.website_portfolio (
    id text NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    category character varying(100) NOT NULL,
    "imageSrc" text NOT NULL,
    url text NOT NULL,
    featured boolean DEFAULT false NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.website_portfolio OWNER TO don;

--
-- Name: blog_posts id; Type: DEFAULT; Schema: public; Owner: don
--

ALTER TABLE ONLY public.blog_posts ALTER COLUMN id SET DEFAULT nextval('public.blog_posts_id_seq'::regclass);


--
-- Name: categories id; Type: DEFAULT; Schema: public; Owner: don
--

ALTER TABLE ONLY public.categories ALTER COLUMN id SET DEFAULT nextval('public.categories_id_seq'::regclass);


--
-- Name: pricing id; Type: DEFAULT; Schema: public; Owner: don
--

ALTER TABLE ONLY public.pricing ALTER COLUMN id SET DEFAULT nextval('public.pricing_id_seq'::regclass);


--
-- Name: technology_items id; Type: DEFAULT; Schema: public; Owner: don
--

ALTER TABLE ONLY public.technology_items ALTER COLUMN id SET DEFAULT nextval('public.technology_items_id_seq'::regclass);


--
-- Name: technology_stacks id; Type: DEFAULT; Schema: public; Owner: don
--

ALTER TABLE ONLY public.technology_stacks ALTER COLUMN id SET DEFAULT nextval('public.technology_stacks_id_seq'::regclass);


--
-- Data for Name: _prisma_migrations; Type: TABLE DATA; Schema: public; Owner: don
--

COPY public._prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count) FROM stdin;
62df2045-4682-4b76-9822-bfbde878ee6e	baseline_migration_checksum	2025-05-11 21:51:57.923671+02	20250513000000_baseline	\N	\N	2025-05-11 21:51:57.923671+02	1
\.


--
-- Data for Name: blog_posts; Type: TABLE DATA; Schema: public; Owner: don
--

COPY public.blog_posts (id, title, slug, content, excerpt, author, category, tags, status, created_at, updated_at, published_at) FROM stdin;
1	Why Every Kenyan Business Needs a Professional Website in 2025	why-every-kenyan-business-needs-a-professional-website-in-2025	<p>In today’s digital world, your website is more than just an online brochure—it’s your <strong>first impression</strong>, <strong>sales engine</strong>, and <strong>brand ambassador</strong> rolled into one. For businesses in Kenya, especially in fast-growing urban areas like <strong>Nairobi, Mombasa, Kisumu, and Nakuru</strong>, a professional website is the key to visibility, credibility, and long-term growth.</p><p>At <strong>Mocky Graphics</strong>, we specialize in creating <strong>custom, SEO-optimized websites in Kenya</strong> that don’t just look good—but perform. Here's why your business needs a website and how we can help you thrive online.</p><hr><h3>✅ 1. Your Customers Are Already Online</h3><p>Over <strong>26 million Kenyans are internet users</strong>, and most search online before making purchasing decisions. If your business doesn’t have a website—or if your website isn’t optimized—you’re likely losing out to competitors who’ve invested in their online presence.</p><p><strong>An SEO-friendly website</strong> ensures that your business shows up when people search for:</p><ul><li><p>“web development in Kenya”</p></li><li><p>“affordable web designers Nairobi”</p></li><li><p>“eCommerce websites Kenya”</p></li><li><p>“freelance web developers in Kenya”</p></li></ul><hr><h3>✅ 2. A Website Builds Trust &amp; Professionalism</h3><p>Consumers are more likely to trust a business with a professionally designed website. Whether you run a law firm, sell beauty products, or offer cleaning services in Nairobi, your website gives potential clients confidence in your legitimacy and quality of service.</p><p>We design <strong>clean, responsive, and custom websites</strong> that reflect your brand and build trust instantly.</p><hr><h3>✅ 3. SEO Drives Organic Traffic (and Saves You Money)</h3><p>Search Engine Optimization (SEO) helps your website rank high in Google results. A properly structured website can drive <strong>free, consistent traffic</strong> from people actively searching for your services.</p><p>Our web development packages at <strong>Mocky Graphics</strong> include:</p><ul><li><p>Optimized site speed</p></li><li><p>Mobile responsiveness</p></li><li><p>Keyword-rich content structure</p></li><li><p>SEO metadata setup (titles, descriptions, tags)</p></li><li><p>Google Analytics integration</p></li></ul><p>We build your site to <strong>rank and convert</strong> from day one.</p><hr><h3>✅ 4. E-Commerce &amp; Booking Features Help You Sell 24/7</h3><p>Whether you're selling physical products, courses, or booking appointments, your website can automate sales, payments, and communication.</p><p>Our eCommerce and service-based websites include:</p><ul><li><p>Product listings and inventory tracking</p></li><li><p>M-Pesa and card payment integration</p></li><li><p>Booking forms with automated responses</p></li><li><p>Blog sections to boost SEO even further</p></li></ul><hr><h3>✅ 5. Full Control Over Your Digital Brand</h3><p>With your own website, you control your brand messaging, design, and marketing strategy. You’re not limited by social media algorithms or platform policies. You own your digital real estate—and that matters.</p><hr><h2>💼 Why Choose Mocky Graphics?</h2><p>We’re a Nairobi-based creative agency with a national footprint, helping businesses across Kenya go online confidently. Here’s what sets us apart:</p><ul><li><p>✅ Local expertise with global design standards</p></li><li><p>✅ Fast turnaround times (7–14 days average)</p></li><li><p>✅ Affordable packages tailored to your budget</p></li><li><p>✅ 1-on-1 consultation and post-launch support</p></li><li><p>✅ Proven portfolio across multiple industries</p></li></ul><hr><h2>📌 Ready to Get Started?</h2><ol><li><p><strong>Browse our website packages:</strong> <a target="_blank" rel="noopener noreferrer nofollow" href="https://mocky.co.ke/pricing">https://mocky.co.ke/pricing</a></p></li><li><p><strong>Check our work process:</strong> <a target="_blank" rel="noopener noreferrer nofollow" href="https://mocky.co.ke/process">https://mocky.co.ke/process</a></p></li><li><p><strong>View our portfolio:</strong> <a target="_blank" rel="noopener noreferrer nofollow" href="https://mocky.co.ke/web">https://mocky.co.ke/web</a></p></li><li><p><strong>Fill the form to get started:</strong> <a target="_blank" rel="noopener noreferrer nofollow" href="https://mocky.co.ke/web#form">https://mocky.co.ke/web#form</a></p></li></ol><p>Let’s build a website that ranks high, converts leads, and grows your business in Kenya and beyond.</p>	In today’s digital world, your website is more than just an online brochure—it’s your first impression, sales engine, and brand ambassador rolled into one. For businesses in Kenya, especially in fast-growing urban areas like Nairobi, Mombasa, Kisumu, and Nakuru, a professional website is the key to visibility, credibility, and long-term growth.\n\nAt Mocky Graphics, we specialize in creating custom, SEO-optimized websites in Kenya that don’t just look good—but perform. Here's why your business needs a website and how we can help you thrive online.	Admin	uncategorized	{}	published	2025-05-11 16:53:08.876	2025-05-11 16:53:08.876	2025-05-11 16:53:08.866
\.


--
-- Data for Name: categories; Type: TABLE DATA; Schema: public; Owner: don
--

COPY public.categories (id, name, slug, description, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: company_story; Type: TABLE DATA; Schema: public; Owner: don
--

COPY public.company_story (id, title, subtitle, "imageSrc", quote1, quote2, "founderName", "founderRole", "linkedinUrl", "twitterUrl", "instagramUrl", "tiktokUrl", "isActive", created_at, updated_at) FROM stdin;
434c29aa-656d-4794-a38c-ae9400fdbdc0	Our Journey	From humble beginnings to digital excellence	/images/about/ceo.jpg	We started Mocky Digital with a simple mission: to help Kenyan businesses succeed in the digital world through exceptional design and marketing.	Every day, we strive to deliver work that not only looks great but also drives real business results for our clients.	Don Omondi	Founder & Creative Director	https://ke.linkedin.com/in/don-omondi-*********	https://x.com/onyango__omondi	https://www.instagram.com/tk_omondi/	https://www.tiktok.com/@mocky_digital	t	2025-05-11 19:20:12.605	2025-05-11 19:38:24.65
\.


--
-- Data for Name: pricing; Type: TABLE DATA; Schema: public; Owner: don
--

COPY public.pricing (id, service, price, description, created_at, updated_at, features, icon, popular) FROM stdin;
\.


--
-- Data for Name: site_settings; Type: TABLE DATA; Schema: public; Owner: don
--

COPY public.site_settings (id, "siteName", "siteDescription", "contactEmail", "phoneNumber", address, "facebookUrl", "twitterUrl", "instagramUrl", "tiktokUrl", "linkedinUrl", "metaTitle", "metaDescription", "googleAnalyticsId", created_at, updated_at) FROM stdin;
b08a3ac3-af09-4db2-b9f2-da2bca200df2	Mocky Digital	Professional web design and digital marketing services	<EMAIL>	+*********** 670	Nairobi, Kenya	https://facebook.com/mockydigital	https://x.com/mockydigital	https://instagram.com/mockydigital	https://tiktok.com/@mocky_digital	https://linkedin.com/company/mockydigital	Mocky Digital - Web Design & Digital Marketing	Professional web design, development, and digital marketing services in Nairobi, Kenya.		2025-05-11 15:54:20.193	2025-05-11 15:54:20.193
\.


--
-- Data for Name: storage_config; Type: TABLE DATA; Schema: public; Owner: don
--

COPY public.storage_config (id, provider, region, endpoint, "bucketName", "accessKeyId", "secretAccessKey", "isDefault", created_at, updated_at) FROM stdin;
35566b0e-68ed-4310-b9c4-9e52c640af48	S3	fr-par-1	https://fr-par-1.linodeobjects.com	mocky	73OQS52ORLRPBO3KG6YN	p5UW5FZ7Gog5PMP749MpdHGhPRXUKYnStFJtAaMx	t	2025-05-11 16:05:01.589	2025-05-11 16:05:01.589
\.


--
-- Data for Name: team_members; Type: TABLE DATA; Schema: public; Owner: don
--

COPY public.team_members (id, name, role, bio, "imageSrc", "order", "linkedinUrl", "twitterUrl", "githubUrl", "emailAddress", created_at, updated_at) FROM stdin;
f474e871-7141-41e9-98a2-7a382ae91f8e	Don Omondi Onyango	Graphics designer|| Web developer	Don Omondi Onyango is a passionate Graphics Designer and Web Developer based in Nairobi, Kenya. With over 5 years of experience in crafting visual identities and building responsive, SEO-optimized websites, Don brings a unique blend of creativity and technical expertise to every project. He specializes in logo design, UI/UX, WordPress development, and modern web frameworks, helping businesses stand out and thrive online. Don is committed to delivering high-quality digital solutions tailored to client goals, whether for startups, SMEs, or established brands.	https://fr-par-1.linodeobjects.com/mocky/team/f474e871-7141-41e9-98a2-7a382ae91f8e/1746992116168_WhatsApp_Image_2025-05-08_at_06.20.35__1_.jpeg	0	\N	\N	\N	\N	2025-05-11 17:50:15.996	2025-05-11 19:35:16.477
fd76aae7-ba7c-43b1-ba92-1f1b2cc83965	Jack Sequeira Onyango	Graphics designer	With a strong understanding of design trends and digital aesthetics, he transforms client visions into compelling visuals that stand out in today’s competitive market. Jack is passionate about helping businesses communicate effectively through design and is known for his creativity, precision, and reliability.	https://fr-par-1.linodeobjects.com/mocky/team/1746993581803_retry2_hatsApp_Image_2025-05-11_at_22.57.32_5cb37efd.jpg	0	\N	\N	\N	\N	2025-05-11 19:59:58.201	2025-05-11 19:59:58.201
\.


--
-- Data for Name: technology_items; Type: TABLE DATA; Schema: public; Owner: don
--

COPY public.technology_items (id, name, icon, level, experience, "stackId", created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: technology_stacks; Type: TABLE DATA; Schema: public; Owner: don
--

COPY public.technology_stacks (id, category, icon, description, "bgColor", "textColor", created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: website_portfolio; Type: TABLE DATA; Schema: public; Owner: don
--

COPY public.website_portfolio (id, title, description, category, "imageSrc", url, featured, created_at, updated_at) FROM stdin;
b71a38bb-094a-48d3-ba8a-4705eb1bd64e	Home Experience	Small Online shop website	e-commerce	https://fr-par-1.linodeobjects.com/mocky/images/portfolio/websites/17.jpg	https://homestore.co.ke	f	2025-05-11 16:56:43.162	2025-05-11 16:56:43.162
\.


--
-- Name: blog_posts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: don
--

SELECT pg_catalog.setval('public.blog_posts_id_seq', 1, true);


--
-- Name: categories_id_seq; Type: SEQUENCE SET; Schema: public; Owner: don
--

SELECT pg_catalog.setval('public.categories_id_seq', 1, false);


--
-- Name: pricing_id_seq; Type: SEQUENCE SET; Schema: public; Owner: don
--

SELECT pg_catalog.setval('public.pricing_id_seq', 1, false);


--
-- Name: technology_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: don
--

SELECT pg_catalog.setval('public.technology_items_id_seq', 1, false);


--
-- Name: technology_stacks_id_seq; Type: SEQUENCE SET; Schema: public; Owner: don
--

SELECT pg_catalog.setval('public.technology_stacks_id_seq', 1, false);


--
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: don
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- Name: blog_posts blog_posts_pkey; Type: CONSTRAINT; Schema: public; Owner: don
--

ALTER TABLE ONLY public.blog_posts
    ADD CONSTRAINT blog_posts_pkey PRIMARY KEY (id);


--
-- Name: categories categories_pkey; Type: CONSTRAINT; Schema: public; Owner: don
--

ALTER TABLE ONLY public.categories
    ADD CONSTRAINT categories_pkey PRIMARY KEY (id);


--
-- Name: company_story company_story_pkey; Type: CONSTRAINT; Schema: public; Owner: don
--

ALTER TABLE ONLY public.company_story
    ADD CONSTRAINT company_story_pkey PRIMARY KEY (id);


--
-- Name: pricing pricing_pkey; Type: CONSTRAINT; Schema: public; Owner: don
--

ALTER TABLE ONLY public.pricing
    ADD CONSTRAINT pricing_pkey PRIMARY KEY (id);


--
-- Name: site_settings site_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: don
--

ALTER TABLE ONLY public.site_settings
    ADD CONSTRAINT site_settings_pkey PRIMARY KEY (id);


--
-- Name: storage_config storage_config_pkey; Type: CONSTRAINT; Schema: public; Owner: don
--

ALTER TABLE ONLY public.storage_config
    ADD CONSTRAINT storage_config_pkey PRIMARY KEY (id);


--
-- Name: team_members team_members_pkey; Type: CONSTRAINT; Schema: public; Owner: don
--

ALTER TABLE ONLY public.team_members
    ADD CONSTRAINT team_members_pkey PRIMARY KEY (id);


--
-- Name: technology_items technology_items_pkey; Type: CONSTRAINT; Schema: public; Owner: don
--

ALTER TABLE ONLY public.technology_items
    ADD CONSTRAINT technology_items_pkey PRIMARY KEY (id);


--
-- Name: technology_stacks technology_stacks_pkey; Type: CONSTRAINT; Schema: public; Owner: don
--

ALTER TABLE ONLY public.technology_stacks
    ADD CONSTRAINT technology_stacks_pkey PRIMARY KEY (id);


--
-- Name: website_portfolio website_portfolio_pkey; Type: CONSTRAINT; Schema: public; Owner: don
--

ALTER TABLE ONLY public.website_portfolio
    ADD CONSTRAINT website_portfolio_pkey PRIMARY KEY (id);


--
-- Name: blog_posts_slug_key; Type: INDEX; Schema: public; Owner: don
--

CREATE UNIQUE INDEX blog_posts_slug_key ON public.blog_posts USING btree (slug);


--
-- Name: categories_slug_key; Type: INDEX; Schema: public; Owner: don
--

CREATE UNIQUE INDEX categories_slug_key ON public.categories USING btree (slug);


--
-- Name: technology_items technology_items_stackId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: don
--

ALTER TABLE ONLY public.technology_items
    ADD CONSTRAINT "technology_items_stackId_fkey" FOREIGN KEY ("stackId") REFERENCES public.technology_stacks(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: don
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;


--
-- PostgreSQL database dump complete
--

