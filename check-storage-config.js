// Check StorageConfig entries in the database
require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Checking StorageConfig entries in the database...');
    
    // Check if StorageConfig model exists
    if (!prisma.storageConfig) {
      console.error('StorageConfig model not found in Prisma client');
      return;
    }
    
    // Get all StorageConfig entries
    const configs = await prisma.storageConfig.findMany();
    
    console.log(`Found ${configs.length} StorageConfig entries`);
    
    if (configs.length > 0) {
      configs.forEach((config, index) => {
        console.log(`\nConfig #${index + 1}:`);
        console.log(`  ID: ${config.id}`);
        console.log(`  Provider: ${config.provider}`);
        console.log(`  Region: ${config.region}`);
        console.log(`  Endpoint: ${config.endpoint}`);
        console.log(`  Bucket: ${config.bucketName}`);
        console.log(`  Access Key: ${config.accessKeyId ? '✓ Set' : '✗ Not set'}`);
        console.log(`  Secret Key: ${config.secretAccessKey ? '✓ Set' : '✗ Not set'}`);
        console.log(`  Is Default: ${config.isDefault ? 'Yes' : 'No'}`);
        console.log(`  Created At: ${config.createdAt}`);
        console.log(`  Updated At: ${config.updatedAt}`);
      });
    } else {
      console.log('No StorageConfig entries found. Creating a default one...');
      
      // Create a default StorageConfig entry
      const newConfig = await prisma.storageConfig.create({
        data: {
          provider: 'S3',
          region: 'fr-par-1',
          endpoint: 'https://fr-par-1.linodeobjects.com',
          bucketName: 'mocky',
          accessKeyId: '73OQS52ORLRPBO3KG6YN',
          secretAccessKey: 'p5UW5FZ7Gog5PMP749MpdHGhPRXUKYnStFJtAaMx',
          isDefault: true
        }
      });
      
      console.log('Created default StorageConfig entry:');
      console.log(`  ID: ${newConfig.id}`);
      console.log(`  Provider: ${newConfig.provider}`);
      console.log(`  Region: ${newConfig.region}`);
      console.log(`  Endpoint: ${newConfig.endpoint}`);
      console.log(`  Bucket: ${newConfig.bucketName}`);
      console.log(`  Access Key: ${newConfig.accessKeyId ? '✓ Set' : '✗ Not set'}`);
      console.log(`  Secret Key: ${newConfig.secretAccessKey ? '✓ Set' : '✗ Not set'}`);
      console.log(`  Is Default: ${newConfig.isDefault ? 'Yes' : 'No'}`);
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
