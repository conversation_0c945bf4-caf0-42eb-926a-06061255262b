const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const path = require('path');
const numCPUs = require('os').cpus().length;
const net = require('net');

const dev = process.env.NODE_ENV !== 'production';
const hostname = process.env.HOSTNAME || '0.0.0.0';
const initialPort = parseInt(process.env.PORT || '3000', 10);

// Optimize file system operations
const fs = require('fs').promises;
process.env.UV_THREADPOOL_SIZE = 8; // Optimize libuv thread pool for I/O

// Error handling utility
const handleError = (err, res) => {
  console.error(err);
  res.statusCode = 500;
  res.setHeader('Content-Type', 'application/json');
  res.end(JSON.stringify({
    error: 'Internal Server Error',
    message: dev ? err.message : 'An error occurred'
  }));
};

// Function to check if a port is available
function isPortAvailable(port) {
  return new Promise((resolve) => {
    const server = net.createServer();

    server.once('error', (err) => {
      if (err.code === 'EADDRINUSE' || err.code === 'EACCES') {
        console.log(`Port ${port} is not available, error code: ${err.code}`);
        resolve(false);
      } else {
        console.log(`Port ${port} check error: ${err.code}, assuming available`);
        resolve(true);
      }
    });

    server.once('listening', () => {
      // Close the server and resolve with true (port is available)
      server.close(() => {
        console.log(`Port ${port} is available`);
        resolve(true);
      });
    });

    // Try to listen on the port
    try {
      server.listen(port, hostname);
    } catch (err) {
      console.log(`Exception when checking port ${port}: ${err.message}`);
      resolve(false);
    }
  });
}

// Function to find the next available port
async function findAvailablePort(startPort, maxAttempts = 20) {
  let port = startPort;
  let attempts = 0;

  console.log(`Looking for an available port starting from ${startPort}...`);

  while (attempts < maxAttempts) {
    console.log(`Checking port ${port} (attempt ${attempts + 1}/${maxAttempts})...`);
    const available = await isPortAvailable(port);

    if (available) {
      console.log(`Found available port: ${port}`);
      return port;
    }

    console.log(`Port ${port} is in use, trying next port...`);
    port++;
    attempts++;
  }

  console.error(`Could not find an available port after ${maxAttempts} attempts`);
  // If we can't find an available port, return a high random port as a last resort
  const fallbackPort = Math.floor(10000 + Math.random() * 50000);
  console.log(`Using fallback port: ${fallbackPort}`);
  return fallbackPort;
}

async function startServer() {
  try {
    // Ensure required directories exist
    console.log('Ensuring required directories exist...');
    const requiredDirs = [
      path.join(process.cwd(), 'public', 'uploads'),
      path.join(process.cwd(), 'public', 'uploads', 'receipts'),
      path.join(process.cwd(), 'public', 'uploads', 'blog')
    ];

    for (const dir of requiredDirs) {
      try {
        await fs.mkdir(dir, { recursive: true });
        console.log(`Directory ensured: ${dir}`);
      } catch (err) {
        console.warn(`Warning: Could not create directory ${dir}:`, err.message);
      }
    }

    // Find an available port starting from initialPort
    const port = await findAvailablePort(initialPort);

    // Prepare the Next.js app
    const app = next({ dev, hostname, port });
    const handle = app.getRequestHandler();

    await app.prepare();

    const server = createServer(async (req, res) => {
      try {
        // Basic security headers
        res.setHeader('X-Content-Type-Options', 'nosniff');
        res.setHeader('X-Frame-Options', 'DENY');
        res.setHeader('X-XSS-Protection', '1; mode=block');

        // CORS headers for API routes
        if (req.url?.startsWith('/api/')) {
          res.setHeader('Access-Control-Allow-Origin', process.env.NEXT_PUBLIC_APP_URL || '*');
          res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
          res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

          if (req.method === 'OPTIONS') {
            res.statusCode = 204;
            res.end();
            return;
          }
        }

        const parsedUrl = parse(req.url || '', true);

        // Add cache control for static assets
        if (req.url?.match(/^\/static\/|^\/images\/|\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/)) {
          res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
        }

        await handle(req, res, parsedUrl);
      } catch (err) {
        handleError(err, res);
      }
    });

    // Server optimization
    server.keepAliveTimeout = 120000;
    server.headersTimeout = 120000;

    // Start listening
    server.listen(port, hostname, (err) => {
      if (err) throw err;
      console.log(`> Ready on http://${hostname}:${port}`);
      if (port !== initialPort) {
        console.log(`> Note: Port ${initialPort} was in use, using port ${port} instead`);
      }
      console.log('> Mode:', process.env.NODE_ENV);
      console.log('> Worker:', process.pid);

      // Signal ready to PM2
      if (process.send) {
        process.send('ready');
      }
    });

    // Graceful shutdown
    const gracefulShutdown = () => {
      console.log('Received kill signal, shutting down gracefully');
      server.close(() => {
        console.log('Closed out remaining connections');
        process.exit(0);
      });

      setTimeout(() => {
        console.error('Could not close connections in time, forcefully shutting down');
        process.exit(1);
      }, 30000);
    };

    process.on('SIGTERM', gracefulShutdown);
    process.on('SIGINT', gracefulShutdown);

    return server;
  } catch (error) {
    console.error('Error during server startup:', error);
    throw error;
  }
}

// Directly call startServer instead of using cluster module
// PM2 will handle the clustering for us
startServer().catch(err => {
  console.error('Error starting server:', err);
  process.exit(1);
});