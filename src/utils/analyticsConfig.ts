/**
 * Analytics Configuration Utilities
 * 
 * This file contains utilities for validating and managing Google Analytics configuration
 */

export interface AnalyticsConfig {
  measurementId: string;
  propertyId: string;
  clientEmail: string;
  privateKey: string;
  isConfigured: boolean;
  isValid: boolean;
}

/**
 * Check if Google Analytics is properly configured
 */
export function getAnalyticsConfig(): AnalyticsConfig {
  const measurementId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || '';
  const propertyId = process.env.GOOGLE_ANALYTICS_PROPERTY_ID || '';
  const clientEmail = process.env.GOOGLE_ANALYTICS_CLIENT_EMAIL || '';
  const privateKey = process.env.GOOGLE_ANALYTICS_PRIVATE_KEY || '';

  // Check if basic configuration exists
  const isConfigured = !!(measurementId && propertyId && clientEmail && privateKey);

  // Check if configuration values are not placeholder values
  const isValid = isConfigured && 
    measurementId !== 'G-XXXXXXXXXX' &&
    propertyId !== 'properties/*********' &&
    clientEmail !== '<EMAIL>' &&
    privateKey !== '-----BEGIN PRIVATE KEY-----\nYour Private Key Here\n-----END PRIVATE KEY-----';

  return {
    measurementId,
    propertyId,
    clientEmail,
    privateKey,
    isConfigured,
    isValid
  };
}

/**
 * Validate Google Analytics Measurement ID format
 */
export function isValidMeasurementId(id: string): boolean {
  return /^G-[A-Z0-9]{10}$/.test(id);
}

/**
 * Validate Google Analytics Property ID format
 */
export function isValidPropertyId(id: string): boolean {
  return /^properties\/\d+$/.test(id);
}

/**
 * Validate service account email format
 */
export function isValidServiceAccountEmail(email: string): boolean {
  return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.iam\.gserviceaccount\.com$/.test(email);
}

/**
 * Validate private key format
 */
export function isValidPrivateKey(key: string): boolean {
  return key.includes('-----BEGIN PRIVATE KEY-----') && 
         key.includes('-----END PRIVATE KEY-----') &&
         key.length > 100; // Basic length check
}

/**
 * Get configuration status message
 */
export function getConfigurationStatus(): {
  status: 'valid' | 'configured' | 'missing';
  message: string;
  details: string[];
} {
  const config = getAnalyticsConfig();
  const details: string[] = [];

  if (!config.isConfigured) {
    if (!config.measurementId) details.push('Missing NEXT_PUBLIC_GA_MEASUREMENT_ID');
    if (!config.propertyId) details.push('Missing GOOGLE_ANALYTICS_PROPERTY_ID');
    if (!config.clientEmail) details.push('Missing GOOGLE_ANALYTICS_CLIENT_EMAIL');
    if (!config.privateKey) details.push('Missing GOOGLE_ANALYTICS_PRIVATE_KEY');

    return {
      status: 'missing',
      message: 'Google Analytics is not configured. Missing required environment variables.',
      details
    };
  }

  if (!config.isValid) {
    if (!isValidMeasurementId(config.measurementId)) {
      details.push('Invalid NEXT_PUBLIC_GA_MEASUREMENT_ID format (should be G-XXXXXXXXXX)');
    }
    if (!isValidPropertyId(config.propertyId)) {
      details.push('Invalid GOOGLE_ANALYTICS_PROPERTY_ID format (should be properties/*********)');
    }
    if (!isValidServiceAccountEmail(config.clientEmail)) {
      details.push('Invalid GOOGLE_ANALYTICS_CLIENT_EMAIL format');
    }
    if (!isValidPrivateKey(config.privateKey)) {
      details.push('Invalid GOOGLE_ANALYTICS_PRIVATE_KEY format');
    }

    return {
      status: 'configured',
      message: 'Google Analytics is configured but using placeholder values. Please update with real credentials.',
      details
    };
  }

  return {
    status: 'valid',
    message: 'Google Analytics is properly configured and ready to use.',
    details: []
  };
}

/**
 * Check if we should use mock data
 */
export function shouldUseMockData(): boolean {
  const config = getAnalyticsConfig();
  return !config.isValid;
}
