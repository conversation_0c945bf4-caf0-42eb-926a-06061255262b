import { ErrorTracker } from '@/services/errorTracking';

// Performance monitoring
export class CataloguePerformanceMonitor {
  private static metrics = new Map<string, {
    count: number;
    totalTime: number;
    minTime: number;
    maxTime: number;
    errors: number;
  }>();

  static startTimer(operation: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.recordMetric(operation, duration);
    };
  }

  private static recordMetric(operation: string, duration: number) {
    const existing = this.metrics.get(operation) || {
      count: 0,
      totalTime: 0,
      minTime: Infinity,
      maxTime: 0,
      errors: 0
    };

    existing.count++;
    existing.totalTime += duration;
    existing.minTime = Math.min(existing.minTime, duration);
    existing.maxTime = Math.max(existing.maxTime, duration);

    this.metrics.set(operation, existing);
  }

  static recordError(operation: string) {
    const existing = this.metrics.get(operation);
    if (existing) {
      existing.errors++;
    }
  }

  static getMetrics() {
    const result: Record<string, any> = {};
    
    for (const [operation, metrics] of this.metrics.entries()) {
      result[operation] = {
        count: metrics.count,
        avgTime: metrics.count > 0 ? metrics.totalTime / metrics.count : 0,
        minTime: metrics.minTime === Infinity ? 0 : metrics.minTime,
        maxTime: metrics.maxTime,
        errorRate: metrics.count > 0 ? (metrics.errors / metrics.count) * 100 : 0,
        errors: metrics.errors
      };
    }
    
    return result;
  }

  static reset() {
    this.metrics.clear();
  }
}

// Cache management
export class CatalogueCache {
  private static cache = new Map<string, {
    data: any;
    timestamp: number;
    ttl: number;
  }>();

  static set(key: string, data: any, ttlMs: number = 300000) { // 5 minutes default
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMs
    });
  }

  static get(key: string): any | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  static invalidate(pattern?: string) {
    if (!pattern) {
      this.cache.clear();
      return;
    }

    const regex = new RegExp(pattern);
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
      }
    }
  }

  static getStats() {
    const now = Date.now();
    let validEntries = 0;
    let expiredEntries = 0;

    for (const entry of this.cache.values()) {
      if (now - entry.timestamp > entry.ttl) {
        expiredEntries++;
      } else {
        validEntries++;
      }
    }

    return {
      totalEntries: this.cache.size,
      validEntries,
      expiredEntries,
      hitRate: 0 // Would need to track hits/misses separately
    };
  }
}

// Health check utilities
export class CatalogueHealthCheck {
  static async checkDatabaseConnection(): Promise<boolean> {
    try {
      const { PrismaClient } = await import('@prisma/client');
      const prisma = new PrismaClient();
      
      await prisma.$queryRaw`SELECT 1`;
      await prisma.$disconnect();
      
      return true;
    } catch (error) {
      ErrorTracker.trackError(error as Error, 'CatalogueHealthCheck.checkDatabaseConnection');
      return false;
    }
  }

  static async checkCatalogueTableHealth(): Promise<{
    isHealthy: boolean;
    totalItems: number;
    recentItems: number;
    issues: string[];
  }> {
    const issues: string[] = [];
    let totalItems = 0;
    let recentItems = 0;

    try {
      const { PrismaClient } = await import('@prisma/client');
      const prisma = new PrismaClient();

      // Check total items
      totalItems = await prisma.catalogue.count();

      // Check recent items (last 24 hours)
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
      recentItems = await prisma.catalogue.count({
        where: {
          createdAt: {
            gte: yesterday
          }
        }
      });

      // Check for duplicate service names
      const duplicates = await prisma.$queryRaw<Array<{ service: string; count: number }>>`
        SELECT LOWER(service) as service, COUNT(*) as count
        FROM catalogue
        GROUP BY LOWER(service)
        HAVING COUNT(*) > 1
      `;

      if (duplicates.length > 0) {
        issues.push(`Found ${duplicates.length} duplicate service names`);
      }

      // Check for invalid prices
      const invalidPrices = await prisma.catalogue.count({
        where: {
          OR: [
            { price: { lte: 0 } },
            { price: { gt: 10000000 } }
          ]
        }
      });

      if (invalidPrices > 0) {
        issues.push(`Found ${invalidPrices} items with invalid prices`);
      }

      // Check for empty service names
      const emptyServices = await prisma.catalogue.count({
        where: {
          OR: [
            { service: '' },
            { service: null }
          ]
        }
      });

      if (emptyServices > 0) {
        issues.push(`Found ${emptyServices} items with empty service names`);
      }

      await prisma.$disconnect();

      return {
        isHealthy: issues.length === 0,
        totalItems,
        recentItems,
        issues
      };
    } catch (error) {
      ErrorTracker.trackError(error as Error, 'CatalogueHealthCheck.checkCatalogueTableHealth');
      issues.push(`Database query failed: ${(error as Error).message}`);
      
      return {
        isHealthy: false,
        totalItems,
        recentItems,
        issues
      };
    }
  }

  static async performFullHealthCheck(): Promise<{
    overall: boolean;
    database: boolean;
    catalogue: {
      isHealthy: boolean;
      totalItems: number;
      recentItems: number;
      issues: string[];
    };
    performance: Record<string, any>;
    cache: {
      totalEntries: number;
      validEntries: number;
      expiredEntries: number;
      hitRate: number;
    };
    timestamp: string;
  }> {
    const database = await this.checkDatabaseConnection();
    const catalogue = await this.checkCatalogueTableHealth();
    const performance = CataloguePerformanceMonitor.getMetrics();
    const cache = CatalogueCache.getStats();

    return {
      overall: database && catalogue.isHealthy,
      database,
      catalogue,
      performance,
      cache,
      timestamp: new Date().toISOString()
    };
  }
}

// Query optimization utilities
export class CatalogueQueryOptimizer {
  static buildOptimizedWhere(filters: any) {
    const where: any = {};
    
    // Use indexed fields efficiently
    if (filters.category) {
      where.category = filters.category;
    }
    
    if (filters.popular !== undefined) {
      where.popular = filters.popular;
    }
    
    // Optimize price range queries
    if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
      where.price = {};
      if (filters.minPrice !== undefined) {
        where.price.gte = filters.minPrice;
      }
      if (filters.maxPrice !== undefined) {
        where.price.lte = filters.maxPrice;
      }
    }
    
    // Use full-text search for text queries
    if (filters.search) {
      const searchTerm = filters.search.trim();
      if (searchTerm.length >= 2) {
        where.OR = [
          { service: { contains: searchTerm, mode: 'insensitive' } },
          { description: { contains: searchTerm, mode: 'insensitive' } },
          { category: { contains: searchTerm, mode: 'insensitive' } }
        ];
      }
    }
    
    return where;
  }

  static buildOptimizedOrderBy(sortBy?: string, sortOrder?: string) {
    const validSortFields = ['service', 'price', 'createdAt', 'updatedAt', 'popular'];
    const validSortOrders = ['asc', 'desc'];
    
    const field = validSortFields.includes(sortBy || '') ? sortBy : 'service';
    const order = validSortOrders.includes(sortOrder || '') ? sortOrder : 'asc';
    
    // Optimize sorting for common patterns
    if (field === 'popular') {
      return [
        { popular: order },
        { service: 'asc' } // Secondary sort for consistency
      ];
    }
    
    return { [field]: order };
  }

  static calculateOptimalLimit(requestedLimit?: number, hasFilters: boolean = false) {
    const defaultLimit = 20;
    const maxLimit = hasFilters ? 50 : 100; // Lower limit when filtering
    
    if (!requestedLimit) {
      return defaultLimit;
    }
    
    return Math.min(Math.max(requestedLimit, 1), maxLimit);
  }
}

// Audit logging
export class CatalogueAuditLogger {
  static async logAction(
    action: 'CREATE' | 'UPDATE' | 'DELETE' | 'BULK_DELETE',
    catalogueId: string | string[],
    userId?: string,
    ipAddress?: string,
    userAgent?: string,
    oldData?: any,
    newData?: any
  ) {
    try {
      const logEntry = {
        action,
        catalogueId: Array.isArray(catalogueId) ? catalogueId.join(',') : catalogueId,
        userId,
        ipAddress,
        userAgent,
        oldData: oldData ? JSON.stringify(oldData) : null,
        newData: newData ? JSON.stringify(newData) : null,
        timestamp: new Date().toISOString()
      };

      // In a real implementation, this would write to a database or external service
      console.log('Catalogue Audit Log:', logEntry);
      
      // Could also send to external monitoring service
      if (process.env.NODE_ENV === 'production') {
        // await sendToMonitoringService(logEntry);
      }
    } catch (error) {
      ErrorTracker.trackError(error as Error, 'CatalogueAuditLogger.logAction');
    }
  }
}
