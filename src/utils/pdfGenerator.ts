/**
 * Utility for generating PDF documents (receipts, invoices, quotes)
 */
import PDFDocument from 'pdfkit';
import { promises as fs } from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import fs_sync from 'fs';
import axios from 'axios';

// Define supported paper sizes
export type PaperSize = 'A4' | 'A5' | 'LETTER' | 'LEGAL' | 'TABLOID' | 'EXECUTIVE' | 'AUTO';

// Define paper dimensions in points (72 points = 1 inch)
const PAPER_DIMENSIONS = {
  A4: { width: 595.28, height: 841.89 },
  A5: { width: 419.53, height: 595.28 },
  LETTER: { width: 612, height: 792 },
  LEGAL: { width: 612, height: 1008 },
  TABLOID: { width: 792, height: 1224 },
  EXECUTIVE: { width: 521.86, height: 756 },
  AUTO: { width: 0, height: 0 } // Will be determined dynamically
};

// Define common item data interface
export interface ItemData {
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  serviceName?: string;
}

// Define the receipt data interface
export interface ReceiptData {
  receiptNumber: string;
  customerName: string;
  phoneNumber: string;
  email?: string;
  transactionId: string;
  transactionDate: Date;
  items: ItemData[];
  totalAmount: number;
  amountPaid: number;
  balance: number;
  notes?: string;
  issuedAt: Date;
  paperSize?: PaperSize; // Optional paper size, defaults to A4
  autoScale?: boolean; // Whether to automatically scale content to fit the page
}

// Define the invoice data interface
export interface InvoiceData {
  invoiceNumber: string;
  customerName: string;
  phoneNumber: string;
  email?: string;
  items: ItemData[];
  totalAmount: number;
  amountPaid: number;
  balance: number;
  notes?: string;
  issuedAt: string;
  dueDate: string;
  status: string;
  paperSize?: PaperSize; // Optional paper size, defaults to A4
  autoScale?: boolean; // Whether to automatically scale content to fit the page
}

// Define the quote data interface
export interface QuoteData {
  quoteNumber: string;
  customerName: string;
  phoneNumber: string;
  email?: string;
  items: ItemData[];
  totalAmount: number;
  notes?: string;
  issuedAt: string;
  validUntil: string;
  status: string;
  paperSize?: PaperSize; // Optional paper size, defaults to A4
  autoScale?: boolean; // Whether to automatically scale content to fit the page
}

// For backward compatibility
export type ReceiptItemData = ItemData;

/**
 * Generate a PDF receipt
 * @param receiptData The receipt data
 * @returns The path to the generated PDF file
 */
export async function generateReceiptPDF(receiptData: ReceiptData): Promise<string> {
  try {
    // Validate receipt data before proceeding
    if (!receiptData || !receiptData.receiptNumber) {
      console.error('Invalid receipt data provided to PDF generator');
      throw new Error('Invalid receipt data');
    }

    console.log(`Starting PDF generation for receipt: ${receiptData.receiptNumber}`);

    // Create a filename for the PDF using the transaction ID (which is now the receipt number)
    // The receipt number should be the M-Pesa transaction ID
    const transactionId = receiptData.receiptNumber;
    const filename = `${transactionId}.pdf`;
    console.log(`Generated filename using transaction ID: ${filename}`);

    // Create the uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'receipts');
    console.log(`Ensuring directory exists: ${uploadsDir}`);

    try {
      // Use synchronous version to ensure directory exists before proceeding
      if (!fs_sync.existsSync(uploadsDir)) {
        fs_sync.mkdirSync(uploadsDir, { recursive: true });
        console.log(`Directory created: ${uploadsDir}`);
      } else {
        console.log(`Directory already exists: ${uploadsDir}`);
      }
    } catch (mkdirError) {
      console.error(`Error creating directory ${uploadsDir}:`, mkdirError);
      throw mkdirError;
    }

    // Set the output file path
    const outputPath = path.join(uploadsDir, filename);
    console.log(`Output path for PDF: ${outputPath}`);

    // Determine paper size from receipt data or use default A4
    const paperSize = receiptData.paperSize || 'A4';
    const autoScale = receiptData.autoScale !== undefined ? receiptData.autoScale : true;

    console.log(`Using paper size: ${paperSize}, Auto-scale: ${autoScale}`);

    // Get paper dimensions
    const pageDimensions = PAPER_DIMENSIONS[paperSize];

    // Create a PDF document with the specified paper size
    const doc = new PDFDocument({
      size: paperSize === 'AUTO' ? [pageDimensions.width, pageDimensions.height] : paperSize,
      margin: 50,
      autoFirstPage: true,
      layout: 'portrait',
      // Use a standard PDF font that doesn't require external files
      font: 'Courier',
      info: {
        Title: `Receipt ${receiptData.receiptNumber}`,
        Author: 'Mocky Digital',
        Subject: 'Payment Receipt',
        CreationDate: new Date(),
      },
      // Enable auto-scaling if requested
      autoFirstPageOptions: {
        scale: autoScale ? 'fit' : 1,
      },
    });

    // Create a write stream to the output file using promises-based fs
    const stream = require('fs').createWriteStream(outputPath);

    // Pipe the PDF document to the file stream
    doc.pipe(stream);

    console.log(`Creating PDF file at: ${outputPath}`);

    // Add basic content with simplified formatting to avoid corruption
    try {
      // Get page dimensions for scaling calculations
      const pageWidth = doc.page.width;
      const pageHeight = doc.page.height;
      const margin = 50;
      const contentWidth = pageWidth - (margin * 2);

      // Calculate scaling factor based on paper size if auto-scaling is enabled
      const autoScale = receiptData.autoScale !== undefined ? receiptData.autoScale : true;
      const scaleFactor = autoScale ? Math.min(1, contentWidth / 500) : 1; // 500 is a reference width

      console.log(`Page dimensions: ${pageWidth}x${pageHeight}, Content width: ${contentWidth}, Scale factor: ${scaleFactor}`);

      // Create a header with logo on left and company info on right
      const logoPath = path.join(process.cwd(), 'public', 'images', 'logo.png');
      console.log(`Adding logo from: ${logoPath}`);

      try {
        // Save the current position
        const startY = doc.y;

        // Calculate logo size based on scale factor
        const logoWidth = 120 * scaleFactor;
        const logoHeight = 80 * scaleFactor;

        // Add logo on the left
        doc.image(logoPath, {
          fit: [logoWidth, logoHeight], // Scale logo based on paper size
          align: 'left'
        });

        // Move to the right side for company info
        doc.moveUp(4 * scaleFactor); // Scale the movement
        doc.x = pageWidth - (150 * scaleFactor); // Scale the position

        // Scale font size based on paper size
        const companyInfoFontSize = 12 * scaleFactor;

        // Add company info on the right with right alignment
        doc.fontSize(companyInfoFontSize).text('Mocky Digital', { align: 'right' });
        doc.text('Nairobi, Kenya', { align: 'right' });
        doc.text('Phone: +*********** 670', { align: 'right' });
        doc.text('Email: <EMAIL>', { align: 'right' });
        doc.text('Tax PIN: P052373324V', { align: 'right' });

        // Move to the center for receipt title
        doc.x = (contentWidth / 2) + margin; // Center position
        doc.y = startY + (20 * scaleFactor); // Scale the position

        console.log('Logo and company info added successfully');
      } catch (logoError) {
        console.error('Error adding logo to PDF:', logoError);
        // Continue without the logo if there's an error
      }

      // Header - centered between logo and company info
      const titleFontSize = 18 * scaleFactor;
      doc.fontSize(titleFontSize).text('RECEIPT', { align: 'center', width: 200 * scaleFactor });
      doc.moveDown(0.5);

      // Receipt details - centered between logo and company info
      const detailsFontSize = 12 * scaleFactor;
      doc.fontSize(detailsFontSize);
      doc.text(`Transaction ID: ${receiptData.receiptNumber}`, { align: 'center', width: 200 * scaleFactor });
      doc.text(`Date: ${receiptData.issuedAt.toLocaleDateString()}`, { align: 'center', width: 200 * scaleFactor });

      // Reset position for the rest of the content
      doc.x = margin;
      doc.y += 30 * scaleFactor; // Scale the spacing
      doc.moveDown();

      // Customer info
      doc.text('CUSTOMER INFORMATION', { align: 'left' });
      doc.text(`Name: ${receiptData.customerName}`, { align: 'left' });
      doc.text(`Phone: ${receiptData.phoneNumber}`, { align: 'left' });

      if (receiptData.email) {
        doc.text(`Email: ${receiptData.email}`, { align: 'left' });
      }

      doc.moveDown();

      // Items
      doc.text('ITEMS', { align: 'left' });
      doc.moveDown(0.5);

      // Add items with simplified formatting and scaled spacing
      for (const item of receiptData.items) {
        doc.text(`${item.description} - ${item.quantity} x KES ${item.unitPrice} = KES ${item.totalPrice}`, { align: 'left' });
      }

      doc.moveDown();

      // Totals - align to right with scaled positioning
      const rightMargin = pageWidth - margin;
      doc.text(`Total: KES ${receiptData.totalAmount}`, { align: 'right' });
      doc.text(`Amount Paid: KES ${receiptData.amountPaid}`, { align: 'right' });
      doc.text(`Balance: KES ${receiptData.balance}`, { align: 'right' });

      // Add notes if any
      if (receiptData.notes) {
        doc.moveDown();
        doc.text('Notes:', { align: 'left' });
        doc.text(receiptData.notes, { align: 'left' });
      }

      // Add footer - ensure it's at the bottom of the content area
      const footerY = Math.min(doc.y + (50 * scaleFactor), pageHeight - (100 * scaleFactor));
      doc.y = footerY;

      doc.moveDown();
      doc.text('Thank you for your business!', { align: 'center' });
      doc.text('This is a computer-generated receipt and does not require a signature.', { align: 'center' });

      console.log('PDF content added successfully');
    } catch (contentError) {
      console.error('Error adding content to PDF:', contentError);

      // Add minimal content if there was an error
      doc.fontSize(16).text('RECEIPT', { align: 'center' });
      doc.moveDown();
      doc.fontSize(12).text(`Transaction ID: ${receiptData.receiptNumber}`, { align: 'center' });
      doc.text('Mocky Digital', { align: 'center' });
      doc.moveDown();
      doc.text('Error generating complete receipt. Please contact support.', { align: 'center' });
    }

    // Finalize the PDF with proper error handling
    console.log('Finalizing PDF document...');
    doc.end();

    // Wait for the stream to finish with improved error handling
    await new Promise<void>((resolve, reject) => {
      // Set a timeout to detect hanging processes
      const timeout = setTimeout(() => {
        console.warn('PDF generation is taking longer than expected...');
      }, 5000);

      // Handle stream completion
      stream.on('finish', () => {
        clearTimeout(timeout);
        console.log(`PDF file created successfully: ${filename}`);

        // Verify the file exists and has content
        fs.stat(outputPath).then(stats => {
          if (stats.size > 0) {
            console.log(`PDF file size: ${stats.size} bytes`);
            resolve();
          } else {
            console.error('PDF file was created but is empty');
            reject(new Error('Generated PDF file is empty'));
          }
        }).catch(err => {
          console.error(`Error verifying PDF file: ${err.message}`);
          reject(err);
        });
      });

      // Handle stream errors
      stream.on('error', (err) => {
        clearTimeout(timeout);
        console.error(`Error writing PDF file: ${err.message}`);
        reject(err);
      });
    });

    // Return the relative path to the PDF file
    return `/uploads/receipts/${filename}`;
  } catch (error) {
    console.error('Error generating PDF:', error);

    // Check if the error is related to directory creation
    if (error.code === 'ENOENT') {
      console.error('Directory does not exist. Attempting to create it...');
      try {
        // Create the directory and try again
        const dirPath = path.join(process.cwd(), 'public', 'uploads', 'receipts');

        // Use synchronous version to ensure directory exists before proceeding
        if (!fs_sync.existsSync(dirPath)) {
          fs_sync.mkdirSync(dirPath, { recursive: true });
          console.log(`Directory created for retry: ${dirPath}`);
        } else {
          console.log(`Directory already exists for retry: ${dirPath}`);
        }

        // Try to generate the PDF again
        try {
          // Create a filename for the PDF using the transaction ID with a retry suffix
          const transactionId = receiptData.receiptNumber;
          const retryFilename = `${transactionId}-retry.pdf`;
          console.log(`Retry with filename: ${retryFilename}`);

          // Set the output file path
          const retryOutputPath = path.join(dirPath, retryFilename);

          // Determine paper size from receipt data or use default A4
          const paperSize = receiptData.paperSize || 'A4';
          const autoScale = receiptData.autoScale !== undefined ? receiptData.autoScale : true;

          console.log(`Retry using paper size: ${paperSize}, Auto-scale: ${autoScale}`);

          // Get paper dimensions
          const pageDimensions = PAPER_DIMENSIONS[paperSize];

          // Create a very simple PDF document with minimal options
          const retryDoc = new PDFDocument({
            size: paperSize === 'AUTO' ? [pageDimensions.width, pageDimensions.height] : paperSize,
            margin: 50,
            autoFirstPage: true,
            layout: 'portrait',
            // Use a standard PDF font that doesn't require external files
            font: 'Courier',
            info: {
              Title: `Receipt ${receiptData.receiptNumber}`,
              Author: 'Mocky Digital',
              Subject: 'Payment Receipt',
            },
            // Enable auto-scaling if requested
            autoFirstPageOptions: {
              scale: autoScale ? 'fit' : 1,
            },
          });

          // Create a write stream to the output file
          const retryStream = require('fs').createWriteStream(retryOutputPath);
          retryDoc.pipe(retryStream);

          console.log(`Creating fallback PDF file at: ${retryOutputPath}`);

          try {
            // Create a header with logo on left and company info on right
            const logoPath = path.join(process.cwd(), 'public', 'images', 'logo.png');
            console.log(`Adding logo to fallback PDF from: ${logoPath}`);

            try {
              // Get page dimensions for scaling calculations
              const pageWidth = retryDoc.page.width;
              const pageHeight = retryDoc.page.height;
              const margin = 50;
              const contentWidth = pageWidth - (margin * 2);

              // Calculate scaling factor based on paper size if auto-scaling is enabled
              const autoScale = receiptData.autoScale !== undefined ? receiptData.autoScale : true;
              const scaleFactor = autoScale ? Math.min(1, contentWidth / 500) : 1; // 500 is a reference width

              console.log(`Fallback PDF dimensions: ${pageWidth}x${pageHeight}, Content width: ${contentWidth}, Scale factor: ${scaleFactor}`);

              // Save the current position
              const startY = retryDoc.y;

              // Calculate logo size based on scale factor
              const logoWidth = 120 * scaleFactor;
              const logoHeight = 80 * scaleFactor;

              // Add logo on the left
              retryDoc.image(logoPath, {
                fit: [logoWidth, logoHeight], // Scale logo based on paper size
                align: 'left'
              });

              // Move to the right side for company info
              retryDoc.moveUp(4 * scaleFactor); // Scale the movement
              retryDoc.x = pageWidth - (150 * scaleFactor); // Scale the position

              // Scale font size based on paper size
              const companyInfoFontSize = 12 * scaleFactor;

              // Add company info on the right with right alignment
              retryDoc.fontSize(companyInfoFontSize).text('Mocky Digital', { align: 'right' });
              retryDoc.text('Nairobi, Kenya', { align: 'right' });
              retryDoc.text('Phone: +*********** 670', { align: 'right' });
              retryDoc.text('Email: <EMAIL>', { align: 'right' });
              retryDoc.text('Tax PIN: P052373324V', { align: 'right' });

              // Move to the center for receipt title
              retryDoc.x = (contentWidth / 2) + margin; // Center position
              retryDoc.y = startY + (20 * scaleFactor); // Scale the position

              console.log('Logo and company info added successfully to fallback PDF');
            } catch (logoError) {
              console.error('Error adding logo to fallback PDF:', logoError);
              // Continue without the logo if there's an error
            }

            // Header - centered between logo and company info
            const titleFontSize = 16 * scaleFactor;
            retryDoc.fontSize(titleFontSize).text('RECEIPT', { align: 'center', width: 200 * scaleFactor });
            retryDoc.moveDown(0.5);

            // Receipt details - centered between logo and company info
            const detailsFontSize = 12 * scaleFactor;
            retryDoc.fontSize(detailsFontSize);
            retryDoc.text(`Transaction ID: ${receiptData.receiptNumber}`, { align: 'center', width: 200 * scaleFactor });
            retryDoc.text(`Date: ${new Date().toLocaleDateString()}`, { align: 'center', width: 200 * scaleFactor });

            // Reset position for the rest of the content
            retryDoc.x = margin;
            retryDoc.y += 30 * scaleFactor; // Scale the spacing
            retryDoc.moveDown();

            // Add minimal content with scaled font and positioning
            retryDoc.text('This is a simplified receipt.', { align: 'center' });
            retryDoc.moveDown();
            retryDoc.text(`Customer: ${receiptData.customerName}`, { align: 'left' });
            retryDoc.text(`Amount: KES ${receiptData.amountPaid}`, { align: 'left' });

            // Add footer - ensure it's at the bottom of the content area
            const footerY = Math.min(retryDoc.y + (50 * scaleFactor), pageHeight - (100 * scaleFactor));
            retryDoc.y = footerY;

            console.log('Fallback PDF content added successfully');
          } catch (contentError) {
            console.error('Error adding content to fallback PDF:', contentError);
          }

          // Finalize the PDF
          console.log('Finalizing fallback PDF document...');
          retryDoc.end();

          // Wait for the stream to finish with improved error handling
          await new Promise<void>((resolve, reject) => {
            // Set a timeout to detect hanging processes
            const timeout = setTimeout(() => {
              console.warn('Fallback PDF generation is taking longer than expected...');
            }, 5000);

            // Handle stream completion
            retryStream.on('finish', () => {
              clearTimeout(timeout);
              console.log(`Fallback PDF file created successfully: ${retryFilename}`);

              // Verify the file exists and has content
              fs.stat(retryOutputPath).then(stats => {
                if (stats.size > 0) {
                  console.log(`Fallback PDF file size: ${stats.size} bytes`);
                  resolve();
                } else {
                  console.error('Fallback PDF file was created but is empty');
                  reject(new Error('Generated fallback PDF file is empty'));
                }
              }).catch(err => {
                console.error(`Error verifying fallback PDF file: ${err.message}`);
                reject(err);
              });
            });

            // Handle stream errors
            retryStream.on('error', (err) => {
              clearTimeout(timeout);
              console.error(`Error writing fallback PDF file: ${err.message}`);
              reject(err);
            });
          });

          // Return the relative path to the PDF file
          return `/uploads/receipts/${retryFilename}`;
        } catch (retryError) {
          console.error('Failed to create PDF in retry attempt:', retryError);
        }
      } catch (mkdirError) {
        console.error('Failed to create directory:', mkdirError);
      }
    }

    // Return a placeholder URL in case of error
    console.log(`Returning fallback URL for receipt: ${receiptData.receiptNumber}`);
    return `/api/receipts/${receiptData.receiptNumber}`;
  }
}

/**
 * Generate a PDF invoice
 * @param invoiceData The invoice data
 * @returns The path to the generated PDF file
 */
export async function generateInvoicePDF(invoiceData: InvoiceData): Promise<string> {
  try {
    // Validate invoice data before proceeding
    if (!invoiceData || !invoiceData.invoiceNumber) {
      console.error('Invalid invoice data provided to PDF generator');
      throw new Error('Invalid invoice data');
    }

    console.log(`Starting PDF generation for invoice: ${invoiceData.invoiceNumber}`);

    // Create a filename for the PDF using the invoice number
    const filename = `${invoiceData.invoiceNumber}.pdf`;
    console.log(`Generated filename using invoice number: ${filename}`);

    // Create the uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'invoices');
    console.log(`Ensuring directory exists: ${uploadsDir}`);

    try {
      // Use synchronous version to ensure directory exists before proceeding
      if (!fs_sync.existsSync(uploadsDir)) {
        fs_sync.mkdirSync(uploadsDir, { recursive: true });
        console.log(`Directory created: ${uploadsDir}`);
      } else {
        console.log(`Directory already exists: ${uploadsDir}`);
      }
    } catch (mkdirError) {
      console.error(`Error creating directory ${uploadsDir}:`, mkdirError);
      throw mkdirError;
    }

    // Set the output file path
    const outputPath = path.join(uploadsDir, filename);
    console.log(`Output path for PDF: ${outputPath}`);

    // Determine paper size from invoice data or use default A4
    const paperSize = invoiceData.paperSize || 'A4';
    const autoScale = invoiceData.autoScale !== undefined ? invoiceData.autoScale : true;

    console.log(`Using paper size: ${paperSize}, Auto-scale: ${autoScale}`);

    // Get paper dimensions
    const pageDimensions = PAPER_DIMENSIONS[paperSize];

    // Create a PDF document with the specified paper size
    const doc = new PDFDocument({
      size: paperSize === 'AUTO' ? [pageDimensions.width, pageDimensions.height] : paperSize,
      margin: 40, // Reduced margin to fit more content
      autoFirstPage: true,
      layout: 'portrait',
      // Use a standard PDF font that doesn't require external files
      font: 'Courier',
      info: {
        Title: `Invoice ${invoiceData.invoiceNumber}`,
        Author: 'Mocky Digital',
        Subject: 'Invoice',
        CreationDate: new Date(),
      },
      // Enable auto-scaling if requested
      autoFirstPageOptions: {
        scale: autoScale ? 'fit' : 1,
      },
    });

    // Create a write stream to the output file using promises-based fs
    const stream = require('fs').createWriteStream(outputPath);

    // Pipe the PDF document to the file stream
    doc.pipe(stream);

    console.log(`Creating PDF file at: ${outputPath}`);

    // Add basic content with simplified formatting to avoid corruption
    try {
      // Get page dimensions for scaling calculations
      const pageWidth = doc.page.width;
      const pageHeight = doc.page.height;
      const margin = 50; // Standard margin
      const contentWidth = pageWidth - (margin * 2);

      // Calculate scaling factor based on paper size if auto-scaling is enabled
      // A4 size is 595.28 x 841.89 points
      // Adjust scaling factor to ensure content fits properly on A4
      const scaleFactor = autoScale ? Math.min(1, contentWidth / 500) : 1; // 500 is a reference width

      console.log(`Page dimensions: ${pageWidth}x${pageHeight}, Content width: ${contentWidth}, Scale factor: ${scaleFactor}`);

      // Create a header with logo on left and company info on right
      const logoPath = path.join(process.cwd(), 'public', 'images', 'logo.png');
      console.log(`Adding logo from: ${logoPath}`);

      try {
        // Save the current position
        const startY = doc.y;

        // Calculate logo size based on scale factor
        const logoWidth = 120 * scaleFactor;
        const logoHeight = 80 * scaleFactor;

        // Add logo on the left
        doc.image(logoPath, {
          fit: [logoWidth, logoHeight], // Scale logo based on paper size
          align: 'left'
        });

        // Move to the right side for company info
        doc.moveUp(4 * scaleFactor); // Scale the movement
        doc.x = pageWidth - (150 * scaleFactor); // Scale the position

        // Scale font size based on paper size
        const companyInfoFontSize = 12 * scaleFactor;

        // Add company info on the right with right alignment
        doc.fontSize(companyInfoFontSize).text('Mocky Digital', { align: 'right' });
        doc.text('Nairobi, Kenya', { align: 'right' });
        doc.text('Phone: +*********** 670', { align: 'right' });
        doc.text('Email: <EMAIL>', { align: 'right' });
        doc.text('Tax PIN: P052373324V', { align: 'right' });

        // Move to the center for invoice title
        doc.x = (contentWidth / 2) + margin; // Center position
        doc.y = startY + (100 * scaleFactor); // Move down to position title below header
      } catch (logoError) {
        console.error('Error adding logo to PDF:', logoError);
      }

      // Header - centered
      const titleFontSize = 18 * scaleFactor;
      doc.fontSize(titleFontSize).text('INVOICE', { align: 'center' });
      doc.moveDown(0.5);

      // Invoice details - centered
      const detailsFontSize = 12 * scaleFactor;
      doc.fontSize(detailsFontSize);
      doc.text(`Invoice #: ${invoiceData.invoiceNumber}`, { align: 'center' });
      doc.text(`Date: ${new Date(invoiceData.issuedAt).toLocaleDateString()}`, { align: 'center' });
      doc.text(`Due Date: ${new Date(invoiceData.dueDate).toLocaleDateString()}`, { align: 'center' });
      doc.text(`Status: ${invoiceData.status.toUpperCase()}`, { align: 'center' });

      // Reset position for the rest of the content
      doc.x = margin;
      doc.y += 30 * scaleFactor;
      doc.moveDown();

      // Customer info
      doc.fontSize(12 * scaleFactor).text('CUSTOMER INFORMATION', { align: 'left' });
      doc.moveDown(0.5);
      doc.fontSize(12 * scaleFactor);
      doc.text(`Name: ${invoiceData.customerName}`, { align: 'left' });
      doc.text(`Phone: ${invoiceData.phoneNumber}`, { align: 'left' });

      if (invoiceData.email) {
        doc.text(`Email: ${invoiceData.email}`, { align: 'left' });
      }

      doc.moveDown();

      // Items section
      doc.fontSize(12 * scaleFactor).text('ITEMS', { align: 'left' });
      doc.moveDown(0.5);

      // Table header
      const colWidths = {
        desc: contentWidth * 0.4,
        qty: contentWidth * 0.2,
        price: contentWidth * 0.2,
        total: contentWidth * 0.2
      };

      // Draw table header with background
      const tableTop = doc.y;
      doc.rect(margin, tableTop, contentWidth, 20 * scaleFactor).fill('#f2f2f2');
      doc.fillColor('black');

      // Table header text
      doc.fontSize(12 * scaleFactor);
      doc.text('Description', margin + 5, tableTop + 5, { width: colWidths.desc - 10 });
      doc.text('Quantity', margin + colWidths.desc + 5, tableTop + 5, { width: colWidths.qty - 10 });
      doc.text('Unit Price (KES)', margin + colWidths.desc + colWidths.qty + 5, tableTop + 5, { width: colWidths.price - 10 });
      doc.text('Total (KES)', margin + colWidths.desc + colWidths.qty + colWidths.price + 5, tableTop + 5, { width: colWidths.total - 10 });

      doc.y = tableTop + (25 * scaleFactor);

      // Table rows
      for (const item of invoiceData.items) {
        const rowTop = doc.y;
        doc.fontSize(12 * scaleFactor);
        doc.text(item.description || item.serviceName || '', margin + 5, rowTop, { width: colWidths.desc - 10 });
        doc.text(item.quantity.toString(), margin + colWidths.desc + 5, rowTop, { width: colWidths.qty - 10 });
        doc.text(`${item.unitPrice.toLocaleString()}`, margin + colWidths.desc + colWidths.qty + 5, rowTop, { width: colWidths.price - 10 });
        doc.text(`${item.totalPrice.toLocaleString()}`, margin + colWidths.desc + colWidths.qty + colWidths.price + 5, rowTop, { width: colWidths.total - 10 });

        // Draw bottom border
        doc.moveTo(margin, doc.y + (15 * scaleFactor))
           .lineTo(margin + contentWidth, doc.y + (15 * scaleFactor))
           .stroke();

        doc.moveDown(1.5);
      }

      doc.moveDown();

      // Summary - right aligned
      const summaryX = pageWidth - margin - 200;
      const summaryWidth = 200;

      doc.fontSize(12 * scaleFactor);
      doc.text('Total Amount:', summaryX, doc.y, { width: summaryWidth * 0.6, align: 'left' });
      doc.text(`KES ${invoiceData.totalAmount.toLocaleString()}`, summaryX + summaryWidth * 0.6, doc.y, { width: summaryWidth * 0.4, align: 'right' });
      doc.moveDown(0.5);

      doc.text('Amount Paid:', summaryX, doc.y, { width: summaryWidth * 0.6, align: 'left' });
      doc.text(`KES ${invoiceData.amountPaid.toLocaleString()}`, summaryX + summaryWidth * 0.6, doc.y, { width: summaryWidth * 0.4, align: 'right' });
      doc.moveDown(0.5);

      doc.fontSize(14 * scaleFactor).text('Balance Due:', summaryX, doc.y, { width: summaryWidth * 0.6, align: 'left' });
      doc.fontSize(14 * scaleFactor).text(`KES ${invoiceData.balance.toLocaleString()}`, summaryX + summaryWidth * 0.6, doc.y, { width: summaryWidth * 0.4, align: 'right' });

      if (invoiceData.notes) {
        doc.moveDown(0.5);
        doc.fontSize(10 * scaleFactor).text('NOTES', { align: 'left' });
        doc.fontSize(9 * scaleFactor).text(invoiceData.notes, { align: 'left' });
      }

      // Add footer - ensure it's at the bottom of the content area
      const footerY = Math.min(doc.y + (50 * scaleFactor), pageHeight - (70 * scaleFactor));
      doc.y = footerY;

      doc.fontSize(12 * scaleFactor); // Larger font for footer
      doc.text('Thank you for your business!', { align: 'center' });
      doc.moveDown(0.5);
      doc.fontSize(10 * scaleFactor);
      doc.text('This is a computer-generated invoice and does not require a signature.', { align: 'center' });

      console.log('PDF content added successfully');
    } catch (contentError) {
      console.error('Error adding content to PDF:', contentError);

      // Add minimal content if there was an error
      doc.fontSize(16).text('INVOICE', { align: 'center' });
      doc.moveDown();
      doc.fontSize(12).text(`Invoice #: ${invoiceData.invoiceNumber}`, { align: 'center' });
      doc.text('Mocky Digital', { align: 'center' });
      doc.moveDown();
      doc.text('Error generating complete invoice. Please contact support.', { align: 'center' });
    }

    // Finalize the PDF with proper error handling
    console.log('Finalizing PDF document...');
    doc.end();

    // Wait for the stream to finish with improved error handling
    await new Promise<void>((resolve, reject) => {
      // Set a timeout to detect hanging processes
      const timeout = setTimeout(() => {
        console.warn('PDF generation is taking longer than expected...');
      }, 5000);

      // Handle stream completion
      stream.on('finish', () => {
        clearTimeout(timeout);
        console.log(`PDF file created successfully: ${filename}`);

        // Verify the file exists and has content
        fs.stat(outputPath).then(stats => {
          if (stats.size > 0) {
            console.log(`PDF file size: ${stats.size} bytes`);
            resolve();
          } else {
            console.error('PDF file was created but is empty');
            reject(new Error('Generated PDF file is empty'));
          }
        }).catch(err => {
          console.error(`Error verifying PDF file: ${err.message}`);
          reject(err);
        });
      });

      // Handle stream errors
      stream.on('error', (err) => {
        clearTimeout(timeout);
        console.error(`Error writing PDF file: ${err.message}`);
        reject(err);
      });
    });

    // Return the relative path to the PDF file
    return `/uploads/invoices/${filename}`;
  } catch (error) {
    console.error('Error generating invoice PDF:', error);
    // Return a placeholder URL in case of error
    return `/api/invoices/${invoiceData.invoiceNumber}`;
  }
}

/**
 * Generate a PDF quote
 * @param quoteData The quote data
 * @returns The path to the generated PDF file
 */
export async function generateQuotePDF(quoteData: QuoteData): Promise<string> {
  try {
    // Validate quote data before proceeding
    if (!quoteData || !quoteData.quoteNumber) {
      console.error('Invalid quote data provided to PDF generator');
      throw new Error('Invalid quote data');
    }

    console.log(`Starting PDF generation for quote: ${quoteData.quoteNumber}`);

    // Create a filename for the PDF using the quote number
    const filename = `${quoteData.quoteNumber}.pdf`;
    console.log(`Generated filename using quote number: ${filename}`);

    // Create the uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'quotes');
    console.log(`Ensuring directory exists: ${uploadsDir}`);

    try {
      // Use synchronous version to ensure directory exists before proceeding
      if (!fs_sync.existsSync(uploadsDir)) {
        fs_sync.mkdirSync(uploadsDir, { recursive: true });
        console.log(`Directory created: ${uploadsDir}`);
      } else {
        console.log(`Directory already exists: ${uploadsDir}`);
      }
    } catch (mkdirError) {
      console.error(`Error creating directory ${uploadsDir}:`, mkdirError);
      throw mkdirError;
    }

    // Set the output file path
    const outputPath = path.join(uploadsDir, filename);
    console.log(`Output path for PDF: ${outputPath}`);

    // Determine paper size from quote data or use default A4
    const paperSize = quoteData.paperSize || 'A4';
    const autoScale = quoteData.autoScale !== undefined ? quoteData.autoScale : true;

    console.log(`Using paper size: ${paperSize}, Auto-scale: ${autoScale}`);

    // Get paper dimensions
    const pageDimensions = PAPER_DIMENSIONS[paperSize];

    // Create a PDF document with the specified paper size
    const doc = new PDFDocument({
      size: paperSize === 'AUTO' ? [pageDimensions.width, pageDimensions.height] : paperSize,
      margin: 50,
      autoFirstPage: true,
      layout: 'portrait',
      // Use a standard PDF font that doesn't require external files
      font: 'Courier',
      info: {
        Title: `Quote ${quoteData.quoteNumber}`,
        Author: 'Mocky Digital',
        Subject: 'Quote',
        CreationDate: new Date(),
      },
      // Enable auto-scaling if requested
      autoFirstPageOptions: {
        scale: autoScale ? 'fit' : 1,
      },
    });

    // Create a write stream to the output file using promises-based fs
    const stream = require('fs').createWriteStream(outputPath);

    // Pipe the PDF document to the file stream
    doc.pipe(stream);

    console.log(`Creating PDF file at: ${outputPath}`);

    // Add basic content with simplified formatting to avoid corruption
    try {
      // Get page dimensions for scaling calculations
      const pageWidth = doc.page.width;
      const pageHeight = doc.page.height;
      const margin = 50;
      const contentWidth = pageWidth - (margin * 2);

      // Calculate scaling factor based on paper size if auto-scaling is enabled
      const scaleFactor = autoScale ? Math.min(1, contentWidth / 500) : 1; // 500 is a reference width

      console.log(`Page dimensions: ${pageWidth}x${pageHeight}, Content width: ${contentWidth}, Scale factor: ${scaleFactor}`);

      // Create a header with logo on left and company info on right
      const logoPath = path.join(process.cwd(), 'public', 'images', 'logo.png');
      console.log(`Adding logo from: ${logoPath}`);

      try {
        // Save the current position
        const startY = doc.y;

        // Calculate logo size based on scale factor
        const logoWidth = 120 * scaleFactor;
        const logoHeight = 80 * scaleFactor;

        // Add logo on the left
        doc.image(logoPath, {
          fit: [logoWidth, logoHeight], // Scale logo based on paper size
          align: 'left'
        });

        // Move to the right side for company info
        doc.moveUp(4 * scaleFactor); // Scale the movement
        doc.x = pageWidth - (150 * scaleFactor); // Scale the position

        // Scale font size based on paper size
        const companyInfoFontSize = 12 * scaleFactor;

        // Add company info on the right with right alignment
        doc.fontSize(companyInfoFontSize).text('Mocky Digital', { align: 'right' });
        doc.text('Nairobi, Kenya', { align: 'right' });
        doc.text('Phone: +*********** 670', { align: 'right' });
        doc.text('Email: <EMAIL>', { align: 'right' });
        doc.text('Tax PIN: P052373324V', { align: 'right' });

        // Move to the center for quote title
        doc.x = (contentWidth / 2) + margin; // Center position
        doc.y = startY + (20 * scaleFactor); // Scale the position
      } catch (logoError) {
        console.error('Error adding logo to PDF:', logoError);
      }

      // Header - centered between logo and company info
      const titleFontSize = 18 * scaleFactor;
      doc.fontSize(titleFontSize).text('QUOTATION', { align: 'center', width: 200 * scaleFactor });
      doc.moveDown(0.5);

      // Quote details - centered between logo and company info
      const detailsFontSize = 12 * scaleFactor;
      doc.fontSize(detailsFontSize);
      doc.text(`Quote #: ${quoteData.quoteNumber}`, { align: 'center', width: 200 * scaleFactor });
      doc.text(`Date: ${new Date(quoteData.issuedAt).toLocaleDateString()}`, { align: 'center', width: 200 * scaleFactor });
      doc.text(`Valid Until: ${new Date(quoteData.validUntil).toLocaleDateString()}`, { align: 'center', width: 200 * scaleFactor });

      // Reset position for the rest of the content
      doc.x = margin;
      doc.y += 30 * scaleFactor; // Scale the spacing
      doc.moveDown();

      // Customer info
      doc.text('CUSTOMER INFORMATION', { align: 'left' });
      doc.text(`Name: ${quoteData.customerName}`, { align: 'left' });
      doc.text(`Phone: ${quoteData.phoneNumber}`, { align: 'left' });

      if (quoteData.email) {
        doc.text(`Email: ${quoteData.email}`, { align: 'left' });
      }

      doc.moveDown();

      // Items
      doc.text('ITEMS', { align: 'left' });
      doc.moveDown(0.5);

      // Add items with simplified formatting and scaled spacing
      for (const item of quoteData.items) {
        doc.text(`${item.description || item.serviceName} - ${item.quantity} x KES ${item.unitPrice} = KES ${item.totalPrice}`, { align: 'left' });
      }

      doc.moveDown();

      // Summary
      doc.text('SUMMARY', { align: 'left' });
      doc.text(`Total Amount: KES ${quoteData.totalAmount}`, { align: 'left' });

      if (quoteData.notes) {
        doc.moveDown();
        doc.text('NOTES', { align: 'left' });
        doc.text(quoteData.notes, { align: 'left' });
      }

      // Add footer - ensure it's at the bottom of the content area
      const footerY = Math.min(doc.y + (50 * scaleFactor), pageHeight - (100 * scaleFactor));
      doc.y = footerY;

      doc.moveDown();
      doc.text('Thank you for your interest in our services!', { align: 'center' });
      doc.text('This quote is valid until the date specified above.', { align: 'center' });
      doc.text('This is a computer-generated quote and does not require a signature.', { align: 'center' });

      console.log('PDF content added successfully');
    } catch (contentError) {
      console.error('Error adding content to PDF:', contentError);

      // Add minimal content if there was an error
      doc.fontSize(16).text('QUOTATION', { align: 'center' });
      doc.moveDown();
      doc.fontSize(12).text(`Quote #: ${quoteData.quoteNumber}`, { align: 'center' });
      doc.text('Mocky Digital', { align: 'center' });
      doc.moveDown();
      doc.text('Error generating complete quote. Please contact support.', { align: 'center' });
    }

    // Finalize the PDF with proper error handling
    console.log('Finalizing PDF document...');
    doc.end();

    // Wait for the stream to finish with improved error handling
    await new Promise<void>((resolve, reject) => {
      // Set a timeout to detect hanging processes
      const timeout = setTimeout(() => {
        console.warn('PDF generation is taking longer than expected...');
      }, 5000);

      // Handle stream completion
      stream.on('finish', () => {
        clearTimeout(timeout);
        console.log(`PDF file created successfully: ${filename}`);

        // Verify the file exists and has content
        fs.stat(outputPath).then(stats => {
          if (stats.size > 0) {
            console.log(`PDF file size: ${stats.size} bytes`);
            resolve();
          } else {
            console.error('PDF file was created but is empty');
            reject(new Error('Generated PDF file is empty'));
          }
        }).catch(err => {
          console.error(`Error verifying PDF file: ${err.message}`);
          reject(err);
        });
      });

      // Handle stream errors
      stream.on('error', (err) => {
        clearTimeout(timeout);
        console.error(`Error writing PDF file: ${err.message}`);
        reject(err);
      });
    });

    // Return the relative path to the PDF file
    return `/uploads/quotes/${filename}`;
  } catch (error) {
    console.error('Error generating quote PDF:', error);
    // Return a placeholder URL in case of error
    return `/api/quotes/${quoteData.quoteNumber}`;
  }
}
