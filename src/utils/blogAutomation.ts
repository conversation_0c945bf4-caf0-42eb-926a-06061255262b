import { createLogger } from '@/utils/logger';
import { getOpenAIClient } from '@/utils/openaiClient';
import * as blogService from '@/services/blogService';
import slugify from 'slugify';
import { BlogPost } from '@/types/blog';
import { markdownToHtml } from '@/utils/markdownToHtml';

// Create a logger for blog automation
const logger = createLogger('BlogAutomation');

// Define topic categories for blog posts
const BLOG_CATEGORIES = [
  'web-design',
  'graphic-design',
  'branding',
  'marketing',
  'ui-ux',
  'design-trends',
  'business',
  'technology'
];

// Define blog post generation options
interface AutomatedBlogPostOptions {
  category?: string;
  tone?: 'professional' | 'casual' | 'humorous' | 'technical' | 'conversational';
  length?: 'short' | 'medium' | 'long';
  targetAudience?: string;
  publishImmediately?: boolean;
}

/**
 * Generate a topic for a blog post based on the category
 * @param category The category to generate a topic for
 * @returns A generated topic
 */
async function generateBlogTopic(category: string): Promise<string> {
  try {
    logger.info(`Generating blog topic for category: ${category}`);

    const openai = getOpenAIClient();

    const prompt = `Generate an interesting and specific blog post topic about ${category} that would be relevant for a design agency's blog.
    The topic should be specific, not generic, and should be something that would be valuable to potential clients.
    Return only the topic as a string, without quotes or additional text.`;

    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: 'You are a helpful assistant that generates blog topics.' },
        { role: 'user', content: prompt }
      ],
      temperature: 0.8,
      max_tokens: 100,
    });

    const topic = completion.choices[0].message.content?.trim() || `Latest Trends in ${category}`;
    logger.info(`Generated topic: ${topic}`);

    return topic;
  } catch (error) {
    logger.error('Error generating blog topic:', error);
    // Fallback to a default topic if generation fails
    return `Latest Trends in ${category} for ${new Date().getFullYear()}`;
  }
}

/**
 * Generate and create a blog post
 * @param options Options for blog post generation
 * @returns The created blog post
 */
export async function generateAndCreateBlogPost(options: AutomatedBlogPostOptions = {}): Promise<BlogPost | null> {
  try {
    // Select a random category if none provided
    const category = options.category ||
      BLOG_CATEGORIES[Math.floor(Math.random() * BLOG_CATEGORIES.length)];

    // Generate a topic based on the category
    const topic = await generateBlogTopic(category);

    // Generate the blog post content
    logger.info(`Generating blog post for topic: ${topic}`);

    const openai = getOpenAIClient();

    // Determine content length based on the length option
    const wordCount = options.length === 'short'
      ? '600-800'
      : options.length === 'medium'
        ? '1000-1200'
        : '1500-2000';

    // Determine the tone
    const tone = options.tone || 'professional';

    // Determine the target audience
    const targetAudience = options.targetAudience || 'potential clients interested in design services';

    // Build the prompt
    const prompt = `Write a comprehensive blog post about "${topic}" in a ${tone} tone for ${targetAudience}.
    The blog post should be approximately ${wordCount} words and should be well-structured with:

    1. A compelling title
    2. An engaging introduction
    3. Well-organized sections with appropriate headings (use markdown format: # for main heading, ## for subheadings, ### for sub-subheadings)
    4. Practical insights and actionable advice
    5. A conclusion that summarizes the key points

    Use proper markdown formatting:
    - Use # for main headings, ## for subheadings, ### for sub-subheadings
    - Use **bold** for emphasis
    - Use *italic* for subtle emphasis
    - Use - for bullet points
    - Use 1. 2. 3. for numbered lists
    - Use blank lines between paragraphs

    Format the response with:
    TITLE: [The blog post title]
    EXCERPT: [A 2-3 sentence summary of the blog post]
    CONTENT: [The full blog post content with proper markdown formatting as described above]`;

    // Generate the content
    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are a professional blog writer for a design agency. Create engaging, well-structured content that demonstrates expertise and provides value to readers. Use proper markdown formatting for all content, including headings, lists, emphasis, and paragraph breaks. Do not include HTML tags in your response, only use markdown formatting.'
        },
        { role: 'user', content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 3000,
    });

    const generatedContent = completion.choices[0].message.content || '';

    // Parse the generated content
    const titleMatch = generatedContent.match(/TITLE:\s*(.*?)(?=\n|EXCERPT:|CONTENT:|$)/s);
    const excerptMatch = generatedContent.match(/EXCERPT:\s*(.*?)(?=\n|CONTENT:|$)/s);
    const contentMatch = generatedContent.match(/CONTENT:\s*([\s\S]*)/);

    const title = titleMatch && titleMatch[1] ? titleMatch[1].trim() : `Blog Post about ${topic}`;
    const excerpt = excerptMatch && excerptMatch[1] ? excerptMatch[1].trim() : '';
    let content = contentMatch && contentMatch[1] ? contentMatch[1].trim() : generatedContent;

    // Convert markdown to HTML
    content = markdownToHtml(content);

    // Log the content processing
    logger.info(`Content processed: ${content.substring(0, 100)}...`);

    logger.info(`Successfully generated blog post: "${title}"`);

    // Generate slug from title
    const slug = slugify(title, {
      lower: true,
      strict: true,
      trim: true,
    });

    // Determine status based on options
    const status = options.publishImmediately ? 'published' : 'draft';

    // Create the blog post
    const blogPost = await blogService.createBlogPost({
      title,
      slug,
      content,
      excerpt: excerpt || content.substring(0, 150) + '...',
      author: 'AI Writer',
      category,
      tags: [category, 'automated'],
      status,
      publishedAt: status === 'published' ? new Date().toISOString() : null,
    });

    logger.info(`Blog post created with ID: ${blogPost.id}, Status: ${status}`);

    return blogPost;
  } catch (error) {
    logger.error('Error generating and creating blog post:', error);
    return null;
  }
}

/**
 * Publish a draft blog post
 * @param id The ID of the blog post to publish
 * @returns The published blog post
 */
export async function publishDraftBlogPost(id: string): Promise<BlogPost | null> {
  try {
    logger.info(`Publishing draft blog post with ID: ${id}`);

    // Get the blog post
    const blogPost = await blogService.getBlogPostById(id);

    if (!blogPost) {
      logger.error(`Blog post with ID ${id} not found`);
      return null;
    }

    if (blogPost.status === 'published') {
      logger.info(`Blog post with ID ${id} is already published`);
      return blogPost;
    }

    // Update the blog post status to published
    const updatedBlogPost = await blogService.updateBlogPost(id, {
      status: 'published',
    });

    logger.info(`Blog post with ID ${id} published successfully`);

    return updatedBlogPost;
  } catch (error) {
    logger.error(`Error publishing blog post with ID ${id}:`, error);
    return null;
  }
}
