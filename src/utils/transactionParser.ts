/**
 * Utility for parsing M-PESA transaction messages
 * Extracts transaction details from the message text
 */

interface TransactionData {
  transactionId: string;
  amount: number;
  customerName: string;
  phoneNumber: string;
  transactionDate: Date;
  rawMessage: string;
}

/**
 * Parse an M-PESA transaction message to extract relevant information
 * @param message The raw M-PESA transaction message
 * @returns Extracted transaction data or null if parsing fails
 */
export function parseTransactionMessage(message: string): TransactionData | null {
  try {
    // Clean up the message
    const cleanMessage = message.trim();

    // Return null if message is empty
    if (!cleanMessage) {
      return null;
    }

    // Extract transaction ID using regex
    // Format: TEE5PIQ2H7, TED3JYV8SX, TDF9TBL1BV, etc.
    // Also handles formats like: MPESA confirmation. TEE5PIQ2H7 Confirmed.
    const transactionIdMatch = cleanMessage.match(/([A-Z]{2,3}\d[A-Z0-9]{6,7})/);
    const transactionId = transactionIdMatch ? transactionIdMatch[1] : '';

    if (!transactionId) {
      console.error('Failed to extract transaction ID from message:', cleanMessage);
      return null;
    }

    // Check if this is a valid M-Pesa transaction ID format
    if (!transactionId.match(/^[A-Z]{2,3}\d[A-Z0-9]{6,7}$/)) {
      console.error('Invalid transaction ID format:', transactionId);
      return null;
    }

    // Extract amount using regex
    // Format: KES 1500, KES 2500, etc.
    // Also handles formats like: Ksh1,500.00, Ksh 1,500.00
    let amountMatch = cleanMessage.match(/KES\s+([0-9,]+(\.[0-9]{2})?)/i);

    // Try alternative format if first pattern doesn't match
    if (!amountMatch) {
      amountMatch = cleanMessage.match(/Ksh\.?\s*([0-9,]+(\.[0-9]{2})?)/i);
    }

    // Try another alternative format
    if (!amountMatch) {
      amountMatch = cleanMessage.match(/([0-9,]+(\.[0-9]{2})?) shillings/i);
    }

    const amountStr = amountMatch ? amountMatch[1].replace(/,/g, '') : '0';
    const amount = parseFloat(amountStr);

    if (isNaN(amount) || amount <= 0) {
      console.error('Failed to extract valid amount from message:', cleanMessage);
      return null;
    }

    // Extract customer name using regex
    // Format: from Wesley Moturi, from DAMARIS JUMA, from SHUKRI OMAR, etc.
    let customerNameMatch = cleanMessage.match(/from\s+([A-Z][A-Z\s]+)\s+\d+/i);

    // Try alternative format if first pattern doesn't match
    if (!customerNameMatch) {
      customerNameMatch = cleanMessage.match(/from\s+([A-Z][A-Z\s]+)/i);
    }

    // Try another alternative format for messages with different structure
    if (!customerNameMatch) {
      customerNameMatch = cleanMessage.match(/received from\s+([A-Z][A-Z\s]+)/i);
    }

    const customerName = customerNameMatch ? customerNameMatch[1].trim() : '';

    if (!customerName) {
      console.error('Failed to extract customer name from message:', cleanMessage);
      return null;
    }

    // Extract phone number using regex
    // Format: 254720335304, 254715018467, 254722474248, etc.
    let phoneNumberMatch = cleanMessage.match(/(\d{12})/);

    // Try alternative format if first pattern doesn't match (10 digits without country code)
    if (!phoneNumberMatch) {
      phoneNumberMatch = cleanMessage.match(/(\d{10})/);
      // If found, add country code
      if (phoneNumberMatch) {
        phoneNumberMatch[1] = '254' + phoneNumberMatch[1].substring(1);
      }
    }

    // Try another format with dashes or spaces
    if (!phoneNumberMatch) {
      const dashMatch = cleanMessage.match(/(\d{3}[\s-]\d{3}[\s-]\d{3}[\s-]\d{3})/);
      if (dashMatch) {
        phoneNumberMatch = ['', dashMatch[1].replace(/[\s-]/g, '')];
      }
    }

    const phoneNumber = phoneNumberMatch ? phoneNumberMatch[1] : '';

    if (!phoneNumber) {
      console.error('Failed to extract phone number from message:', cleanMessage);
      return null;
    }

    // Validate phone number format (should be 12 digits starting with 254)
    if (!phoneNumber.match(/^254\d{9}$/)) {
      console.warn('Phone number may not be in the expected format:', phoneNumber);
      // Continue anyway as we might have extracted a valid but differently formatted number
    }

    // Extract transaction date using regex
    // Format: 14/05/2025 at 05:00 PM, 13/05/2025 at 12:55 PM, 15/04/2025 at 11:00 AM, etc.
    let dateMatch = cleanMessage.match(/(\d{1,2}\/\d{1,2}\/\d{4})\s+at\s+(\d{1,2}:\d{2}\s+[AP]M)/i);

    // Try alternative format if first pattern doesn't match
    if (!dateMatch) {
      dateMatch = cleanMessage.match(/on\s+(\d{1,2}\/\d{1,2}\/\d{4})\s+at\s+(\d{1,2}:\d{2}\s+[AP]M)/i);
    }

    // Try another format with different date separator
    if (!dateMatch) {
      dateMatch = cleanMessage.match(/(\d{1,2}-\d{1,2}-\d{4})\s+at\s+(\d{1,2}:\d{2}\s+[AP]M)/i);
    }

    let transactionDate: Date;

    if (dateMatch) {
      const dateStr = dateMatch[1]; // e.g., 14/05/2025 or 14-05-2025
      const timeStr = dateMatch[2]; // e.g., 05:00 PM

      // Parse date parts, handling both slash and dash separators
      const dateSeparator = dateStr.includes('/') ? '/' : '-';
      const [day, month, year] = dateStr.split(dateSeparator).map(Number);

      // Parse time parts
      const timeParts = timeStr.match(/(\d{1,2}):(\d{2})\s+([AP]M)/i);

      if (timeParts) {
        let hours = parseInt(timeParts[1]);
        const minutes = parseInt(timeParts[2]);
        const isPM = timeParts[3].toUpperCase() === 'PM';

        // Convert to 24-hour format
        if (isPM && hours < 12) {
          hours += 12;
        } else if (!isPM && hours === 12) {
          hours = 0;
        }

        // Create date object
        transactionDate = new Date(year, month - 1, day, hours, minutes);

        // Validate date (check if it's a valid date and not in the future)
        const now = new Date();
        if (isNaN(transactionDate.getTime()) || transactionDate > now) {
          console.warn('Invalid or future date detected, using current date instead');
          transactionDate = now;
        }
      } else {
        // Fallback to current date and time
        transactionDate = new Date();
      }
    } else {
      // Fallback to current date and time
      console.warn('No date found in message, using current date');
      transactionDate = new Date();
    }

    return {
      transactionId,
      amount,
      customerName,
      phoneNumber,
      transactionDate,
      rawMessage: cleanMessage
    };
  } catch (error) {
    console.error('Error parsing transaction message:', error);
    return null;
  }
}

/**
 * Batch parse multiple M-PESA transaction messages
 * @param messages Array of raw M-PESA transaction messages
 * @returns Array of successfully parsed transaction data
 */
export function parseTransactionMessages(messages: string[]): TransactionData[] {
  const results: TransactionData[] = [];

  for (const message of messages) {
    const parsedData = parseTransactionMessage(message);
    if (parsedData) {
      results.push(parsedData);
    }
  }

  return results;
}
