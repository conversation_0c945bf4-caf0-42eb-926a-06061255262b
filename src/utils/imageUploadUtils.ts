// Enhanced image upload utilities with comprehensive error handling

export interface UploadResult {
  success: boolean;
  url?: string;
  key?: string;
  filename?: string;
  error?: string;
  warning?: string;
  fallbackUrl?: string;
}

export interface UploadOptions {
  category?: string;
  maxRetries?: number;
  timeoutMs?: number;
  isOptional?: boolean;
  onProgress?: (progress: number) => void;
  onStatusUpdate?: (message: string, type: 'info' | 'warning' | 'error') => void;
}

export class ImageUploadError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public isRetryable: boolean = false
  ) {
    super(message);
    this.name = 'ImageUploadError';
  }
}

export class ImageUploadService {
  private static readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  private static readonly VALID_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  private static readonly FALLBACK_URL = '/images/placeholder.jpg';

  /**
   * Validate file before upload
   */
  static validateFile(file: File): { isValid: boolean; error?: string } {
    if (!file) {
      return { isValid: false, error: 'No file provided' };
    }

    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      return {
        isValid: false,
        error: `File size (${Math.round(file.size / 1024 / 1024)}MB) exceeds maximum allowed size of ${this.MAX_FILE_SIZE / 1024 / 1024}MB`
      };
    }

    // Check file type
    if (!this.VALID_TYPES.includes(file.type)) {
      return {
        isValid: false,
        error: `Invalid file type: ${file.type}. Allowed types: ${this.VALID_TYPES.join(', ')}`
      };
    }

    return { isValid: true };
  }

  /**
   * Upload a single image with comprehensive error handling
   */
  static async uploadImage(
    file: File,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    const {
      category = 'catalogue',
      maxRetries = 3,
      timeoutMs = 60000,
      isOptional = false,
      onProgress,
      onStatusUpdate
    } = options;

    // Validate file first
    const validation = this.validateFile(file);
    if (!validation.isValid) {
      throw new ImageUploadError(
        validation.error!,
        'VALIDATION_ERROR',
        400,
        false
      );
    }

    onStatusUpdate?.(`Uploading ${file.name}...`, 'info');
    onProgress?.(0);

    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 1) {
          onStatusUpdate?.(`Retry attempt ${attempt}/${maxRetries}...`, 'info');
          // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt - 1) * 1000));
        }

        const result = await this.performUpload(file, category, timeoutMs, onProgress);
        
        onStatusUpdate?.('Upload completed successfully', 'info');
        onProgress?.(100);
        
        return result;

      } catch (error) {
        lastError = error as Error;
        console.error(`Upload attempt ${attempt} failed:`, error);

        if (error instanceof ImageUploadError && !error.isRetryable) {
          // Don't retry for non-retryable errors
          break;
        }

        if (attempt === maxRetries) {
          // Last attempt failed
          break;
        }

        onStatusUpdate?.(`Attempt ${attempt} failed, retrying...`, 'warning');
      }
    }

    // All attempts failed
    const errorMessage = lastError?.message || 'Upload failed after multiple attempts';
    
    if (isOptional) {
      onStatusUpdate?.('Optional image upload failed, using fallback', 'warning');
      return {
        success: false,
        url: this.FALLBACK_URL,
        key: 'fallback',
        filename: 'placeholder.jpg',
        warning: `Upload failed: ${errorMessage}. Using placeholder image.`,
        fallbackUrl: this.FALLBACK_URL
      };
    }

    throw new ImageUploadError(
      errorMessage,
      'UPLOAD_FAILED',
      500,
      true
    );
  }

  /**
   * Perform the actual upload
   */
  private static async performUpload(
    file: File,
    category: string,
    timeoutMs: number,
    onProgress?: (progress: number) => void
  ): Promise<UploadResult> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('category', category);

    // Create timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new ImageUploadError(
          `Upload timeout after ${timeoutMs / 1000} seconds`,
          'TIMEOUT_ERROR',
          408,
          true
        ));
      }, timeoutMs);
    });

    // Create upload promise
    const uploadPromise = fetch('/api/upload', {
      method: 'POST',
      body: formData,
    });

    onProgress?.(25);

    try {
      // Race upload against timeout
      const response = await Promise.race([uploadPromise, timeoutPromise]);
      onProgress?.(50);

      let data: any;
      try {
        data = await response.json();
        onProgress?.(75);
      } catch (parseError) {
        throw new ImageUploadError(
          'Failed to parse server response',
          'PARSE_ERROR',
          500,
          true
        );
      }

      if (!response.ok) {
        const errorMessage = data.error || `HTTP ${response.status}: ${response.statusText}`;
        throw new ImageUploadError(
          errorMessage,
          data.errorCode || 'HTTP_ERROR',
          response.status,
          response.status >= 500 // Retry server errors
        );
      }

      // Validate response structure
      if (!data.url && !data.warning) {
        throw new ImageUploadError(
          'Invalid server response: missing URL',
          'INVALID_RESPONSE',
          500,
          true
        );
      }

      return {
        success: true,
        url: data.url,
        key: data.key,
        filename: data.filename,
        warning: data.warning
      };

    } catch (error) {
      if (error instanceof ImageUploadError) {
        throw error;
      }

      // Handle network errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new ImageUploadError(
          'Network error: Please check your internet connection',
          'NETWORK_ERROR',
          0,
          true
        );
      }

      throw new ImageUploadError(
        error instanceof Error ? error.message : 'Unknown upload error',
        'UNKNOWN_ERROR',
        500,
        true
      );
    }
  }

  /**
   * Upload multiple images with progress tracking
   */
  static async uploadMultipleImages(
    files: (File | null)[],
    options: UploadOptions = {}
  ): Promise<(UploadResult | null)[]> {
    const results: (UploadResult | null)[] = [];
    const totalFiles = files.filter(f => f !== null).length;
    let completedFiles = 0;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      if (!file) {
        results.push(null);
        continue;
      }

      try {
        const fileOptions = {
          ...options,
          isOptional: i > 0, // First image is required, others are optional
          onProgress: (progress: number) => {
            const overallProgress = ((completedFiles + progress / 100) / totalFiles) * 100;
            options.onProgress?.(overallProgress);
          },
          onStatusUpdate: (message: string, type: 'info' | 'warning' | 'error') => {
            options.onStatusUpdate?.(`Image ${i + 1}: ${message}`, type);
          }
        };

        const result = await this.uploadImage(file, fileOptions);
        results.push(result);
        completedFiles++;

      } catch (error) {
        console.error(`Failed to upload image ${i + 1}:`, error);
        
        if (i === 0) {
          // First image is critical
          throw error;
        } else {
          // Optional images can fail
          results.push({
            success: false,
            error: error instanceof Error ? error.message : 'Upload failed',
            fallbackUrl: this.FALLBACK_URL
          });
          completedFiles++;
        }
      }
    }

    return results;
  }

  /**
   * Get upload progress for UI display
   */
  static getProgressMessage(progress: number, filename: string): string {
    if (progress === 0) return `Preparing ${filename}...`;
    if (progress < 25) return `Uploading ${filename}...`;
    if (progress < 50) return `Processing ${filename}...`;
    if (progress < 75) return `Finalizing ${filename}...`;
    if (progress < 100) return `Almost done with ${filename}...`;
    return `${filename} uploaded successfully!`;
  }

  /**
   * Clean up error messages for user display
   */
  static cleanErrorMessage(error: string): string {
    return error
      .replace('AI image uploads failed', 'Image upload failed')
      .replace('Please check your commission privacy again', 'Please try again')
      .replace(/key is not defined/g, 'Server configuration error')
      .replace(/undefined/g, 'unknown');
  }
}
