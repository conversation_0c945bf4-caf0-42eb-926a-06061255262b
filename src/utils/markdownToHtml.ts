/**
 * Utility to convert markdown-like content to HTML
 * This is especially useful for processing content generated by OpenAI
 */

/**
 * Converts markdown-like text to HTML
 * @param content The markdown-like content to convert
 * @returns HTML formatted content
 */
export function markdownToHtml(content: string): string {
  // If content already has HTML tags, return it as is
  if (content.includes('<p>') || content.includes('<h')) {
    return content;
  }

  let processedContent = content;

  // Process headings (# Heading)
  processedContent = processedContent.replace(/^###\s+(.*)$/gm, '<h3>$1</h3>');
  processedContent = processedContent.replace(/^##\s+(.*)$/gm, '<h2>$1</h2>');
  processedContent = processedContent.replace(/^#\s+(.*)$/gm, '<h1>$1</h1>');

  // Process bold (**text**)
  processedContent = processedContent.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

  // Process italic (*text*)
  processedContent = processedContent.replace(/\*(.*?)\*/g, '<em>$1</em>');

  // Process unordered lists
  processedContent = processedContent.replace(/^-\s+(.*)$/gm, '<li>$1</li>');
  
  // Wrap consecutive list items in <ul> tags
  let hasUl = false;
  const lines = processedContent.split('\n');
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].startsWith('<li>') && lines[i].endsWith('</li>')) {
      if (!hasUl) {
        lines[i] = '<ul>' + lines[i];
        hasUl = true;
      }
    } else if (hasUl) {
      lines[i-1] = lines[i-1] + '</ul>';
      hasUl = false;
    }
  }
  if (hasUl) {
    lines[lines.length-1] = lines[lines.length-1] + '</ul>';
  }
  processedContent = lines.join('\n');

  // Process numbered lists
  processedContent = processedContent.replace(/^\d+\.\s+(.*)$/gm, '<li>$1</li>');
  
  // Wrap consecutive numbered list items in <ol> tags
  let hasOl = false;
  const numberedLines = processedContent.split('\n');
  for (let i = 0; i < numberedLines.length; i++) {
    if (numberedLines[i].startsWith('<li>') && numberedLines[i].endsWith('</li>') && !numberedLines[i-1]?.endsWith('</ul>')) {
      if (!hasOl) {
        numberedLines[i] = '<ol>' + numberedLines[i];
        hasOl = true;
      }
    } else if (hasOl) {
      numberedLines[i-1] = numberedLines[i-1] + '</ol>';
      hasOl = false;
    }
  }
  if (hasOl) {
    numberedLines[numberedLines.length-1] = numberedLines[numberedLines.length-1] + '</ol>';
  }
  processedContent = numberedLines.join('\n');

  // Split by double newlines and wrap remaining text in paragraph tags
  processedContent = processedContent.split('\n\n')
    .map(para => para.trim())
    .filter(para => para.length > 0)
    .map(para => {
      // Skip if it's already an HTML element
      if (para.startsWith('<') && para.endsWith('>')) {
        return para;
      }
      // Skip if it contains HTML tags
      if (para.includes('<h') || para.includes('<ul') || para.includes('<ol') || para.includes('<li>')) {
        return para;
      }
      return `<p>${para}</p>`;
    })
    .join('\n\n');

  return processedContent;
}
