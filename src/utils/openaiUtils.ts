import { createLogger } from '@/utils/logger';
import { getOpenAIClient } from '@/utils/openaiClient';
import { markdownToHtml } from '@/utils/markdownToHtml';

// Create a logger for OpenAI operations
const logger = createLogger('OpenAI');

// Define types for the blog post generation
export interface BlogPostGenerationOptions {
  topic: string;
  tone?: 'professional' | 'casual' | 'humorous' | 'technical' | 'conversational';
  length?: 'short' | 'medium' | 'long';
  targetAudience?: string;
  includeHeadings?: boolean;
  includeBulletPoints?: boolean;
  includeConclusion?: boolean;
}

// Define the response type
export interface BlogPostGenerationResponse {
  success: boolean;
  title?: string;
  content?: string;
  excerpt?: string;
  error?: string;
}

/**
 * Generate a blog post using OpenAI API
 * @param options Options for blog post generation
 * @returns Generated blog post content
 */
export async function generateBlogPost(options: BlogPostGenerationOptions): Promise<BlogPostGenerationResponse> {
  try {
    logger.info(`Generating blog post about: ${options.topic}`);

    // Get the OpenAI client
    const openai = getOpenAIClient();

    // Determine content length based on the length option
    const wordCount = options.length === 'short'
      ? '300-500'
      : options.length === 'medium'
        ? '600-800'
        : '1000-1500';

    // Build the prompt based on the options
    const prompt = buildPrompt(options, wordCount);

    // Call OpenAI API using the client
    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are a professional blog writer who creates engaging, well-structured content. Use proper markdown formatting for all content, including headings, lists, emphasis, and paragraph breaks. Do not include HTML tags in your response, only use markdown formatting.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    });

    const generatedContent = completion.choices[0].message.content || '';

    // Parse the generated content to extract title and content
    const { title, content, excerpt } = parseGeneratedContent(generatedContent);

    logger.info(`Successfully generated blog post: "${title}"`);

    return {
      success: true,
      title,
      content,
      excerpt
    };
  } catch (error) {
    logger.error('Error generating blog post:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error generating blog post'
    };
  }
}

/**
 * Build the prompt for OpenAI based on the options
 */
function buildPrompt(options: BlogPostGenerationOptions, wordCount: string): string {
  const {
    topic,
    tone = 'professional',
    targetAudience = 'general readers',
    includeHeadings = true,
    includeBulletPoints = true,
    includeConclusion = true
  } = options;

  let prompt = `Write a blog post about "${topic}" in a ${tone} tone for ${targetAudience}. `;
  prompt += `The blog post should be approximately ${wordCount} words. `;

  if (includeHeadings) {
    prompt += 'Include a compelling title and organize the content with appropriate headings (use markdown format: # for main heading, ## for subheadings, ### for sub-subheadings). ';
  }

  if (includeBulletPoints) {
    prompt += 'Use bullet points (- item) where appropriate to break down complex information. ';
  }

  if (includeConclusion) {
    prompt += 'Include a conclusion that summarizes the key points. ';
  }

  prompt += 'Use proper markdown formatting:\n';
  prompt += '- Use # for main headings, ## for subheadings, ### for sub-subheadings\n';
  prompt += '- Use **bold** for emphasis\n';
  prompt += '- Use *italic* for subtle emphasis\n';
  prompt += '- Use - for bullet points\n';
  prompt += '- Use 1. 2. 3. for numbered lists\n';
  prompt += '- Use blank lines between paragraphs\n\n';

  prompt += 'Format the response with the title at the top preceded by "TITLE: ", ';
  prompt += 'followed by a brief excerpt (2-3 sentences) preceded by "EXCERPT: ", ';
  prompt += 'and then the full content preceded by "CONTENT: " using the markdown formatting described above.';

  return prompt;
}

/**
 * Parse the generated content to extract title, excerpt, and content
 */
function parseGeneratedContent(generatedContent: string): { title: string; content: string; excerpt: string } {
  // Default values
  let title = 'Generated Blog Post';
  let content = generatedContent;
  let excerpt = '';

  // Extract title
  const titleMatch = generatedContent.match(/TITLE:\s*(.*?)(?=\n|EXCERPT:|CONTENT:|$)/s);
  if (titleMatch && titleMatch[1]) {
    title = titleMatch[1].trim();
  }

  // Extract excerpt
  const excerptMatch = generatedContent.match(/EXCERPT:\s*(.*?)(?=\n|CONTENT:|$)/s);
  if (excerptMatch && excerptMatch[1]) {
    excerpt = excerptMatch[1].trim();
  }

  // Extract content
  const contentMatch = generatedContent.match(/CONTENT:\s*([\s\S]*)/);
  if (contentMatch && contentMatch[1]) {
    content = contentMatch[1].trim();

    // Convert markdown to HTML
    content = markdownToHtml(content);

    // Log the first part of the processed content
    logger.info(`Content processed: ${content.substring(0, 100)}...`);
  }

  return { title, content, excerpt };
}
