// Mark as server component
'use server';

import { readFile } from 'node:fs/promises';
import { join } from 'node:path';
import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3';
import type { ImageItem } from './getImages';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Function to get S3 client with configuration from database
async function getS3Client() {
  try {
    // Get the default storage configuration from the database
    // @ts-ignore - Using the StorageConfig table that's defined in our schema
    const config = await prisma.storageConfig.findFirst({
      where: { isDefault: true },
    });

    if (config) {
      console.log('Using storage configuration from database:', {
        provider: config.provider,
        region: config.region,
        endpoint: config.endpoint,
        bucketName: config.bucketName,
      });

      // Import NodeHttpHandler dynamically to avoid server component issues
      let NodeHttpHandler;
      try {
        NodeHttpHandler = require('@aws-sdk/node-http-handler').NodeHttpHandler;
      } catch (err) {
        console.warn('NodeHttpHandler not available, using default handler');
      }

      return {
        client: new S3Client({
          region: config.region,
          endpoint: config.endpoint,
          credentials: {
            accessKeyId: config.accessKeyId,
            secretAccessKey: config.secretAccessKey,
          },
          forcePathStyle: true,
          maxAttempts: 3,
          retryMode: 'standard',
          requestHandler: NodeHttpHandler ? new NodeHttpHandler({
            connectionTimeout: 10000, // 10 seconds connection timeout
            socketTimeout: 60000,    // 60 seconds socket timeout
          }) : undefined
        }),
        bucketName: config.bucketName,
        endpoint: config.endpoint
      };
    }
  } catch (error) {
    console.error('Error fetching storage config from database:', error);
  }

  // Fallback to environment variables
  console.log('Falling back to environment variables for S3 configuration');

  // Import NodeHttpHandler dynamically to avoid server component issues
  let NodeHttpHandler;
  try {
    NodeHttpHandler = require('@aws-sdk/node-http-handler').NodeHttpHandler;
  } catch (err) {
    console.warn('NodeHttpHandler not available, using default handler');
  }

  return {
    client: new S3Client({
      region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
      endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
      credentials: {
        accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
        secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
      },
      forcePathStyle: true,
      maxAttempts: 3,
      retryMode: 'standard',
      requestHandler: NodeHttpHandler ? new NodeHttpHandler({
        connectionTimeout: 10000, // 10 seconds connection timeout
        socketTimeout: 60000,    // 60 seconds socket timeout
      }) : undefined
    }),
    bucketName: process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky',
    endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com'
  };
};

// Get images from S3 based on category
export async function getImagesFromServer(dirPath: string): Promise<ImageItem[]> {
  try {
    // Get S3 client and configuration
    const { client, bucketName, endpoint } = await getS3Client();

    if (!bucketName) {
      console.warn('S3 bucket not configured');
      return [];
    }

    // Extract category from path and clean it
    const category = dirPath.split('/').pop() || '';
    console.log('Fetching images for category:', category);

    // Construct the prefix to include images/portfolio/ directory
    const prefix = `images/portfolio/${category}/`;
    console.log('Using S3 prefix:', prefix);

    const command = new ListObjectsV2Command({
      Bucket: bucketName,
      Prefix: prefix,
      MaxKeys: 1000, // Increase max keys to ensure we get all images
    });

    const response = await client.send(command);
    console.log(`Found ${response.Contents?.length || 0} items in S3 for category:`, category);

    if (!response.Contents || response.Contents.length === 0) {
      console.warn(`No images found in S3 for category: ${category}. Using fallback data.`);
      return [];
    }

    const images: ImageItem[] = [];

    if (response.Contents) {
      for (const item of response.Contents) {
        if (!item.Key) {
          console.log('Skipping item with no key');
          continue;
        }

        // Skip if not an image or if it's just the folder itself
        if (!/\.(jpg|jpeg|png|webp|gif)$/i.test(item.Key) || item.Key === prefix) {
          console.log('Skipping non-image or folder:', item.Key);
          continue;
        }

        // Extract filename from the key
        const filename = item.Key.split('/').pop() || '';
        console.log('Processing image:', filename);

        // Construct the URL properly
        const imageUrl = `${endpoint}/${bucketName}/${item.Key}`;
        console.log('Image URL:', imageUrl);

        images.push({
          id: images.length + 1,
          src: imageUrl,
          url: imageUrl, // Include url property as well for compatibility
          alt: filename.replace(/\.(jpg|jpeg|png|webp|gif)$/i, '').replace(/-/g, ' '),
          size: item.Size || 0,
          createdAt: item.LastModified?.toISOString() || new Date().toISOString(),
          category: category
        });
      }
    }

    console.log(`Processed ${images.length} valid images for category:`, category);
    if (images.length === 0) {
      console.warn(`No valid images found for category: ${category}. Check S3 path and configuration.`);
    }
    return images;
  } catch (error) {
    console.error('Error fetching images from S3:', error);
    return [];
  }
}

// Add getTestimonialsData function for server-side testimonial fetching
export async function getTestimonialsData() {
  try {
    const dataPath = join(process.cwd(), 'src', 'data', 'testimonials.json');
    const fileContents = await readFile(dataPath, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading testimonials data:', error);
    return [];
  }
}