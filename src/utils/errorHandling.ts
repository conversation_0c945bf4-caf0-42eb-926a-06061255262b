import { toast } from 'react-hot-toast';

/**
 * Handles API errors in a consistent way across the application
 * 
 * @param error The error object
 * @param defaultMessage Default message to show if error doesn't have a message
 * @param options Additional options for error handling
 */
export function handleApiError(
  error: any, 
  defaultMessage: string,
  options: {
    logToConsole?: boolean;
    showToast?: boolean;
    toastType?: 'error' | 'warning';
  } = {
    logToConsole: true,
    showToast: true,
    toastType: 'error'
  }
) {
  // Extract error message
  const errorMessage = error?.message || defaultMessage;
  
  // Log to console if enabled
  if (options.logToConsole !== false) {
    console.error(`Error: ${defaultMessage}`, error);
  }
  
  // Show toast notification if enabled
  if (options.showToast !== false) {
    toast[options.toastType || 'error'](errorMessage);
  }
  
  // Return the error message for further use
  return errorMessage;
}

/**
 * Handles form submission errors
 * 
 * @param error The error object
 * @param defaultMessage Default message to show
 * @param setError Function to set error state in the form
 */
export function handleFormError(
  error: any,
  defaultMessage: string,
  setError?: (error: string) => void
) {
  const errorMessage = handleApiError(error, defaultMessage);
  
  // Set form error state if provided
  if (setError) {
    setError(errorMessage);
  }
  
  return errorMessage;
}

/**
 * Creates a try-catch wrapper for async functions with consistent error handling
 * 
 * @param asyncFn The async function to wrap
 * @param errorMessage Default error message
 * @param options Error handling options
 * @returns A wrapped function with error handling
 */
export function withErrorHandling<T extends (...args: any[]) => Promise<any>>(
  asyncFn: T,
  errorMessage: string,
  options: {
    onError?: (error: any) => void;
    showToast?: boolean;
  } = {}
): (...args: Parameters<T>) => Promise<ReturnType<T> | null> {
  return async (...args: Parameters<T>) => {
    try {
      return await asyncFn(...args);
    } catch (error) {
      handleApiError(error, errorMessage, {
        showToast: options.showToast !== false
      });
      
      if (options.onError) {
        options.onError(error);
      }
      
      return null;
    }
  };
}
