import { OpenAI } from 'openai';
import { createLogger } from '@/utils/logger';

// Create a logger for OpenAI operations
const logger = createLogger('OpenAI Client');

// Create a singleton instance of the OpenAI client
let openaiInstance: OpenAI | null = null;

/**
 * Get the OpenAI client instance
 * @returns OpenAI client instance
 */
export function getOpenAIClient(): OpenAI {
  if (!openaiInstance) {
    const apiKey = process.env.OPENAI_API_KEY;
    
    if (!apiKey) {
      logger.error('OpenAI API key is not configured');
      throw new Error('OpenAI API key is not configured. Please add it to your environment variables.');
    }
    
    logger.info('Initializing OpenAI client');
    openaiInstance = new OpenAI({
      apiKey,
    });
  }
  
  return openaiInstance;
}

/**
 * Generate a simple text completion using OpenAI
 * @param prompt The prompt to send to OpenAI
 * @param model The model to use (defaults to gpt-4)
 * @returns The generated text
 */
export async function generateCompletion(
  prompt: string, 
  model: string = 'gpt-4'
): Promise<string> {
  try {
    logger.info(`Generating completion with model: ${model}`);
    
    const openai = getOpenAIClient();
    
    const completion = await openai.chat.completions.create({
      model,
      messages: [
        { role: 'user', content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 1000,
    });
    
    const generatedText = completion.choices[0].message.content || '';
    logger.info('Successfully generated completion');
    
    return generatedText;
  } catch (error) {
    logger.error('Error generating completion:', error);
    throw error;
  }
}

/**
 * Generate a chat completion using OpenAI
 * @param messages The messages to send to OpenAI
 * @param model The model to use (defaults to gpt-4)
 * @returns The generated response
 */
export async function generateChatCompletion(
  messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>,
  model: string = 'gpt-4'
): Promise<string> {
  try {
    logger.info(`Generating chat completion with model: ${model}`);
    
    const openai = getOpenAIClient();
    
    const completion = await openai.chat.completions.create({
      model,
      messages,
      temperature: 0.7,
      max_tokens: 1000,
    });
    
    const generatedText = completion.choices[0].message.content || '';
    logger.info('Successfully generated chat completion');
    
    return generatedText;
  } catch (error) {
    logger.error('Error generating chat completion:', error);
    throw error;
  }
}
