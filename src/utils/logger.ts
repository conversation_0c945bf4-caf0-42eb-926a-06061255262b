/**
 * Enhanced logging utility for better debugging
 * 
 * This module provides structured logging with context information,
 * log levels, and optional persistence to help with debugging issues
 * in both development and production environments.
 */

// Log levels
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
}

// Log entry structure
export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  context: string;
  message: string;
  data?: any;
}

// In-memory log storage for recent logs (limited to last 1000 entries)
const recentLogs: LogEntry[] = [];
const MAX_LOGS = 1000;

/**
 * Add a log entry to the in-memory storage
 */
function addToRecentLogs(entry: LogEntry): void {
  recentLogs.push(entry);
  if (recentLogs.length > MAX_LOGS) {
    recentLogs.shift(); // Remove oldest log
  }
}

/**
 * Format a log entry for console output
 */
function formatLogEntry(entry: LogEntry): string {
  const { timestamp, level, context, message } = entry;
  return `[${timestamp}] ${level.padEnd(5)} [${context}] ${message}`;
}

/**
 * Create a logger instance with a specific context
 */
export function createLogger(context: string) {
  return {
    /**
     * Log a debug message
     */
    debug: (message: string, data?: any) => {
      // Only log debug in development
      if (process.env.NODE_ENV !== 'production') {
        const entry: LogEntry = {
          timestamp: new Date().toISOString(),
          level: LogLevel.DEBUG,
          context,
          message,
          data,
        };
        console.debug(formatLogEntry(entry), data ? data : '');
        addToRecentLogs(entry);
      }
    },

    /**
     * Log an info message
     */
    info: (message: string, data?: any) => {
      const entry: LogEntry = {
        timestamp: new Date().toISOString(),
        level: LogLevel.INFO,
        context,
        message,
        data,
      };
      console.log(formatLogEntry(entry), data ? data : '');
      addToRecentLogs(entry);
    },

    /**
     * Log a warning message
     */
    warn: (message: string, data?: any) => {
      const entry: LogEntry = {
        timestamp: new Date().toISOString(),
        level: LogLevel.WARN,
        context,
        message,
        data,
      };
      console.warn(formatLogEntry(entry), data ? data : '');
      addToRecentLogs(entry);
    },

    /**
     * Log an error message
     */
    error: (message: string, error?: any) => {
      const entry: LogEntry = {
        timestamp: new Date().toISOString(),
        level: LogLevel.ERROR,
        context,
        message,
        data: error,
      };
      console.error(formatLogEntry(entry), error ? error : '');
      addToRecentLogs(entry);
    },
  };
}

/**
 * Get recent logs, optionally filtered by level and context
 */
export function getRecentLogs(options?: { 
  level?: LogLevel; 
  context?: string;
  limit?: number;
}): LogEntry[] {
  const { level, context, limit = MAX_LOGS } = options || {};
  
  let filteredLogs = [...recentLogs];
  
  if (level) {
    filteredLogs = filteredLogs.filter(log => log.level === level);
  }
  
  if (context) {
    filteredLogs = filteredLogs.filter(log => log.context.includes(context));
  }
  
  // Return most recent logs first, limited to requested amount
  return filteredLogs.reverse().slice(0, limit);
}

/**
 * Clear all recent logs
 */
export function clearRecentLogs(): void {
  recentLogs.length = 0;
}

// Create a default logger for general use
export const logger = createLogger('app');

// Export default logger
export default logger;
