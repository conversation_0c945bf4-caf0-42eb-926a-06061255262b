import { z } from 'zod';
import DOMPurify from 'isomorphic-dompurify';
import { TeamMemberValidationError } from '@/types/team';

/**
 * Sanitizes HTML content to prevent XSS attacks
 */
export function sanitizeHtml(input: string): string {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  });
}

/**
 * Sanitizes and validates text input
 */
export function sanitizeText(input: string, maxLength: number = 1000): string {
  if (typeof input !== 'string') {
    throw new TeamMemberValidationError('Input must be a string');
  }
  
  // Remove HTML tags and sanitize
  const sanitized = sanitizeHtml(input.trim());
  
  // Check length
  if (sanitized.length > maxLength) {
    throw new TeamMemberValidationError(`Input exceeds maximum length of ${maxLength} characters`);
  }
  
  return sanitized;
}

/**
 * Validates and sanitizes URL input
 */
export function sanitizeUrl(url: string): string | null {
  if (!url || url.trim() === '') {
    return null;
  }
  
  const sanitized = sanitizeText(url, 255);
  
  try {
    const urlObj = new URL(sanitized);
    // Only allow https URLs
    if (urlObj.protocol !== 'https:') {
      throw new TeamMemberValidationError('Only HTTPS URLs are allowed');
    }
    return urlObj.toString();
  } catch (error) {
    throw new TeamMemberValidationError('Invalid URL format');
  }
}

/**
 * Validates email format
 */
export function validateEmail(email: string): string | null {
  if (!email || email.trim() === '') {
    return null;
  }
  
  const sanitized = sanitizeText(email, 100);
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (!emailRegex.test(sanitized)) {
    throw new TeamMemberValidationError('Invalid email format');
  }
  
  return sanitized.toLowerCase();
}

/**
 * Validates image file
 */
export function validateImageFile(file: File): void {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const maxSize = 15 * 1024 * 1024; // 15MB
  
  if (!allowedTypes.includes(file.type)) {
    throw new TeamMemberValidationError(
      'Invalid image format. Only JPEG, PNG, GIF, and WebP are allowed',
      'image',
      'INVALID_FORMAT'
    );
  }
  
  if (file.size > maxSize) {
    throw new TeamMemberValidationError(
      `Image file is too large (${Math.round(file.size / 1024 / 1024)}MB). Maximum size is 15MB`,
      'image',
      'FILE_TOO_LARGE'
    );
  }
  
  if (file.size === 0) {
    throw new TeamMemberValidationError(
      'Image file is empty',
      'image',
      'EMPTY_FILE'
    );
  }
}

/**
 * Validates order number
 */
export function validateOrder(order: any): number {
  const parsed = parseInt(order, 10);
  
  if (isNaN(parsed)) {
    throw new TeamMemberValidationError('Order must be a valid number');
  }
  
  if (parsed < 0) {
    throw new TeamMemberValidationError('Order must be non-negative');
  }
  
  if (parsed > 999) {
    throw new TeamMemberValidationError('Order must be less than 1000');
  }
  
  return parsed;
}

/**
 * Comprehensive validation for team member data
 */
export interface ValidatedTeamMemberData {
  name: string;
  role: string;
  bio: string;
  order: number;
  linkedinUrl: string | null;
  twitterUrl: string | null;
  githubUrl: string | null;
  emailAddress: string | null;
}

export function validateTeamMemberData(data: any): ValidatedTeamMemberData {
  const errors: string[] = [];
  
  try {
    // Validate and sanitize name
    const name = sanitizeText(data.name, 100);
    if (!name) {
      errors.push('Name is required');
    } else if (!/^[a-zA-Z\s\-'\.]+$/.test(name)) {
      errors.push('Name contains invalid characters');
    }
    
    // Validate and sanitize role
    const role = sanitizeText(data.role, 100);
    if (!role) {
      errors.push('Role is required');
    } else if (!/^[a-zA-Z\s\-|&,\.]+$/.test(role)) {
      errors.push('Role contains invalid characters');
    }
    
    // Validate and sanitize bio
    const bio = sanitizeText(data.bio, 1000);
    if (!bio) {
      errors.push('Bio is required');
    } else if (bio.length < 10) {
      errors.push('Bio must be at least 10 characters');
    }
    
    // Validate order
    const order = validateOrder(data.order);
    
    // Validate optional URLs
    const linkedinUrl = data.linkedinUrl ? sanitizeUrl(data.linkedinUrl) : null;
    const twitterUrl = data.twitterUrl ? sanitizeUrl(data.twitterUrl) : null;
    const githubUrl = data.githubUrl ? sanitizeUrl(data.githubUrl) : null;
    
    // Validate LinkedIn URL format
    if (linkedinUrl && !/^https:\/\/(www\.)?linkedin\.com\/in\/[a-zA-Z0-9\-]+\/?$/.test(linkedinUrl)) {
      errors.push('Invalid LinkedIn profile URL format');
    }
    
    // Validate Twitter URL format
    if (twitterUrl && !/^https:\/\/(www\.)?(twitter\.com|x\.com)\/[a-zA-Z0-9_]+\/?$/.test(twitterUrl)) {
      errors.push('Invalid Twitter profile URL format');
    }
    
    // Validate GitHub URL format
    if (githubUrl && !/^https:\/\/(www\.)?github\.com\/[a-zA-Z0-9\-]+\/?$/.test(githubUrl)) {
      errors.push('Invalid GitHub profile URL format');
    }
    
    // Validate email
    const emailAddress = data.emailAddress ? validateEmail(data.emailAddress) : null;
    
    if (errors.length > 0) {
      throw new TeamMemberValidationError(errors.join(', '));
    }
    
    return {
      name,
      role,
      bio,
      order,
      linkedinUrl,
      twitterUrl,
      githubUrl,
      emailAddress
    };
  } catch (error) {
    if (error instanceof TeamMemberValidationError) {
      throw error;
    }
    throw new TeamMemberValidationError('Validation failed: ' + (error as Error).message);
  }
}

/**
 * Rate limiting utility
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  identifier: string, 
  maxRequests: number = 10, 
  windowMs: number = 60000
): boolean {
  const now = Date.now();
  const record = rateLimitMap.get(identifier);
  
  if (!record || now > record.resetTime) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (record.count >= maxRequests) {
    return false;
  }
  
  record.count++;
  return true;
}

/**
 * Clean up expired rate limit entries
 */
export function cleanupRateLimit(): void {
  const now = Date.now();
  for (const [key, record] of rateLimitMap.entries()) {
    if (now > record.resetTime) {
      rateLimitMap.delete(key);
    }
  }
}

// Clean up every 5 minutes
setInterval(cleanupRateLimit, 5 * 60 * 1000);
