/**
 * Unified Test Utilities
 *
 * This module provides a centralized set of test utilities for the application.
 * It combines functionality from various test scripts and provides a consistent
 * interface for running tests from the admin interface.
 */

import fs from 'fs';
import path from 'path';
import { PrismaClient } from '@prisma/client';
import { S3Client, ListObjectsV2Command, HeadO<PERSON>Command, PutO<PERSON>Command, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';

// Initialize Prisma client
const prisma = new PrismaClient();

// Test types
export enum TestType {
  S3_CONNECTION = 's3_connection',
  S3_UPLOAD = 's3_upload',
  DATABASE = 'database',
  COMPONENTS = 'components',
  API = 'api',
  OPENAI = 'openai',
}

// Test result interface
export interface TestResult {
  success: boolean;
  message: string;
  details?: any;
  duration?: number;
}

// S3 configuration interface
export interface S3Config {
  region: string;
  endpoint: string;
  bucketName: string;
  accessKeyId: string;
  secretAccessKey: string;
}

/**
 * Get S3 configuration from environment variables
 */
export function getS3ConfigFromEnv(): S3Config {
  return {
    region: process.env.NEXT_PUBLIC_S3_REGION || '',
    endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || '',
    bucketName: process.env.NEXT_PUBLIC_S3_BUCKET || '',
    accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
    secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
  };
}

/**
 * Create an S3 client with the given configuration
 */
export function createS3Client(config: S3Config): S3Client {
  return new S3Client({
    region: config.region,
    endpoint: config.endpoint,
    credentials: {
      accessKeyId: config.accessKeyId,
      secretAccessKey: config.secretAccessKey,
    },
    forcePathStyle: true,
  });
}

/**
 * Test S3 connection
 */
export async function testS3Connection(config?: S3Config): Promise<TestResult> {
  const startTime = Date.now();

  try {
    // Use provided config or get from env
    const s3Config = config || getS3ConfigFromEnv();

    // Validate configuration
    if (!s3Config.region || !s3Config.endpoint || !s3Config.bucketName ||
        !s3Config.accessKeyId || !s3Config.secretAccessKey) {
      return {
        success: false,
        message: 'S3 configuration is incomplete',
        details: {
          missingFields: Object.entries(s3Config)
            .filter(([_, value]) => !value)
            .map(([key]) => key)
        },
        duration: Date.now() - startTime
      };
    }

    // Create S3 client
    const s3Client = createS3Client(s3Config);

    // List objects in the bucket
    const command = new ListObjectsV2Command({
      Bucket: s3Config.bucketName,
      MaxKeys: 5,
    });

    const response = await s3Client.send(command);

    return {
      success: true,
      message: `Successfully connected to S3 bucket: ${s3Config.bucketName}`,
      details: {
        objectCount: response.KeyCount || 0,
        objects: response.Contents?.slice(0, 5).map(item => ({
          key: item.Key,
          size: item.Size,
          lastModified: item.LastModified
        }))
      },
      duration: Date.now() - startTime
    };
  } catch (error: any) {
    return {
      success: false,
      message: `Failed to connect to S3: ${error.message}`,
      details: { error: error.toString() },
      duration: Date.now() - startTime
    };
  }
}

/**
 * Test S3 upload
 */
export async function testS3Upload(config?: S3Config): Promise<TestResult> {
  const startTime = Date.now();

  try {
    // Use provided config or get from env
    const s3Config = config || getS3ConfigFromEnv();

    // Create S3 client
    const s3Client = createS3Client(s3Config);

    // Generate a unique test key
    const testKey = `test/test-upload-${Date.now()}.txt`;

    // Create a test file content
    const testContent = `Test file created at ${new Date().toISOString()}`;

    // Upload the test file
    const uploadCommand = new PutObjectCommand({
      Bucket: s3Config.bucketName,
      Key: testKey,
      Body: testContent,
      ContentType: 'text/plain',
      ACL: 'public-read',
    });

    await s3Client.send(uploadCommand);

    // Verify the upload
    const getCommand = new GetObjectCommand({
      Bucket: s3Config.bucketName,
      Key: testKey,
    });

    await s3Client.send(getCommand);

    // Generate URL
    const imageUrl = `${s3Config.endpoint}/${s3Config.bucketName}/${testKey}`;

    // Clean up
    const deleteCommand = new DeleteObjectCommand({
      Bucket: s3Config.bucketName,
      Key: testKey,
    });

    await s3Client.send(deleteCommand);

    return {
      success: true,
      message: 'Successfully uploaded, verified, and cleaned up test file',
      details: {
        testKey,
        url: imageUrl,
        uploadTime: Date.now() - startTime
      },
      duration: Date.now() - startTime
    };
  } catch (error: any) {
    return {
      success: false,
      message: `Failed to test S3 upload: ${error.message}`,
      details: { error: error.toString() },
      duration: Date.now() - startTime
    };
  }
}

/**
 * Test database connection
 */
export async function testDatabaseConnection(): Promise<TestResult> {
  const startTime = Date.now();

  try {
    // Test connection by querying the database
    const result = await prisma.$queryRaw`SELECT 1 as connected`;

    return {
      success: true,
      message: 'Successfully connected to database',
      details: { result },
      duration: Date.now() - startTime
    };
  } catch (error: any) {
    return {
      success: false,
      message: `Failed to connect to database: ${error.message}`,
      details: { error: error.toString() },
      duration: Date.now() - startTime
    };
  }
}

/**
 * Test UI components
 */
export async function testComponents(): Promise<TestResult> {
  const startTime = Date.now();

  try {
    // Define the components and pages to test
    const filesToCheck = [
      // User Management
      'src/app/admin/users/page.tsx',
      'src/app/admin/users/new/page.tsx',
      'src/app/admin/users/edit/[id]/page.tsx',
      'src/app/admin/users/layout.tsx',

      // Role Management
      'src/app/admin/roles/page.tsx',
      'src/app/admin/roles/new/page.tsx',
      'src/app/admin/roles/edit/[id]/page.tsx',
      'src/app/admin/roles/layout.tsx',

      // Activity Logs
      'src/app/admin/activity-logs/page.tsx',
      'src/app/admin/activity-logs/layout.tsx',

      // Components
      'src/components/admin/PermissionSelector.tsx',

      // API Routes
      'src/app/api/admin/users/route.ts',
      'src/app/api/admin/users/[id]/route.ts',
      'src/app/api/admin/roles/route.ts',
      'src/app/api/admin/roles/[id]/route.ts',
      'src/app/api/admin/activity-logs/route.ts',

      // Utilities
      'src/utils/passwordUtils.ts',
      'src/middleware/adminAuth.ts'
    ];

    // Check each file
    const results = filesToCheck.map(file => {
      const exists = fs.existsSync(file);
      return { file, exists };
    });

    // Check if the sidebar has been updated
    const sidebarPath = 'src/components/admin/AdminSidebar.tsx';
    let sidebarResults = { exists: false, hasUserManagement: false, hasUserManagementItems: false };

    if (fs.existsSync(sidebarPath)) {
      const sidebarContent = fs.readFileSync(sidebarPath, 'utf8');
      sidebarResults = {
        exists: true,
        hasUserManagement: sidebarContent.includes('userManagementOpen'),
        hasUserManagementItems: sidebarContent.includes('userManagementMenuItems')
      };
    }

    return {
      success: results.every(r => r.exists) && sidebarResults.exists &&
               sidebarResults.hasUserManagement && sidebarResults.hasUserManagementItems,
      message: results.every(r => r.exists) ? 'All components exist' : 'Some components are missing',
      details: {
        components: results,
        sidebar: sidebarResults
      },
      duration: Date.now() - startTime
    };
  } catch (error: any) {
    return {
      success: false,
      message: `Failed to test components: ${error.message}`,
      details: { error: error.toString() },
      duration: Date.now() - startTime
    };
  }
}

/**
 * Test API endpoints
 */
export async function testApiEndpoints(): Promise<TestResult> {
  const startTime = Date.now();

  try {
    // Define the API endpoints to test
    const endpoints = [
      '/api/blog',
      '/api/portfolio',
      '/api/testimonials',
      '/api/catalogue',
    ];

    // Test each endpoint
    const results = await Promise.all(
      endpoints.map(async (endpoint) => {
        try {
          const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}${endpoint}`);
          return {
            endpoint,
            status: response.status,
            success: response.ok,
            message: response.ok ? 'Success' : 'Failed',
          };
        } catch (error: any) {
          return {
            endpoint,
            status: 500,
            success: false,
            message: error.message,
          };
        }
      })
    );

    return {
      success: results.every(r => r.success),
      message: results.every(r => r.success)
        ? 'All API endpoints are working'
        : 'Some API endpoints are not working',
      details: { endpoints: results },
      duration: Date.now() - startTime
    };
  } catch (error: any) {
    return {
      success: false,
      message: `Failed to test API endpoints: ${error.message}`,
      details: { error: error.toString() },
      duration: Date.now() - startTime
    };
  }
}

/**
 * Test OpenAI integration
 */
export async function testOpenAI(): Promise<TestResult> {
  const startTime = Date.now();

  try {
    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY) {
      return {
        success: false,
        message: 'OpenAI API key is not configured',
        details: { error: 'OPENAI_API_KEY is not set in environment variables' },
        duration: Date.now() - startTime
      };
    }

    // Test the OpenAI API endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/test/openai`);

    if (!response.ok) {
      const errorData = await response.json();
      return {
        success: false,
        message: 'Failed to test OpenAI integration',
        details: { error: errorData.error || 'Unknown error' },
        duration: Date.now() - startTime
      };
    }

    const data = await response.json();

    return {
      success: data.success,
      message: data.success ? 'OpenAI integration is working' : 'OpenAI integration failed',
      details: data,
      duration: Date.now() - startTime
    };
  } catch (error: any) {
    return {
      success: false,
      message: `Failed to test OpenAI integration: ${error.message}`,
      details: { error: error.toString() },
      duration: Date.now() - startTime
    };
  }
}