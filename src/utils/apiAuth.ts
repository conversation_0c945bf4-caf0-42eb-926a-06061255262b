import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { logActivity } from '@/utils/passwordUtils';

/**
 * Middleware to protect API routes with authentication and permission checks
 * 
 * @param handler The API route handler function
 * @param requiredPermission Optional permission required to access this route
 * @param options Additional options for the middleware
 * @returns A function that wraps the handler with authentication and permission checks
 */
export async function withAuth(
  handler: (req: NextRequest, session: any) => Promise<NextResponse>,
  requiredPermission?: string,
  options: {
    logAction?: string;
    resourceType?: string;
  } = {}
) {
  return async (req: NextRequest) => {
    try {
      // Get the session
      const session = await getServerSession(authOptions);
      
      // Check if user is authenticated
      if (!session) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      
      // Check if user has required permission
      if (requiredPermission && 
          session.user.role?.name !== 'admin' && 
          !session.user.role?.permissions.includes(requiredPermission) &&
          !session.user.role?.permissions.includes('*')) {
        
        // Log unauthorized access attempt
        await logActivity({
          userId: session.user.id,
          action: 'unauthorized_access',
          details: `Attempted to access route requiring permission: ${requiredPermission}`,
          resourceType: options.resourceType || 'api',
        });
        
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
      }
      
      // Log the action if specified
      if (options.logAction) {
        await logActivity({
          userId: session.user.id,
          action: options.logAction,
          details: `Accessed ${req.method} ${req.nextUrl.pathname}`,
          resourceType: options.resourceType || 'api',
        });
      }
      
      // Call the handler with the session
      return handler(req, session);
    } catch (error) {
      console.error('API authentication error:', error);
      return NextResponse.json(
        { error: 'Internal Server Error', details: error.message },
        { status: 500 }
      );
    }
  };
}

/**
 * Helper function to validate request body against a schema
 * 
 * @param req The NextRequest object
 * @param schema The validation schema (e.g., Zod schema)
 * @returns The validated data or throws an error
 */
export async function validateRequest(req: NextRequest, schema: any) {
  try {
    const body = await req.json();
    const result = schema.safeParse(body);
    
    if (!result.success) {
      return {
        success: false,
        error: result.error.format(),
        status: 400,
        message: 'Invalid request data'
      };
    }
    
    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    return {
      success: false,
      error,
      status: 400,
      message: 'Failed to parse request body'
    };
  }
}
