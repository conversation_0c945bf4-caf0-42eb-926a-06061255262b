import { <PERSON><PERSON><PERSON> } from 'jsdom';
import createDOMPurify from 'dompurify';

/**
 * Sanitizes HTML content to prevent XSS attacks
 * Works on both server and client side
 * @param html The HTML content to sanitize
 * @returns Sanitized HTML content
 */
export function sanitizeHtml(html: string): string {
  try {
    // For server-side rendering
    if (typeof window === 'undefined') {
      const window = new JSDOM('').window;
      const DOMPurify = createDOMPurify(window);
      return DOMPurify.sanitize(html, {
        USE_PROFILES: { html: true },
        ADD_ATTR: ['target', 'href', 'class'],
        ADD_TAGS: ['iframe', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'a', 'ul', 'ol', 'li', 'blockquote', 'img', 'br', 'strong', 'em', 'code', 'pre', 'hr', 'table', 'tr', 'td', 'th', 'thead', 'tbody', 'div', 'span'],
        FORBID_TAGS: ['script', 'style', 'form', 'input', 'textarea', 'select', 'button'],
        FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover'],
        KEEP_CONTENT: true,
      });
    }

    // For client-side rendering
    const DOMPurify = createDOMPurify(window);
    return DOMPurify.sanitize(html, {
      USE_PROFILES: { html: true },
      ADD_ATTR: ['target', 'href', 'class'],
      ADD_TAGS: ['iframe', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'a', 'ul', 'ol', 'li', 'blockquote', 'img', 'br', 'strong', 'em', 'code', 'pre', 'hr', 'table', 'tr', 'td', 'th', 'thead', 'tbody', 'div', 'span'],
      FORBID_TAGS: ['script', 'style', 'form', 'input', 'textarea', 'select', 'button'],
      FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover'],
      KEEP_CONTENT: true,
    });
  } catch (error) {
    console.error('Error sanitizing HTML:', error);
    // Return the original HTML if sanitization fails
    return html;
  }
}
