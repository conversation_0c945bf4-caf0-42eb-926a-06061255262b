import { z } from 'zod';

// Enhanced validation schemas for catalogue
export const CatalogueCreateSchema = z.object({
  service: z.string()
    .min(1, 'Service name is required')
    .max(255, 'Service name too long')
    .regex(/^[a-zA-Z0-9\s\-&.,()]+$/, 'Invalid characters in service name')
    .transform(str => str.trim()),
  price: z.number()
    .positive('Price must be positive')
    .max(10000000, 'Price too high')
    .int('Price must be a whole number'),
  description: z.string()
    .max(1000, 'Description too long')
    .transform(str => str?.trim() || '')
    .optional(),
  features: z.array(
    z.string()
      .max(100, 'Feature too long')
      .transform(str => str.trim())
  )
    .max(10, 'Too many features')
    .optional()
    .default([]),
  icon: z.string()
    .max(100, 'Icon name too long')
    .regex(/^[a-zA-Z0-9\-_]+$/, 'Invalid icon name')
    .optional(),
  popular: z.boolean().optional().default(false),
  imageUrl: z.string()
    .url('Invalid image URL')
    .max(500, 'Image URL too long')
    .optional(),
  imageUrl2: z.string()
    .url('Invalid image URL')
    .max(500, 'Image URL too long')
    .optional(),
  imageUrl3: z.string()
    .url('Invalid image URL')
    .max(500, 'Image URL too long')
    .optional(),
  category: z.string()
    .max(100, 'Category name too long')
    .regex(/^[a-zA-Z0-9\s\-&.,()]+$/, 'Invalid characters in category')
    .transform(str => str?.trim() || 'Other')
    .optional()
    .default('Other')
});

export const CatalogueUpdateSchema = CatalogueCreateSchema.partial();

export const CatalogueFiltersSchema = z.object({
  category: z.string().optional(),
  minPrice: z.number().min(0).optional(),
  maxPrice: z.number().min(0).optional(),
  popular: z.boolean().optional(),
  search: z.string().max(100).optional()
});

export const PaginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  sortBy: z.enum(['service', 'price', 'createdAt', 'updatedAt']).optional().default('service'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('asc')
});

export const CatalogueIdSchema = z.string()
  .regex(/^\d+$/, 'Invalid catalogue ID format')
  .transform(str => parseInt(str, 10))
  .refine(num => num > 0, 'Catalogue ID must be positive');

export const BulkDeleteSchema = z.object({
  ids: z.array(z.string().regex(/^\d+$/, 'Invalid ID format'))
    .min(1, 'At least one ID required')
    .max(50, 'Too many items to delete at once')
});

// Custom validation errors
export class CatalogueValidationError extends Error {
  constructor(
    message: string,
    public field?: string,
    public code?: string
  ) {
    super(message);
    this.name = 'CatalogueValidationError';
  }
}

// Validation helper functions
export function validateCatalogueCreate(data: unknown) {
  try {
    return CatalogueCreateSchema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      throw new CatalogueValidationError(
        firstError.message,
        firstError.path.join('.'),
        firstError.code
      );
    }
    throw error;
  }
}

export function validateCatalogueUpdate(data: unknown) {
  try {
    return CatalogueUpdateSchema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      throw new CatalogueValidationError(
        firstError.message,
        firstError.path.join('.'),
        firstError.code
      );
    }
    throw error;
  }
}

export function validateCatalogueFilters(data: unknown) {
  try {
    return CatalogueFiltersSchema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      throw new CatalogueValidationError(
        firstError.message,
        firstError.path.join('.'),
        firstError.code
      );
    }
    throw error;
  }
}

export function validatePagination(data: unknown) {
  try {
    return PaginationSchema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      throw new CatalogueValidationError(
        firstError.message,
        firstError.path.join('.'),
        firstError.code
      );
    }
    throw error;
  }
}

export function validateCatalogueId(id: unknown) {
  try {
    return CatalogueIdSchema.parse(id);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new CatalogueValidationError('Invalid catalogue ID');
    }
    throw error;
  }
}

export function validateBulkDelete(data: unknown) {
  try {
    return BulkDeleteSchema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      throw new CatalogueValidationError(
        firstError.message,
        firstError.path.join('.'),
        firstError.code
      );
    }
    throw error;
  }
}

// Rate limiting helper
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  identifier: string,
  maxRequests: number = 100,
  windowMs: number = 60000
): boolean {
  const now = Date.now();
  const windowStart = now - windowMs;
  
  const current = rateLimitMap.get(identifier);
  
  if (!current || current.resetTime < windowStart) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now });
    return true;
  }
  
  if (current.count >= maxRequests) {
    return false;
  }
  
  current.count++;
  return true;
}

// Sanitization helpers
export function sanitizeSearchTerm(search: string): string {
  return search
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML
    .replace(/['"]/g, '') // Remove quotes
    .substring(0, 100); // Limit length
}

export function sanitizeServiceName(service: string): string {
  return service
    .trim()
    .replace(/\s+/g, ' ') // Normalize whitespace
    .substring(0, 255);
}

// Type guards
export function isCatalogueCreateData(data: any): data is z.infer<typeof CatalogueCreateSchema> {
  try {
    CatalogueCreateSchema.parse(data);
    return true;
  } catch {
    return false;
  }
}

export function isCatalogueUpdateData(data: any): data is z.infer<typeof CatalogueUpdateSchema> {
  try {
    CatalogueUpdateSchema.parse(data);
    return true;
  } catch {
    return false;
  }
}
