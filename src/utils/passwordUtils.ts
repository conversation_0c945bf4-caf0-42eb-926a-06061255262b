import { compare, hash } from 'bcryptjs';
import prisma from '@/lib/prisma';
import { User } from '@prisma/client';

/**
 * Hash a password using bcrypt
 * @param password The plain text password to hash
 * @returns The hashed password
 */
export async function hashPassword(password: string): Promise<string> {
  return hash(password, 12);
}

/**
 * Verify a password against a hash
 * @param password The plain text password to verify
 * @param hashedPassword The hashed password to compare against
 * @returns True if the password matches, false otherwise
 */
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return compare(password, hashedPassword);
}

/**
 * Find a user by username or email
 * @param usernameOrEmail The username or email to search for
 * @returns The user if found, null otherwise
 */
export async function findUserByUsernameOrEmail(usernameOrEmail: string): Promise<User | null> {
  return prisma.user.findFirst({
    where: {
      OR: [
        { username: usernameOrEmail },
        { email: usernameOrEmail }
      ],
      active: true
    },
    include: {
      role: true
    }
  });
}

/**
 * Update a user's last login time
 * @param userId The ID of the user to update
 */
export async function updateUserLastLogin(userId: string): Promise<void> {
  await prisma.user.update({
    where: { id: userId },
    data: { lastLogin: new Date() }
  });
}

/**
 * Log a user activity
 * @param userId The ID of the user performing the action
 * @param action The action being performed
 * @param details Additional details about the action
 * @param ipAddress The IP address of the user
 * @param userAgent The user agent of the user's browser
 * @param resourceType The type of resource being acted upon
 * @param resourceId The ID of the resource being acted upon
 */
export async function logActivity(
  userId: string,
  action: string,
  details?: string,
  ipAddress?: string,
  userAgent?: string,
  resourceType?: string,
  resourceId?: string
): Promise<void> {
  await prisma.activityLog.create({
    data: {
      userId,
      action,
      details,
      ipAddress,
      userAgent,
      resourceType,
      resourceId
    }
  });
}

/**
 * Check if a user has a specific permission
 * @param user The user to check
 * @param permission The permission to check for
 * @returns True if the user has the permission, false otherwise
 */
export function hasPermission(user: any, permission: string): boolean {
  // Admin role has all permissions
  if (user.role?.name === 'admin' || user.role?.permissions?.includes('*')) {
    return true;
  }
  
  return user.role?.permissions?.includes(permission) || false;
}

/**
 * Create a default admin user if no users exist
 * This should be called during application startup
 */
export async function ensureAdminUser(): Promise<void> {
  const userCount = await prisma.user.count();
  
  if (userCount === 0) {
    // Get admin role
    const adminRole = await prisma.role.findFirst({
      where: { name: 'admin' }
    });
    
    if (!adminRole) {
      console.error('Admin role not found. Please run migrations first.');
      return;
    }
    
    // Create default admin user from environment variables
    const adminUsername = process.env.ADMIN_USERNAME;
    const adminPassword = process.env.ADMIN_PASSWORD;
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminName = process.env.ADMIN_NAME || 'Admin';
    
    if (!adminUsername || !adminPassword) {
      console.error('Admin credentials not configured in environment variables.');
      return;
    }
    
    const passwordHash = await hashPassword(adminPassword);
    
    await prisma.user.create({
      data: {
        username: adminUsername,
        email: adminEmail,
        name: adminName,
        passwordHash,
        roleId: adminRole.id,
        active: true
      }
    });
    
    console.log('Default admin user created successfully.');
  }
}
