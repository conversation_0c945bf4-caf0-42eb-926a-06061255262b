import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Define route permissions mapping
const routePermissions: Record<string, string> = {
  // Dashboard
  '/admin/dashboard': 'dashboard:read',
  '/admin/analytics': 'analytics:read',

  // Content Management
  '/admin/blog': 'blog:read',
  '/admin/blog/new': 'blog:write',
  '/admin/blog/edit': 'blog:write',
  '/admin/scheduled-blog-posts': 'blog:read',
  '/admin/scheduled-blog-posts/new': 'blog:write',
  '/admin/portfolio': 'portfolio:read',
  '/admin/portfolio/new': 'portfolio:write',
  '/admin/portfolio/edit': 'portfolio:write',
  '/admin/website-portfolio': 'portfolio:read',
  '/admin/website-portfolio/new': 'portfolio:write',
  '/admin/website-portfolio/edit': 'portfolio:write',
  '/admin/team': 'team:read',
  '/admin/team/new': 'team:write',
  '/admin/team/edit': 'team:write',

  // Receipting System
  '/admin/transactions': 'transactions:read',
  '/admin/receipts': 'receipts:read',
  '/admin/receipts/new': 'receipts:write',
  '/admin/invoices': 'invoices:read',
  '/admin/invoices/new': 'invoices:write',
  '/admin/quotes': 'quotes:read',
  '/admin/quotes/new': 'quotes:write',
  '/admin/services': 'services:read',
  '/admin/services/new': 'services:write',

  // Website Configuration
  '/admin/pricing': 'pricing:read',
  '/admin/pricing/new': 'pricing:write',
  '/admin/database': 'database:read',
  '/admin/database/backup': 'database:write',
  '/admin/categories': 'categories:read',
  '/admin/categories/new': 'categories:write',
  '/admin/tags': 'tags:read',
  '/admin/tags/new': 'tags:write',
  '/admin/authors': 'authors:read',
  '/admin/authors/new': 'authors:write',

  // Settings
  '/admin/settings': 'settings:read',
  '/admin/settings/storage': 'settings:read',
  '/admin/settings/scripts': 'settings:read',

  // System Tests
  '/admin/tests': 'settings:read',

  // SEO Management
  '/admin/seo': 'seo:read',
  '/admin/seo/pages': 'seo:read',
  '/admin/seo/keywords': 'seo:read',
  '/admin/seo/meta-tags': 'seo:read',
  '/admin/seo/broken-links': 'seo:read',

  // User Management (new section)
  '/admin/users': 'users:read',
  '/admin/users/new': 'users:write',
  '/admin/users/edit': 'users:write',
  '/admin/roles': 'roles:read',
  '/admin/roles/new': 'roles:write',
  '/admin/roles/edit': 'roles:write',
  '/admin/activity-logs': 'logs:read',
};

/**
 * Check if a user has permission to access a route
 * @param token The user's JWT token
 * @param path The path being accessed
 * @returns True if the user has permission, false otherwise
 */
export function hasRoutePermission(token: any, path: string): boolean {
  // Admin role has access to everything
  if (token?.role?.name === 'admin' || token?.role?.permissions?.includes('*')) {
    return true;
  }

  // Find the most specific matching route
  const routeKeys = Object.keys(routePermissions)
    .filter(route => {
      // Exact match
      if (route === path) return true;

      // Path starts with route and next char is / or end of string
      if (path.startsWith(route) && (path.length === route.length || path[route.length] === '/')) {
        return true;
      }

      return false;
    })
    .sort((a, b) => b.length - a.length); // Sort by length descending to get most specific match

  if (routeKeys.length === 0) {
    // No specific permission required for this route
    return true;
  }

  const requiredPermission = routePermissions[routeKeys[0]];
  return token?.role?.permissions?.includes(requiredPermission) || false;
}

/**
 * Middleware to check if a user has permission to access an admin route
 * @param request The incoming request
 * @returns NextResponse
 */
export async function adminAuthMiddleware(request: NextRequest) {
  const path = request.nextUrl.pathname;

  // Skip middleware for login page and API routes
  if (path === '/admin/login' || path.startsWith('/api/')) {
    return NextResponse.next();
  }

  // Just redirect /admin to dashboard
  if (path === '/admin' || path === '/admin/') {
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    // If not authenticated, redirect to login
    if (!token) {
      // Use absolute URL to avoid redirect loops
      const loginUrl = new URL('/admin/login', request.url);
      console.log('Redirecting to login:', loginUrl.toString());
      return NextResponse.redirect(loginUrl);
    }

    // If authenticated, redirect to dashboard
    const dashboardUrl = new URL('/admin/dashboard', request.url);
    console.log('Redirecting to dashboard:', dashboardUrl.toString());
    return NextResponse.redirect(dashboardUrl);
  }

  // For all other admin routes, check authentication
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET,
  });

  // If not authenticated, redirect to login
  if (!token) {
    // Use absolute URL to avoid redirect loops
    const loginUrl = new URL('/admin/login', request.url);
    console.log('Not authenticated, redirecting to login:', loginUrl.toString());
    return NextResponse.redirect(loginUrl);
  }

  // Check token expiration
  const tokenExp = token.exp as number;
  if (tokenExp && tokenExp < Math.floor(Date.now() / 1000)) {
    // Use absolute URL to avoid redirect loops
    const loginUrl = new URL('/admin/login', request.url);
    console.log('Token expired, redirecting to login:', loginUrl.toString());
    return NextResponse.redirect(loginUrl);
  }

  // Check permissions for the route
  if (!hasRoutePermission(token, path)) {
    // Redirect to dashboard with access denied message
    const dashboardUrl = new URL(`/admin/dashboard?error=access_denied&path=${encodeURIComponent(path)}`, request.url);
    console.log('Access denied, redirecting to dashboard:', dashboardUrl.toString());
    return NextResponse.redirect(dashboardUrl);
  }

  // If authenticated and authorized, allow access
  console.log('Access granted for path:', path);
  return NextResponse.next();
}
