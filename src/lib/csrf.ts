import { randomBytes } from 'crypto';

/**
 * Generate a CSRF token
 * @returns A random string to be used as a CSRF token
 */
export function generateCsrfToken(): string {
  return randomBytes(32).toString('hex');
}

/**
 * Validate a CSRF token against the expected token
 * @param token The token to validate
 * @param expectedToken The expected token value
 * @returns Boolean indicating if the token is valid
 */
export function validateCsrfToken(token: string, expectedToken: string): boolean {
  if (!token || !expectedToken) {
    return false;
  }
  
  // Use timing-safe comparison to prevent timing attacks
  return timingSafeEqual(token, expectedToken);
}

/**
 * Timing-safe comparison of two strings
 * This prevents timing attacks by taking the same amount of time
 * regardless of how many characters match
 */
function timingSafeEqual(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false;
  }
  
  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }
  
  return result === 0;
}

/**
 * Set a CSRF token in a cookie
 * @param res The response object
 * @param token The CSRF token
 */
export function setCsrfCookie(res: Response, token: string): Response {
  const cookieOptions = [
    `csrf=${token}`,
    'Path=/',
    'HttpOnly',
    'SameSite=Strict',
    process.env.NODE_ENV === 'production' ? 'Secure' : '',
  ].filter(Boolean).join('; ');
  
  res.headers.set('Set-Cookie', cookieOptions);
  return res;
}

/**
 * Create a CSRF protected API route handler
 * @param handler The original API route handler
 * @returns A new handler with CSRF protection
 */
export function withCsrfProtection(handler: Function) {
  return async (req: Request, ...args: any[]) => {
    // Skip CSRF check for GET requests
    if (req.method === 'GET') {
      return handler(req, ...args);
    }
    
    // For non-GET requests, validate CSRF token
    const csrfTokenFromHeader = req.headers.get('X-CSRF-Token');
    const cookies = req.headers.get('cookie');
    
    if (!cookies) {
      return new Response(JSON.stringify({ error: 'CSRF token missing' }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' },
      });
    }
    
    // Extract CSRF token from cookies
    const csrfCookie = cookies
      .split(';')
      .map(cookie => cookie.trim())
      .find(cookie => cookie.startsWith('csrf='));
      
    const csrfTokenFromCookie = csrfCookie ? csrfCookie.split('=')[1] : null;
    
    if (!csrfTokenFromHeader || !csrfTokenFromCookie) {
      return new Response(JSON.stringify({ error: 'CSRF token missing' }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' },
      });
    }
    
    if (!validateCsrfToken(csrfTokenFromHeader, csrfTokenFromCookie)) {
      return new Response(JSON.stringify({ error: 'Invalid CSRF token' }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' },
      });
    }
    
    // CSRF validation passed, proceed with the original handler
    return handler(req, ...args);
  };
}
