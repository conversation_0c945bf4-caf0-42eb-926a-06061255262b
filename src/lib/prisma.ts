import { PrismaClient } from '@prisma/client';

// Prevent multiple instances of Prisma Client in development
declare global {
  var prisma: PrismaClient | undefined;
}

// Create a function to get a Prisma client with error handling
function getPrismaClient() {
  try {
    // Use existing client if available
    if (global.prisma) {
      return global.prisma;
    }

    // Create new client with logging in development
    const client = new PrismaClient({
      log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL,
        },
      },
    });

    // Save client in development
    if (process.env.NODE_ENV !== 'production') {
      global.prisma = client;
    }

    return client;
  } catch (error) {
    console.error('Failed to initialize Prisma client:', error);
    // Return a mock client that will throw clear errors
    return new Proxy({} as PrismaClient, {
      get: (target, prop) => {
        // Add all models that are used in the application
        if (prop === 'blogPost' || prop === 'siteSettings' || prop === 'category' ||
            prop === 'websitePortfolio' || prop === 'pricing' || prop === 'storageConfig' ||
            prop === 'transaction' || prop === 'service' || prop === 'receipt' || prop === 'receiptItem' ||
            prop === 'user' || prop === 'role' || prop === 'activityLog') {
          return new Proxy({}, {
            get: () => () => {
              console.error(`Database connection failed for ${String(prop)}. Using mock data instead.`);
              // Return null for findFirst to prevent errors
              return null;
            }
          });
        }
        return target[prop as keyof typeof target];
      }
    });
  }
}

const prisma = getPrismaClient();

export { prisma };
export default prisma;
