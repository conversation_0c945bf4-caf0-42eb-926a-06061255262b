import fs from 'fs/promises';
import path from 'path';

// Define log levels
export enum LogLevel {
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
}

// Define log entry structure
export interface AuditLogEntry {
  timestamp: string;
  level: LogLevel;
  userId: string;
  action: string;
  resource: string;
  details?: any;
  ip?: string;
  userAgent?: string;
}

/**
 * Audit Logger class for tracking admin actions
 */
export class AuditLogger {
  private logDir: string;
  private logFile: string;
  
  constructor() {
    this.logDir = path.join(process.cwd(), 'logs');
    this.logFile = path.join(this.logDir, 'audit.log');
  }
  
  /**
   * Log an admin action
   */
  async log(entry: Omit<AuditLogEntry, 'timestamp'>): Promise<void> {
    try {
      // Ensure log directory exists
      await this.ensureLogDir();
      
      // Create full log entry with timestamp
      const logEntry: AuditLogEntry = {
        timestamp: new Date().toISOString(),
        ...entry,
      };
      
      // Format log entry as JSON string with newline
      const logLine = JSON.stringify(logEntry) + '\n';
      
      // Append to log file
      await fs.appendFile(this.logFile, logLine, 'utf8');
      
      // Also log to console in development
      if (process.env.NODE_ENV !== 'production') {
        console.log(`[AUDIT] ${logEntry.level} - ${logEntry.action} - ${logEntry.resource}`);
      }
    } catch (error) {
      // Don't let logging errors affect the application
      console.error('Error writing to audit log:', error);
    }
  }
  
  /**
   * Ensure the log directory exists
   */
  private async ensureLogDir(): Promise<void> {
    try {
      await fs.access(this.logDir);
    } catch (error) {
      // Directory doesn't exist, create it
      await fs.mkdir(this.logDir, { recursive: true });
    }
  }
  
  /**
   * Log an informational event
   */
  async info(userId: string, action: string, resource: string, details?: any, request?: Request): Promise<void> {
    await this.log({
      level: LogLevel.INFO,
      userId,
      action,
      resource,
      details,
      ...(request && this.getRequestMetadata(request)),
    });
  }
  
  /**
   * Log a warning event
   */
  async warning(userId: string, action: string, resource: string, details?: any, request?: Request): Promise<void> {
    await this.log({
      level: LogLevel.WARNING,
      userId,
      action,
      resource,
      details,
      ...(request && this.getRequestMetadata(request)),
    });
  }
  
  /**
   * Log an error event
   */
  async error(userId: string, action: string, resource: string, details?: any, request?: Request): Promise<void> {
    await this.log({
      level: LogLevel.ERROR,
      userId,
      action,
      resource,
      details,
      ...(request && this.getRequestMetadata(request)),
    });
  }
  
  /**
   * Extract metadata from a request object
   */
  private getRequestMetadata(request: Request): { ip?: string; userAgent?: string } {
    return {
      ip: request.headers.get('x-forwarded-for') || 
          request.headers.get('x-real-ip') || 
          'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    };
  }
}

// Export a singleton instance
export const auditLogger = new AuditLogger();
