import { Pool } from 'pg';

// Create a PostgreSQL connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://don:password@localhost:5432/mocky',
});

// Helper function to execute queries
export async function query(text: string, params?: any[]) {
  try {
    const start = Date.now();
    const res = await pool.query(text, params);
    const duration = Date.now() - start;
    console.log('Executed query', { text, duration, rows: res.rowCount });
    return res;
  } catch (error) {
    console.error('Error executing query', { text, error });
    throw error;
  }
}

// Initialize the database with required tables
export async function initializeDatabase() {
  try {
    // Create blog_posts table if it doesn't exist
    await query(`
      CREATE TABLE IF NOT EXISTS blog_posts (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) NOT NULL UNIQUE,
        content TEXT NOT NULL,
        excerpt TEXT,
        featured_image VARCHAR(255),
        author VARCHA<PERSON>(100),
        category VARCHAR(100),
        tags TEXT[],
        status VARCHAR(20) DEFAULT 'draft',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        published_at TIMESTAMP WITH TIME ZONE
      )
    `);

    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Error initializing database', error);
    throw error;
  }
}

// Function to create a sample blog post
export async function createSampleBlogPost() {
  try {
    // Check if we already have blog posts
    const existingPosts = await query('SELECT COUNT(*) FROM blog_posts');

    if (parseInt(existingPosts.rows[0].count) > 0) {
      console.log('Sample blog posts already exist');
      return;
    }

    // Create sample blog posts
    const samplePosts = [
      {
        title: 'The Importance of Responsive Design in 2023',
        slug: 'importance-of-responsive-design-2023',
        content: `<h2>Why Responsive Design Matters</h2><p>In today's digital landscape, having a responsive website is no longer optional—it's essential. With the increasing variety of devices used to access the internet, from smartphones and tablets to desktops and even smart TVs, your website needs to provide an optimal viewing experience across all platforms.</p><p>Responsive design ensures that your website adapts seamlessly to different screen sizes and resolutions, providing users with a consistent and user-friendly experience regardless of the device they're using.</p><h2>Key Benefits of Responsive Design</h2><ul><li><strong>Improved User Experience:</strong> A responsive website provides a seamless experience across all devices, reducing bounce rates and increasing engagement.</li><li><strong>Better SEO Performance:</strong> Google prioritizes mobile-friendly websites in its search rankings, making responsive design crucial for SEO success.</li><li><strong>Cost-Effective:</strong> Maintaining a single responsive website is more cost-effective than managing separate mobile and desktop versions.</li><li><strong>Faster Loading Times:</strong> Responsive websites typically load faster, which is critical for user retention and conversion rates.</li><li><strong>Future-Proofing:</strong> As new devices with different screen sizes enter the market, a responsive design will automatically adapt without requiring additional updates.</li></ul><h2>Implementing Responsive Design</h2><p>At Mocky Digital, we prioritize responsive design in all our web development projects. Our approach includes:</p><ol><li>Using flexible grid layouts that adapt to screen size</li><li>Implementing responsive images that scale appropriately</li><li>Utilizing CSS media queries to apply different styles based on device characteristics</li><li>Testing thoroughly across multiple devices and browsers</li></ol><p>By investing in responsive design, you're not just improving your website's current performance—you're also preparing for the future of web browsing, whatever devices it may bring.</p>`,
        excerpt: 'Discover why responsive design is crucial for your website\'s success in 2023 and beyond, and how it impacts user experience and SEO performance.',
        featured_image: '/images/blog/responsive-design.jpg',
        author: 'Jane Smith',
        category: 'web-design',
        tags: ['responsive design', 'web development', 'user experience', 'mobile-friendly'],
        status: 'published',
        published_at: new Date().toISOString()
      },
      {
        title: '5 Logo Design Trends to Watch in 2023',
        slug: 'logo-design-trends-2023',
        content: `<h2>Logo Design Trends for 2023</h2><p>As we move further into 2023, logo design continues to evolve with new trends emerging and others fading away. Staying current with these trends can help your brand remain relevant and appealing to your target audience.</p><p>Here are five logo design trends that are making waves this year:</p><h3>1. Minimalism 2.0</h3><p>Minimalism has been popular for years, but it's evolving. The new wave of minimalist logos incorporates subtle textures and gradients while maintaining simplicity. This approach creates logos that are both clean and visually interesting.</p><h3>2. Dynamic Logos</h3><p>With digital platforms dominating brand experiences, static logos are giving way to dynamic, adaptable designs that can change based on context or user interaction. These responsive logos maintain brand recognition while offering flexibility across different media.</p><h3>3. Nostalgic Typography</h3><p>Retro-inspired typography is making a comeback, with brands leveraging nostalgia to create emotional connections with their audience. Vintage letterforms with modern twists are particularly popular this year.</p><h3>4. Abstract Geometric Shapes</h3><p>Abstract geometric logos continue to trend, offering a timeless quality while allowing for creative expression. These designs often use simple shapes in unexpected ways to create distinctive brand identifiers.</p><h3>5. Sustainable and Eco-Friendly Aesthetics</h3><p>As environmental consciousness grows, logos that communicate sustainability through natural colors, organic shapes, and eco-friendly imagery are resonating with consumers who prioritize brands with strong environmental values.</p><h2>Implementing These Trends</h2><p>While it's important to be aware of trends, remember that your logo should primarily reflect your brand's unique identity and values. At Mocky Digital, we help businesses incorporate current design elements while ensuring their logos remain authentic and timeless.</p><p>The most successful logos strike a balance between trendy and timeless, ensuring they won't feel dated after just a few years.</p>`,
        excerpt: 'Explore the top five logo design trends of 2023, from minimalism 2.0 to sustainable aesthetics, and learn how to keep your brand identity fresh and relevant.',
        featured_image: '/images/blog/logo-design-trends.jpg',
        author: 'Michael Johnson',
        category: 'graphic-design',
        tags: ['logo design', 'branding', 'design trends', 'visual identity'],
        status: 'published',
        published_at: new Date().toISOString()
      }
    ];

    for (const post of samplePosts) {
      await query(`
        INSERT INTO blog_posts
        (title, slug, content, excerpt, featured_image, author, category, tags, status, published_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      `, [
        post.title,
        post.slug,
        post.content,
        post.excerpt,
        post.featured_image,
        post.author,
        post.category,
        post.tags,
        post.status,
        post.published_at
      ]);
    }

    console.log('Sample blog posts created successfully');
  } catch (error) {
    console.error('Error creating sample blog posts', error);
    throw error;
  }
}

// Export the pool for direct use if needed
export default pool;
