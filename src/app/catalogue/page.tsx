'use client';

import { useState, useEffect } from 'react';
import SimplePricingCard from '@/components/SimplePricingCard';
import Link from 'next/link';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';

interface CatalogueItem {
  id: string;
  service: string;
  price: number;
  description?: string;
  features?: string[];
  icon?: string;
  popular?: boolean;
  imageUrl?: string;
  imageUrl2?: string;
  imageUrl3?: string;
  category?: string;
  createdAt: string;
  updatedAt: string;
}

export default function CataloguePage() {
  const [loading, setLoading] = useState(false);
  const [catalogueItems, setCatalogueItems] = useState<CatalogueItem[]>([]);
  const [filteredItems, setCatalogueItemsFiltered] = useState<CatalogueItem[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [categories, setCategories] = useState<string[]>([]);

  // Fetch catalogue data when component mounts
  useEffect(() => {
    setLoading(true);
    fetchCatalogue();
  }, []);

  // Extract categories from catalogue items
  useEffect(() => {
    if (!catalogueItems.length) return;

    // Extract unique categories
    const uniqueCategories = Array.from(
      new Set(catalogueItems.map(item => item.category || 'Other'))
    ).sort();

    // Add 'All' category at the beginning
    setCategories(['All', ...uniqueCategories]);
  }, [catalogueItems]);

  // Filter items when search term or selected category changes
  useEffect(() => {
    if (!catalogueItems.length) return;

    let filtered = [...catalogueItems];

    // Filter by category first (if not 'All')
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(item =>
        (item.category || 'Other') === selectedCategory
      );
    }

    // Then filter by search term
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(item =>
        item.service.toLowerCase().includes(term) ||
        (item.description && item.description.toLowerCase().includes(term)) ||
        item.price.toString().includes(term)
      );
    }

    setCatalogueItemsFiltered(filtered);
  }, [searchTerm, selectedCategory, catalogueItems]);

  // Function to fetch catalogue data
  const fetchCatalogue = async () => {
    try {
      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/catalogue?t=${timestamp}`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to fetch catalogue data');
      }

      const data = await response.json();

      if (!Array.isArray(data)) {
        throw new Error('Invalid data format received from server');
      }

      setCatalogueItems(data);
      setCatalogueItemsFiltered(data);
    } catch (err: any) {
      console.error('Error fetching catalogue data:', err);
      setError(err.message || 'Failed to load catalogue information. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle WhatsApp click
  const handleWhatsAppClick = () => {
    window.open('https://wa.me/254741590670', '_blank', 'noopener,noreferrer');
  };

  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-[#1a2942] to-[#121f35] text-white py-20 page-header-padding">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Catalogue</h1>
            <p className="text-lg md:text-xl text-gray-300 mb-8">
              Browse our complete catalogue of products and services with transparent pricing
            </p>
          </div>
        </div>
      </section>

      {/* Pricing Cards Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4" suppressHydrationWarning>
          {loading && (
            <div className="flex justify-center items-center py-20">
              <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-gray-900"></div>
            </div>
          )}

          {!loading && error && (
            <div className="text-center py-20">
              <div className="text-red-500 mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-10 w-10 mx-auto"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-medium text-gray-900 mb-2">{error}</h3>
              <button
                onClick={fetchCatalogue}
                className="mt-4 inline-flex items-center px-5 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-gray-900 hover:bg-gray-800 transition-colors"
              >
                Try Again
              </button>
            </div>
          )}

          {!loading && !error && catalogueItems.length === 0 && (
            <div className="text-center py-20">
              <h3 className="text-xl font-medium text-gray-900 mb-2">No catalogue information available</h3>
              <p className="text-gray-500 mb-4">
                Our catalogue is currently being updated. Please check back later or contact us for more details.
              </p>
              <Link
                href="/contact"
                className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-[#FF5400] hover:bg-[#E64A00]"
              >
                Contact Us
              </Link>
            </div>
          )}

          {!loading && !error && catalogueItems.length > 0 && (
            <>
              {/* Title Section */}
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">Complete Product Catalogue</h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Explore our comprehensive collection of high-quality products and services
                </p>
              </div>

              {/* Search and Filter Section */}
              <div className="max-w-4xl mx-auto mb-12">
                {/* Search Bar */}
                <div className="relative max-w-md mx-auto mb-6">
                  <input
                    type="text"
                    placeholder="Search our catalogue..."
                    value={searchTerm}
                    onChange={handleSearchChange}
                    className="w-full px-4 py-3 pl-10 rounded-lg border border-gray-200 shadow-sm focus:border-[#FF5400] focus:ring-1 focus:ring-[#FF5400] focus:outline-none transition-all"
                  />
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  {searchTerm && (
                    <button
                      onClick={() => setSearchTerm('')}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </button>
                  )}
                </div>

                {/* Category Filter - Visual Cards */}
                {selectedCategory === 'All' && categories.length > 1 && (
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-10">
                    {categories.filter(cat => cat !== 'All').map((category) => (
                      <button
                        key={category}
                        onClick={() => setSelectedCategory(category)}
                        className="group relative h-40 overflow-hidden rounded-lg shadow-md transition-all duration-300 hover:shadow-lg"
                      >
                        {/* Background color based on category */}
                        <div className={`absolute inset-0 bg-gradient-to-br ${
                          category === 'Banners' ? 'from-blue-500 to-blue-700' :
                          category === 'Digital Services' ? 'from-purple-500 to-purple-700' :
                          category === 'Printing Services' ? 'from-green-500 to-green-700' :
                          category === 'Office Essentials' ? 'from-red-500 to-red-700' :
                          category === 'Diaries & Notebooks' ? 'from-yellow-500 to-yellow-700' :
                          category === 'Gift Sets' ? 'from-pink-500 to-pink-700' :
                          category === 'Drinkware' ? 'from-teal-500 to-teal-700' :
                          'from-gray-500 to-gray-700'
                        } opacity-90 group-hover:opacity-100 transition-opacity`}></div>

                        {/* Category name */}
                        <div className="absolute inset-0 flex items-center justify-center p-4">
                          <h3 className="text-xl font-bold text-white text-center drop-shadow-md">{category}</h3>
                        </div>
                      </button>
                    ))}
                  </div>
                )}

                {/* Category Pills - Only show when a category is selected or searching */}
                {(selectedCategory !== 'All' || searchTerm) && (
                  <div className="flex flex-wrap justify-center gap-2 mb-6">
                    {selectedCategory !== 'All' && (
                      <button
                        onClick={() => setSelectedCategory('All')}
                        className="px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
                      >
                        ← All Categories
                      </button>
                    )}
                    {selectedCategory !== 'All' && (
                      <span className="px-4 py-2 rounded-full text-sm font-medium bg-[#FF5400] text-white">
                        {selectedCategory}
                      </span>
                    )}
                    {selectedCategory === 'All' && categories.map((category) => (
                      <button
                        key={category}
                        onClick={() => setSelectedCategory(category)}
                        className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                          selectedCategory === category
                            ? 'bg-[#FF5400] text-white'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        {category}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* No Results Message */}
              {filteredItems.length === 0 ? (
                <div className="text-center py-10 bg-white rounded-lg shadow-sm max-w-md mx-auto">
                  <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                  <h3 className="text-lg font-medium text-gray-800 mb-2">No products found</h3>
                  <p className="text-gray-500 mb-4">We couldn't find any products matching your search criteria.</p>
                  <button
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedCategory('All');
                    }}
                    className="px-4 py-2 bg-[#FF5400] hover:bg-[#E64A00] text-white rounded-md transition-colors duration-300 text-sm font-medium"
                  >
                    Clear filters
                  </button>
                </div>
              ) : (
                /* Pricing Cards Grid */
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
                  {filteredItems.map((item) => (
                    <SimplePricingCard
                      key={item.id}
                      id={item.id}
                      service={item.service}
                      price={item.price}
                      description={item.description}
                      features={item.features}
                      icon={item.icon}
                      popular={item.popular}
                      imageUrl={item.imageUrl}
                      imageUrl2={item.imageUrl2}
                      imageUrl3={item.imageUrl3}
                      category={item.category}
                    />
                  ))}
                </div>
              )}
            </>
          )}
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Need a Custom Solution?</h2>
            <p className="text-lg text-gray-600 mb-8">
              Don't see what you're looking for? Contact us for a personalized quote tailored to your specific needs.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link
                href="/contact"
                className="px-6 py-3 bg-[#FF5400] hover:bg-[#E64A00] text-white rounded-md transition-colors duration-300"
              >
                Contact Us
              </Link>
              <button
                onClick={handleWhatsAppClick}
                className="px-6 py-3 bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 rounded-md transition-colors duration-300"
                suppressHydrationWarning
              >
                WhatsApp Us
              </button>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
