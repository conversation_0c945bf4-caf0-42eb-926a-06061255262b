'use client';

import { motion } from 'framer-motion';

export default function Terms() {
  return (
    <main className="pt-28 pb-16 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Header Section */}
        <div className="max-w-4xl mx-auto mb-12">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-4xl md:text-5xl font-bold text-black mb-6 text-center"
          >
            Terms and Conditions
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="text-lg text-black text-center font-medium"
          >
            Last Updated: {new Date().toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}
          </motion.p>
        </div>

        {/* Terms Content */}
        <div className="max-w-4xl mx-auto bg-white rounded-2xl shadow-lg p-8 md:p-12">
          <div className="space-y-16">
            {/* Introduction */}
            <section className="bg-white rounded-xl">
              <h2 className="text-2xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">1. Introduction</h2>
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-100">
                <div className="space-y-4">
                  <p className="text-black leading-relaxed">
                    Welcome to Mocky Graphics Limited ("we," "our," or "us"). By accessing or using our services, you agree to be bound by these Terms and Conditions ("Terms"). These Terms constitute a legally binding agreement between you ("Client" or "you") and Mocky Graphics Limited, registered under the Companies Act of Kenya with registration number PVL-ZXUWD7K.
                  </p>
                  <div className="bg-white p-4 rounded-lg border border-gray-100">
                    <p className="text-black font-medium">
                      If you do not agree with any part of these Terms, please do not use our services.
                    </p>
                  </div>
                </div>
              </div>
            </section>

            {/* Services */}
            <section className="bg-white rounded-xl">
              <h2 className="text-2xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">2. Services</h2>
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-100">
                <div className="space-y-4">
                  <p className="text-black leading-relaxed">
                    We provide graphic design, web development, branding, and related creative services. Our services include but are not limited to:
                  </p>
                  <ul className="space-y-2">
                    {[
                      'Logo design and brand identity development',
                      'Marketing and promotional materials design',
                      'Website design and development',
                      'E-commerce solutions',
                      'Web application development',
                      'Website maintenance and support'
                    ].map((service, index) => (
                      <li key={index} className="flex items-center gap-2 text-black">
                        <span className="text-primary">•</span>
                        <span>{service}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </section>

            {/* Web Development Terms */}
            <section className="bg-white rounded-xl">
              <h2 className="text-2xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">3. Web Development Terms</h2>
              <div className="space-y-8">
                {[
                  {
                    title: '3.1. Domain and Hosting',
                    items: [
                      'Client is responsible for providing and maintaining domain names and hosting services unless explicitly included in the project scope.',
                      'We can recommend and assist in setting up hosting and domains but are not liable for third-party services.'
                    ]
                  },
                  {
                    title: '3.2. Website Maintenance',
                    items: [
                      'Post-launch support and maintenance are not included unless specified in a separate maintenance agreement.',
                      'Emergency support services are available at additional costs.',
                      'Regular updates and security patches may require a maintenance contract.'
                    ]
                  },
                  {
                    title: '3.3. Content and SEO',
                    items: [
                      'Client is responsible for providing website content unless content creation is explicitly included in the scope.',
                      'Basic on-page SEO is included, but ongoing SEO services require a separate agreement.'
                    ]
                  }
                ].map((subsection, index) => (
                  <div key={index} className="bg-gray-50 p-6 rounded-lg border border-gray-100">
                    <h3 className="font-bold text-black mb-4 text-lg">{subsection.title}</h3>
                    <ul className="list-none pl-0 space-y-3">
                      {subsection.items.map((item, itemIndex) => (
                        <li key={itemIndex} className="flex items-start space-x-3">
                          <span className="text-primary mt-1.5">•</span>
                          <span className="text-black leading-relaxed">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </section>

            {/* Payment Terms */}
            <section className="bg-white rounded-xl">
              <h2 className="text-2xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">4. Payment Terms</h2>
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-100">
                <div className="space-y-4">
                  {[
                    {
                      title: '4.1. Pricing',
                      content: 'All prices are quoted in Kenyan Shillings (KES) unless otherwise specified.'
                    },
                    {
                      title: '4.2. Initial Deposit',
                      content: 'A non-refundable deposit of 50% of the total project cost is required before work commences.'
                    },
                    {
                      title: '4.3. Final Payment',
                      content: 'The remaining balance must be paid before the final files are delivered.'
                    },
                    {
                      title: '4.4. Payment Methods',
                      content: 'Payments can be made through M-PESA or other approved payment methods.'
                    },
                    {
                      title: '4.5. Late Payments',
                      content: 'Late payments may incur additional charges of 1.5% per month on the outstanding amount.'
                    }
                  ].map((item, index) => (
                    <div key={index} className="bg-white p-4 rounded-lg border border-gray-100">
                      <h3 className="font-semibold text-black mb-2">{item.title}</h3>
                      <p className="text-black leading-relaxed">{item.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* Intellectual Property */}
            <section className="bg-white rounded-xl">
              <h2 className="text-2xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">5. Intellectual Property Rights</h2>
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-100">
                <div className="space-y-4">
                  {[
                    {
                      title: '5.1. Ownership',
                      content: 'Until final payment is received, we retain ownership of all intellectual property rights in the work created.'
                    },
                    {
                      title: '5.2. Rights Transfer',
                      content: 'Upon receipt of full payment, you will be granted exclusive rights to use the final deliverables for their intended purpose.'
                    },
                    {
                      title: '5.3. Portfolio Rights',
                      content: 'We reserve the right to use the work for our portfolio, marketing materials, and promotional purposes unless explicitly agreed otherwise in writing.'
                    },
                    {
                      title: '5.4. Client Materials',
                      content: 'Any existing trademarks, logos, or copyrighted materials provided by you remain your property.'
                    }
                  ].map((item, index) => (
                    <div key={index} className="bg-white p-4 rounded-lg border border-gray-100">
                      <h3 className="font-semibold text-black mb-2">{item.title}</h3>
                      <p className="text-black leading-relaxed">{item.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* Revisions and Amendments */}
            <section className="bg-white rounded-xl">
              <h2 className="text-2xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">6. Revisions and Amendments</h2>
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-100">
                <div className="space-y-4">
                  {[
                    {
                      title: '6.1. Revision Rounds',
                      content: 'Each project includes a specified number of revision rounds as outlined in the project proposal.'
                    },
                    {
                      title: '6.2. Additional Revisions',
                      content: 'Additional revisions beyond the specified number will be charged at our standard hourly rate.'
                    },
                    {
                      title: '6.3. Scope Changes',
                      content: 'Major changes to the project scope may require a new quote and additional costs.'
                    }
                  ].map((item, index) => (
                    <div key={index} className="bg-white p-4 rounded-lg border border-gray-100">
                      <h3 className="font-semibold text-black mb-2">{item.title}</h3>
                      <p className="text-black leading-relaxed">{item.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* Project Timeline */}
            <section className="bg-white rounded-xl">
              <h2 className="text-2xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">7. Project Timeline</h2>
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-100">
                <div className="space-y-4">
                  {[
                    {
                      title: '7.1. Project Timelines',
                      content: 'Project timelines will be specified in the project proposal.'
                    },
                    {
                      title: '7.2. Delays',
                      content: 'Delays caused by late client feedback or content delivery will affect the final delivery date.'
                    },
                    {
                      title: '7.3. Rush Fees',
                      content: 'Rush fees may apply for expedited projects or tight deadlines.'
                    }
                  ].map((item, index) => (
                    <div key={index} className="bg-white p-4 rounded-lg border border-gray-100">
                      <h3 className="font-semibold text-black mb-2">{item.title}</h3>
                      <p className="text-black leading-relaxed">{item.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* Cancellation */}
            <section className="bg-white rounded-xl">
              <h2 className="text-2xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">8. Cancellation Policy</h2>
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-100">
                <div className="space-y-4">
                  {[
                    {
                      title: '8.1. Deposit Forfeiture',
                      content: 'Projects cancelled after work has commenced will forfeit the deposit.'
                    },
                    {
                      title: '8.2. Written Notice',
                      content: 'Cancellation must be made in writing or Email.'
                    },
                    {
                      title: '8.3. Final Billing',
                      content: 'You will be billed for any work completed up to the cancellation date.'
                    }
                  ].map((item, index) => (
                    <div key={index} className="bg-white p-4 rounded-lg border border-gray-100">
                      <h3 className="font-semibold text-black mb-2">{item.title}</h3>
                      <p className="text-black leading-relaxed">{item.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* Confidentiality */}
            <section className="bg-white rounded-xl">
              <h2 className="text-2xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">9. Confidentiality</h2>
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-100">
                <div className="space-y-4">
                  {[
                    {
                      title: '9.1. Information Protection',
                      content: 'We will maintain the confidentiality of any proprietary information shared during the project.'
                    },
                    {
                      title: '9.2. Ongoing Obligation',
                      content: 'This obligation survives the termination of our agreement.'
                    }
                  ].map((item, index) => (
                    <div key={index} className="bg-white p-4 rounded-lg border border-gray-100">
                      <h3 className="font-semibold text-black mb-2">{item.title}</h3>
                      <p className="text-black leading-relaxed">{item.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* Limitation of Liability */}
            <section className="bg-white rounded-xl">
              <h2 className="text-2xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">10. Limitation of Liability</h2>
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-100">
                <div className="space-y-4">
                  {[
                    {
                      title: '10.1. Liability Limit',
                      content: 'Our liability shall be limited to the amount paid for the services.'
                    },
                    {
                      title: '10.2. Damages Exclusion',
                      content: 'We are not liable for any consequential, incidental, indirect, or special damages.'
                    },
                    {
                      title: '10.3. Indemnification',
                      content: 'You agree to indemnify us against any claims arising from the use of our services.'
                    }
                  ].map((item, index) => (
                    <div key={index} className="bg-white p-4 rounded-lg border border-gray-100">
                      <h3 className="font-semibold text-black mb-2">{item.title}</h3>
                      <p className="text-black leading-relaxed">{item.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* Governing Law */}
            <section className="bg-white rounded-xl">
              <h2 className="text-2xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">11. Governing Law</h2>
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-100">
                <div className="space-y-4">
                  {[
                    {
                      title: '11.1. Applicable Law',
                      content: 'These Terms are governed by the laws of Kenya.'
                    },
                    {
                      title: '11.2. Jurisdiction',
                      content: 'Any disputes shall be subject to the exclusive jurisdiction of the courts in Nairobi, Kenya.'
                    }
                  ].map((item, index) => (
                    <div key={index} className="bg-white p-4 rounded-lg border border-gray-100">
                      <h3 className="font-semibold text-black mb-2">{item.title}</h3>
                      <p className="text-black leading-relaxed">{item.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* Amendments to Terms */}
            <section className="bg-white rounded-xl">
              <h2 className="text-2xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">12. Amendments to Terms</h2>
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-100">
                <div className="space-y-4">
                  {[
                    {
                      title: '12.1. Modification Rights',
                      content: 'We reserve the right to modify these Terms at any time.'
                    },
                    {
                      title: '12.2. Effective Changes',
                      content: 'Changes will be effective immediately upon posting on our website.'
                    },
                    {
                      title: '12.3. Acceptance',
                      content: 'Continued use of our services constitutes acceptance of the modified Terms.'
                    }
                  ].map((item, index) => (
                    <div key={index} className="bg-white p-4 rounded-lg border border-gray-100">
                      <h3 className="font-semibold text-black mb-2">{item.title}</h3>
                      <p className="text-black leading-relaxed">{item.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* Contact Information */}
            <section className="bg-white rounded-xl">
              <h2 className="text-2xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">13. Contact Information</h2>
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-100">
                <p className="text-black leading-relaxed mb-6">
                  For any questions regarding these Terms, please contact us at:
                </p>
                <div className="bg-white p-8 rounded-lg border border-gray-200 shadow-sm">
                  <h3 className="font-bold text-xl text-black mb-4">Mocky Graphics Limited</h3>
                  <div className="space-y-3">
                    <p className="text-black leading-relaxed flex items-center space-x-3">
                      <svg className="w-5 h-5 text-primary flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <span className="text-black">Nairobi, Kenya</span>
                    </p>
                    <p className="text-black leading-relaxed flex items-center space-x-3">
                      <svg className="w-5 h-5 text-primary flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                      <span className="text-black"><EMAIL></span>
                    </p>
                    <p className="text-black leading-relaxed flex items-center space-x-3">
                      <svg className="w-5 h-5 text-primary flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                      <span className="text-black">+254 741 590 670</span>
                    </p>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </main>
  );
} 