import { getImagesFromServer } from '@/utils/serverUtils';
import { Suspense } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import ClientGallerySection from '@/components/ClientGallerySection';

// Shuffles an array using Fisher-Yates algorithm for randomization
function shuffleArray<T>(array: T[]): T[] {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
}

// This needs to be a Server Component to use async
export default async function PortfolioPage() {
  // Get initial data for server-side rendering, with parallel requests for better performance
  const [cards, fliers, letterheads, logos, profiles, branding] = await Promise.all([
    getImagesFromServer('cards'),
    getImagesFromServer('fliers'),
    getImagesFromServer('letterheads'),
    getImagesFromServer('logos'),
    getImagesFromServer('profiles'),
    getImagesFromServer('branding')
  ]);

  // Prepare the data with category labels (categories are already set from S3)
  const cardsWithCategory = cards;
  const fliersWithCategory = fliers;
  const letterheadsWithCategory = letterheads;
  const logosWithCategory = logos;
  const profilesWithCategory = profiles;
  const brandingWithCategory = branding;

  // Shuffle each array to randomize display
  const shuffledCards = shuffleArray(cardsWithCategory);
  const shuffledFliers = shuffleArray(fliersWithCategory);
  const shuffledLetterheads = shuffleArray(letterheadsWithCategory);
  const shuffledLogos = shuffleArray(logosWithCategory);
  const shuffledProfiles = shuffleArray(profilesWithCategory);
  const shuffledBranding = shuffleArray(brandingWithCategory);

  // Select a few random items for the featured section
  const featuredItems = shuffleArray([
    ...shuffledCards.slice(0, 2),
    ...shuffledFliers.slice(0, 2),
    ...shuffledLetterheads.slice(0, 2),
    ...shuffledLogos.slice(0, 2),
    ...shuffledProfiles.slice(0, 2),
    ...shuffledBranding.slice(0, 2)
  ]).slice(0, 8);

  return (
    <main className="pt-16 md:pt-20 bg-white">
      {/* Hero Section with gradient background matching catalogue page */}
      <section className="bg-gradient-to-b from-[#1a2942] to-[#121f35] text-white py-16 md:py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-1 bg-[#FF5400]"></div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Portfolio</h1>
            <p className="text-lg md:text-xl text-gray-300 mb-8">
              Explore our creative work showcasing design excellence and innovative solutions
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link
                href="#logos"
                className="px-6 py-3 bg-white text-[#0A1929] hover:bg-blue-50 rounded-full font-medium transition-colors"
              >
                Logos
              </Link>
              <Link
                href="#branding"
                className="px-6 py-3 bg-transparent border border-white hover:bg-white/10 text-white rounded-full font-medium transition-colors"
              >
                Branding
              </Link>
              <Link
                href="#fliers"
                className="px-6 py-3 bg-transparent border border-white hover:bg-white/10 text-white rounded-full font-medium transition-colors"
              >
                Fliers
              </Link>
              <Link
                href="#cards"
                className="px-6 py-3 bg-transparent border border-white hover:bg-white/10 text-white rounded-full font-medium transition-colors"
              >
                Cards
              </Link>
              <Link
                href="#letterheads"
                className="px-6 py-3 bg-transparent border border-white hover:bg-white/10 text-white rounded-full font-medium transition-colors"
              >
                Letterheads
              </Link>
              <Link
                href="#profiles"
                className="px-6 py-3 bg-transparent border border-white hover:bg-white/10 text-white rounded-full font-medium transition-colors"
              >
                Profiles
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Portfolio Section - Visually Appealing Grid */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">Featured Projects</h2>

          <Suspense fallback={<div className="py-20 text-center"><LoadingSpinner size="lg" /></div>}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {featuredItems.map((image, index) => (
                <div
                  key={`featured-${index}`}
                  className="group relative aspect-square overflow-hidden rounded-xl bg-gray-100 shadow-sm hover:shadow-md transition-all duration-300"
                >
                  {/* Static placeholder that's immediately visible */}
                  <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl"></div>

                  {/* Image with priority for first 4 items to improve LCP */}
                  <Image
                    src={image.src}
                    alt={image.alt || 'Portfolio piece'}
                    fill
                    sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1280px) 25vw, 25vw"
                    className="object-cover transition-all group-hover:scale-105 duration-500"
                    loading={index < 4 ? "eager" : "lazy"}
                    quality={75}
                    fetchPriority={index < 2 ? "high" : "auto"}
                  />

                  {/* Overlay with info */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4">
                    <h3 className="text-white text-lg font-medium">{image.alt}</h3>
                    <p className="text-white/80 text-sm capitalize">
                      {image.category}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </Suspense>
        </div>
      </section>

      {/* Logo Design Section - Client Component for refresh functionality */}
      <section id="logos" className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center">Logo Design</h2>
          <p className="text-center text-gray-600 max-w-3xl mx-auto mb-12">
            Professional logos that establish brand identity and make lasting impressions
          </p>

          <ClientGallerySection
            items={shuffledLogos}
            gridCols="grid-cols-2 md:grid-cols-4"
            aspectRatio="aspect-square"
            objectFit="object-cover"
            category="logos"
          />
        </div>
      </section>

      {/* Branding Section - Client Component for refresh functionality */}
      <section id="branding" className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center">Branding & Identity</h2>
          <p className="text-center text-gray-600 max-w-3xl mx-auto mb-12">
            Comprehensive branding solutions that communicate your unique identity and values
          </p>

          <ClientGallerySection
            items={shuffledBranding}
            gridCols="grid-cols-2 md:grid-cols-4"
            aspectRatio="aspect-square"
            objectFit="object-cover"
            category="branding"
          />
        </div>
      </section>

      {/* Fliers Section - Client Component for refresh functionality */}
      <section id="fliers" className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center">Fliers & Marketing Materials</h2>
          <p className="text-center text-gray-600 max-w-3xl mx-auto mb-12">
            Eye-catching fliers and marketing materials designed to capture attention and drive engagement
          </p>

          <ClientGallerySection
            items={shuffledFliers}
            gridCols="grid-cols-2 md:grid-cols-4"
            aspectRatio="aspect-[3/4]"
            objectFit="object-cover"
            category="fliers"
          />
        </div>
      </section>

      {/* Cards Section - Client Component for refresh functionality */}
      <section id="cards" className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center">Business Cards</h2>
          <p className="text-center text-gray-600 max-w-3xl mx-auto mb-12">
            Professional business card designs that make a lasting first impression
          </p>

          <ClientGallerySection
            items={shuffledCards}
            gridCols="grid-cols-2 md:grid-cols-4"
            aspectRatio="aspect-[16/9]"
            objectFit="object-cover"
            category="cards"
          />
        </div>
      </section>

      {/* Letterheads Section - Client Component for refresh functionality */}
      <section id="letterheads" className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center">Letterheads</h2>
          <p className="text-center text-gray-600 max-w-3xl mx-auto mb-12">
            Professional letterhead designs for your business correspondence
          </p>

          <ClientGallerySection
            items={shuffledLetterheads}
            gridCols="grid-cols-2 md:grid-cols-4"
            aspectRatio="aspect-[3/4]"
            objectFit="object-cover"
            category="letterheads"
          />
        </div>
      </section>

      {/* Profiles Section - Client Component for refresh functionality */}
      <section id="profiles" className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center">Company Profiles</h2>
          <p className="text-center text-gray-600 max-w-3xl mx-auto mb-12">
            Professional company profile designs that showcase your business
          </p>

          <ClientGallerySection
            items={shuffledProfiles}
            gridCols="grid-cols-2 md:grid-cols-4"
            aspectRatio="aspect-[3/4]"
            objectFit="object-cover"
            category="profiles"
          />
        </div>
      </section>
    </main>
  );
}

export const metadata = {
  title: 'Portfolio | Mocky Digital Kenya',
  description: 'Browse our diverse portfolio of logos, branding, fliers, business cards, letterheads, and company profiles.',
  keywords: 'logo design, branding, identity design, fliers, business cards, letterheads, company profiles, graphic design, kenya, nairobi'
};