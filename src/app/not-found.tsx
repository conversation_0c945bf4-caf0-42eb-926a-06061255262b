'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

export default function NotFound() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white flex items-center justify-center p-4">
      <div className="max-w-2xl w-full text-center relative">
        {/* Background decoration */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[500px] h-[500px] bg-blue-100/50 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[300px] h-[300px] bg-orange-100/50 rounded-full blur-3xl"></div>
        </div>

        {/* 404 Text */}
        <div className="animate-fade-in">
          <h1 className="text-[150px] font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#FF5400] to-blue-600 leading-none">
            404
          </h1>
        </div>

        {/* Message */}
        <div className="animate-fade-in-delay-200">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mt-8 mb-4">
            Page Not Found
          </h2>
          <p className="text-gray-600 text-lg mb-8 max-w-md mx-auto">
            The page you're looking for doesn't exist or has been moved.
          </p>
        </div>

        {/* Actions */}
        <div className="animate-fade-in-delay-400 space-y-4 md:space-y-0 md:space-x-4">
          <Link
            href="/"
            className="inline-flex items-center px-6 py-3 rounded-full bg-[#FF5400] text-white font-medium hover:bg-[#FF5400]/90 transition-colors duration-200"
          >
            <svg 
              className="w-5 h-5 mr-2" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" 
              />
            </svg>
            Return Home
          </Link>
          <Link
            href="/contact"
            className="inline-flex items-center px-6 py-3 rounded-full bg-gray-100 text-gray-700 font-medium hover:bg-gray-200 transition-colors duration-200"
          >
            <svg 
              className="w-5 h-5 mr-2" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" 
              />
            </svg>
            Contact Support
          </Link>
        </div>

        {/* Helpful Links */}
        <div className="animate-fade-in-delay-600 mt-12 pt-8 border-t border-gray-200">
          <p className="text-sm text-gray-500 mb-4">You might want to check these pages:</p>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <Link href="/services" className="text-blue-600 hover:text-blue-700">Services</Link>
            <Link href="/portfolio" className="text-blue-600 hover:text-blue-700">Portfolio</Link>
            <Link href="/about" className="text-blue-600 hover:text-blue-700">About Us</Link>
            <Link href="/blog" className="text-blue-600 hover:text-blue-700">Blog</Link>
          </div>
        </div>
      </div>
    </div>
  );
} 