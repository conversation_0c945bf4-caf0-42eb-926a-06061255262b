'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

interface ProductDetailProps {
  params: {
    id: string;
  };
}

interface PricingItem {
  id: string;
  service: string;
  price: number;
  description?: string;
  features?: string[];
  icon?: string;
  popular?: boolean;
  imageUrl?: string;
  imageUrl2?: string;
  imageUrl3?: string;
  category?: string;
  createdAt: string;
  updatedAt: string;
}

export default function ProductDetailPage({ params }: ProductDetailProps) {
  const router = useRouter();
  const [product, setProduct] = useState<PricingItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [relatedProducts, setRelatedProducts] = useState<PricingItem[]>([]);

  useEffect(() => {
    const fetchProduct = async () => {
      setLoading(true);
      try {
        // Fetch the specific product
        const response = await fetch(`/api/catalogue/${params.id}`);

        if (!response.ok) {
          throw new Error('Product not found');
        }

        const data = await response.json();
        setProduct(data);

        // Fetch related products (same category)
        if (data.category) {
          const relatedResponse = await fetch(`/api/catalogue?category=${data.category}`);
          if (relatedResponse.ok) {
            const relatedData = await relatedResponse.json();
            // Filter out the current product and limit to 4 items
            setRelatedProducts(
              relatedData
                .filter((item: PricingItem) => item.id !== data.id)
                .slice(0, 4)
            );
          }
        }
      } catch (err: any) {
        console.error('Error fetching product:', err);
        setError(err.message || 'Failed to load product information');
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchProduct();
    }
  }, [params.id]);

  // Image navigation handlers
  const goToNextImage = () => {
    if (!product) return;

    // Get normalized image URLs
    const images = [product.imageUrl, product.imageUrl2, product.imageUrl3]
      .filter(Boolean)
      .map(url => {
        // If URL is missing protocol but contains linodeobjects.com, add https://
        if (url && url.includes('linodeobjects.com') && !url.startsWith('http')) {
          return `https://${url}`;
        }
        return url;
      }) as string[];

    setCurrentImageIndex((prevIndex) =>
      prevIndex === images.length - 1 ? 0 : prevIndex + 1
    );
  };

  const goToPrevImage = () => {
    if (!product) return;

    // Get normalized image URLs
    const images = [product.imageUrl, product.imageUrl2, product.imageUrl3]
      .filter(Boolean)
      .map(url => {
        // If URL is missing protocol but contains linodeobjects.com, add https://
        if (url && url.includes('linodeobjects.com') && !url.startsWith('http')) {
          return `https://${url}`;
        }
        return url;
      }) as string[];

    setCurrentImageIndex((prevIndex) =>
      prevIndex === 0 ? images.length - 1 : prevIndex - 1
    );
  };

  // Handle WhatsApp click
  const handleWhatsAppClick = () => {
    if (!product) return;
    const formattedPrice = product.price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    const productUrl = `https://mocky.co.ke/product/${product.id}`;
    const whatsappMessage = `Hello! I'm interested in the ${product.service} (KSh ${formattedPrice}). Can you provide more information?\n\nProduct Link: ${productUrl}`;
    const whatsappLink = `https://wa.me/254741590670?text=${encodeURIComponent(whatsappMessage)}`;
    window.open(whatsappLink, '_blank', 'noopener,noreferrer');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">Product Not Found</h1>
        <p className="text-gray-600 mb-6">The product you're looking for doesn't exist or has been removed.</p>
        <Link
          href="/catalogue"
          className="flex items-center text-orange-500 hover:text-orange-600 font-medium"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          Back to Products
        </Link>
      </div>
    );
  }

  // Collect all available images and ensure they have proper URLs
  const images = [product.imageUrl, product.imageUrl2, product.imageUrl3]
    .filter(Boolean)
    .map(url => {
      // If URL is missing protocol but contains linodeobjects.com, add https://
      if (url && url.includes('linodeobjects.com') && !url.startsWith('http')) {
        return `https://${url}`;
      }
      return url;
    }) as string[];

  const hasMultipleImages = images.length > 1;
  const formattedPrice = product.price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  return (
    <main className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        {/* Breadcrumb */}
        <div className="mb-6">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href="/" className="text-sm text-gray-500 hover:text-orange-500">
                  Home
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <span className="mx-2 text-gray-400">/</span>
                  <Link href="/catalogue" className="text-sm text-gray-500 hover:text-orange-500">
                    Products
                  </Link>
                </div>
              </li>
              {product.category && (
                <li>
                  <div className="flex items-center">
                    <span className="mx-2 text-gray-400">/</span>
                    <Link
                      href={`/catalogue?category=${encodeURIComponent(product.category)}`}
                      className="text-sm text-gray-500 hover:text-orange-500"
                    >
                      {product.category}
                    </Link>
                  </div>
                </li>
              )}
              <li aria-current="page">
                <div className="flex items-center">
                  <span className="mx-2 text-gray-400">/</span>
                  <span className="text-sm text-gray-700 truncate max-w-[150px] md:max-w-xs">
                    {product.service}
                  </span>
                </div>
              </li>
            </ol>
          </nav>
        </div>

        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 p-6">
            {/* Product Images */}
            <div className="relative bg-gray-50 rounded-lg overflow-hidden">
              {images.length > 0 ? (
                <div className="relative aspect-square">
                  <img
                    src={images[currentImageIndex]}
                    alt={`${product.service} - image ${currentImageIndex + 1}`}
                    className="w-full h-full object-contain p-4"
                  />

                  {/* Navigation arrows for multiple images */}
                  {hasMultipleImages && (
                    <>
                      <button
                        onClick={goToPrevImage}
                        className="absolute left-2 top-1/2 -translate-y-1/2 bg-white hover:bg-gray-100 text-gray-800 rounded-full p-2 transition-all duration-200 shadow-md opacity-80 hover:opacity-100"
                        aria-label="Previous image"
                      >
                        <ChevronLeftIcon className="h-5 w-5" />
                      </button>
                      <button
                        onClick={goToNextImage}
                        className="absolute right-2 top-1/2 -translate-y-1/2 bg-white hover:bg-gray-100 text-gray-800 rounded-full p-2 transition-all duration-200 shadow-md opacity-80 hover:opacity-100"
                        aria-label="Next image"
                      >
                        <ChevronRightIcon className="h-5 w-5" />
                      </button>
                    </>
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center aspect-square bg-gray-100">
                  {product.icon ? (
                    <i className={`fas fa-${product.icon} text-5xl text-gray-400`}></i>
                  ) : (
                    <span className="text-gray-400">No image available</span>
                  )}
                </div>
              )}

              {/* Thumbnail navigation */}
              {hasMultipleImages && (
                <div className="flex mt-4 space-x-2 overflow-x-auto pb-2">
                  {images.map((img, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`flex-shrink-0 w-16 h-16 rounded-md overflow-hidden border-2 ${
                        currentImageIndex === index ? 'border-orange-500' : 'border-gray-200'
                      }`}
                    >
                      <img
                        src={img}
                        alt={`Thumbnail ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Product Details */}
            <div className="flex flex-col">
              {product.category && (
                <div className="mb-2">
                  <span className="inline-block bg-orange-100 text-orange-800 text-xs px-2.5 py-1 rounded-sm font-medium">
                    {product.category}
                  </span>
                </div>
              )}

              <h1 className="text-2xl font-bold text-gray-900 mb-4">{product.service}</h1>

              <div className="mb-6">
                <div className="flex items-baseline">
                  <span className="text-sm font-medium text-gray-500 mr-1">KSh</span>
                  <span className="text-3xl font-bold text-orange-600">{formattedPrice}</span>
                </div>
              </div>

              {product.description && (
                <div className="mb-6">
                  <h2 className="text-sm font-semibold text-gray-700 mb-2">Description</h2>
                  <p className="text-gray-600">{product.description}</p>
                </div>
              )}

              {product.features && product.features.length > 0 && (
                <div className="mb-6">
                  <h2 className="text-sm font-semibold text-gray-700 mb-2">Features</h2>
                  <ul className="space-y-2">
                    {product.features.map((feature, index) => (
                      <li key={index} className="flex items-start text-sm text-gray-600">
                        <svg className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="mt-auto">
                <button
                  onClick={handleWhatsAppClick}
                  className="w-full py-3 px-4 bg-orange-500 hover:bg-orange-600 text-white text-center rounded-md transition-all duration-300 font-medium shadow-sm hover:shadow-md"
                >
                  Inquire via WhatsApp
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <div className="mt-12">
            <h2 className="text-xl font-bold text-gray-900 mb-6">Related Products</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
              {relatedProducts.map((item) => (
                <Link
                  key={item.id}
                  href={`/product/${item.id}`}
                  className="group flex flex-col h-full bg-white rounded-sm overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 border border-gray-200"
                >
                  {/* Product Image */}
                  {item.imageUrl && (
                    <div className="relative w-full pt-[100%] bg-gray-50 overflow-hidden">
                      <img
                        src={item.imageUrl}
                        alt={item.service}
                        className="absolute inset-0 w-full h-full object-contain p-4 transition-transform duration-300 group-hover:scale-105"
                      />
                    </div>
                  )}

                  <div className="p-4">
                    <h3 className="text-sm font-medium text-gray-800 mb-1 group-hover:text-orange-600 transition-colors duration-200 line-clamp-2">
                      {item.service}
                    </h3>
                    <div className="flex items-baseline">
                      <span className="text-xs font-medium text-gray-500 mr-1">KSh</span>
                      <span className="text-base font-bold text-orange-600">
                        {item.price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      </span>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </main>
  );
}
