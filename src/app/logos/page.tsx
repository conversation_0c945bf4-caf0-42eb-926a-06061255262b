import type { Metadata } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import type { ImageItem } from '@/utils/getImages';
import { getImagesFromServer } from '@/utils/serverUtils';
import ModernPageHero from '@/components/ModernPageHero';
import ModernLogoGallery from '@/components/ModernLogoGallery';
import LogoPackagesSection from '@/components/LogoPackagesSection';
import LogoTypesSection from '@/components/LogoTypesSection';
import ModernProcessSection from '@/components/ModernProcessSection';
import ModernFAQSection from '@/components/ModernFAQSection';

export const metadata: Metadata = {
  title: 'Logo Design Services | Mocky Digital Kenya',
  description: 'Professional logo design services in Kenya. Get a unique, memorable logo that represents your brand perfectly.',
  keywords: 'logo design kenya, professional logo design, brand identity, logo designer nairobi, corporate branding kenya',
};



// Logo types to display in the education section
const logoTypes = [
  {
    type: 'Wordmark',
    description: 'Text-based logos that focus on the company name',
    image: '/images/portfolio/logo-types/wordmark1.png',
    examples: ['Coca-Cola', 'Google', 'FedEx']
  },
  {
    type: 'Lettermark',
    description: 'Minimalist logos using initials or acronyms',
    image: '/images/portfolio/logo-types/lettermark1.png',
    examples: ['IBM', 'HBO', 'NASA']
  },
  {
    type: 'Symbol',
    description: 'Iconic, standalone graphics that represent the brand',
    image: '/images/portfolio/logo-types/symbol1.png',
    examples: ['Apple', 'Twitter', 'Nike']
  },
  {
    type: 'Combination',
    description: 'Text and symbol merged into one cohesive design',
    image: '/images/portfolio/logo-types/combination1.png',
    examples: ['Burger King', 'Amazon', 'Adidas']
  }
];

// Sample logos for the gallery - using real logo examples as fallback
const sampleLogos = [
  {
    id: 1,
    url: '/images/portfolio/logos/andytech.jpg',
    src: '/images/portfolio/logos/andytech.jpg',
    alt: 'AndyTech Logo'
  },
  {
    id: 2,
    url: '/images/portfolio/logos/marie-market.jpg',
    src: '/images/portfolio/logos/marie-market.jpg',
    alt: 'Marie Market Logo'
  },
  {
    id: 3,
    url: '/images/portfolio/logos/ascia.jpg',
    src: '/images/portfolio/logos/ascia.jpg',
    alt: 'Ascia Logo'
  },
  {
    id: 4,
    url: '/images/portfolio/logos/azhar.jpg',
    src: '/images/portfolio/logos/azhar.jpg',
    alt: 'Azhar Maize Flour Logo'
  },
  {
    id: 5,
    url: '/images/portfolio/logos/coppa.jpg',
    src: '/images/portfolio/logos/coppa.jpg',
    alt: 'Coppa Logo'
  },
  {
    id: 6,
    url: '/images/portfolio/logos/dawa.jpg',
    src: '/images/portfolio/logos/dawa.jpg',
    alt: 'Dawa Logo'
  },
  {
    id: 7,
    url: '/images/portfolio/logos/kofar.jpg',
    src: '/images/portfolio/logos/kofar.jpg',
    alt: 'Kofar Logo'
  },
  {
    id: 8,
    url: '/images/portfolio/logos/lachique.jpg',
    src: '/images/portfolio/logos/lachique.jpg',
    alt: 'LaChique Logo'
  }
];

export default async function LogosPage() {
  // Fetch logos with error handling
  let logos: ImageItem[] = [];
  let error: Error | null = null;

  try {
    // Fetch logos directly from S3 using the serverUtils function
    logos = await getImagesFromServer('logos');

    // Verify we have valid data
    if (!Array.isArray(logos) || logos.length === 0) {
      console.warn('No logo data returned or invalid format, using sample logos');
      logos = sampleLogos as ImageItem[];
    }
  } catch (e) {
    error = e instanceof Error ? e : new Error('Failed to fetch logos');
    console.error('Error fetching logos:', error);
    // Use sample logos as fallback
    logos = sampleLogos as ImageItem[];
    error = null; // Clear error since we have a fallback
  }

  // Process steps for the logo design process
  const processSteps = [
    {
      step: '01',
      title: 'Discovery',
      description: 'We begin by understanding your brand identity, target audience, and business goals. This crucial first step helps us create a logo that truly represents your brand and resonates with your customers.'
    },
    {
      step: '02',
      title: 'Concept',
      description: 'Based on your requirements, our designers create multiple unique concepts. Each concept is carefully crafted to reflect your brand personality and stand out in your industry.'
    },
    {
      step: '03',
      title: 'Revision',
      description: 'We refine the chosen concept based on your feedback, making adjustments to colors, typography, and other elements until the design perfectly matches your vision.'
    },
    {
      step: '04',
      title: 'Delivery',
      description: 'Once approved, we deliver your logo in all necessary file formats for both digital and print use, ensuring you have everything needed for consistent brand application.'
    }
  ];

  // FAQs for the logo design service
  const faqs = [
    {
      question: "How long does the logo design process take?",
      answer: "The timeline varies depending on the package you choose. Our Simple Logo takes 1-2 days, Basic Logo 3 days, Professional Logo 5 days, and Premium Logo 3 days with priority service. Each package includes different numbers of revision rounds that may extend the timeline."
    },
    {
      question: "What file formats will I receive?",
      answer: "All packages include web-ready formats (JPG, PNG). Higher-tier packages include professional vector formats (AI, EPS, SVG) that are scalable to any size without losing quality, ideal for printing and professional applications."
    },
    {
      question: "Do I own the copyright to my logo?",
      answer: "Yes, once your project is complete and final payment is made, you own all rights to your logo design. We transfer full copyright ownership to you."
    },
    {
      question: "Can I request revisions to my logo design?",
      answer: "Yes, all packages include revision rounds. The Simple and Basic packages include 2 rounds, the Professional package includes 3 rounds, and the Premium package includes unlimited revisions."
    },
    {
      question: "How do I get started with my logo design project?",
      answer: "Simply click the 'Order Now' button on your preferred package. You'll be asked to fill out a brief form with your logo requirements, including your business name, industry, logo type preferences, and any additional information. After submitting the form, you'll be connected with our design team via WhatsApp to discuss your project further."
    }
  ];

  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <ModernPageHero
        title="Professional Logo Design"
        subtitle="Create a memorable brand identity with our professional logo design services"
        bgImage="/images/logo-design-hero.jpg"
      />

      {/* Portfolio Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center">Our Logo Design Portfolio</h2>
          <p className="text-center text-gray-600 max-w-3xl mx-auto mb-12">
            Browse through our collection of professionally designed logos for various industries and businesses
          </p>

          {error ? (
            <div className="text-center py-20">
              <div className="inline-block p-4 rounded-lg bg-red-50 mb-4">
                <svg className="w-10 h-10 text-red-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <p className="text-red-600 font-medium mb-2">Failed to load logos. Please try again later.</p>
              <p className="text-gray-500">Our team has been notified and is working on a solution.</p>
            </div>
          ) : (
            <ModernLogoGallery logos={logos} />
          )}
        </div>
      </section>

      {/* Logo Types Section */}
      <LogoTypesSection logoTypes={logoTypes} />

      {/* Pricing Section */}
      <section id="pricing-section">
        <LogoPackagesSection />
      </section>

      {/* Process Section */}
      <ModernProcessSection steps={processSteps} />

      {/* FAQ Section */}
      <ModernFAQSection faqs={faqs} />
    </main>
  );
}