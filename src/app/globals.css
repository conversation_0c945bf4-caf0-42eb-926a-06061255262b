@tailwind base;
@tailwind components;
@tailwind utilities;

/* Your custom CSS below */

/* Hide scrollbar for clean UI */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  scroll-behavior: smooth; /* Smooth scrolling */
}
.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Snap scrolling */
.snap-x {
  scroll-snap-type: x mandatory;
}
.snap-start {
  scroll-snap-align: start;
}

/* Fix for navbar overlap with content */
.page-header-padding {
  padding-top: 7rem !important; /* Add extra padding to prevent content from being hidden under navbar */
}
:root {
  --foreground-rgb: 0, 0, 0;
  --background-rgb: 255, 255, 255;
  --primary: #0070f3;
  --primary-dark: #0051b3;
  --primary-color: #1a237e;
  --secondary-color: #0d47a1;
  --accent-color: #2196f3;
  --accent-hover: #1976d2;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --text-color: #f8fafc;
  --light-text: #f8fafc;
  --background: #ffffff;
  --light-background: #f8fafc;
  --card-background: #ffffff;
  --border-color: #e2e8f0;
  --border-radius: 0.5rem;
  --transition: all 0.3s ease-in-out;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes blob {
  0% {
    transform: translate(0, 0) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0, 0) scale(1);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes particle-float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
  }
}

@layer base {
  body {
    @apply bg-gray-50 min-h-screen antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold tracking-tight text-orange-500;
  }

  p {
    @apply text-gray-700;
  }

  html {
    scroll-behavior: smooth;
  }
}

/* Blog Styles */
.blog-content h1 {
  @apply text-3xl font-bold text-gray-900 mb-6 mt-8;
}

.blog-content h2 {
  @apply text-2xl font-bold text-gray-900 mb-4 mt-8;
}

.blog-content h3 {
  @apply text-xl font-bold text-gray-900 mb-3 mt-6;
}

.blog-content p {
  @apply text-gray-700 mb-5 leading-relaxed;
}

.blog-content ul, .blog-content ol {
  @apply mb-6 pl-6;
}

.blog-content ul {
  @apply list-disc;
}

.blog-content ol {
  @apply list-decimal;
}

.blog-content li {
  @apply mb-2 text-gray-700;
}

.blog-content a {
  @apply text-gray-900 font-medium underline hover:text-black;
}

.blog-content blockquote {
  @apply border-l-2 border-gray-900 pl-4 italic my-6 text-gray-700 py-1;
}

.blog-content img {
  @apply rounded-lg my-6 max-w-full h-auto;
}

.blog-content pre {
  @apply bg-gray-100 text-gray-800 p-4 rounded-sm overflow-x-auto my-6;
}

.blog-content code {
  @apply bg-gray-100 text-gray-800 px-1 py-0.5 rounded-sm text-sm font-mono;
}

@layer components {
  .container {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Category Navigation */
  @media (max-width: 768px) {
    .category-navigation {
      @apply hidden;
    }
  }

  /* Header & Navigation */
  .header {
    @apply fixed top-0 left-0 w-full z-50 transition-all duration-300 bg-white bg-opacity-80 backdrop-blur-lg;
  }

  .header.scrolled {
    @apply shadow-lg bg-white bg-opacity-90;
  }

  .navbar {
    @apply flex items-center justify-between py-4;
  }

  .logo {
    @apply flex items-center gap-3;
  }

  .logo:hover {
    @apply opacity-90 transition-opacity;
  }

  .logo a {
    @apply flex items-center gap-2 text-xl font-bold text-primary;
  }

  .nav-menu {
    @apply flex items-center gap-8;
  }

  .nav-menu a {
    @apply text-gray-700 hover:text-blue-600 transition-colors relative;
  }

  .nav-menu a::after {
    @apply content-[''] absolute left-0 bottom-0 w-0 h-0.5 bg-blue-600 transition-all duration-300;
  }

  .nav-menu a:hover::after {
    @apply w-full;
  }

  .nav-menu a.active {
    @apply text-primary font-medium;
  }

  .nav-menu a.active::after {
    @apply w-full;
  }

  /* Mobile Navigation */
  @media (max-width: 1023px) {
    .nav-menu {
      @apply fixed top-0 -right-full w-64 h-screen bg-white shadow-lg flex flex-col items-start pt-20 px-6 transition-all duration-300 ease-in-out;
    }

    .nav-menu.active {
      @apply right-0;
    }

    .nav-menu li {
      @apply w-full border-b border-gray-100 last:border-none;
    }

    .nav-menu a {
      @apply block py-4 text-gray-800 hover:text-orange-500 transition-colors w-full;
    }

    .nav-toggle {
      @apply block fixed right-4 top-6 z-[60] w-10 h-10 bg-transparent border-0 cursor-pointer;
    }

    .hamburger {
      @apply relative w-6 h-5;
    }

    .hamburger span {
      @apply absolute left-0 w-full h-0.5 bg-gray-700 transition-all duration-300 rounded-full;
    }

    .hamburger span:first-child {
      @apply top-0;
    }

    .hamburger span:nth-child(2) {
      @apply top-1/2 -translate-y-1/2;
    }

    .hamburger span:last-child {
      @apply bottom-0;
    }

    .hamburger.active span:first-child {
      @apply top-1/2 -translate-y-1/2 rotate-45;
    }

    .hamburger.active span:nth-child(2) {
      @apply opacity-0;
    }

    .hamburger.active span:last-child {
      @apply bottom-1/2 translate-y-1/2 -rotate-45;
    }
  }

  @media (min-width: 1024px) {
    .nav-toggle {
      @apply hidden;
    }

    .nav-menu {
      @apply flex items-center gap-8;
    }

    .nav-menu a {
      @apply text-gray-700 hover:text-blue-600 transition-colors relative;
    }

    .nav-menu a::after {
      @apply content-[''] absolute left-0 bottom-0 w-0 h-0.5 bg-blue-600 transition-all duration-300;
    }

    .nav-menu a:hover::after {
      @apply w-full;
    }

    .nav-menu > li:hover > ul {
      @apply opacity-100 visible translate-y-0;
    }

    .nav-menu ul {
      @apply absolute top-full left-0 min-w-[200px] bg-white shadow-lg rounded-lg
      opacity-0 invisible translate-y-2 transition-all duration-300 border border-gray-100
      p-1;
    }

    .nav-menu li > a,
    .nav-menu li > button {
      @apply block px-4 py-2 rounded-lg text-gray-700 hover:text-primary hover:bg-gray-50
      transition-all whitespace-nowrap;
    }
  }

  /* Hero Section */
  .hero {
    @apply bg-primary relative min-h-screen;
  }

  .hero::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 5rem;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23f8fafc' fill-opacity='1' d='M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,112C672,96,768,96,864,112C960,128,1056,160,1152,160C1248,160,1344,128,1392,112L1440,96L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E") bottom center/cover no-repeat;
  }

  .hero-wrapper {
    @apply container grid lg:grid-cols-2 gap-12 items-center relative z-10;
  }

  .floating-shapes {
    @apply absolute inset-0 overflow-hidden pointer-events-none;
  }

  .shape {
    @apply absolute rounded-full mix-blend-multiply filter blur-xl opacity-70;
  }

  .shape-1 {
    @apply w-72 h-72 -top-10 -right-10;
    background-color: rgba(33, 150, 243, 0.6);
    animation: float 8s infinite;
  }

  .shape-2 {
    @apply w-96 h-96 top-1/2 right-1/4;
    background-color: rgba(13, 71, 161, 0.6);
    animation: float 12s infinite;
  }

  .shape-3 {
    @apply w-80 h-80 bottom-1/4 right-1/3;
    background-color: rgba(26, 35, 126, 0.6);
    animation: float 10s infinite;
  }

  .hero-content {
    @apply relative z-10;
  }

  .hero-tag {
    @apply animate-fadeIn;
  }

  .hero-title {
    @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6;
  }

  .hero-description {
    @apply text-base sm:text-lg md:text-xl text-gray-200 mb-6 sm:mb-8 max-w-xl mx-auto lg:mx-0;
  }

  .hero-buttons {
    @apply flex flex-col sm:flex-row gap-4 justify-center lg:justify-start;
  }

  /* Buttons */
  .cta-button, .secondary-button {
    @apply relative overflow-hidden;
  }

  .cta-button::before, .secondary-button::before {
    content: '';
    @apply absolute inset-0 bg-white/20 transform scale-x-0 origin-left transition-transform duration-300;
  }

  .cta-button:hover::before, .secondary-button:hover::after {
    @apply scale-x-100;
  }

  .cta-button {
    @apply bg-accent hover:bg-accent-hover text-white font-semibold px-8 py-4 rounded-full transition-colors;
    box-shadow: 0 4px 14px rgba(33, 150, 243, 0.4);
  }

  .cta-button:hover {
    @apply transform -translate-y-0.5;
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.5);
  }

  .cta-button:active {
    @apply transform translate-y-0;
    box-shadow: 0 2px 10px rgba(33, 150, 243, 0.4);
  }

  .secondary-button {
    @apply bg-white/10 hover:bg-white/20 text-white font-semibold px-8 py-4 rounded-full transition-colors;
  }

  .secondary-button:hover {
    @apply transform -translate-y-0.5;
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
  }

  /* Service Cards */
  .service-card {
    @apply relative bg-white rounded-2xl p-8 shadow-lg transition-all duration-500 hover:-translate-y-2 hover:shadow-xl overflow-hidden flex flex-col h-full;
  }

  .service-card::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-br opacity-0 transition-all duration-500;
    background: linear-gradient(to bottom right, rgba(26, 35, 126, 0.1), rgba(33, 150, 243, 0.1));
  }

  .service-card:hover::before {
    @apply opacity-100;
  }

  .service-card .icon-wrapper {
    @apply relative w-16 h-16 rounded-2xl flex items-center justify-center mb-6 transition-all duration-500 z-10;
    background-color: rgba(26, 35, 126, 0.1);
  }

  .service-card:hover .icon-wrapper {
    @apply transform scale-110 rotate-3;
  }

  .service-card .icon {
    @apply text-2xl text-primary transition-all duration-500;
  }

  .service-card:hover .icon {
    @apply text-accent;
  }

  .service-card h3 {
    @apply text-xl font-semibold mb-4 transition-all duration-500;
  }

  .service-card:hover h3 {
    @apply text-accent;
  }

  .service-card p {
    @apply relative text-gray-600 mb-6 z-10;
  }

  .service-content {
    @apply flex flex-col flex-grow;
  }

  .service-features {
    @apply mt-6 space-y-3 flex-grow;
  }

  .feature-item {
    @apply flex items-center gap-2 text-gray-600 transition-all duration-300;
  }

  .service-card:hover .feature-item {
    @apply text-gray-700;
  }

  .service-link {
    @apply inline-flex items-center gap-2 text-primary font-semibold mt-6 transition-all duration-300 w-full justify-center bg-gray-50 py-3 rounded-xl hover:bg-gray-100;
  }

  .service-link:hover {
    @apply text-accent;
  }

  .service-link i {
    @apply transition-transform duration-300;
  }

  .service-link:hover i {
    @apply transform translate-x-1;
  }

  /* Portfolio Cards */
  .portfolio-card {
    @apply bg-white rounded-xl shadow-lg transition-all duration-500 hover:-translate-y-2 hover:shadow-xl overflow-hidden;
  }

  .portfolio-card h4 {
    @apply text-gray-900 transition-colors duration-300;
  }

  .portfolio-card:hover h4 {
    @apply text-primary;
  }

  .portfolio-card p {
    @apply text-gray-600;
  }

  .portfolio-card a {
    @apply font-semibold;
  }

  .portfolio-card a i {
    @apply transition-transform duration-300;
  }

  .portfolio-card a:hover i {
    @apply transform translate-x-1;
  }

  /* Price Tag */
  .price-tag {
    @apply mt-6 text-center;
  }

  .price-tag .amount {
    @apply text-2xl sm:text-3xl;
  }

  .price-tag .currency {
    @apply text-lg text-gray-600 font-normal;
  }

  .price-tag .period {
    @apply text-sm text-gray-500;
  }

  /* Section Headers */
  .section-header {
    @apply text-center mb-8 sm:mb-12 lg:mb-16;
  }

  .section-header h2 {
    @apply text-2xl sm:text-3xl lg:text-4xl font-bold mb-3 sm:mb-4;
  }

  .section-header p {
    @apply text-base sm:text-lg lg:text-xl text-gray-600;
  }

  /* Grid Layout */
  .services-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8;
  }

  /* Responsive Images */
  .responsive-image {
    @apply w-full h-48 sm:h-56 lg:h-64;
  }

  /* Custom Shadows */
  .hover-shadow {
    @apply transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/10;
  }

  /* Gradient Backgrounds */
  .gradient-bg {
    background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
  }

  /* Custom Animations */
  @keyframes float {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
    100% {
      transform: translateY(0px);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  /* Glassmorphism */
  .glass {
    @apply bg-white bg-opacity-80 backdrop-blur-lg border border-white border-opacity-20 shadow-lg;
  }

  /* Custom Scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-blue-500 bg-opacity-50 rounded-full hover:bg-blue-600 hover:bg-opacity-50;
  }

  /* Navigation */
  .nav-link {
    @apply relative px-4 py-2 text-gray-700 hover:text-blue-600 transition-colors duration-300 after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-blue-600 after:transition-all after:duration-300 hover:after:w-full;
  }

  .nav-link.active {
    @apply text-blue-600 after:w-full;
  }

  /* Pricing Cards */
  .pricing-card {
    @apply relative bg-white rounded-2xl p-6 sm:p-8 shadow-lg transition-all duration-500 hover:-translate-y-2 hover:shadow-xl border border-gray-100;
  }

  .pricing-card.popular {
    @apply scale-100 lg:scale-105;
  }

  .pricing-card .popular-tag {
    @apply absolute -top-4 left-1/2 transform -translate-x-1/2 bg-blue-500 text-white px-6 py-1 rounded-full text-sm font-medium;
  }

  /* Footer */
  .footer {
    @apply bg-gray-900 text-white py-16;
  }

  .footer-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 sm:gap-12;
  }

  .footer-link {
    @apply text-gray-400 hover:text-white transition-colors duration-300;
  }

  /* Custom Utilities */
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent;
  }

  .backdrop-blur {
    @apply backdrop-blur-lg bg-white/80;
  }

  /* Move all media queries to the end of the components layer */
  @media (max-width: 1023px) {
    .nav-toggle {
      @apply block;
    }

    .nav-menu {
      @apply fixed top-0 right-0 w-64 h-screen bg-white shadow-lg transform translate-x-full transition-transform duration-300 ease-in-out pt-20 px-6;
    }

    .nav-menu.active {
      @apply translate-x-0;
    }

    .nav-menu li {
      @apply block mb-4;
    }

    .nav-menu a {
      @apply block py-2 text-gray-800 hover:text-orange-500;
    }

    .hero-wrapper {
      @apply pt-20 pb-12;
    }

    .hero-content {
      @apply text-center;
    }

    .hero-buttons {
      @apply flex-col items-stretch;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeIn {
    animation: fadeIn 1s ease-out;
  }

  .animate-slideUp {
    animation: slideUp 1s ease-out 0.5s both;
  }

  /* Responsive shapes */
  @media (max-width: 768px) {
    .shape-1 {
      @apply w-48 h-48 -top-6 -right-6;
    }

    .shape-2 {
      @apply w-64 h-64;
    }

    .shape-3 {
      @apply w-56 h-56;
    }
  }

  /* Responsive pricing cards */
  .pricing-card {
    @apply p-6 sm:p-8;
  }

  .pricing-card.popular {
    @apply scale-100 lg:scale-105;
  }

  /* Responsive images */
  .responsive-image {
    @apply w-full h-48 sm:h-56 lg:h-64;
  }

  /* Responsive hero section */
  .hero {
    @apply min-h-[calc(100vh-72px)] py-12 sm:py-16 lg:py-0;
  }

  /* Responsive features list */
  .feature-item {
    @apply text-sm sm:text-base;
  }

  /* Responsive price tag */
  .price-tag .amount {
    @apply text-2xl sm:text-3xl;
  }

  /* Responsive service features */
  .service-features {
    @apply grid grid-cols-1 gap-2 sm:gap-3;
  }

  /* Responsive animations */
  @media (prefers-reduced-motion: reduce) {
    .animate-float,
    .animate-fadeIn,
    .animate-slideUp {
      animation: none;
    }
  }

  /* Improved mobile menu animation */
  @keyframes menuSlideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .nav-menu.active {
    animation: menuSlideDown 0.3s ease-out forwards;
  }

  /* Responsive contact form */
  .contact-form {
    @apply w-full max-w-xl mx-auto;
  }

  .contact-form input,
  .contact-form textarea,
  .contact-form select {
    @apply w-full px-4 py-3 text-base sm:text-lg;
  }

  /* Responsive CTA sections */
  .cta {
    @apply py-12 sm:py-16 lg:py-20;
  }

  .cta h2 {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }

  .cta p {
    @apply text-base sm:text-lg lg:text-xl;
  }

  /* Add these to your existing styles */

  .nav-menu li {
    position: relative;
  }

  .nav-menu li > a,
  .nav-menu li > button {
    display: block;
    padding: 0.5rem 1rem;
    color: inherit;
    text-decoration: none;
    transition: color 0.2s;
  }

  .nav-menu li > button {
    background: none;
    border: none;
    cursor: pointer;
    font: inherit;
    width: 100%;
    text-align: left;
  }

  /* Desktop styles */
  @media (min-width: 1024px) {
    .nav-menu > li:hover > ul {
      display: block;
    }

    .nav-menu ul {
      position: absolute;
      top: 100%;
      left: 0;
      min-width: 200px;
      background: white;
      box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1);
      border-radius: 0.5rem;
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      transition: all 0.3s;
    }

    .nav-menu li:hover > ul {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }
  }

  /* Mobile styles */
  @media (max-width: 1023px) {
    .nav-menu ul {
      background: rgba(0,0,0,0.05);
      margin: 0.5rem 0;
      border-radius: 0.5rem;
    }
  }

  /* Add to your existing animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  @keyframes blob {
    0% {
      transform: translate(0, 0) scale(1);
    }
    33% {
      transform: translate(30px, -50px) scale(1.1);
    }
    66% {
      transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
      transform: translate(0, 0) scale(1);
    }
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes particle-float {
    0% {
      transform: translateY(0) rotate(0deg);
    }
    100% {
      transform: translateY(-100vh) rotate(360deg);
    }
  }

  /* Add these utility classes */
  .animate-float {
    animation: float 8s ease-in-out infinite;
  }

  .animate-blob {
    animation: blob 7s infinite;
  }

  .animate-gradient {
    background: linear-gradient(to right, #ff7e33, #ff4b1f);
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  /* Particle styles */
  .particle {
    opacity: 0.6;
    animation: particle-float linear infinite;
  }

  /* Add to your existing animations */
  @keyframes fadeInOut {
    0% {
      opacity: 0;
      transform: scale(0.9) rotate(6deg) translateY(20px);
    }
    100% {
      opacity: 1;
      transform: scale(1) rotate(0) translateY(0);
    }
  }

  .fade-in-out {
    animation: fadeInOut 0.5s ease-in-out forwards;
  }

  /* Update/Add these animations */
  @keyframes float {
    0% {
      transform: translateY(100vh) scale(0.8) rotate(0deg);
    }
    20% {
      transform: translateY(80vh) scale(1) rotate(45deg);
    }
    100% {
      transform: translateY(-20vh) scale(0.8) rotate(90deg);
    }
  }

  @keyframes glow {
    0% {
      box-shadow: 0 0 5px rgba(255, 255, 255, 0.1),
                  0 0 10px rgba(255, 255, 255, 0.1);
    }
    100% {
      box-shadow: 0 0 10px rgba(255, 255, 255, 0.3),
                  0 0 20px rgba(255, 255, 255, 0.2);
    }
  }

  /* Update particle styles */
  .particle {
    will-change: transform;
    transform: translateZ(0);
    pointer-events: none;
    transition: opacity 0.3s ease-in-out;
  }

  /* Add these new utility classes */
  .animate-float-slow {
    animation: float 35s linear infinite;
  }

  .animate-float-medium {
    animation: float 30s linear infinite;
  }

  .animate-float-fast {
    animation: float 25s linear infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  /* Add to your existing animations */
  @keyframes ripple {
    0% {
      transform: translate(-50%, -50%) scale(0);
      opacity: 0.5;
    }
    100% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0;
    }
  }

  .ripple {
    animation: ripple 1s ease-out forwards;
    will-change: transform, opacity;
  }

  /* Optional: Add a subtle gradient to the ripples */
  .ripple-gradient {
    background: radial-gradient(
      circle,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 255, 255, 0.05) 70%,
      rgba(255, 255, 255, 0) 100%
    );
  }

  /* Add these new animations and styles */
  .bg-gradient-mesh {
    background-color: #0c1d48;
    background-image:
      radial-gradient(at 40% 20%, rgba(28,99,242,0.15) 0px, transparent 50%),
      radial-gradient(at 80% 0%, rgba(33,150,243,0.15) 0px, transparent 50%),
      radial-gradient(at 0% 50%, rgba(50,108,255,0.15) 0px, transparent 50%),
      radial-gradient(at 80% 50%, rgba(76,81,191,0.15) 0px, transparent 50%),
      radial-gradient(at 0% 100%, rgba(28,99,242,0.15) 0px, transparent 50%),
      radial-gradient(at 80% 100%, rgba(33,150,243,0.15) 0px, transparent 50%),
      radial-gradient(at 0% 0%, rgba(50,108,255,0.15) 0px, transparent 50%);
    animation: gradientShift 15s ease infinite;
  }

  @keyframes gradientShift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Update Animated Lines styles */
  .lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    margin: auto;
    width: 100vw;
    z-index: 1;
  }

  .line {
    position: absolute;
    width: 1px;
    height: 100%;
    top: 0;
    left: 50%;
    background: rgba(255, 255, 255, 0.05);
    overflow: hidden;
    opacity: 0.3;
  }

  .line::after {
    content: '';
    display: block;
    position: absolute;
    height: 15vh;
    width: 100%;
    top: -50%;
    left: 0;
    background: linear-gradient(to bottom,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.5) 75%,
      rgba(255, 255, 255, 0.7) 100%
    );
    animation: drop 7s 0s infinite;
    animation-fill-mode: forwards;
    animation-timing-function: cubic-bezier(0.4, 0.26, 0, 0.97);
  }

  .line:nth-child(1) { margin-left: -20%; }
  .line:nth-child(2) { margin-left: 0%; }
  .line:nth-child(3) { margin-left: 20%; }
  .line:nth-child(4) { margin-left: -30%; }
  .line:nth-child(5) { margin-left: 30%; }
  .line:nth-child(6) { margin-left: -40%; }

  .line:nth-child(1)::after { animation-delay: 2s; }
  .line:nth-child(2)::after { animation-delay: 1s; }
  .line:nth-child(3)::after { animation-delay: 3s; }
  .line:nth-child(4)::after { animation-delay: 2.5s; }
  .line:nth-child(5)::after { animation-delay: 1.5s; }
  .line:nth-child(6)::after { animation-delay: 3.5s; }

  @keyframes drop {
    0% {
      top: -50%;
    }
    100% {
      top: 110%;
    }
  }

  .line::after {
    animation-duration: 10s;
  }

  /* Add these new animations */
  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Add these utility classes */
  .animate-slideUp {
    animation: slideUp 0.6s ease-out forwards;
  }

  .animate-fadeIn {
    animation: fadeIn 0.5s ease-out forwards;
  }

  .animate-fadeInUp {
    animation: fadeInUp 0.5s ease-out forwards;
  }

  /* Optional: Add hover effects for inputs */
  input:hover, select:hover, textarea:hover {
    border-color: theme('colors.gray.300');
  }

  /* Add focus transition for all form elements */
  input, select, textarea, button {
    transition: all 0.2s ease-in-out;
  }

  /* Add these new animations */
  @keyframes bounce-slow {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  .animate-bounce-slow {
    animation: bounce-slow 3s ease-in-out infinite;
  }

  /* Update existing animations for smoother transitions */
  .animate-fadeInUp {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animate-fadeIn {
    opacity: 0;
    animation: fadeIn 0.6s ease-out forwards;
  }

  .bg-primary {
    background-color: var(--primary);
  }

  .bg-primary-dark {
    background-color: var(--primary-dark);
  }

  .hover\:bg-primary-dark:hover {
    background-color: var(--primary-dark);
  }
}

/* 404 Page Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-fade-in-delay-200 {
  opacity: 0;
  animation: fadeIn 0.5s ease-out 0.2s forwards;
}

.animate-fade-in-delay-400 {
  opacity: 0;
  animation: fadeIn 0.5s ease-out 0.4s forwards;
}

.animate-fade-in-delay-600 {
  opacity: 0;
  animation: fadeIn 0.5s ease-out 0.6s forwards;
}
