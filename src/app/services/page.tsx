import PageHero from '@/components/PageHero';
import Image from 'next/image';
import Link from 'next/link';

const services = [
  {
    title: "Professional Web Design",
    description: "Custom website development solutions for businesses in Kenya. We create responsive, SEO-friendly websites that drive results.",
    features: [
      "Responsive Web Design",
      "E-commerce Development",
      "WordPress Solutions",
      "SEO Optimization",
      "Website Maintenance",
      "SSL Security"
    ],
    icon: "fa-code",
    image: "/images/services/web-design.jpeg",
    link: "/web-development"
  },
  {
    title: "Graphic Design Services",
    description: "Expert graphic design services in Nairobi. From logos to marketing materials, we bring your brand to life.",
    features: [
      "Logo Design",
      "Brand Identity",
      "Marketing Materials",
      "Social Media Graphics",
      "Print Design",
      "Packaging Design"
    ],
    icon: "fa-pen-nib",
    image: "/images/services/graphic-design.jpeg",
    link: "/graphics"
  },
  {
    title: "Tech Solutions",
    description: "Comprehensive IT solutions and technical support for businesses in Kenya. We help you leverage technology for growth.",
    features: [
      "VPS Hosting Solutions",
      "Domain Registration",
      "Email Hosting",
      "Cloud Services",
      "Technical Support",
      "Server Management"
    ],
    icon: "fa-server",
    image: "/images/services/tech-solutions.jpg",
    link: "/vps-solutions"
  },
  {
    title: "Digital Marketing",
    description: "Comprehensive digital marketing strategies to grow your business in Kenya's competitive market.",
    features: [
      "SEO Services",
      "Social Media Marketing",
      "Content Marketing",
      "Email Marketing",
      "PPC Advertising",
      "Analytics & Reporting"
    ],
    icon: "fa-chart-line",
    image: "/images/services/digital-marketing.jpg",
    link: "/social-media"
  }
];

export const metadata = {
  title: 'Professional Digital Services | Web Design & Branding in Kenya - Mocky Digital',
  description: 'Comprehensive digital services including website development, graphic design, SEO optimization, and social media marketing for businesses in Kenya.',
  keywords: 'web design kenya, graphic design services, digital marketing nairobi, seo services kenya, social media marketing, business branding, e-commerce development'
};

export default function ServicesPage() {
  // Client testimonials data
  const testimonials = [
    {
      name: "Sarah Kimani",
      company: "Savanna Creations",
      testimonial: "Excellent service and professional team. They delivered our project on time and exceeded our expectations."
    },
    {
      name: "James Ochieng",
      company: "TechHub Kenya",
      testimonial: "Working with Mocky Digital transformed our online presence. Their attention to detail and creative solutions set them apart."
    },
    {
      name: "Amina Hassan",
      company: "Coastal Adventures",
      testimonial: "From concept to completion, their team was responsive and delivered a website that perfectly represents our brand."
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <PageHero
        title="Digital Services in Kenya"
        subtitle="Professional Web Design, Graphic Design & Digital Marketing Solutions"
      />

      {/* Enhanced Services Section */}
      <section className="py-12 md:py-20 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-4">
            Our Professional Digital Services
          </h2>
          <p className="text-gray-600 text-center mb-10 md:mb-16 max-w-3xl mx-auto text-sm md:text-base">
            Comprehensive digital solutions tailored for businesses in Kenya. From web design to digital marketing, we help you establish a strong online presence.
          </p>

          {services.map((service, index) => (
            <div key={index} className="mb-12 md:mb-20 last:mb-0">
              <div className="grid md:grid-cols-2 gap-6 md:gap-12 items-center">
                {/* Text Content */}
                <div className={`${index % 2 === 0 ? 'order-2 md:order-2' : 'order-2 md:order-1'} mt-6 md:mt-0`}>
                  <h3 className="text-2xl md:text-3xl font-bold text-gray-900">{service.title}</h3>
                  <p className="text-gray-600 text-base md:text-lg mt-3 md:mt-4">{service.description}</p>
                  <ul className="space-y-2 md:space-y-4 mt-4 md:mt-6">
                    {service.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center gap-3 text-gray-700 text-sm md:text-base">
                        <i className="fas fa-check-circle text-primary"></i>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Link
                    href={service.link}
                    className="inline-flex items-center px-5 py-2.5 md:px-6 md:py-3 bg-[#0A2647] text-white rounded-lg hover:bg-[#0A2647]/90 transition-colors mt-6 md:mt-8 text-sm md:text-base"
                  >
                    Learn More
                    <i className="fas fa-arrow-right ml-2"></i>
                  </Link>
                </div>

                {/* Image */}
                <div className={index % 2 === 0 ? 'order-1 md:order-1' : 'order-1 md:order-2'}>
                  <div className="relative h-[250px] md:h-[400px] rounded-xl md:rounded-2xl overflow-hidden">
                    <Image
                      src={service.image}
                      alt={service.title}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, 50vw"
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Process Section */}
      <section className="py-12 md:py-20 bg-white">
        <div className="container mx-auto px-4 md:px-6">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-4">Our Process</h2>
          <p className="text-gray-600 text-center mb-10 md:mb-16 max-w-3xl mx-auto text-sm md:text-base">
            We follow a proven methodology to deliver exceptional results for our clients
          </p>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8">
            {[
              {
                step: "01",
                title: "Discovery",
                description: "We learn about your business goals and requirements"
              },
              {
                step: "02",
                title: "Strategy",
                description: "Develop a tailored plan to achieve your objectives"
              },
              {
                step: "03",
                title: "Execute",
                description: "Implement solutions with precision and care"
              },
              {
                step: "04",
                title: "Optimize",
                description: "Continuously improve based on data and feedback"
              }
            ].map((item, index) => (
              <div key={index} className="text-center p-4">
                <div className="inline-flex items-center justify-center w-12 h-12 md:w-16 md:h-16 rounded-full bg-primary/10 text-primary font-bold text-lg md:text-xl mb-4 md:mb-6">
                  {item.step}
                </div>
                <h3 className="text-lg md:text-xl font-bold mb-2 md:mb-3">{item.title}</h3>
                <p className="text-gray-600 text-sm md:text-base">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-12 md:py-20">
        <div className="container mx-auto px-4 md:px-6">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-10 md:mb-16">
            Why Choose Our Digital Services?
          </h2>

          <div className="grid md:grid-cols-2 gap-8 md:gap-12 items-center">
            <div className="space-y-4 md:space-y-6">
              <div className="bg-white p-4 md:p-6 rounded-lg shadow-md">
                <h3 className="text-lg md:text-xl font-semibold mb-2 md:mb-3">Expert Team in Kenya</h3>
                <p className="text-gray-600 text-sm md:text-base">Professional designers and developers with years of experience in the Kenyan market.</p>
              </div>

              <div className="bg-white p-4 md:p-6 rounded-lg shadow-md">
                <h3 className="text-lg md:text-xl font-semibold mb-2 md:mb-3">Affordable Solutions</h3>
                <p className="text-gray-600 text-sm md:text-base">Competitive pricing for web design and digital marketing services in Nairobi.</p>
              </div>

              <div className="bg-white p-4 md:p-6 rounded-lg shadow-md">
                <h3 className="text-lg md:text-xl font-semibold mb-2 md:mb-3">Results-Driven Approach</h3>
                <p className="text-gray-600 text-sm md:text-base">Focus on delivering measurable results and ROI for your business.</p>
              </div>
            </div>

            <div className="relative h-[250px] md:h-[400px] rounded-xl md:rounded-2xl overflow-hidden">
              <Image
                src="/images/services/web-design.jpeg"
                alt="Digital Services in Kenya"
                fill
                className="object-cover"
                priority
              />
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-12 md:py-20 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-10 md:mb-16">What Our Clients Say</h2>
          <div className="grid md:grid-cols-3 gap-6 md:gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white p-6 md:p-8 rounded-xl md:rounded-2xl shadow-md md:shadow-lg">
                <div className="mb-3 border-b border-gray-100 pb-3">
                  <h3 className="font-bold text-base md:text-lg text-[#FF5400]">{testimonial.name}</h3>
                  <p className="text-gray-600 text-sm md:text-base">{testimonial.company}</p>
                </div>
                <p className="text-gray-700 text-sm md:text-base">
                  "{testimonial.testimonial}"
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}