'use client';

import PageHero from '@/components/PageHero';
import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import WebDevelopmentPortfolio from '@/components/WebDevelopmentPortfolio';

// Types for our technologies section
type TechStack = {
  id: string;
  category: string;
  icon: string;
  description: string;
  items: {
    name: string;
    icon: string;
    level: 'Basic' | 'Advanced' | 'Expert';
    experience: string;
  }[];
  bgColor: string;
  textColor: string;
};

// Technologies data
const technologies: TechStack[] = [
  {
    id: 'frontend',
    category: 'Frontend',
    icon: 'fas fa-laptop-code',
    description: 'Modern and responsive user interfaces',
    bgColor: 'bg-gradient-to-br from-blue-50 to-indigo-50',
    textColor: 'text-blue-600',
    items: [
      {
        name: 'React/Next.js',
        icon: 'fab fa-react',
        level: 'Expert',
        experience: '5+ years'
      },
      {
        name: 'TypeScript',
        icon: 'fas fa-code',
        level: 'Expert',
        experience: '4+ years'
      },
      {
        name: 'Tailwind CSS',
        icon: 'fas fa-paint-brush',
        level: 'Expert',
        experience: '3+ years'
      },
      {
        name: 'Vue.js',
        icon: 'fab fa-vuejs',
        level: 'Advanced',
        experience: '3+ years'
      }
    ]
  },
  {
    id: 'backend',
    category: 'Backend',
    icon: 'fas fa-server',
    description: 'Scalable and secure server solutions',
    bgColor: 'bg-gradient-to-br from-green-50 to-emerald-50',
    textColor: 'text-green-600',
    items: [
      {
        name: 'Node.js',
        icon: 'fab fa-node-js',
        level: 'Expert',
        experience: '5+ years'
      },
      {
        name: 'Python',
        icon: 'fab fa-python',
        level: 'Advanced',
        experience: '4+ years'
      },
      {
        name: 'PHP/Laravel',
        icon: 'fab fa-php',
        level: 'Expert',
        experience: '5+ years'
      },
      {
        name: 'Express',
        icon: 'fas fa-server',
        level: 'Expert',
        experience: '4+ years'
      }
    ]
  },
  {
    id: 'database',
    category: 'Database',
    icon: 'fas fa-database',
    description: 'Reliable data storage solutions',
    bgColor: 'bg-gradient-to-br from-purple-50 to-fuchsia-50',
    textColor: 'text-purple-600',
    items: [
      {
        name: 'MySQL',
        icon: 'fas fa-database',
        level: 'Expert',
        experience: '6+ years'
      },
      {
        name: 'PostgreSQL',
        icon: 'fas fa-database',
        level: 'Advanced',
        experience: '4+ years'
      },
      {
        name: 'MongoDB',
        icon: 'fas fa-leaf',
        level: 'Expert',
        experience: '4+ years'
      },
      {
        name: 'Redis',
        icon: 'fas fa-bolt',
        level: 'Advanced',
        experience: '3+ years'
      }
    ]
  },
  {
    id: 'cms',
    category: 'CMS',
    icon: 'fas fa-file-code',
    description: 'Content management solutions',
    bgColor: 'bg-gradient-to-br from-orange-50 to-amber-50',
    textColor: 'text-orange-600',
    items: [
      {
        name: 'WordPress',
        icon: 'fab fa-wordpress',
        level: 'Expert',
        experience: '7+ years'
      },
      {
        name: 'Strapi',
        icon: 'fas fa-box',
        level: 'Advanced',
        experience: '3+ years'
      },
      {
        name: 'Sanity',
        icon: 'fas fa-cube',
        level: 'Advanced',
        experience: '2+ years'
      },
      {
        name: 'Contentful',
        icon: 'fas fa-feather',
        level: 'Advanced',
        experience: '3+ years'
      }
    ]
  }
];

export default function WebDevelopment() {
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const [showRequestForm, setShowRequestForm] = useState<boolean>(false);
  const [activeFaq, setActiveFaq] = useState<number | null>(null);
  const [mounted, setMounted] = useState<boolean>(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleFaq = (index: number) => {
    setActiveFaq(activeFaq === index ? null : index);
  };

  if (!mounted) {
    return null; // Return null on server-side to prevent hydration mismatch
  }

  return (
    <div className="min-h-screen bg-gray-50">
        <PageHero
          title="Web Development Services"
          subtitle="Modern, responsive websites that elevate your brand and drive results"
          bgImage="/images/web-dev-hero.jpg"
        />

      {/* Service Overview Section */}
      <section className="py-24 relative overflow-hidden">
        {/* Subtle background elements */}
        <div className="absolute inset-0 overflow-hidden opacity-[0.02] pointer-events-none">
          <div className="absolute -left-1/4 -top-1/4 w-1/2 h-1/2 bg-primary rounded-full blur-3xl" />
          <div className="absolute -right-1/4 -bottom-1/4 w-1/2 h-1/2 bg-primary rounded-full blur-3xl" />
        </div>

        <div className="container mx-auto px-4 relative">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Professional Web Development</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">Creating digital experiences that drive business growth</p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
            {[
              {
                title: "Custom Web Development",
                description: "Tailored websites and web applications designed specifically for your business needs.",
                icon: "fas fa-laptop-code",
                color: "text-blue-500",
                delay: 0.1
              },
              {
                title: "Responsive Design",
                description: "Websites that look and function perfectly across all devices and screen sizes.",
                icon: "fas fa-mobile-alt",
                color: "text-purple-500",
                delay: 0.2
              },
              {
                title: "SEO Optimization",
                description: "Built-in search engine optimization to help your website rank higher.",
                icon: "fas fa-search",
                color: "text-green-500",
                delay: 0.3
              },
              {
                title: "Security",
                description: "Robust security measures to protect your website and user data from threats.",
                icon: "fas fa-shield-alt",
                color: "text-red-500",
                delay: 0.4
              }
            ].map((service, index) => (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: service.delay }}
                viewport={{ once: true }}
                className="group relative bg-white rounded-xl p-8 border border-gray-100 hover:border-gray-200 shadow-sm hover:shadow-md transition-all duration-300"
              >
                {/* Icon */}
                <div className="mb-6">
                  <span className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-50 ${service.color} group-hover:scale-110 transition-transform duration-300`}>
                    <i className={`${service.icon} text-xl`}></i>
                  </span>
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary transition-colors">
                  {service.title}
                </h3>

                <p className="text-gray-600 leading-relaxed text-sm">
                  {service.description}
                </p>
              </motion.div>
            ))}
          </div>

          {/* Additional Features */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            viewport={{ once: true }}
            className="mt-20 max-w-4xl mx-auto"
          >
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
              {[
                { text: "Modern Technologies", icon: "fas fa-code" },
                { text: "Fast Loading Speed", icon: "fas fa-bolt" },
                { text: "24/7 Support", icon: "fas fa-headset" },
                { text: "Regular Updates", icon: "fas fa-sync" },
                { text: "Cloud Hosting", icon: "fas fa-cloud" }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex flex-col items-center gap-3 p-4"
                >
                  <div className="w-12 h-12 rounded-full bg-gray-50 flex items-center justify-center text-primary">
                    <i className={`${feature.icon} text-xl`}></i>
                  </div>
                  <span className="text-gray-700 text-sm font-medium">{feature.text}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Website Development Pricing</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Transparent pricing packages tailored to your business needs
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                title: "Basic Website",
                price: "20,000",
                priceRange: "35,000",
                features: [
                  "Essential Pages (Home, About, Services, Contact)",
                  "Domain Registration (.com/.co.ke)",
                  "1 Year Web Hosting",
                  "SSL Certificate",
                  "Mobile Responsive Design",
                  "Basic SEO Setup"
                ]
              },
              {
                title: "Business Website",
                price: "35,000",
                priceRange: "75,000",
                features: [
                  "Up to 8 Custom Pages",
                  "Premium Domain & Hosting (1 Year)",
                  "Advanced Functionalities",
                  "Blog Integration",
                  "Advanced SEO Optimization",
                  "Social Media Integration"
                ],
                popular: true
              },
              {
                title: "E-commerce",
                price: "75,000",
                priceRange: "150,000",
                features: [
                  "Full E-commerce Website",
                  "Premium Domain & Hosting (1 Year)",
                  "Product Catalog (Up to 100 Products)",
                  "Payment Gateway Integration",
                  "Order Management System",
                  "Inventory Management"
                ]
              },
              {
                title: "Premium Custom",
                price: "150,000",
                priceRange: "+",
                features: [
                  "Custom Design & Features",
                  "Premium Domain & Hosting (1 Year)",
                  "Advanced Integrations",
                  "Custom Plugins Development",
                  "Unlimited Pages & Products",
                  "Priority Support"
                ]
              }
            ].map((plan, index) => {
              // Create WhatsApp message for each plan
              const whatsappMessage = `Hello! I'm interested in the ${plan.title} package (KSH ${plan.price}${plan.priceRange ? ` - ${plan.priceRange}` : ''}).

Features included:
${plan.features.map(feature => `✓ ${feature}`).join('\n')}

Please provide more information about this package.`;

              const whatsappUrl = `https://wa.me/254741590670?text=${encodeURIComponent(whatsappMessage)}`;

              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className={`bg-white rounded-xl border ${
                    plan.popular ? 'border-primary shadow-md' : 'border-gray-100 shadow-sm'
                  } hover:shadow-lg transition-all duration-300 overflow-hidden relative`}
                >
                  {plan.popular && (
                    <div className="absolute top-0 right-0">
                      <div className="bg-primary text-white text-xs font-medium px-3 py-1 rounded-bl-lg">
                        Popular Choice
                      </div>
                    </div>
                  )}
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-3">{plan.title}</h3>
                    <div className="mb-5">
                      <div className="flex items-baseline gap-1">
                        <span className="text-xs font-medium text-gray-500">KSH</span>
                        <span className="text-2xl font-bold text-primary">{plan.price}</span>
                        {plan.priceRange && (
                          <>
                            <span className="text-sm font-medium text-gray-500 mx-1">-</span>
                            <span className="text-lg font-bold text-primary">{plan.priceRange}</span>
                          </>
                        )}
                      </div>
                    </div>
                    <ul className="space-y-2 mb-6 min-h-[220px]">
                      {plan.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <i className="fas fa-check text-green-500 text-xs mt-1"></i>
                          <span className="text-gray-600 text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                    <a
                      href={whatsappUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`w-full py-2.5 px-4 rounded-lg text-sm font-medium transition-all duration-300 inline-flex items-center justify-center ${
                        plan.popular
                          ? 'bg-primary text-white hover:bg-primary-dark'
                          : 'border border-gray-200 text-gray-800 hover:bg-gray-50'
                      }`}
                    >
                      Get Started
                    </a>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Technologies We Use Section */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Technologies We Use</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">Modern tools for exceptional digital experiences</p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
              {technologies.map((tech, index) => (
                <motion.div
                  key={tech.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden"
                >
                  <div className="p-6 border-b border-gray-100">
                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 rounded-full bg-gray-50 flex items-center justify-center text-gray-700">
                        <i className={`${tech.icon} text-lg`}></i>
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-gray-900">{tech.category}</h3>
                        <p className="text-xs text-gray-500">{tech.description}</p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4">
                    <div className="space-y-2">
                      {tech.items.map((item) => (
                        <div
                          key={item.name}
                          className="flex items-center justify-between py-2 px-2 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <div className="flex items-center gap-2">
                            <i className={`${item.icon} text-gray-500 text-sm`}></i>
                            <span className="text-sm font-medium text-gray-700">{item.name}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className={`text-xs px-2 py-0.5 rounded-full ${
                              item.level === 'Expert' ? 'bg-green-50 text-green-600' :
                              item.level === 'Advanced' ? 'bg-blue-50 text-blue-600' :
                              'bg-gray-50 text-gray-600'
                            }`}>
                              {item.level}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

      {/* Website Types Section */}
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Types of Websites We Create</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Tailored solutions for every business need
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
            {[
              {
                title: "Corporate Websites",
                icon: "fas fa-building",
                description: "Professional websites that establish credibility and showcase your brand's unique value proposition.",
                features: ["Brand Storytelling", "Team Profiles", "Service Showcases", "Contact Information"]
              },
              {
                title: "E-commerce Websites",
                icon: "fas fa-shopping-cart",
                description: "Fully-functional online stores with secure payment gateways and inventory management.",
                features: ["Product Catalog", "Secure Checkout", "Customer Accounts", "Order Tracking"]
              },
              {
                title: "Portfolio Websites",
                icon: "fas fa-images",
                description: "Visually stunning showcases for creative professionals to display their work.",
                features: ["Project Galleries", "Case Studies", "Testimonials", "Inquiry Forms"]
              },
              {
                title: "Landing Pages",
                icon: "fas fa-bullseye",
                description: "Conversion-focused pages designed to drive specific actions like sign-ups or purchases.",
                features: ["Clear CTAs", "Lead Capture", "A/B Testing", "Conversion Tracking"]
              },
              {
                title: "Blogs & News Sites",
                icon: "fas fa-newspaper",
                description: "Content-rich platforms for sharing articles and engaging with your audience.",
                features: ["Content Categories", "Search Functionality", "Social Sharing", "Comment Systems"]
              },
              {
                title: "Web Applications",
                icon: "fas fa-cogs",
                description: "Interactive, feature-rich applications that offer specific functionalities.",
                features: ["User Authentication", "Real-time Updates", "Data Visualization", "Custom Functionality"]
              }
            ].map((item, index) => (
              <motion.div
                key={item.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300 group overflow-hidden"
              >
                <div className="p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 rounded-full bg-gray-50 flex items-center justify-center text-primary">
                      <i className={`${item.icon} text-lg`}></i>
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 group-hover:text-primary transition-colors">
                      {item.title}
                    </h3>
                  </div>
                  <p className="text-gray-600 text-sm mb-5">{item.description}</p>
                  <div className="pt-3 border-t border-gray-100">
                    <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-3">Key Features</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {item.features.map((feature) => (
                        <div key={feature} className="flex items-center gap-2">
                          <i className="fas fa-check text-xs text-green-500"></i>
                          <span className="text-xs text-gray-600">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
            className="mt-16 text-center"
          >
            <p className="text-gray-600 mb-4">
              Need a custom solution that doesn't fit these categories?
            </p>
            <Link
              href="/contact"
              className="inline-flex items-center gap-2 text-primary hover:text-primary-dark font-medium transition-colors"
            >
              Contact us for a custom quote <i className="fas fa-arrow-right text-sm"></i>
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Portfolio Section */}
      <WebDevelopmentPortfolio />

      {/* FAQ Section */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Common questions about our web development services
            </p>
          </motion.div>

          {/* FAQ Cards - 2 Column Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-5xl mx-auto">
            {[
              {
                question: "How long does it take to build a website?",
                answer: "The timeline varies based on complexity. A simple website might take 2-4 weeks, while e-commerce sites or web applications can take 2-3 months. We'll provide a timeline based on your specific needs during our consultation.",
                icon: "fas fa-clock",
                color: "text-amber-500"
              },
              {
                question: "How much does a website cost?",
                answer: "Website costs depend on design complexity, functionality, and content volume. Basic websites start at KSH 20,000, while e-commerce sites and custom applications typically range from KSH 75,000 to KSH 150,000+. We provide detailed quotes after understanding your requirements.",
                icon: "fas fa-dollar-sign",
                color: "text-green-500"
              },
              {
                question: "Will my website be mobile-friendly?",
                answer: "Absolutely! All our websites are fully responsive and optimized for all devices. We thoroughly test on multiple screen sizes to ensure a consistent, user-friendly experience across smartphones, tablets, and desktops.",
                icon: "fas fa-mobile-alt",
                color: "text-blue-500"
              },
              {
                question: "Do you provide website maintenance?",
                answer: "Yes, we offer maintenance packages to keep your website secure, updated, and performing optimally. Our services include security updates, performance monitoring, content updates, technical support, and regular backups.",
                icon: "fas fa-tools",
                color: "text-purple-500"
              },
              {
                question: "Will my website be SEO-friendly?",
                answer: "Yes, all our websites are built with SEO best practices. We implement proper HTML structure, mobile optimization, schema markup, fast loading speeds, and user-friendly navigation. We also offer additional SEO services for comprehensive search engine marketing.",
                icon: "fas fa-search",
                color: "text-red-500"
              },
              {
                question: "Do you provide content for websites?",
                answer: "While we focus on design and development, we can assist with content creation through our network of professional copywriters for an additional fee. We can also provide guidance if you prefer to create the content yourself.",
                icon: "fas fa-file-alt",
                color: "text-indigo-500"
              }
            ].map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300"
              >
                <button
                  onClick={() => toggleFaq(index)}
                  className="w-full text-left p-6 focus:outline-none"
                >
                  <div className="flex items-start gap-4">
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full bg-gray-50 flex items-center justify-center ${faq.color}`}>
                      <i className={`${faq.icon} text-sm`}></i>
                    </div>
                    <div className="flex-grow">
                      <h3 className={`text-lg font-bold text-gray-900 mb-2 flex items-center justify-between`}>
                        {faq.question}
                        <span className={`transform transition-transform duration-300 ml-2 ${activeFaq === index ? 'rotate-180' : ''}`}>
                          <i className="fas fa-chevron-down text-gray-400 text-sm"></i>
                        </span>
                      </h3>
                      <div
                        className={`overflow-hidden transition-all duration-300 ${
                          activeFaq === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                        }`}
                      >
                        <p className="text-gray-600 text-sm leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </div>
                  </div>
                </button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Floating Get Started Button */}
      <div className="fixed bottom-6 right-6 z-50">
        <button
          onClick={() => setShowRequestForm(true)}
          className="bg-primary hover:bg-primary-dark text-white rounded-full shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2 group"
        >
          <span className="w-12 h-12 flex items-center justify-center">
            <i className="fas fa-comments"></i>
          </span>
          <span className="pr-5 font-medium text-sm">Get Started</span>
        </button>
      </div>

      {/* Get Started Modal */}
      {showRequestForm && (
        <div
          className="fixed inset-0 bg-black/40 z-50 flex items-center justify-center p-4 backdrop-blur-sm"
          onClick={() => setShowRequestForm(false)}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-xl p-6 max-w-3xl w-full max-h-[90vh] overflow-y-auto"
            onClick={e => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-6 pb-4 border-b border-gray-100">
              <h3 className="text-xl font-bold text-gray-900">Start Your Web Project</h3>
              <button
                onClick={() => setShowRequestForm(false)}
                className="p-1 rounded-full hover:bg-gray-100 transition-colors text-gray-400 hover:text-gray-600"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <WebDevelopmentForm />
          </motion.div>
        </div>
      )}
    </div>
  );
}

// Modernized WebDevelopmentForm component
function WebDevelopmentForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    companyName: '',
    projectType: 'corporate',
    budget: 'medium',
    timeline: 'flexible',
    requirements: '',
    hearAbout: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});

  const projectTypes = [
    { value: 'corporate', label: 'Corporate Website' },
    { value: 'ecommerce', label: 'E-commerce Store' },
    { value: 'landing', label: 'Landing Page' },
    { value: 'blog', label: 'Blog/Content Site' },
    { value: 'portfolio', label: 'Portfolio Website' },
    { value: 'web-app', label: 'Web Application' },
    { value: 'other', label: 'Other' }
  ];

  const budgetRanges = [
    { value: 'basic', label: 'KSH 20,000 - 35,000' },
    { value: 'business', label: 'KSH 35,000 - 75,000' },
    { value: 'ecommerce', label: 'KSH 75,000 - 150,000' },
    { value: 'premium', label: 'KSH 150,000 - 200,000' },
    { value: 'enterprise', label: 'Above KSH 200,000' },
    { value: 'undecided', label: 'Not sure yet' }
  ];

  const timelineOptions = [
    { value: 'urgent', label: 'Urgent (< 2 weeks)' },
    { value: 'standard', label: 'Standard (2-4 weeks)' },
    { value: 'relaxed', label: 'Relaxed (1-2 months)' },
    { value: 'flexible', label: 'Flexible / Not sure yet' }
  ];

  const validateForm = () => {
    const errors: {[key: string]: string} = {};

    if (!formData.name.trim()) errors.name = "Name is required";
    if (!formData.email.trim()) {
      errors.email = "Email is required";
    } else if (!/^\S+@\S+\.\S+$/.test(formData.email)) {
      errors.email = "Email is invalid";
    }

    if (!formData.phone.trim()) errors.phone = "Phone number is required";
    if (!formData.requirements.trim()) errors.requirements = "Project requirements are required";

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error for this field if it exists
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = {...prev};
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    // Format the message for WhatsApp
    const projectTypeLabel = projectTypes.find(type => type.value === formData.projectType)?.label || formData.projectType;
    const budgetLabel = budgetRanges.find(budget => budget.value === formData.budget)?.label || formData.budget;
    const timelineLabel = timelineOptions.find(timeline => timeline.value === formData.timeline)?.label || formData.timeline;

    const message = `
*Web Development Project Inquiry*

*Contact Information:*
Name: ${formData.name}
Email: ${formData.email}
Phone: ${formData.phone}
Company: ${formData.companyName || 'N/A'}

*Project Details:*
Type: ${projectTypeLabel}
Budget Range: ${budgetLabel}
Timeline: ${timelineLabel}

*Requirements:*
${formData.requirements}

*How they heard about us:*
${formData.hearAbout || 'Not specified'}

*Sent from the Web Development contact form*
    `.trim();

    // Phone number for WhatsApp
    const phoneNumber = '254741590670'; // Replace with your actual phone number

    // Create WhatsApp URL
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;

    // Open WhatsApp in a new tab
    window.open(whatsappUrl, '_blank');

    // Reset form state
    setIsSubmitting(false);

    // Optional: reset form after submission
    setFormData({
      name: '',
      email: '',
      phone: '',
      companyName: '',
      projectType: 'corporate',
      budget: 'medium',
      timeline: 'flexible',
      requirements: '',
      hearAbout: ''
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Contact Information Section */}
      <div>
        <h3 className="text-base font-semibold mb-4 text-gray-900">Contact Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="name" className="block text-xs font-medium text-gray-500 mb-1">Full Name <span className="text-red-500">*</span></label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className={`w-full p-2.5 text-sm border ${formErrors.name ? 'border-red-500' : 'border-gray-200'} rounded-lg focus:ring-1 focus:ring-primary focus:border-primary transition-colors`}
              placeholder="Your full name"
            />
            {formErrors.name && <p className="mt-1 text-xs text-red-500">{formErrors.name}</p>}
          </div>

          <div>
            <label htmlFor="email" className="block text-xs font-medium text-gray-500 mb-1">Email Address <span className="text-red-500">*</span></label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className={`w-full p-2.5 text-sm border ${formErrors.email ? 'border-red-500' : 'border-gray-200'} rounded-lg focus:ring-1 focus:ring-primary focus:border-primary transition-colors`}
              placeholder="<EMAIL>"
            />
            {formErrors.email && <p className="mt-1 text-xs text-red-500">{formErrors.email}</p>}
          </div>

          <div>
            <label htmlFor="phone" className="block text-xs font-medium text-gray-500 mb-1">Phone Number <span className="text-red-500">*</span></label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              className={`w-full p-2.5 text-sm border ${formErrors.phone ? 'border-red-500' : 'border-gray-200'} rounded-lg focus:ring-1 focus:ring-primary focus:border-primary transition-colors`}
              placeholder="Your contact number"
            />
            {formErrors.phone && <p className="mt-1 text-xs text-red-500">{formErrors.phone}</p>}
          </div>

          <div>
            <label htmlFor="companyName" className="block text-xs font-medium text-gray-500 mb-1">Company Name</label>
            <input
              type="text"
              id="companyName"
              name="companyName"
              value={formData.companyName}
              onChange={handleChange}
              className="w-full p-2.5 text-sm border border-gray-200 rounded-lg focus:ring-1 focus:ring-primary focus:border-primary transition-colors"
              placeholder="Your company (if applicable)"
            />
          </div>
        </div>
      </div>

      {/* Project Details Section */}
      <div className="pt-2">
        <h3 className="text-base font-semibold mb-4 text-gray-900">Project Details</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="projectType" className="block text-xs font-medium text-gray-500 mb-1">Type of Website</label>
            <select
              id="projectType"
              name="projectType"
              value={formData.projectType}
              onChange={handleChange}
              className="w-full p-2.5 text-sm border border-gray-200 rounded-lg focus:ring-1 focus:ring-primary focus:border-primary transition-colors bg-white"
            >
              {projectTypes.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="budget" className="block text-xs font-medium text-gray-500 mb-1">Budget Range</label>
            <select
              id="budget"
              name="budget"
              value={formData.budget}
              onChange={handleChange}
              className="w-full p-2.5 text-sm border border-gray-200 rounded-lg focus:ring-1 focus:ring-primary focus:border-primary transition-colors bg-white"
            >
              {budgetRanges.map(budget => (
                <option key={budget.value} value={budget.value}>{budget.label}</option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="timeline" className="block text-xs font-medium text-gray-500 mb-1">Preferred Timeline</label>
            <select
              id="timeline"
              name="timeline"
              value={formData.timeline}
              onChange={handleChange}
              className="w-full p-2.5 text-sm border border-gray-200 rounded-lg focus:ring-1 focus:ring-primary focus:border-primary transition-colors bg-white"
            >
              {timelineOptions.map(timeline => (
                <option key={timeline.value} value={timeline.value}>{timeline.label}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Requirements Section */}
      <div className="pt-2">
        <h3 className="text-base font-semibold mb-4 text-gray-900">Project Requirements</h3>
        <div className="space-y-4">
          <div>
            <label htmlFor="requirements" className="block text-xs font-medium text-gray-500 mb-1">Describe Your Project <span className="text-red-500">*</span></label>
            <textarea
              id="requirements"
              name="requirements"
              value={formData.requirements}
              onChange={handleChange}
              rows={4}
              className={`w-full p-2.5 text-sm border ${formErrors.requirements ? 'border-red-500' : 'border-gray-200'} rounded-lg focus:ring-1 focus:ring-primary focus:border-primary transition-colors`}
              placeholder="Please provide details about your project: key features, functionality requirements, design preferences, etc."
            ></textarea>
            {formErrors.requirements && <p className="mt-1 text-xs text-red-500">{formErrors.requirements}</p>}
          </div>

          <div>
            <label htmlFor="hearAbout" className="block text-xs font-medium text-gray-500 mb-1">How did you hear about us?</label>
            <input
              type="text"
              id="hearAbout"
              name="hearAbout"
              value={formData.hearAbout}
              onChange={handleChange}
              className="w-full p-2.5 text-sm border border-gray-200 rounded-lg focus:ring-1 focus:ring-primary focus:border-primary transition-colors"
              placeholder="Google, Referral, etc."
            />
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <div className="pt-4 flex justify-end">
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-6 py-2.5 bg-primary text-white rounded-lg shadow-sm hover:bg-primary-dark transition-colors duration-300 font-medium text-sm inline-flex items-center justify-center"
        >
          {isSubmitting ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </>
          ) : (
            'Submit Project Inquiry'
          )}
        </button>
      </div>
    </form>
  );
}