'use client';

import { useState, useEffect } from 'react';

export default function OpenAITestPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    prompt?: string;
    response?: string;
    error?: string;
  } | null>(null);

  const testOpenAI = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/test/openai');
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">OpenAI Integration Test</h1>
      
      <button
        onClick={testOpenAI}
        disabled={loading}
        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-blue-300"
      >
        {loading ? 'Testing...' : 'Test OpenAI Integration'}
      </button>
      
      {result && (
        <div className="mt-6 p-4 border rounded">
          <h2 className="text-xl font-semibold mb-2">
            {result.success ? 'Success!' : 'Error'}
          </h2>
          
          {result.success ? (
            <>
              <div className="mb-4">
                <p className="font-medium">Prompt:</p>
                <p className="bg-gray-100 p-2 rounded">{result.prompt}</p>
              </div>
              
              <div>
                <p className="font-medium">Response:</p>
                <p className="bg-gray-100 p-2 rounded whitespace-pre-wrap">{result.response}</p>
              </div>
            </>
          ) : (
            <div className="text-red-600">
              <p>Error: {result.error}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
