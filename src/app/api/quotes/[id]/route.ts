import { NextRequest, NextResponse } from 'next/server';
import { getQuoteById } from '@/services/quoteService';
import { promises as fs } from 'fs';
import path from 'path';

/**
 * Generate HTML for a quote
 * @param quote The quote data
 * @returns HTML string
 */
function generateQuoteHtml(quote: any): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Quote ${quote.quoteNumber}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        .header-container {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 30px;
          position: relative;
        }
        .logo-container {
          flex: 0 0 25%;
          text-align: left;
        }
        .company-info {
          flex: 0 0 25%;
          text-align: right;
        }
        .quote-center {
          flex: 0 0 50%;
          text-align: center;
          padding: 0 15px;
        }
        .quote-title {
          margin: 0 0 15px 0;
        }
        .quote-details {
          text-align: center;
          margin-bottom: 10px;
        }
        .customer-info {
          margin-bottom: 30px;
        }
        .section-title {
          font-weight: bold;
          margin-bottom: 10px;
          color: #0F2557;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
        }
        th, td {
          padding: 10px;
          text-align: left;
          border-bottom: 1px solid #ddd;
        }
        th {
          background-color: #f2f2f2;
        }
        .summary {
          margin-left: auto;
          width: 300px;
          margin-bottom: 30px;
        }
        .summary-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
        }
        .total {
          font-weight: bold;
          font-size: 18px;
          color: #0F2557;
        }
        .notes {
          margin-bottom: 30px;
        }
        .footer {
          text-align: center;
          margin-top: 50px;
          color: #666;
          font-size: 14px;
        }
        .print-button {
          display: block;
          margin: 30px auto;
          padding: 10px 20px;
          background-color: #0F2557;
          color: white;
          border: none;
          border-radius: 5px;
          cursor: pointer;
          font-weight: bold;
          font-size: 16px;
        }
        /* Responsive scaling for different screen sizes */
        @media screen and (max-width: 600px) {
          body {
            padding: 10px;
            font-size: 14px;
          }
          .header-container {
            flex-direction: column;
            align-items: center;
          }
          .logo-container, .company-info, .quote-center {
            flex: 0 0 100%;
            text-align: center;
            margin-bottom: 15px;
          }
          .summary {
            width: 100%;
          }
        }
        /* Print-specific styles with auto-scaling */
        @media print {
          @page {
            size: auto;  /* auto is the default value */
            margin: 0mm; /* this affects the margin in the printer settings */
          }
          html {
            background-color: #FFFFFF;
            margin: 0; /* this affects the margin on the html before sending to printer */
          }
          body {
            padding: 10mm 15mm; /* margin you want for the content */
            margin: 0;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
            width: 100%;
            height: 100%;
            box-sizing: border-box;
          }
          .print-button {
            display: none !important;
          }
          /* Scale content to fit the page */
          table, .summary {
            max-width: 100%;
            width: 100%;
            font-size: 10pt;
          }
          th, td {
            padding: 5px;
          }
          h1 {
            font-size: 18pt;
          }
          h2 {
            font-size: 14pt;
          }
          h3 {
            font-size: 12pt;
          }
          p {
            font-size: 10pt;
          }
        }
      </style>
    </head>
    <body>
      <div class="header-container">
        <div class="logo-container">
          <img src="/images/logo.png" alt="Mocky Digital Logo" style="max-width: 120px; height: auto;">
        </div>

        <div class="quote-center">
          <div class="quote-title">
            <h1 style="margin: 10px 0;">QUOTATION</h1>
          </div>

          <div class="quote-details">
            <p style="margin: 5px 0;">Quote #: <strong>${quote.quoteNumber}</strong></p>
            <p style="margin: 5px 0;">Date: ${new Date(quote.issuedAt).toLocaleDateString()}</p>
            <p style="margin: 5px 0;">Valid Until: ${new Date(quote.validUntil).toLocaleDateString()}</p>
            <p style="margin: 5px 0;">Status: ${quote.status.toUpperCase()}</p>
          </div>
        </div>

        <div class="company-info">
          <h2 style="margin-top: 0;">Mocky Digital</h2>
          <p style="margin: 5px 0;">Nairobi, Kenya</p>
          <p style="margin: 5px 0;">Phone: +*********** 670</p>
          <p style="margin: 5px 0;">Email: <EMAIL></p>
          <p style="margin: 5px 0;">Tax PIN: P052373324V</p>
        </div>
      </div>

      <div class="customer-info">
        <h3>Customer Information</h3>
        <p>Name: ${quote.customerName}</p>
        <p>Phone: ${quote.phoneNumber}</p>
        ${quote.email ? `<p>Email: ${quote.email}</p>` : ''}
      </div>

      <h3>Quote Items</h3>
      <table>
        <thead>
          <tr>
            <th>Description</th>
            <th>Quantity</th>
            <th>Unit Price</th>
            <th>Total</th>
          </tr>
        </thead>
        <tbody>
          ${quote.items.map(item => `
            <tr>
              <td>${item.description || item.serviceName || ''}</td>
              <td>${item.quantity}</td>
              <td>KES ${item.unitPrice.toLocaleString()}</td>
              <td>KES ${item.totalPrice.toLocaleString()}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      <table class="summary">
        <tr>
          <td><strong>Total Amount:</strong></td>
          <td><strong>KES ${quote.totalAmount.toLocaleString()}</strong></td>
        </tr>
      </table>

      ${quote.notes ? `
        <div class="notes">
          <h3>Notes</h3>
          <p>${quote.notes}</p>
        </div>
      ` : ''}

      <div class="footer">
        <p>Thank you for your interest in our services!</p>
        <p>This quote is valid until the date specified above.</p>
        <p>This is a computer-generated quote and does not require a signature.</p>
      </div>

      <button class="print-button" style="display: block; margin: 30px auto; padding: 10px 20px; background-color: #0F2557; color: white; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;">
        Print Quote
      </button>

      <script>
        // Function to handle printing with scaling
        function printQuote() {
          // Set print-specific styles
          const style = document.createElement('style');
          style.textContent =
            "@media print {" +
              "@page {" +
                "size: auto;" +
                "margin: 0;" +
              "}" +
              "body {" +
                "zoom: 1;" +
                "transform-origin: top left;" +
                "transform: scale(1);" +
              "}" +
            "}";
          document.head.appendChild(style);

          // Trigger print dialog
          window.print();

          // Remove the style after printing
          setTimeout(() => {
            document.head.removeChild(style);
          }, 1000);
        }

        // Auto-trigger print dialog when the page loads
        window.addEventListener('load', function() {
          // Add a small delay to ensure the page is fully rendered
          setTimeout(function() {
            // Check if the logo is loaded
            var logo = document.querySelector('.logo-container img');
            if (logo) {
              logo.onload = function() {
                // Logo is loaded, now we can print
                // Uncomment the line below to auto-print when page loads
                // printQuote();
              };

              // If logo is already loaded or fails to load, still allow printing
              if (logo.complete) {
                // Uncomment the line below to auto-print when page loads
                // printQuote();
              }
            } else {
              // No logo found, print anyway
              // Uncomment the line below to auto-print when page loads
              // printQuote();
            }

            // Add click event to print button
            const printButton = document.querySelector('.print-button');
            if (printButton) {
              printButton.addEventListener('click', function(e) {
                e.preventDefault();
                printQuote();
              });
            }
          }, 500);
        });
      </script>
    </body>
    </html>
  `;
}

/**
 * GET /api/quotes/[id]
 * Generate and serve a quote as PDF or HTML
 */
export async function GET(
  req: NextRequest,
  context: { params: { id: string } }
) {
  try {
    // Get the ID from params
    const id = context.params.id;
    console.log(`Quote download requested for ID: ${id}`);

    if (!id || id === 'undefined' || id === 'null') {
      console.error('Invalid quote ID provided:', id);
      return NextResponse.json({ error: 'Invalid quote ID' }, { status: 400 });
    }

    // Get the quote
    const quote = await getQuoteById(id);

    if (!quote) {
      console.error(`Quote not found: ${id}`);
      return NextResponse.json({ error: 'Quote not found' }, { status: 404 });
    }

    // Check if the quote has a PDF URL
    if (quote.pdfUrl) {
      console.log(`Quote has PDF URL: ${quote.pdfUrl}`);

      try {
        // Get the PDF file path
        const pdfPath = path.join(process.cwd(), 'public', quote.pdfUrl);

        // Check if the file exists
        try {
          await fs.access(pdfPath);
          console.log(`PDF file exists: ${pdfPath}`);
        } catch (accessError) {
          console.error(`PDF file does not exist: ${pdfPath}`);

          // Generate HTML quote
          const html = generateQuoteHtml(quote);

          // Return the HTML content
          return new NextResponse(html, {
            headers: {
              'Content-Type': 'text/html',
            },
          });
        }

        // Generate HTML quote instead of serving PDF
        console.log('Returning HTML quote instead of PDF');
        const html = generateQuoteHtml(quote);

        // Return the HTML content
        return new NextResponse(html, {
          headers: {
            'Content-Type': 'text/html',
          },
        });
      } catch (error) {
        console.error(`Error serving quote PDF: ${error}`);

        // Generate HTML quote as fallback
        const html = generateQuoteHtml(quote);

        // Return the HTML content
        return new NextResponse(html, {
          headers: {
            'Content-Type': 'text/html',
          },
        });
      }
    } else {
      console.log(`Quote does not have a PDF URL, will return HTML version`);

      // Generate HTML quote
      const html = generateQuoteHtml(quote);

      // Return the HTML content
      return new NextResponse(html, {
        headers: {
          'Content-Type': 'text/html',
        },
      });
    }
  } catch (error) {
    console.error('Error generating quote:', error);
    return NextResponse.json({ error: 'Failed to generate quote' }, { status: 500 });
  }
}
