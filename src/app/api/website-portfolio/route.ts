import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { WebsitePortfolioItem } from '@/types/portfolio';

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const featured = searchParams.get('featured');
    
    // Build the query
    const query: any = {};
    
    if (category && category !== 'all') {
      query.where = { ...query.where, category };
    }
    
    if (featured === 'true') {
      query.where = { ...query.where, featured: true };
    }
    
    // Fetch website portfolio items from database
    const websitePortfolioItems = await prisma.websitePortfolio.findMany({
      ...query,
      orderBy: { createdAt: 'desc' }
    });

    // Convert to WebsitePortfolioItem type
    const items: WebsitePortfolioItem[] = websitePortfolioItems.map(item => ({
      id: item.id,
      title: item.title,
      description: item.description || '',
      category: item.category,
      imageSrc: item.imageSrc,
      url: item.url,
      featured: item.featured,
      createdAt: item.createdAt.toISOString(),
      updatedAt: item.updatedAt.toISOString(),
    }));
    
    // Return the website portfolio items with no-cache headers
    return NextResponse.json(items, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      },
    });
  } catch (error) {
    console.error('Error in website portfolio API route:', error);
    return NextResponse.json(
      { error: 'Failed to fetch website portfolio items' },
      { status: 500 }
    );
  }
}
