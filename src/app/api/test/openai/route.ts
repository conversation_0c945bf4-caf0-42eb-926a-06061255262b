import { NextRequest, NextResponse } from 'next/server';
import { generateChatCompletion } from '@/utils/openaiClient';

export async function GET(request: NextRequest) {
  try {
    // Generate a simple haiku about AI
    const prompt = "Write a haiku about AI";
    
    const response = await generateChatCompletion([
      { role: 'user', content: prompt }
    ], 'gpt-4o-mini');
    
    return NextResponse.json({
      success: true,
      prompt,
      response
    });
  } catch (error) {
    console.error('Error testing OpenAI:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
