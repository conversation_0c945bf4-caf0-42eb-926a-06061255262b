import { NextResponse } from 'next/server';
import { getSiteSettings, initializeSiteSettings } from '@/services/siteSettingsService';

export const dynamic = 'force-dynamic';
export const revalidate = 60; // Cache for 1 minute

export async function GET() {
  try {
    // Get site settings
    let settings = await getSiteSettings();
    
    // If no settings exist, initialize with defaults
    if (!settings) {
      settings = await initializeSiteSettings();
    }
    
    // Return the settings with caching headers
    return NextResponse.json(settings, {
      headers: {
        'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=300'
      }
    });
  } catch (error) {
    console.error('Error fetching site settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch site settings' },
      { status: 500 }
    );
  }
}
