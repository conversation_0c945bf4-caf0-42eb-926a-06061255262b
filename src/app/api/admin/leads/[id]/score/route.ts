import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { calculateLeadScore } from '@/services/leadManagementService';

interface Params {
  params: {
    id: string;
  };
}

/**
 * POST handler for lead score recalculation API
 * Recalculates the score for a lead
 */
export async function POST(request: NextRequest, { params }: Params) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Calculate lead score
    const score = await calculateLeadScore(params.id);

    // Return the updated score
    return NextResponse.json({ score });
  } catch (error) {
    console.error(`Error calculating score for lead ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to calculate lead score' },
      { status: 500 }
    );
  }
}
