import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { recordInteraction } from '@/services/leadManagementService';

interface Params {
  params: {
    id: string;
  };
}

/**
 * POST handler for lead interactions API
 * Creates a new interaction for a lead
 */
export async function POST(request: NextRequest, { params }: Params) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await request.json();

    // Validate required fields
    if (!body.type || !body.details) {
      return NextResponse.json(
        { error: 'Type and details are required' },
        { status: 400 }
      );
    }

    // Record interaction
    const interaction = await recordInteraction({
      leadId: params.id,
      type: body.type,
      details: body.details,
      createdBy: session.user?.id,
    });

    // Return the created interaction
    return NextResponse.json(interaction);
  } catch (error) {
    console.error(`Error creating interaction for lead ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to create interaction' },
      { status: 500 }
    );
  }
}
