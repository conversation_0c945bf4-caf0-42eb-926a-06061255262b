import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getLeadById, updateLead } from '@/services/leadManagementService';

interface Params {
  params: {
    id: string;
  };
}

/**
 * GET handler for individual lead API
 * Returns a specific lead by ID
 */
export async function GET(request: NextRequest, { params }: Params) {
  // Extract and store the ID to avoid the Next.js warning about using params.id directly
  const leadId = params.id;

  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Validate the ID parameter
    if (!leadId || typeof leadId !== 'string' || leadId.trim() === '') {
      console.error('Invalid lead ID provided:', leadId);
      return NextResponse.json({ error: 'Invalid lead ID' }, { status: 400 });
    }

    console.log(`API: Fetching lead with ID: ${leadId}`);

    // Get lead by ID
    const lead = await getLeadById(leadId);

    if (!lead) {
      console.error(`Lead not found with ID: ${leadId}`);
      return NextResponse.json({
        error: 'Lead not found',
        message: `No lead exists with ID: ${leadId}`
      }, { status: 404 });
    }

    console.log(`API: Successfully retrieved lead: ${lead.id}`);

    // Return the lead
    return NextResponse.json(lead);
  } catch (error) {
    console.error(`Error fetching lead ${leadId}:`, error);
    return NextResponse.json(
      {
        error: 'Failed to fetch lead',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for individual lead API
 * Updates a specific lead by ID
 */
export async function PUT(request: NextRequest, { params }: Params) {
  // Extract and store the ID to avoid the Next.js warning about using params.id directly
  const leadId = params.id;

  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Validate the ID parameter
    if (!leadId || typeof leadId !== 'string' || leadId.trim() === '') {
      console.error('Invalid lead ID provided:', leadId);
      return NextResponse.json({ error: 'Invalid lead ID' }, { status: 400 });
    }

    // Get request body
    const body = await request.json();

    console.log(`API: Updating lead with ID: ${leadId}`, body);

    // Update lead
    const lead = await updateLead(leadId, {
      status: body.status,
      assignedToUserId: body.assignedToUserId || null,
      nextFollowUpDate: body.nextFollowUpDate ? new Date(body.nextFollowUpDate) : null,
      notes: body.notes,
    });

    console.log(`API: Successfully updated lead: ${lead.id}`);

    // Return the updated lead
    return NextResponse.json(lead);
  } catch (error) {
    console.error(`Error updating lead ${leadId}:`, error);
    return NextResponse.json(
      {
        error: 'Failed to update lead',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
