import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { 
  CatalogueCRUDService, 
  CatalogueError, 
  CatalogueValidationError,
  CatalogueNotFoundError,
  CatalogueDuplicateError 
} from '@/services/catalogueCRUDService';

interface RouteParams {
  params: { id: string };
}

// Enhanced GET handler for single item
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized',
        message: 'Authentication required',
        timestamp: new Date().toISOString()
      }, { status: 401 });
    }

    const { id } = params;
    console.log(`API v3: Fetching catalogue item with ID: ${id}`);

    // Get catalogue item using enhanced CRUD service
    const catalogueItem = await CatalogueCRUDService.findById(id);

    if (!catalogueItem) {
      return NextResponse.json({
        success: false,
        error: 'Not Found',
        message: `Catalogue item with ID ${id} not found`,
        timestamp: new Date().toISOString()
      }, { status: 404 });
    }

    const response = {
      success: true,
      data: catalogueItem,
      timestamp: new Date().toISOString()
    };

    console.log(`API v3: Successfully fetched catalogue item: ${catalogueItem.service}`);

    return NextResponse.json(response, {
      status: 200,
      headers: {
        'Cache-Control': 'no-store, max-age=0, must-revalidate',
        'ETag': `"${catalogueItem.id}-${catalogueItem.updatedAt}"`
      }
    });

  } catch (error) {
    console.error(`API v3: Error in GET /admin/catalogue-v3/${params.id}:`, error);

    if (error instanceof CatalogueValidationError) {
      return NextResponse.json({
        success: false,
        error: 'Validation Error',
        message: error.message,
        field: error.field,
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    if (error instanceof CatalogueError) {
      return NextResponse.json({
        success: false,
        error: error.code,
        message: error.message,
        timestamp: new Date().toISOString()
      }, { status: error.statusCode });
    }

    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to fetch catalogue item',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Enhanced PUT handler for updating items
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized',
        message: 'Authentication required',
        timestamp: new Date().toISOString()
      }, { status: 401 });
    }

    // Get client context
    const clientIp = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    const { id } = params;

    // Parse request body with error handling
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json({
        success: false,
        error: 'Invalid JSON',
        message: 'Request body must be valid JSON',
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    console.log(`API v3: Updating catalogue item ${id}:`, body);

    // Update catalogue item using enhanced CRUD service
    const updatedCatalogue = await CatalogueCRUDService.update(id, body, {
      userId: session.user?.email || 'unknown',
      ipAddress: clientIp,
      userAgent: userAgent
    });

    const response = {
      success: true,
      data: updatedCatalogue,
      message: 'Catalogue item updated successfully',
      timestamp: new Date().toISOString()
    };

    console.log(`API v3: Updated catalogue item: ${updatedCatalogue.service}`);

    return NextResponse.json(response, {
      status: 200,
      headers: {
        'Cache-Control': 'no-store',
        'ETag': `"${updatedCatalogue.id}-${updatedCatalogue.updatedAt}"`
      }
    });

  } catch (error) {
    console.error(`API v3: Error in PUT /admin/catalogue-v3/${params.id}:`, error);

    if (error instanceof CatalogueValidationError) {
      return NextResponse.json({
        success: false,
        error: 'Validation Error',
        message: error.message,
        field: error.field,
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    if (error instanceof CatalogueNotFoundError) {
      return NextResponse.json({
        success: false,
        error: 'Not Found',
        message: error.message,
        timestamp: new Date().toISOString()
      }, { status: 404 });
    }

    if (error instanceof CatalogueDuplicateError) {
      return NextResponse.json({
        success: false,
        error: 'Duplicate Error',
        message: error.message,
        timestamp: new Date().toISOString()
      }, { status: 409 });
    }

    if (error instanceof CatalogueError) {
      return NextResponse.json({
        success: false,
        error: error.code,
        message: error.message,
        timestamp: new Date().toISOString()
      }, { status: error.statusCode });
    }

    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to update catalogue item',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Enhanced DELETE handler for single item
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized',
        message: 'Authentication required',
        timestamp: new Date().toISOString()
      }, { status: 401 });
    }

    // Get client context
    const clientIp = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    const { id } = params;
    console.log(`API v3: Deleting catalogue item with ID: ${id}`);

    // Delete catalogue item using enhanced CRUD service
    const success = await CatalogueCRUDService.delete(id, {
      userId: session.user?.email || 'unknown',
      ipAddress: clientIp,
      userAgent: userAgent
    });

    const response = {
      success: true,
      data: { deleted: success },
      message: 'Catalogue item deleted successfully',
      timestamp: new Date().toISOString()
    };

    console.log(`API v3: Successfully deleted catalogue item with ID: ${id}`);

    return NextResponse.json(response, {
      status: 200,
      headers: {
        'Cache-Control': 'no-store'
      }
    });

  } catch (error) {
    console.error(`API v3: Error in DELETE /admin/catalogue-v3/${params.id}:`, error);

    if (error instanceof CatalogueValidationError) {
      return NextResponse.json({
        success: false,
        error: 'Validation Error',
        message: error.message,
        field: error.field,
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    if (error instanceof CatalogueNotFoundError) {
      return NextResponse.json({
        success: false,
        error: 'Not Found',
        message: error.message,
        timestamp: new Date().toISOString()
      }, { status: 404 });
    }

    if (error instanceof CatalogueError) {
      return NextResponse.json({
        success: false,
        error: error.code,
        message: error.message,
        timestamp: new Date().toISOString()
      }, { status: error.statusCode });
    }

    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to delete catalogue item',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
