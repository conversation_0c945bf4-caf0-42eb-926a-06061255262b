import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { 
  CatalogueCRUDService, 
  CatalogueError, 
  CatalogueValidationError,
  CatalogueDuplicateError 
} from '@/services/catalogueCRUDService';

// Enhanced GET handler with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized',
        message: 'Authentication required',
        timestamp: new Date().toISOString()
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    
    // Parse query parameters with validation
    const filters = {
      category: searchParams.get('category') || undefined,
      popular: searchParams.get('popular') ? searchParams.get('popular') === 'true' : undefined,
      search: searchParams.get('search') || undefined,
      minPrice: searchParams.get('minPrice') ? Number(searchParams.get('minPrice')) : undefined,
      maxPrice: searchParams.get('maxPrice') ? Number(searchParams.get('maxPrice')) : undefined,
      limit: Math.min(Number(searchParams.get('limit')) || 20, 100),
      offset: Math.max(Number(searchParams.get('offset')) || 0, 0),
      sortBy: (searchParams.get('sortBy') as any) || 'service',
      sortOrder: (searchParams.get('sortOrder') as any) || 'asc'
    };

    console.log('API v3: Fetching catalogue items with filters:', filters);

    // Get catalogue items using enhanced CRUD service
    const result = await CatalogueCRUDService.findMany(filters);

    const response = {
      success: true,
      data: result.items,
      meta: {
        total: result.total,
        limit: filters.limit,
        offset: filters.offset,
        hasMore: filters.offset + result.items.length < result.total,
        totalPages: Math.ceil(result.total / filters.limit)
      },
      filters: filters,
      timestamp: new Date().toISOString()
    };

    console.log(`API v3: Retrieved ${result.items.length} of ${result.total} catalogue items`);

    return NextResponse.json(response, {
      status: 200,
      headers: {
        'Cache-Control': 'no-store, max-age=0, must-revalidate',
        'Pragma': 'no-cache',
        'X-Total-Count': result.total.toString(),
        'X-Has-More': (filters.offset + result.items.length < result.total).toString()
      }
    });

  } catch (error) {
    console.error('API v3: Error in GET /admin/catalogue-v3:', error);

    if (error instanceof CatalogueValidationError) {
      return NextResponse.json({
        success: false,
        error: 'Validation Error',
        message: error.message,
        field: error.field,
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    if (error instanceof CatalogueError) {
      return NextResponse.json({
        success: false,
        error: error.code,
        message: error.message,
        timestamp: new Date().toISOString()
      }, { status: error.statusCode });
    }

    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to fetch catalogue items',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Enhanced POST handler for creating catalogue items
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized',
        message: 'Authentication required',
        timestamp: new Date().toISOString()
      }, { status: 401 });
    }

    // Get client context
    const clientIp = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    // Parse request body with error handling
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json({
        success: false,
        error: 'Invalid JSON',
        message: 'Request body must be valid JSON',
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    console.log('API v3: Creating catalogue item:', body);

    // Create catalogue item using enhanced CRUD service
    const newCatalogue = await CatalogueCRUDService.create(body, {
      userId: session.user?.email || 'unknown',
      ipAddress: clientIp,
      userAgent: userAgent
    });

    const response = {
      success: true,
      data: newCatalogue,
      message: 'Catalogue item created successfully',
      timestamp: new Date().toISOString()
    };

    console.log(`API v3: Created catalogue item: ${newCatalogue.service}`);

    return NextResponse.json(response, {
      status: 201,
      headers: {
        'Cache-Control': 'no-store',
        'Location': `/api/admin/catalogue-v3/${newCatalogue.id}`
      }
    });

  } catch (error) {
    console.error('API v3: Error in POST /admin/catalogue-v3:', error);

    if (error instanceof CatalogueValidationError) {
      return NextResponse.json({
        success: false,
        error: 'Validation Error',
        message: error.message,
        field: error.field,
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    if (error instanceof CatalogueDuplicateError) {
      return NextResponse.json({
        success: false,
        error: 'Duplicate Error',
        message: error.message,
        timestamp: new Date().toISOString()
      }, { status: 409 });
    }

    if (error instanceof CatalogueError) {
      return NextResponse.json({
        success: false,
        error: error.code,
        message: error.message,
        timestamp: new Date().toISOString()
      }, { status: error.statusCode });
    }

    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to create catalogue item',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Enhanced DELETE handler for bulk operations
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized',
        message: 'Authentication required',
        timestamp: new Date().toISOString()
      }, { status: 401 });
    }

    // Get client context
    const clientIp = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json({
        success: false,
        error: 'Invalid JSON',
        message: 'Request body must be valid JSON',
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    const { ids } = body;

    console.log('API v3: Bulk deleting catalogue items:', ids);

    // Bulk delete using enhanced CRUD service
    const result = await CatalogueCRUDService.bulkDelete(ids, {
      userId: session.user?.email || 'unknown',
      ipAddress: clientIp,
      userAgent: userAgent
    });

    const response = {
      success: result.success,
      data: {
        deletedCount: result.deletedCount,
        errors: result.errors
      },
      message: `Successfully deleted ${result.deletedCount} catalogue items`,
      timestamp: new Date().toISOString()
    };

    console.log(`API v3: Bulk deleted ${result.deletedCount} catalogue items`);

    return NextResponse.json(response, {
      status: 200,
      headers: {
        'Cache-Control': 'no-store'
      }
    });

  } catch (error) {
    console.error('API v3: Error in DELETE /admin/catalogue-v3:', error);

    if (error instanceof CatalogueValidationError) {
      return NextResponse.json({
        success: false,
        error: 'Validation Error',
        message: error.message,
        field: error.field,
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    if (error instanceof CatalogueError) {
      return NextResponse.json({
        success: false,
        error: error.code,
        message: error.message,
        timestamp: new Date().toISOString()
      }, { status: error.statusCode });
    }

    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to bulk delete catalogue items',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
