import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import prisma from '@/lib/prisma';
import { uploadImageToS3 } from '@/utils/s3Utils';

// GET handler - Get a specific team member by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    const id = params.id;

    // Fetch team member from database
    const teamMember = await prisma.teamMember.findUnique({
      where: { id }
    });

    if (!teamMember) {
      return new NextResponse(JSON.stringify({ error: 'Team member not found' }), {
        status: 404,
      });
    }

    // Return the team member with no-cache headers
    return NextResponse.json(teamMember, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      },
    });
  } catch (error) {
    console.error(`Error fetching team member ${params.id}:`, error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}

// PUT handler - Update a specific team member by ID
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    const id = params.id;

    // Check if team member exists
    const existingTeamMember = await prisma.teamMember.findUnique({
      where: { id }
    });

    if (!existingTeamMember) {
      return new NextResponse(JSON.stringify({ error: 'Team member not found' }), {
        status: 404,
      });
    }

    // Parse form data
    const formData = await request.formData();
    const name = formData.get('name') as string;
    const role = formData.get('role') as string;
    const bio = formData.get('bio') as string;
    const order = parseInt(formData.get('order') as string) || 0;
    const linkedinUrl = formData.get('linkedinUrl') as string || null;
    const twitterUrl = formData.get('twitterUrl') as string || null;
    const githubUrl = formData.get('githubUrl') as string || null;
    const emailAddress = formData.get('emailAddress') as string || null;
    const image = formData.get('image') as File;

    // Validate required fields
    if (!name || !role || !bio) {
      return new NextResponse(JSON.stringify({ error: 'Missing required fields' }), {
        status: 400,
      });
    }

    // Prepare update data
    const updateData: any = {
      name,
      role,
      bio,
      order,
      linkedinUrl,
      twitterUrl,
      githubUrl,
      emailAddress,
    };

    // Upload new image if provided
    if (image && image.size > 0) {
      try {
        console.log(`Uploading new image for team member ${id}: ${image.name}`);

        // Create a unique path for the image
        const timestamp = Date.now();
        const safeFileName = image.name.replace(/[^a-zA-Z0-9.-]/g, '_');
        const imagePath = `team/${id}/${timestamp}_${safeFileName}`;

        // Use enhanced upload utility with logging prefix
        const uploadResult = await uploadImageToS3(image, imagePath, {
          logPrefix: `[TeamMember:${id}]`,
          maxRetries: 3,
          timeoutMs: 45000 // 45 seconds
        });

        if (!uploadResult.success) {
          console.error('Image upload failed:', uploadResult.error);

          // Continue with update but don't change the image
          const updatedMember = await prisma.teamMember.update({
            where: { id },
            data: updateData,
          });

          // Return a warning in the response
          return new NextResponse(JSON.stringify({
            warning: `Team member updated but image upload failed: ${uploadResult.error}. Using previous image.`,
            warnings: uploadResult.warnings,
            teamMember: updatedMember
          }));
        }

        // If upload was successful but had warnings, log them
        if (uploadResult.warnings && uploadResult.warnings.length > 0) {
          console.warn(`Image upload warnings for team member ${id}:`, uploadResult.warnings);
        }

        updateData.imageSrc = uploadResult.url;
        console.log(`Using uploaded image URL: ${uploadResult.url}`);
      } catch (uploadError) {
        console.error('Error during image upload process:', uploadError);

        // Continue with update but don't change the image
        const updatedMember = await prisma.teamMember.update({
          where: { id },
          data: updateData,
        });

        // Return a warning in the response
        return new NextResponse(JSON.stringify({
          warning: 'Team member updated but image upload failed due to an unexpected error. Using previous image.',
          teamMember: updatedMember
        }));
      }
    }

    // Update team member in database
    const updatedTeamMember = await prisma.teamMember.update({
      where: { id },
      data: updateData,
    });

    return NextResponse.json(updatedTeamMember);
  } catch (error) {
    console.error(`Error updating team member ${params.id}:`, error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}

// DELETE handler - Delete a specific team member by ID
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    const id = params.id;

    // Check if team member exists
    const existingTeamMember = await prisma.teamMember.findUnique({
      where: { id }
    });

    if (!existingTeamMember) {
      return new NextResponse(JSON.stringify({ error: 'Team member not found' }), {
        status: 404,
      });
    }

    // Delete team member from database
    await prisma.teamMember.delete({
      where: { id }
    });

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error(`Error deleting team member ${params.id}:`, error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}
