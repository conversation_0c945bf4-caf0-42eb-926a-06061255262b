import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { v4 as uuidv4 } from 'uuid';
import prisma from '@/lib/prisma';
import {
  TeamMember,
  TeamMemberResponse,
  TeamMembersListResponse,
  TeamMemberValidationError,
  TeamMemberImageUploadError
} from '@/types/team';
import { uploadImageToS3 } from '@/utils/s3Utils';
import {
  validateTeamMemberData,
  validateImageFile,
  checkRateLimit,
  sanitizeText
} from '@/utils/validation';
import { ErrorTracker } from '@/services/errorTracking';
import { createTeamMember, getAllTeamMembers } from '@/services/teamMemberService';

/**
 * GET handler - Get all team members with improved error handling and caching
 */
export async function GET(request: NextRequest): Promise<NextResponse<TeamMembersListResponse>> {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized access. Please log in to continue.'
        },
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // Rate limiting check
    const clientIP = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';

    if (!checkRateLimit(`team_get_${clientIP}`, 30, 60000)) { // 30 requests per minute
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.'
        },
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'Retry-After': '60'
          }
        }
      );
    }

    console.log('Fetching team members for admin interface');

    // Fetch team members using the service
    const teamMembers = await getAllTeamMembers(true); // Use cache

    console.log(`Successfully fetched ${teamMembers.length} team members`);

    // Return the team members with appropriate headers
    return NextResponse.json(
      {
        success: true,
        data: teamMembers,
        total: teamMembers.length
      },
      {
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
          'Content-Type': 'application/json'
        }
      }
    );
  } catch (error) {
    ErrorTracker.trackError(
      error as Error,
      'GET /api/admin/team',
      {
        userAgent: request.headers.get('user-agent'),
        ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')
      }
    );

    console.error('Error fetching team members:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch team members. Please try again later.'
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}

/**
 * POST handler - Create a new team member with comprehensive validation
 */
export async function POST(request: NextRequest): Promise<NextResponse<TeamMemberResponse>> {
  const startTime = Date.now();
  console.log('POST /api/admin/team - Starting team member creation');

  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      console.log('POST /api/admin/team - Authentication failed');
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized access. Please log in to continue.'
        },
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }
    console.log('POST /api/admin/team - Authentication successful');

    // Rate limiting check
    const clientIP = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';

    if (!checkRateLimit(`team_create_${clientIP}`, 5, 60000)) { // 5 creates per minute
      return NextResponse.json(
        {
          success: false,
          error: 'Too many creation requests. Please try again later.'
        },
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'Retry-After': '60'
          }
        }
      );
    }

    // Parse form data with error handling
    let formData: FormData;
    try {
      formData = await request.formData();
      console.log('POST /api/admin/team - Form data parsed successfully');
    } catch (error) {
      console.error('POST /api/admin/team - Failed to parse form data:', error);
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid form data. Please check your input and try again.'
        },
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // Extract and sanitize form fields
    const rawName = formData.get('name') as string;
    const rawRole = formData.get('role') as string;
    const rawBio = formData.get('bio') as string;
    const rawOrder = formData.get('order') as string;
    const rawLinkedinUrl = formData.get('linkedinUrl') as string;
    const rawTwitterUrl = formData.get('twitterUrl') as string;
    const rawGithubUrl = formData.get('githubUrl') as string;
    const rawEmailAddress = formData.get('emailAddress') as string;
    const image = formData.get('image') as File;

    console.log('POST /api/admin/team - Extracting and validating form data');

    // Validate required fields first
    if (!rawName || !rawRole || !rawBio || !image) {
      console.log('POST /api/admin/team - Validation failed: Missing required fields', {
        hasName: !!rawName,
        hasRole: !!rawRole,
        hasBio: !!rawBio,
        hasImage: !!image
      });
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields. Please fill in name, role, bio, and upload an image.'
        },
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // Validate image file
    try {
      validateImageFile(image);
      console.log('POST /api/admin/team - Image validation successful');
    } catch (error) {
      if (error instanceof TeamMemberValidationError) {
        console.log('POST /api/admin/team - Image validation failed:', error.message);
        return NextResponse.json(
          {
            success: false,
            error: error.message
          },
          {
            status: 400,
            headers: {
              'Content-Type': 'application/json'
            }
          }
        );
      }
      throw error;
    }

    // Validate and sanitize team member data
    let validatedData;
    try {
      validatedData = validateTeamMemberData({
        name: rawName,
        role: rawRole,
        bio: rawBio,
        order: parseInt(rawOrder) || 0,
        linkedinUrl: rawLinkedinUrl || '',
        twitterUrl: rawTwitterUrl || '',
        githubUrl: rawGithubUrl || '',
        emailAddress: rawEmailAddress || ''
      });
      console.log('POST /api/admin/team - Data validation successful');
    } catch (error) {
      if (error instanceof TeamMemberValidationError) {
        console.log('POST /api/admin/team - Data validation failed:', error.message);
        return NextResponse.json(
          {
            success: false,
            error: error.message
          },
          {
            status: 400,
            headers: {
              'Content-Type': 'application/json'
            }
          }
        );
      }
      throw error;
    }

    console.log('POST /api/admin/team - All validation passed:', {
      name: validatedData.name,
      role: validatedData.role,
      bioLength: validatedData.bio.length,
      order: validatedData.order,
      imageSize: Math.round(image.size / 1024),
      imageType: image.type
    });

    // Upload image to S3 with improved error handling
    let imageSrc = '';
    let uploadWarnings: string[] = [];

    try {
      console.log('POST /api/admin/team - Starting image upload');

      // Generate unique ID for the team member
      const id = uuidv4();

      // Create a unique path for the image
      const timestamp = Date.now();
      const baseName = image.name.split(/[\\/]/).pop() || 'unnamed';
      const fileExtension = (baseName.split('.').pop() || '').toLowerCase();
      const validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
      const extension = validExtensions.includes(fileExtension) ? fileExtension : 'jpg';

      // Create a safe filename
      const nameWithoutExtension = baseName.split('.')[0] || 'image';
      const simpleName = nameWithoutExtension
        .replace(/[^a-zA-Z0-9]/g, '_')
        .replace(/_{2,}/g, '_')
        .replace(/^[_]+|[_]+$/g, '')
        .substring(0, 30);

      const safeFileName = `${simpleName}.${extension}`;
      const imagePath = `team/${timestamp}_${safeFileName}`;

      console.log(`Uploading team member image: ${imagePath}, size: ${Math.round(image.size/1024)}KB`);

      // Dynamic timeout based on file size
      const timeoutMs = image.size > 5 * 1024 * 1024 ? 180000 :
                       image.size > 2 * 1024 * 1024 ? 120000 : 60000;

      const uploadResult = await uploadImageToS3(image, imagePath, {
        logPrefix: `[NewTeamMember:${id}]`,
        maxRetries: 5,
        timeoutMs
      });

      if (!uploadResult.success) {
        console.error('Image upload failed:', uploadResult.error);
        imageSrc = '/images/default-profile.jpg';
        uploadWarnings.push('Image upload failed, using default image');
        if (uploadResult.warnings) {
          uploadWarnings.push(...uploadResult.warnings);
        }
      } else {
        imageSrc = uploadResult.url;
        console.log(`Image uploaded successfully: ${imageSrc}`);
        if (uploadResult.warnings) {
          uploadWarnings.push(...uploadResult.warnings);
        }
      }

      // Create team member using the service
      const teamMember = await createTeamMember({
        ...validatedData,
        imageSrc
      });

      const processingTime = Date.now() - startTime;
      console.log(`POST /api/admin/team - Team member created successfully in ${processingTime}ms`);

      // Return success response
      const response: TeamMemberResponse = {
        success: true,
        data: teamMember,
        warnings: uploadWarnings.length > 0 ? uploadWarnings : undefined
      };

      return NextResponse.json(response, {
        status: 201,
        headers: {
          'Content-Type': 'application/json'
        }
      });

    } catch (uploadError) {
      console.error('Error during image upload process:', uploadError);

      if (uploadError instanceof TeamMemberImageUploadError) {
        return NextResponse.json(
          {
            success: false,
            error: uploadError.message
          },
          {
            status: 400,
            headers: {
              'Content-Type': 'application/json'
            }
          }
        );
      }

      throw uploadError; // Re-throw to be caught by outer catch
    }

  } catch (error) {
    const processingTime = Date.now() - startTime;

    ErrorTracker.trackError(
      error as Error,
      'POST /api/admin/team',
      {
        processingTime,
        userAgent: request.headers.get('user-agent'),
        ip: clientIP,
        hasImage: !!image,
        imageSize: image?.size
      }
    );

    console.error(`Error creating team member after ${processingTime}ms:`, error);

    // Handle specific error types
    if (error instanceof TeamMemberValidationError) {
      return NextResponse.json(
        {
          success: false,
          error: error.message
        },
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // Provide more detailed error information
    let errorMessage = 'Failed to create team member. Please try again later.';
    let statusCode = 500;

    if (error instanceof Error) {
      // Check for specific error types
      if (error.message.includes('database') || error.message.includes('prisma')) {
        errorMessage = 'Database error occurred. Please try again later.';
      } else if (error.message.includes('timeout') || error.message.includes('network')) {
        errorMessage = 'Network timeout occurred. Please try again with a smaller image.';
        statusCode = 408; // Request Timeout
      } else if (error.message.includes('storage') || error.message.includes('S3')) {
        errorMessage = 'Storage error occurred. Please try again later.';
      } else if (error.message.includes('DUPLICATE_NAME')) {
        errorMessage = 'A team member with this name already exists.';
        statusCode = 409; // Conflict
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: errorMessage
      },
      {
        status: statusCode,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
