import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { S3Client, PutObjectCommand, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { NodeHttpHandler } from '@aws-sdk/node-http-handler';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// Maximum file size (15MB)
const MAX_FILE_SIZE = 15 * 1024 * 1024;

// Portfolio categories for validation
const VALID_CATEGORIES = ['cards', 'fliers', 'letterheads', 'logos', 'profiles', 'branding', 'websites', 'about', 'general', 'team'];

// Base path for all portfolio images
const PORTFOLIO_BASE_PATH = 'images/portfolio';

// Create S3 client with enhanced configuration
let s3Client;
try {
  s3Client = new S3Client({
    region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
    endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
    credentials: {
      accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
      secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
    },
    forcePathStyle: true, // Required for Linode Object Storage
    maxAttempts: 5, // Increased from 3 to 5 retries
    retryMode: 'adaptive', // Changed to adaptive for better handling of network issues
    requestHandler: new NodeHttpHandler({
      connectionTimeout: 15000, // 15 seconds connection timeout (increased from 10s)
      socketTimeout: 300000,    // 5 minutes socket timeout (increased from 3 min)
    })
  });
  console.log('S3 client initialized successfully');
} catch (error) {
  console.error('Error initializing S3 client:', error);
  // Create a fallback client without the NodeHttpHandler if it fails
  s3Client = new S3Client({
    region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
    endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
    credentials: {
      accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
      secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
    },
    forcePathStyle: true,
    maxAttempts: 5,
    retryMode: 'standard'
  });
  console.log('Fallback S3 client initialized');
}

// Function to get the next available number for a category
async function getNextFileNumber(category: string): Promise<number> {
  try {
    const prefix = `${PORTFOLIO_BASE_PATH}/${category}/`;
    const command = new ListObjectsV2Command({
      Bucket: process.env.NEXT_PUBLIC_S3_BUCKET!,
      Prefix: prefix
    });

    const response = await s3Client.send(command);
    if (!response.Contents) return 1;

    // Extract numbers from existing filenames
    const numbers = response.Contents
      .map(item => {
        const filename = item.Key?.split('/').pop() || '';
        const match = filename.match(/^(\d+)\./);
        return match ? parseInt(match[1]) : 0;
      })
      .filter(num => num > 0);

    // Return the next number in sequence
    return numbers.length > 0 ? Math.max(...numbers) + 1 : 1;
  } catch (error) {
    console.error('Error getting next file number:', error);
    throw error;
  }
}

export async function POST(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse the multipart form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    let category = formData.get('category') as string;

    // Log received data
    console.log('Received upload request:', {
      filename: file?.name,
      category,
      fileType: file?.type,
      fileSize: file?.size
    });

    // Validate inputs
    if (!file || !category) {
      return NextResponse.json(
        { error: 'File and category are required' },
        { status: 400 }
      );
    }

    // Clean and validate category
    category = category.toLowerCase().trim();
    if (!VALID_CATEGORIES.includes(category)) {
      return NextResponse.json(
        { error: `Invalid category. Must be one of: ${VALID_CATEGORIES.join(', ')}` },
        { status: 400 }
      );
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: 'File size exceeds 15MB limit' },
        { status: 400 }
      );
    }

    // Log warning for large files
    if (file.size > 5 * 1024 * 1024) {
      console.warn(`Large file detected (${Math.round(file.size/1024/1024)}MB). Upload may take longer than usual.`);
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, JPG, PNG, and GIF are allowed' },
        { status: 400 }
      );
    }

    // Verify required environment variables
    if (!process.env.NEXT_PUBLIC_S3_BUCKET) {
      console.error('Missing required environment variables');
      return NextResponse.json(
        { error: 'Storage configuration error' },
        { status: 500 }
      );
    }

    // Get the next available number for the filename
    const nextNumber = await getNextFileNumber(category);

    // Generate the filename with the next number
    let fileExtension = file.type.split('/')[1];
    // Normalize jpeg/jpg extension to jpg
    if (fileExtension === 'jpeg') {
      fileExtension = 'jpg';
    }
    const newFilename = `${nextNumber}.${fileExtension}`;

    // Determine the base path based on category
    let basePath = PORTFOLIO_BASE_PATH;
    if (['about', 'general', 'team'].includes(category)) {
      basePath = 'images'; // Use a different base path for non-portfolio categories
    }

    // Construct the full S3 key with the correct path structure
    const s3Key = `${basePath}/${category}/${newFilename}`;
    console.log('Uploading to S3:', {
      bucket: process.env.NEXT_PUBLIC_S3_BUCKET,
      key: s3Key,
      category,
      filename: newFilename,
      basePath
    });

    // Upload to S3
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    try {
      // Log the upload attempt
      console.log(`Attempting to upload file: ${file.name} (${Math.round(file.size/1024)}KB) to ${s3Key}`);

      // Create the command with detailed metadata
      const command = new PutObjectCommand({
        Bucket: process.env.NEXT_PUBLIC_S3_BUCKET,
        Key: s3Key,
        Body: buffer,
        ContentType: file.type,
        ACL: 'public-read',
        Metadata: {
          originalname: file.name,
          category: category,
          uploadTime: new Date().toISOString(),
          fileSize: String(file.size),
          contentType: file.type
        },
      });

      // Set timeout based on file size with increased values
      const timeoutMs = file.size > 5 * 1024 * 1024
        ? 300000 // 5 minutes for large files (>5MB)
        : file.size > 2 * 1024 * 1024
          ? 180000 // 3 minutes for medium files (>2MB)
          : 90000; // 1.5 minutes for small files

      console.log(`Using timeout of ${timeoutMs/1000} seconds for upload based on file size of ${Math.round(file.size/1024)}KB`);

      // Create promises for upload and timeout
      const uploadPromise = s3Client.send(command);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error(`S3 upload timeout after ${timeoutMs/1000} seconds`)), timeoutMs);
      });

      // Add a progress log
      console.log('Upload started, waiting for completion...');

      // Race the upload against the timeout
      await Promise.race([uploadPromise, timeoutPromise]);
      console.log('Upload completed successfully');

      // Generate public URL with proper formatting
      let endpoint = process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com';
      // Ensure endpoint doesn't have trailing slash
      endpoint = endpoint.replace(/\/$/, '');

      const url = `${endpoint}/${process.env.NEXT_PUBLIC_S3_BUCKET}/${s3Key}`;
      console.log('Upload successful:', {
        key: s3Key,
        url,
        category,
        fileSize: `${Math.round(file.size/1024)}KB`
      });

      return NextResponse.json({
        success: true,
        key: s3Key,
        url,
        filename: newFilename,
        category
      });
    } catch (s3Error: any) {
      console.error('S3 upload error:', s3Error);

      // Provide more specific error messages
      let errorMessage = 'Failed to upload file to storage';
      let statusCode = 500;

      if (s3Error.message?.includes('timeout')) {
        errorMessage = 'Upload timed out. Please try a smaller image or check your connection.';
        statusCode = 408; // Request Timeout
      } else if (s3Error.name === 'NetworkError' || s3Error.message?.includes('network')) {
        errorMessage = 'Network error occurred during upload. Please check your connection.';
        statusCode = 503; // Service Unavailable
      } else if (s3Error.name === 'AccessDenied' || s3Error.message?.includes('Access Denied')) {
        errorMessage = 'Access denied to storage. Please check your credentials.';
        statusCode = 403; // Forbidden
      } else if (s3Error.name === 'NoSuchBucket' || s3Error.message?.includes('bucket')) {
        errorMessage = 'Storage bucket not found or not accessible.';
        statusCode = 404; // Not Found
      }

      // Log detailed error information
      console.error('Upload error details:', {
        message: s3Error.message,
        name: s3Error.name,
        code: s3Error.code,
        statusCode,
        errorMessage,
        file: file.name,
        size: `${Math.round(file.size/1024)}KB`,
        s3Key
      });

      return NextResponse.json(
        {
          error: errorMessage,
          details: s3Error.message,
          code: s3Error.code || s3Error.name
        },
        { status: statusCode }
      );
    }
  } catch (error) {
    console.error('Error handling upload:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}