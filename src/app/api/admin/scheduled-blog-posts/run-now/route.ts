import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { generateAndCreateBlogPost } from '@/utils/blogAutomation';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';

const execAsync = promisify(exec);

// POST handler - Run the blog post generation script immediately
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const data = await request.json();
    const { runType = 'api' } = data;

    if (runType === 'script') {
      // Run the script directly using ts-node
      const scriptPath = path.join(process.cwd(), 'scripts', 'scheduled-blog-post.ts');
      
      try {
        const { stdout, stderr } = await execAsync(`npx ts-node ${scriptPath} --run-once`);
        
        if (stderr) {
          console.error('Script error:', stderr);
          return NextResponse.json({
            success: false,
            message: 'Error running script',
            error: stderr
          }, { status: 500 });
        }
        
        return NextResponse.json({
          success: true,
          message: 'Blog post generation script executed successfully',
          output: stdout
        });
      } catch (error) {
        console.error('Error executing script:', error);
        return NextResponse.json({
          success: false,
          message: 'Error executing script',
          error: error instanceof Error ? error.message : String(error)
        }, { status: 500 });
      }
    } else {
      // Run the blog post generation directly from the API
      const options = {
        category: data.category,
        tone: data.tone,
        length: data.length,
        targetAudience: data.targetAudience,
        publishImmediately: data.publishImmediately !== false
      };
      
      const blogPost = await generateAndCreateBlogPost(options);
      
      if (!blogPost) {
        return NextResponse.json({
          success: false,
          message: 'Failed to generate blog post'
        }, { status: 500 });
      }
      
      return NextResponse.json({
        success: true,
        message: 'Blog post generated successfully',
        blogPost
      });
    }
  } catch (error) {
    console.error('Error in POST run-now:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to run blog post generation',
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
