import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for updating scheduled blog post
const UpdateScheduledBlogPostSchema = z.object({
  category: z.string().optional(),
  tone: z.enum(['professional', 'casual', 'humorous', 'technical', 'conversational']).optional(),
  length: z.enum(['short', 'medium', 'long']).optional(),
  targetAudience: z.string().optional(),
  scheduledDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: 'Invalid date format',
  }).optional(),
  status: z.enum(['pending', 'completed', 'failed']).optional(),
});

// GET handler - Get a specific scheduled blog post
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const id = params.id;

    // Get the scheduled blog post
    const scheduledPost = await prisma.scheduledBlogPost.findUnique({
      where: { id }
    });

    if (!scheduledPost) {
      return NextResponse.json(
        { error: 'Scheduled blog post not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(scheduledPost);
  } catch (error) {
    console.error(`Error in GET scheduled blog post ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to fetch scheduled blog post' },
      { status: 500 }
    );
  }
}

// PUT handler - Update a scheduled blog post
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const id = params.id;

    // Parse request body
    const data = await request.json();

    // Validate the data
    const validationResult = UpdateScheduledBlogPostSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Check if the scheduled blog post exists
    const existingPost = await prisma.scheduledBlogPost.findUnique({
      where: { id }
    });

    if (!existingPost) {
      return NextResponse.json(
        { error: 'Scheduled blog post not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};
    
    if (data.category !== undefined) updateData.category = data.category;
    if (data.tone !== undefined) updateData.tone = data.tone;
    if (data.length !== undefined) updateData.length = data.length;
    if (data.targetAudience !== undefined) updateData.targetAudience = data.targetAudience;
    if (data.scheduledDate !== undefined) updateData.scheduledDate = new Date(data.scheduledDate);
    if (data.status !== undefined) updateData.status = data.status;

    // Update the scheduled blog post
    const updatedPost = await prisma.scheduledBlogPost.update({
      where: { id },
      data: updateData
    });

    return NextResponse.json(updatedPost);
  } catch (error) {
    console.error(`Error in PUT scheduled blog post ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to update scheduled blog post' },
      { status: 500 }
    );
  }
}

// DELETE handler - Delete a scheduled blog post
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const id = params.id;

    // Check if the scheduled blog post exists
    const existingPost = await prisma.scheduledBlogPost.findUnique({
      where: { id }
    });

    if (!existingPost) {
      return NextResponse.json(
        { error: 'Scheduled blog post not found' },
        { status: 404 }
      );
    }

    // Delete the scheduled blog post
    await prisma.scheduledBlogPost.delete({
      where: { id }
    });

    return NextResponse.json({ 
      message: 'Scheduled blog post deleted successfully' 
    });
  } catch (error) {
    console.error(`Error in DELETE scheduled blog post ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to delete scheduled blog post' },
      { status: 500 }
    );
  }
}
