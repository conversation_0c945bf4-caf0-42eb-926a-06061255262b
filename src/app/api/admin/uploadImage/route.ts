import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { S3Client, PutObjectCommand, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// Maximum file size (15MB)
const MAX_FILE_SIZE = 15 * 1024 * 1024;

// Portfolio categories for validation
const VALID_CATEGORIES = ['cards', 'fliers', 'letterheads', 'logos', 'profiles', 'branding', 'websites', 'about', 'general', 'team'];

// Base path for all portfolio images
const PORTFOLIO_BASE_PATH = 'images/portfolio';

const s3Client = new S3Client({
  region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
  endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
  credentials: {
    accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
    secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
  },
  forcePathStyle: true // Required for Linode Object Storage
});

// Function to get the next available number for a category
async function getNextFileNumber(category: string): Promise<number> {
  try {
    const prefix = `${PORTFOLIO_BASE_PATH}/${category}/`;
    const command = new ListObjectsV2Command({
      Bucket: process.env.NEXT_PUBLIC_S3_BUCKET!,
      Prefix: prefix
    });

    const response = await s3Client.send(command);
    if (!response.Contents) return 1;

    // Extract numbers from existing filenames
    const numbers = response.Contents
      .map(item => {
        const filename = item.Key?.split('/').pop() || '';
        const match = filename.match(/^(\d+)\./);
        return match ? parseInt(match[1]) : 0;
      })
      .filter(num => num > 0);

    // Return the next number in sequence
    return numbers.length > 0 ? Math.max(...numbers) + 1 : 1;
  } catch (error) {
    console.error('Error getting next file number:', error);
    throw error;
  }
}

export async function POST(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse the multipart form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    let category = formData.get('category') as string;

    // Log received data
    console.log('Received uploadImage request:', {
      filename: file?.name,
      category,
      fileType: file?.type,
      fileSize: file?.size
    });

    // Validate inputs
    if (!file || !category) {
      return NextResponse.json(
        { error: 'File and category are required' },
        { status: 400 }
      );
    }

    // Clean and validate category
    category = category.toLowerCase().trim();
    if (!VALID_CATEGORIES.includes(category)) {
      return NextResponse.json(
        { error: `Invalid category. Must be one of: ${VALID_CATEGORIES.join(', ')}` },
        { status: 400 }
      );
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: 'File size exceeds 15MB limit' },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, JPG, PNG, GIF, and WebP are allowed' },
        { status: 400 }
      );
    }

    // Verify required environment variables
    if (!process.env.NEXT_PUBLIC_S3_BUCKET) {
      console.error('Missing required environment variables');
      return NextResponse.json(
        { error: 'Storage configuration error' },
        { status: 500 }
      );
    }

    // Get the next available number for the filename
    const nextNumber = await getNextFileNumber(category);

    // Generate the filename with the next number
    let fileExtension = file.type.split('/')[1];
    // Normalize jpeg/jpg extension to jpg
    if (fileExtension === 'jpeg') {
      fileExtension = 'jpg';
    }
    const newFilename = `${nextNumber}.${fileExtension}`;

    // Construct the full S3 key with the correct path structure
    const s3Key = `${PORTFOLIO_BASE_PATH}/${category}/${newFilename}`;
    console.log('Uploading to S3:', {
      bucket: process.env.NEXT_PUBLIC_S3_BUCKET,
      key: s3Key,
      category,
      filename: newFilename
    });

    // Upload to S3
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    try {
      await s3Client.send(
        new PutObjectCommand({
          Bucket: process.env.NEXT_PUBLIC_S3_BUCKET,
          Key: s3Key,
          Body: buffer,
          ContentType: file.type,
          ACL: 'public-read',
          Metadata: {
            originalname: file.name,
            category: category,
          },
        })
      );

      // Generate public URL
      const url = `${process.env.NEXT_PUBLIC_S3_ENDPOINT}/${process.env.NEXT_PUBLIC_S3_BUCKET}/${s3Key}`;
      console.log('Upload successful:', {
        key: s3Key,
        url,
        category
      });

      return NextResponse.json({
        success: true,
        key: s3Key,
        url,
        filename: newFilename,
        category
      });
    } catch (s3Error) {
      console.error('S3 upload error:', s3Error);
      return NextResponse.json(
        { error: 'Failed to upload file to storage' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error handling upload:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}