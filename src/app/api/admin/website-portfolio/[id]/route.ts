import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { WebsitePortfolioItem } from '@/types/portfolio';
import prisma from '@/lib/prisma';

// GET handler - Get a specific website portfolio item by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    // Find the item with the specified ID
    const item = await prisma.websitePortfolio.findUnique({
      where: { id }
    });

    if (!item) {
      return NextResponse.json(
        { error: 'Website portfolio item not found' },
        { status: 404 }
      );
    }

    // Convert to WebsitePortfolioItem type
    const websitePortfolioItem: WebsitePortfolioItem = {
      id: item.id,
      title: item.title,
      description: item.description || '',
      category: item.category,
      imageSrc: item.imageSrc,
      url: item.url,
      featured: item.featured,
      createdAt: item.createdAt.toISOString(),
      updatedAt: item.updatedAt.toISOString(),
    };

    return NextResponse.json(websitePortfolioItem);
  } catch (error) {
    console.error('Error in GET website portfolio item:', error);
    return NextResponse.json(
      { error: 'Failed to fetch website portfolio item' },
      { status: 500 }
    );
  }
}

// PUT handler - Update a website portfolio item
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const token = await getToken({ req: request });
    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const id = params.id;

    // Parse request body
    const data = await request.json();

    // Validate required fields
    if (!data.title || !data.category || !data.imageSrc || !data.url) {
      return NextResponse.json(
        { error: 'Missing required fields: title, category, imageSrc, or url' },
        { status: 400 }
      );
    }

    // Check if item exists
    const existingItem = await prisma.websitePortfolio.findUnique({
      where: { id }
    });

    if (!existingItem) {
      return NextResponse.json(
        { error: 'Website portfolio item not found' },
        { status: 404 }
      );
    }

    // Update the item
    const updatedItem = await prisma.websitePortfolio.update({
      where: { id },
      data: {
        title: data.title,
        description: data.description || '',
        category: data.category,
        imageSrc: data.imageSrc,
        url: data.url,
        featured: data.featured || false,
      }
    });

    // Convert to WebsitePortfolioItem type
    const websitePortfolioItem: WebsitePortfolioItem = {
      id: updatedItem.id,
      title: updatedItem.title,
      description: updatedItem.description || '',
      category: updatedItem.category,
      imageSrc: updatedItem.imageSrc,
      url: updatedItem.url,
      featured: updatedItem.featured,
      createdAt: updatedItem.createdAt.toISOString(),
      updatedAt: updatedItem.updatedAt.toISOString(),
    };

    return NextResponse.json(websitePortfolioItem);
  } catch (error) {
    console.error('Error in PUT website portfolio item:', error);
    return NextResponse.json(
      { error: 'Failed to update website portfolio item' },
      { status: 500 }
    );
  }
}

// DELETE handler - Delete a website portfolio item
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const token = await getToken({ req: request });
    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const id = params.id;

    // Check if item exists
    const existingItem = await prisma.websitePortfolio.findUnique({
      where: { id }
    });

    if (!existingItem) {
      return NextResponse.json(
        { error: 'Website portfolio item not found' },
        { status: 404 }
      );
    }

    // Delete the item
    await prisma.websitePortfolio.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE website portfolio item:', error);
    return NextResponse.json(
      { error: 'Failed to delete website portfolio item' },
      { status: 500 }
    );
  }
}
