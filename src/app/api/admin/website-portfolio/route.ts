import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { getToken } from 'next-auth/jwt';
import { WebsitePortfolioItem } from '@/types/portfolio';
import prisma from '@/lib/prisma';

// GET handler - Get all website portfolio items or filter by category
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    // Build the query
    const query = category && category !== 'all'
      ? { where: { category } }
      : {};

    // Fetch website portfolio items from database
    const websitePortfolioItems = await prisma.websitePortfolio.findMany({
      ...query,
      orderBy: { createdAt: 'desc' }
    });

    // Convert to WebsitePortfolioItem type
    const items: WebsitePortfolioItem[] = websitePortfolioItems.map(item => ({
      id: item.id,
      title: item.title,
      description: item.description || '',
      category: item.category,
      imageSrc: item.imageSrc,
      url: item.url,
      featured: item.featured,
      createdAt: item.createdAt.toISOString(),
      updatedAt: item.updatedAt.toISOString(),
    }));

    return NextResponse.json(items, {
      headers: {
        'Cache-Control': 'no-store',
      },
    });
  } catch (error) {
    console.error('Error in GET website portfolio items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch website portfolio items' },
      { status: 500 }
    );
  }
}

// POST handler - Create a new website portfolio item
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const token = await getToken({ req: request });
    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const data = await request.json();

    // Validate required fields
    if (!data.title || !data.category || !data.imageSrc || !data.url) {
      return NextResponse.json(
        { error: 'Missing required fields: title, category, imageSrc, or url' },
        { status: 400 }
      );
    }

    // Create new website portfolio item in database
    const newItem = await prisma.websitePortfolio.create({
      data: {
        title: data.title,
        description: data.description || '',
        category: data.category,
        imageSrc: data.imageSrc,
        url: data.url,
        featured: data.featured || false,
      },
    });

    // Convert to WebsitePortfolioItem type
    const websitePortfolioItem: WebsitePortfolioItem = {
      id: newItem.id,
      title: newItem.title,
      description: newItem.description || '',
      category: newItem.category,
      imageSrc: newItem.imageSrc,
      url: newItem.url,
      featured: newItem.featured,
      createdAt: newItem.createdAt.toISOString(),
      updatedAt: newItem.updatedAt.toISOString(),
    };

    return NextResponse.json(websitePortfolioItem, { status: 201 });
  } catch (error) {
    console.error('Error in POST website portfolio item:', error);
    return NextResponse.json(
      { error: 'Failed to create website portfolio item' },
      { status: 500 }
    );
  }
}
