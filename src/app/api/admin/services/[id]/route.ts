import { NextRequest, NextResponse } from 'next/server';
import * as serviceItemService from '@/services/serviceItemService';

/**
 * GET /api/admin/services/[id]
 * Get a service by ID
 */
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {

    // Get service by ID
    const service = await serviceItemService.getServiceById(params.id);

    if (!service) {
      return NextResponse.json({ error: 'Service not found' }, { status: 404 });
    }

    return NextResponse.json(service);
  } catch (error) {
    console.error(`Error getting service ${params.id}:`, error);
    return NextResponse.json({ error: 'Failed to get service' }, { status: 500 });
  }
}

/**
 * DELETE /api/admin/services/[id]
 * Delete a service
 */
export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {

    // Delete service
    const success = await serviceItemService.deleteService(params.id);

    if (!success) {
      return NextResponse.json({ error: 'Failed to delete service' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(`Error deleting service ${params.id}:`, error);
    return NextResponse.json({ error: 'Failed to delete service' }, { status: 500 });
  }
}
