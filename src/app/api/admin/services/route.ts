import { NextRequest, NextResponse } from 'next/server';
import * as serviceItemService from '@/services/serviceItemService';

/**
 * GET /api/admin/services
 * Get all services or services by category
 */
export async function GET(req: NextRequest) {
  try {

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const category = searchParams.get('category');

    // Get services
    let services;
    if (category) {
      services = await serviceItemService.getServicesByCategory(category);
    } else {
      services = await serviceItemService.getAllServices();
    }

    return NextResponse.json(services);
  } catch (error) {
    console.error('Error getting services:', error);
    return NextResponse.json({ error: 'Failed to get services' }, { status: 500 });
  }
}

/**
 * POST /api/admin/services
 * Create a new service
 */
export async function POST(req: NextRequest) {
  try {

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.name || body.price === undefined || !body.category) {
      return NextResponse.json({ error: 'Name, price, and category are required' }, { status: 400 });
    }

    // Create service
    const service = await serviceItemService.createService({
      name: body.name,
      description: body.description || null,
      price: body.price,
      category: body.category
    });

    return NextResponse.json(service);
  } catch (error) {
    console.error('Error creating service:', error);
    return NextResponse.json({ error: 'Failed to create service' }, { status: 500 });
  }
}

/**
 * PATCH /api/admin/services
 * Update a service
 */
export async function PATCH(req: NextRequest) {
  try {

    // Get request body
    const body = await req.json();

    // Check if id is provided
    if (!body.id) {
      return NextResponse.json({ error: 'ID is required' }, { status: 400 });
    }

    // Update service
    const service = await serviceItemService.updateService(body.id, {
      name: body.name,
      description: body.description,
      price: body.price,
      category: body.category
    });

    return NextResponse.json(service);
  } catch (error) {
    console.error('Error updating service:', error);
    return NextResponse.json({ error: 'Failed to update service' }, { status: 500 });
  }
}
