import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import fs from 'fs';
import { createDatabaseBackup } from '@/services/databaseBackupService';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the description from the query parameters
    const url = new URL(request.url);
    const description = url.searchParams.get('description') || undefined;

    // Create the backup using our service
    const { backup, backupPath } = await createDatabaseBackup(
      description,
      'manual',
      session.user?.name || session.user?.email || 'admin'
    );

    // Read the backup file
    const fileBuffer = await fs.promises.readFile(backupPath);

    // Create response with the file
    const response = new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Disposition': `attachment; filename="${backup.filename}"`,
        'Content-Type': 'application/octet-stream',
      },
    });

    return response;
  } catch (error) {
    console.error('Database backup error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to backup database' },
      { status: 500 }
    );
  }
}
