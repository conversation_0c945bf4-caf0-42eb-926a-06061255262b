import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { restoreDatabaseFromBackup } from '@/services/databaseBackupService';

// Create temp directory if it doesn't exist
const ensureTempDir = async () => {
  const tempDir = path.join(process.cwd(), 'temp');
  if (!fs.existsSync(tempDir)) {
    await fs.promises.mkdir(tempDir, { recursive: true });
  }
  return tempDir;
};

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse the form data to get the uploaded file
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No backup file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    const fileName = file.name.toLowerCase();
    if (!fileName.endsWith('.dump') && !fileName.endsWith('.sql')) {
      return NextResponse.json(
        { error: 'Invalid file type. Only .dump or .sql files are allowed' },
        { status: 400 }
      );
    }

    // Validate file size
    const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: `File size exceeds maximum allowed (${Math.round(file.size / 1024 / 1024)}MB > 100MB)` },
        { status: 400 }
      );
    }

    // Create a temporary file to store the uploaded backup
    const tempDir = await ensureTempDir();
    const tempFilePath = path.join(tempDir, `restore-${uuidv4()}-${file.name}`);

    // Convert the file to a buffer and save it
    const fileBuffer = Buffer.from(await file.arrayBuffer());
    await fs.promises.writeFile(tempFilePath, fileBuffer);

    try {
      // Use our service to restore the database
      const username = session.user?.name || session.user?.email || 'admin';
      const result = await restoreDatabaseFromBackup(tempFilePath, username);

      // Clean up - delete the temporary file
      await fs.promises.unlink(tempFilePath);

      return NextResponse.json({
        message: result.message,
        preRestoreBackupId: result.preRestoreBackupId
      });
    } catch (error) {
      console.error('Database restore error:', error);

      // Clean up - delete the temporary file
      try {
        await fs.promises.unlink(tempFilePath);
      } catch (unlinkError) {
        console.error('Error deleting temp file:', unlinkError);
      }

      throw error;
    }
  } catch (error) {
    console.error('Database restore error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to restore database' },
      { status: 500 }
    );
  }
}
