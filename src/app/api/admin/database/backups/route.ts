import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getDatabaseBackups, getDatabaseBackupById, deleteDatabaseBackup } from '@/services/databaseBackupService';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const type = url.searchParams.get('type') || undefined;
    const id = url.searchParams.get('id');

    // If ID is provided, get a specific backup
    if (id) {
      const backup = await getDatabaseBackupById(id);
      if (!backup) {
        return NextResponse.json({ error: 'Backup not found' }, { status: 404 });
      }
      return NextResponse.json(backup);
    }

    // Otherwise, get all backups
    const backups = await getDatabaseBackups(limit, type);
    return NextResponse.json(backups);
  } catch (error) {
    console.error('Error fetching database backups:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch database backups' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the backup ID from the query parameters
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Backup ID is required' }, { status: 400 });
    }

    // Delete the backup
    const success = await deleteDatabaseBackup(id);

    if (!success) {
      return NextResponse.json({ error: 'Failed to delete backup' }, { status: 500 });
    }

    return NextResponse.json({ message: 'Backup deleted successfully' });
  } catch (error) {
    console.error('Error deleting database backup:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete database backup' },
      { status: 500 }
    );
  }
}
