import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

const s3Client = new S3Client({
  region: process.env.LINODE_REGION || 'us-east-1',
  endpoint: process.env.LINODE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.LINODE_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.LINODE_SECRET_ACCESS_KEY || '',
  }
});

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const command = new ListObjectsV2Command({
      Bucket: process.env.LINODE_BUCKET_NAME,
    });

    const response = await s3Client.send(command);
    
    let totalSize = 0;
    const recentActivity = [];

    if (response.Contents) {
      // Calculate total size
      totalSize = response.Contents.reduce((acc, obj) => acc + (obj.Size || 0), 0);

      // Get recent activity (last 5 uploads)
      const sortedObjects = [...response.Contents].sort((a, b) => 
        (b.LastModified?.getTime() || 0) - (a.LastModified?.getTime() || 0)
      ).slice(0, 5);

      for (const obj of sortedObjects) {
        const timestamp = obj.LastModified?.toISOString() || '';
        const key = obj.Key || '';
        const category = key.split('/')[0];
        
        recentActivity.push({
          type: 'upload',
          message: `Uploaded ${key.split('/').pop()} to ${category}`,
          timestamp: new Date(timestamp).toLocaleString(),
          size: obj.Size
        });
      }
    }

    return NextResponse.json({
      used: totalSize,
      limit: 5 * 1024 * 1024 * 1024, // 5GB
      recentActivity
    });
  } catch (error) {
    console.error('Error fetching storage stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch storage statistics' },
      { status: 500 }
    );
  }
} 