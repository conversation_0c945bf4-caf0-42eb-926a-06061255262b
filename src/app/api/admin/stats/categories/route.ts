import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

const s3Client = new S3Client({
  region: process.env.LINODE_REGION || 'us-east-1',
  endpoint: process.env.LINODE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.LINODE_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.LINODE_SECRET_ACCESS_KEY || '',
  }
});

interface CategoryStats {
  [key: string]: number;
}

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const command = new ListObjectsV2Command({
      Bucket: process.env.LINODE_BUCKET_NAME,
      Delimiter: '/'
    });

    const response = await s3Client.send(command);
    
    // Get unique categories from CommonPrefixes
    const categories = new Set(
      response.CommonPrefixes?.map(prefix => prefix.Prefix?.slice(0, -1)) || []
    );

    // Calculate images per category
    const categoryStats: CategoryStats = {};
    if (response.CommonPrefixes) {
      for (const prefix of response.CommonPrefixes) {
        const categoryName = prefix.Prefix?.slice(0, -1) || '';
        const categoryCommand = new ListObjectsV2Command({
          Bucket: process.env.LINODE_BUCKET_NAME,
          Prefix: prefix.Prefix
        });
        const categoryResponse = await s3Client.send(categoryCommand);
        categoryStats[categoryName] = categoryResponse.Contents?.length || 0;
      }
    }

    return NextResponse.json({
      total: categories.size,
      categories: Array.from(categories),
      stats: categoryStats
    });
  } catch (error) {
    console.error('Error fetching category stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch category statistics' },
      { status: 500 }
    );
  }
} 