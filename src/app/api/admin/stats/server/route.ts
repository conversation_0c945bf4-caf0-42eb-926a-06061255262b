import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getSystemStats } from '@/services/serverStatsService';

/**
 * GET handler for server stats API
 * Returns system statistics including CPU, memory, disk usage, and more
 */
export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get system stats
    const stats = await getSystemStats();

    // Format the data for better readability
    const formattedStats = {
      cpu: {
        ...stats.cpu,
        loadAvg: stats.cpu.loadAvg.map(load => parseFloat(load.toFixed(2))),
      },
      memory: {
        ...stats.memory,
        total: formatBytes(stats.memory.total),
        free: formatBytes(stats.memory.free),
        used: formatBytes(stats.memory.used),
      },
      disk: {
        ...stats.disk,
        total: formatBytes(stats.disk.total),
        free: formatBytes(stats.disk.free),
        used: formatBytes(stats.disk.used),
      },
      uptime: {
        system: formatUptime(stats.uptime.system),
        process: formatUptime(stats.uptime.process),
      },
      os: stats.os,
      network: stats.network,
    };

    return NextResponse.json(formattedStats);
  } catch (error) {
    console.error('Error fetching server statistics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch server statistics' },
      { status: 500 }
    );
  }
}

/**
 * Format bytes to human-readable format
 * @param bytes Number of bytes
 * @returns Formatted string (e.g., "1.5 GB")
 */
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Format uptime to human-readable format
 * @param seconds Uptime in seconds
 * @returns Formatted string (e.g., "5d 2h 30m")
 */
function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / (3600 * 24));
  const hours = Math.floor((seconds % (3600 * 24)) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  let result = '';
  if (days > 0) result += `${days}d `;
  if (hours > 0 || days > 0) result += `${hours}h `;
  result += `${minutes}m`;
  
  return result;
}
