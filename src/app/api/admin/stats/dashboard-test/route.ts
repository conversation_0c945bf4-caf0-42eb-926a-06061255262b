import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET() {
  try {
    // Get blog posts count
    const postsCount = await prisma.blogPost.count();

    // Get categories count
    const categoriesCount = await prisma.category.count();

    // Get unique tags count
    const blogPosts = await prisma.blogPost.findMany({
      select: {
        tags: true,
      },
    });
    
    // Extract all tags and count unique ones
    const allTags = blogPosts.flatMap(post => post.tags);
    const uniqueTags = new Set(allTags);
    const tagsCount = uniqueTags.size;

    // Get authors count (assuming authors are stored in the database)
    // If authors are stored as strings in the blogPost table, we need to count unique authors
    const authors = await prisma.blogPost.findMany({
      select: {
        author: true,
      },
      where: {
        author: {
          not: null,
        },
      },
    });
    
    const uniqueAuthors = new Set(authors.map(a => a.author));
    const authorsCount = uniqueAuthors.size;

    // Return the statistics
    return NextResponse.json({
      posts: postsCount,
      categories: categoriesCount,
      tags: tagsCount,
      authors: authorsCount,
    });
  } catch (error) {
    console.error('Error fetching dashboard statistics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard statistics' },
      { status: 500 }
    );
  }
}
