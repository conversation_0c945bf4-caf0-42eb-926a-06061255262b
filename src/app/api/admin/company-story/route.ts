import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';

// GET - Fetch all company stories or active one
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const activeOnly = searchParams.get('activeOnly') === 'true';

    if (activeOnly) {
      const story = await prisma.companyStory.findFirst({
        where: { isActive: true },
        orderBy: { updatedAt: 'desc' }
      });

      return NextResponse.json(story || null);
    }

    // Check authentication for admin access
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const stories = await prisma.companyStory.findMany({
      orderBy: { updatedAt: 'desc' }
    });

    return NextResponse.json(stories);
  } catch (error) {
    console.error('Error fetching company stories:', error);
    return NextResponse.json({ error: 'Failed to fetch company stories' }, { status: 500 });
  }
}

// POST - Create a new company story
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();

    // Validate required fields
    if (!data.title || !data.subtitle || !data.imageSrc || !data.quote1 || !data.founderName || !data.founderRole) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // If this is set as active, deactivate all other stories
    if (data.isActive) {
      await prisma.companyStory.updateMany({
        where: { isActive: true },
        data: { isActive: false }
      });
    }

    const story = await prisma.companyStory.create({
      data: {
        title: data.title,
        subtitle: data.subtitle,
        imageSrc: data.imageSrc,
        quote1: data.quote1,
        quote2: data.quote2,
        founderName: data.founderName,
        founderRole: data.founderRole,
        linkedinUrl: data.linkedinUrl,
        twitterUrl: data.twitterUrl,
        instagramUrl: data.instagramUrl,
        tiktokUrl: data.tiktokUrl,
        isActive: data.isActive || false
      }
    });

    return NextResponse.json(story);
  } catch (error) {
    console.error('Error creating company story:', error);
    return NextResponse.json({ error: 'Failed to create company story' }, { status: 500 });
  }
}
