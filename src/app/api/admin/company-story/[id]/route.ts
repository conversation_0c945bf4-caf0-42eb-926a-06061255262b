import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';

// GET - Fetch a specific company story by ID
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    const story = await prisma.companyStory.findUnique({
      where: { id }
    });

    if (!story) {
      return NextResponse.json({ error: 'Company story not found' }, { status: 404 });
    }

    return NextResponse.json(story);
  } catch (error) {
    console.error('Error fetching company story:', error);
    return NextResponse.json({ error: 'Failed to fetch company story' }, { status: 500 });
  }
}

// PUT - Update a company story
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;
    const data = await request.json();

    // Validate required fields
    if (!data.title || !data.subtitle || !data.imageSrc || !data.quote1 || !data.founderName || !data.founderRole) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // If this is set as active, deactivate all other stories
    if (data.isActive) {
      await prisma.companyStory.updateMany({
        where: {
          isActive: true,
          id: { not: id }
        },
        data: { isActive: false }
      });
    }

    const story = await prisma.companyStory.update({
      where: { id },
      data: {
        title: data.title,
        subtitle: data.subtitle,
        imageSrc: data.imageSrc,
        quote1: data.quote1,
        quote2: data.quote2,
        founderName: data.founderName,
        founderRole: data.founderRole,
        linkedinUrl: data.linkedinUrl,
        twitterUrl: data.twitterUrl,
        instagramUrl: data.instagramUrl,
        tiktokUrl: data.tiktokUrl,
        isActive: data.isActive || false
      }
    });

    return NextResponse.json(story);
  } catch (error) {
    console.error('Error updating company story:', error);
    return NextResponse.json({ error: 'Failed to update company story' }, { status: 500 });
  }
}

// DELETE - Delete a company story
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    await prisma.companyStory.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting company story:', error);
    return NextResponse.json({ error: 'Failed to delete company story' }, { status: 500 });
  }
}
