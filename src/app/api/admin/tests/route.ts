import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { z } from 'zod';
import {
  TestType,
  testS3Connection,
  testS3Upload,
  testDatabaseConnection,
  testComponents,
  testApiEndpoints,
  testOpenAI
} from '@/utils/testUtils';

// Validation schema for test request
const TestRequestSchema = z.object({
  type: z.nativeEnum(TestType),
  config: z.object({
    region: z.string().optional(),
    endpoint: z.string().optional(),
    bucketName: z.string().optional(),
    accessKeyId: z.string().optional(),
    secretAccessKey: z.string().optional(),
  }).optional(),
});

/**
 * POST handler for running tests
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if the user has permission to run tests
    // Only admins can run tests for now
    if (session.user.role?.name !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = TestRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { type, config } = validationResult.data;

    // Run the appropriate test
    let result;

    switch (type) {
      case TestType.S3_CONNECTION:
        result = await testS3Connection(config);
        break;
      case TestType.S3_UPLOAD:
        result = await testS3Upload(config);
        break;
      case TestType.DATABASE:
        result = await testDatabaseConnection();
        break;
      case TestType.COMPONENTS:
        result = await testComponents();
        break;
      case TestType.API:
        result = await testApiEndpoints();
        break;
      case TestType.OPENAI:
        result = await testOpenAI();
        break;
      default:
        return NextResponse.json(
          { error: `Test type '${type}' not implemented` },
          { status: 400 }
        );
    }

    // Return the test result
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error running test:', error);
    return NextResponse.json(
      { error: 'Internal Server Error', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * GET handler for getting available test types
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if the user has permission to view tests
    // Only admins can view tests for now
    if (session.user.role?.name !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Return available test types
    return NextResponse.json({
      testTypes: Object.values(TestType),
      descriptions: {
        [TestType.S3_CONNECTION]: 'Test connection to S3 storage',
        [TestType.S3_UPLOAD]: 'Test uploading a file to S3 storage',
        [TestType.DATABASE]: 'Test database connection',
        [TestType.COMPONENTS]: 'Test UI components',
        [TestType.API]: 'Test API endpoints',
        [TestType.OPENAI]: 'Test OpenAI integration',
      }
    });
  } catch (error: any) {
    console.error('Error getting test types:', error);
    return NextResponse.json(
      { error: 'Internal Server Error', message: error.message },
      { status: 500 }
    );
  }
}
