import { NextRequest, NextResponse } from 'next/server';
import * as transactionService from '@/services/transactionService';

type Params = { params: { id: string } };

/**
 * GET /api/admin/transactions/[id]
 * Get a transaction by ID
 */
export async function GET(
  request: NextRequest,
  context: Params
) {
  try {
    // Extract the ID from the context
    const id = context.params.id;

    // Get transaction by ID
    const transaction = await transactionService.getTransactionById(id);

    if (!transaction) {
      return NextResponse.json({ error: 'Transaction not found' }, { status: 404 });
    }

    return NextResponse.json(transaction);
  } catch (error) {
    console.error('Error getting transaction:', error);
    return NextResponse.json({ error: 'Failed to get transaction' }, { status: 500 });
  }
}
