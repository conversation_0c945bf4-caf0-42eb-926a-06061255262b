import { NextRequest, NextResponse } from 'next/server';
import * as transactionService from '@/services/transactionService';

/**
 * POST /api/admin/transactions/batch
 * Create multiple transactions from messages
 */
export async function POST(req: NextRequest) {
  try {
    // Get request body
    const body = await req.json();

    // Check if messages are provided
    if (!body.messages || !Array.isArray(body.messages) || body.messages.length === 0) {
      return NextResponse.json({ error: 'Messages array is required' }, { status: 400 });
    }

    // Create transactions from messages
    const transactions = await transactionService.createTransactionsFromMessages(body.messages);

    if (transactions.length === 0) {
      return NextResponse.json({ error: 'Failed to parse any transaction messages' }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      count: transactions.length,
      transactions
    });
  } catch (error) {
    console.error('Error creating transactions:', error);
    return NextResponse.json({ error: 'Failed to create transactions' }, { status: 500 });
  }
}
