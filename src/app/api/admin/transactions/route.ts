import { NextRequest, NextResponse } from 'next/server';
import * as transactionService from '@/services/transactionService';
import prisma from '@/lib/prisma';

/**
 * GET /api/admin/transactions
 * Get all transactions
 */
export async function GET(req: NextRequest) {
  try {
    // Get all transactions
    const transactions = await transactionService.getAllTransactions();

    return NextResponse.json(transactions);
  } catch (error) {
    console.error('Error getting transactions:', error);
    return NextResponse.json({ error: 'Failed to get transactions' }, { status: 500 });
  }
}

/**
 * POST /api/admin/transactions
 * Create a new transaction from a message or direct transaction data
 */
export async function POST(req: NextRequest) {
  try {
    // Get request body
    const body = await req.json();

    let transaction = null;

    // Check if this is a raw message or direct transaction data
    if (body.message) {
      // Create transaction from message
      transaction = await transactionService.createTransactionFromMessage(body.message);
    } else if (body.transactionId && body.rawMessage) {
      // Direct transaction data provided
      // Create transaction directly using the provided data
      const existingTransaction = await prisma.transaction.findUnique({
        where: {
          transactionId: body.transactionId
        }
      });

      if (existingTransaction) {
        transaction = await transactionService.getTransactionByTransactionId(body.transactionId);
      } else {
        // Create the transaction
        const newTransaction = await prisma.transaction.create({
          data: {
            transactionId: body.transactionId,
            amount: body.amount,
            customerName: body.customerName,
            phoneNumber: body.phoneNumber,
            transactionDate: body.transactionDate,
            rawMessage: body.rawMessage,
            status: body.status || 'pending'
          }
        });

        transaction = await transactionService.getTransactionById(newTransaction.id);
      }
    } else {
      return NextResponse.json({ error: 'Either message or transaction data is required' }, { status: 400 });
    }

    if (!transaction) {
      return NextResponse.json({ error: 'Failed to create transaction' }, { status: 400 });
    }

    return NextResponse.json(transaction);
  } catch (error) {
    console.error('Error creating transaction:', error);
    return NextResponse.json({ error: 'Failed to create transaction' }, { status: 500 });
  }
}

/**
 * PATCH /api/admin/transactions
 * Update a transaction's status
 */
export async function PATCH(req: NextRequest) {
  try {
    // Get request body
    const body = await req.json();

    // Check if id and status are provided
    if (!body.id || !body.status) {
      return NextResponse.json({ error: 'ID and status are required' }, { status: 400 });
    }

    // Update transaction status
    const transaction = await transactionService.updateTransactionStatus(body.id, body.status);

    return NextResponse.json(transaction);
  } catch (error) {
    console.error('Error updating transaction:', error);
    return NextResponse.json({ error: 'Failed to update transaction' }, { status: 500 });
  }
}
