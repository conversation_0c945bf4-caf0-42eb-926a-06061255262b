import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';

/**
 * Diagnostic endpoint to test form data processing
 * This endpoint will receive form data and return information about what was received
 * without actually saving anything to the database or S3
 */
export async function POST(request: NextRequest) {
  console.log('POST /api/admin/diagnostic/form-upload - Starting diagnostic');
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      console.log('POST /api/admin/diagnostic/form-upload - Authentication failed');
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }
    console.log('POST /api/admin/diagnostic/form-upload - Authentication successful');

    // Parse form data
    const formData = await request.formData();
    console.log('POST /api/admin/diagnostic/form-upload - Received form data');
    
    // Extract all form fields
    const fields: Record<string, any> = {};
    const fieldNames: string[] = [];
    
    // Process each form field
    for (const [key, value] of formData.entries()) {
      fieldNames.push(key);
      
      if (value instanceof File) {
        fields[key] = {
          type: 'File',
          name: value.name,
          size: value.size,
          contentType: value.type,
          lastModified: value.lastModified
        };
      } else {
        fields[key] = {
          type: 'String',
          value: value,
          length: String(value).length
        };
      }
    }
    
    console.log('POST /api/admin/diagnostic/form-upload - Form data processed', { fieldNames });
    
    // Return diagnostic information
    return NextResponse.json({
      success: true,
      message: 'Form data received and processed successfully',
      fieldNames,
      fields,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in form upload diagnostic:', error);
    const errorMessage = error instanceof Error 
      ? `Error: ${error.message}` 
      : 'Unknown error occurred';
    return new NextResponse(JSON.stringify({ error: errorMessage }), {
      status: 500,
    });
  }
}
