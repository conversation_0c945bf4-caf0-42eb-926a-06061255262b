import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { hashPassword, logActivity } from '@/utils/passwordUtils';
import { z } from 'zod';

// Validation schema for user creation/update
const UserSchema = z.object({
  username: z.string().min(3).max(50),
  email: z.string().email().max(100),
  name: z.string().min(2).max(100).optional(),
  password: z.string().min(8).max(100).optional(),
  roleId: z.string().uuid(),
  active: z.boolean().default(true),
});

// GET handler - Get all users
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Check if the user has permission to view users
    if (session.user.role?.name !== 'admin' && !session.user.role?.permissions.includes('users:read')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get users from database (exclude password hash)
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        active: true,
        lastLogin: true,
        createdAt: true,
        updatedAt: true,
        roleId: true,
        role: {
          select: {
            id: true,
            name: true,
            description: true,
          }
        }
      },
      orderBy: { username: 'asc' }
    });

    return NextResponse.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// POST handler - Create a new user
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Check if the user has permission to create users
    if (session.user.role?.name !== 'admin' && !session.user.role?.permissions.includes('users:write')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse request body
    const data = await request.json();
    
    // Validate input
    const validationResult = UserSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Check if username or email already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: data.username },
          { email: data.email }
        ]
      }
    });
    
    if (existingUser) {
      return NextResponse.json(
        { error: 'Username or email already exists' },
        { status: 400 }
      );
    }
    
    // Check if role exists
    const role = await prisma.role.findUnique({
      where: { id: data.roleId }
    });
    
    if (!role) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 400 }
      );
    }
    
    // Hash password
    if (!data.password) {
      return NextResponse.json(
        { error: 'Password is required' },
        { status: 400 }
      );
    }
    
    const passwordHash = await hashPassword(data.password);
    
    // Create user
    const user = await prisma.user.create({
      data: {
        username: data.username,
        email: data.email,
        name: data.name,
        passwordHash,
        roleId: data.roleId,
        active: data.active
      },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        active: true,
        roleId: true,
        createdAt: true
      }
    });
    
    // Log activity
    await logActivity(
      session.user.id,
      'user_created',
      `User ${data.username} created`,
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      request.headers.get('user-agent') || 'unknown',
      'user',
      user.id
    );
    
    return NextResponse.json(user, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
