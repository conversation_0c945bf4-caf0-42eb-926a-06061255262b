import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { hashPassword, logActivity } from '@/utils/passwordUtils';
import { z } from 'zod';

// Validation schema for user update
const UserUpdateSchema = z.object({
  username: z.string().min(3).max(50).optional(),
  email: z.string().email().max(100).optional(),
  name: z.string().min(2).max(100).optional().nullable(),
  password: z.string().min(8).max(100).optional(),
  roleId: z.string().uuid().optional(),
  active: z.boolean().optional(),
});

// GET handler - Get a specific user
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Check if the user has permission to view users
    if (session.user.role?.name !== 'admin' && !session.user.role?.permissions.includes('users:read')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get user from database (exclude password hash)
    const user = await prisma.user.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        active: true,
        lastLogin: true,
        createdAt: true,
        updatedAt: true,
        roleId: true,
        role: {
          select: {
            id: true,
            name: true,
            description: true,
            permissions: true,
          }
        }
      }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// PUT handler - Update a user
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Check if the user has permission to update users
    if (session.user.role?.name !== 'admin' && !session.user.role?.permissions.includes('users:write')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse request body
    const data = await request.json();
    
    // Validate input
    const validationResult = UserUpdateSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id }
    });
    
    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Check if username or email already exists (if changing)
    if (data.username || data.email) {
      const duplicateUser = await prisma.user.findFirst({
        where: {
          OR: [
            data.username ? { username: data.username } : {},
            data.email ? { email: data.email } : {}
          ],
          NOT: { id: params.id }
        }
      });
      
      if (duplicateUser) {
        return NextResponse.json(
          { error: 'Username or email already exists' },
          { status: 400 }
        );
      }
    }
    
    // Check if role exists (if changing)
    if (data.roleId) {
      const role = await prisma.role.findUnique({
        where: { id: data.roleId }
      });
      
      if (!role) {
        return NextResponse.json(
          { error: 'Role not found' },
          { status: 400 }
        );
      }
    }
    
    // Prepare update data
    const updateData: any = {
      username: data.username,
      email: data.email,
      name: data.name,
      roleId: data.roleId,
      active: data.active
    };
    
    // Only include defined fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key] === undefined) {
        delete updateData[key];
      }
    });
    
    // Hash password if provided
    if (data.password) {
      updateData.passwordHash = await hashPassword(data.password);
    }
    
    // Update user
    const user = await prisma.user.update({
      where: { id: params.id },
      data: updateData,
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        active: true,
        roleId: true,
        updatedAt: true
      }
    });
    
    // Log activity
    await logActivity(
      session.user.id,
      'user_updated',
      `User ${user.username} updated`,
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      request.headers.get('user-agent') || 'unknown',
      'user',
      user.id
    );
    
    return NextResponse.json(user);
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// DELETE handler - Delete a user
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Check if the user has permission to delete users
    if (session.user.role?.name !== 'admin' && !session.user.role?.permissions.includes('users:write')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    
    // Prevent deleting the last admin user
    const user = await prisma.user.findUnique({
      where: { id: params.id },
      include: { role: true }
    });
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    if (user.role.name === 'admin') {
      const adminCount = await prisma.user.count({
        where: {
          role: { name: 'admin' },
          active: true
        }
      });
      
      if (adminCount <= 1) {
        return NextResponse.json(
          { error: 'Cannot delete the last admin user' },
          { status: 400 }
        );
      }
    }
    
    // Delete user
    await prisma.user.delete({
      where: { id: params.id }
    });
    
    // Log activity
    await logActivity(
      session.user.id,
      'user_deleted',
      `User ${user.username} deleted`,
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      request.headers.get('user-agent') || 'unknown',
      'user',
      params.id
    );
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
