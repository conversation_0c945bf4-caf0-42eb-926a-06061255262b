import { NextRequest, NextResponse } from 'next/server';
import * as quoteService from '@/services/quoteService';

/**
 * GET /api/admin/quotes/[id]
 * Get a quote by ID
 */
export async function GET(req: NextRequest, context: { params: { id: string } }) {
  try {
    // Get quote by ID
    const quote = await quoteService.getQuoteById(context.params.id);

    if (!quote) {
      return NextResponse.json({ error: 'Quote not found' }, { status: 404 });
    }

    return NextResponse.json(quote);
  } catch (error) {
    console.error(`Error getting quote ${context.params.id}:`, error);
    return NextResponse.json({ error: 'Failed to get quote' }, { status: 500 });
  }
}
