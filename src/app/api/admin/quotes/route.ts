import { NextRequest, NextResponse } from 'next/server';
import * as quoteService from '@/services/quoteService';

/**
 * GET /api/admin/quotes
 * Get all quotes
 */
export async function GET(req: NextRequest) {
  try {
    // Get all quotes
    const quotes = await quoteService.getAllQuotes();

    return NextResponse.json(quotes);
  } catch (error) {
    console.error('Error getting quotes:', error);
    return NextResponse.json({ error: 'Failed to get quotes' }, { status: 500 });
  }
}

/**
 * POST /api/admin/quotes
 * Create a new quote
 */
export async function POST(req: NextRequest) {
  try {
    console.log('POST /api/admin/quotes - Starting quote creation');

    // Get request body
    const body = await req.json();
    console.log('Quote request body:', JSON.stringify(body));

    // Validate required fields
    if (!body.customerName || !body.phoneNumber || !body.items || !Array.isArray(body.items) || body.items.length === 0) {
      console.log('Quote validation failed: Missing required fields');
      return NextResponse.json({ error: 'Customer name, phone number, and items are required' }, { status: 400 });
    }

    // Validate items
    for (const item of body.items) {
      if (!item.serviceId || !item.quantity) {
        console.log('Quote validation failed: Missing item fields');
        return NextResponse.json({ error: 'Each item must have a service ID and quantity' }, { status: 400 });
      }
    }

    try {
      // Create quote
      console.log('Creating quote for customer:', body.customerName);
      const quote = await quoteService.createQuote({
        customerName: body.customerName,
        phoneNumber: body.phoneNumber,
        email: body.email,
        notes: body.notes,
        validUntil: body.validUntil ? new Date(body.validUntil) : undefined,
        items: body.items
      });

      if (!quote) {
        console.log('Quote creation failed: No quote returned from service');
        return NextResponse.json({ error: 'Failed to create quote' }, { status: 500 });
      }

      console.log('Quote created successfully:', quote.id);
      return NextResponse.json(quote);
    } catch (serviceError) {
      console.error('Error in quote service:', serviceError);
      return NextResponse.json({ error: serviceError.message || 'Failed to create quote' }, { status: 500 });
    }
  } catch (error) {
    console.error('Error creating quote:', error);
    return NextResponse.json({ error: 'Failed to create quote' }, { status: 500 });
  }
}

/**
 * PATCH /api/admin/quotes
 * Update a quote's status
 */
export async function PATCH(req: NextRequest) {
  try {
    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.id || !body.status) {
      return NextResponse.json({ error: 'Quote ID and status are required' }, { status: 400 });
    }

    // Update quote status
    const quote = await quoteService.updateQuoteStatus(body.id, body.status);

    if (!quote) {
      return NextResponse.json({ error: 'Quote not found' }, { status: 404 });
    }

    return NextResponse.json(quote);
  } catch (error) {
    console.error('Error updating quote status:', error);
    return NextResponse.json({ error: 'Failed to update quote status' }, { status: 500 });
  }
}
