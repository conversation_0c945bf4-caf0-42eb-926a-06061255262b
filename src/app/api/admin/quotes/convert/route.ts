import { NextRequest, NextResponse } from 'next/server';
import * as quoteService from '@/services/quoteService';

/**
 * POST /api/admin/quotes/convert
 * Convert a quote to an invoice
 */
export async function POST(req: NextRequest) {
  try {
    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.quoteId) {
      return NextResponse.json({ error: 'Quote ID is required' }, { status: 400 });
    }

    // Convert quote to invoice
    try {
      const invoice = await quoteService.convertQuoteToInvoice(body.quoteId);
      return NextResponse.json(invoice);
    } catch (error) {
      console.error('Error converting quote to invoice:', error);
      return NextResponse.json({ error: error.message || 'Failed to convert quote to invoice' }, { status: 500 });
    }
  } catch (error) {
    console.error('Error converting quote to invoice:', error);
    return NextResponse.json({ error: 'Failed to convert quote to invoice' }, { status: 500 });
  }
}
