import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { logActivity } from '@/utils/passwordUtils';
import { z } from 'zod';

// Validation schema for role update
const RoleUpdateSchema = z.object({
  name: z.string().min(2).max(50).optional(),
  description: z.string().max(255).optional().nullable(),
  permissions: z.array(z.string()).optional(),
});

// GET handler - Get a specific role
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Check if the user has permission to view roles
    if (session.user.role?.name !== 'admin' && !session.user.role?.permissions.includes('roles:read')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get role from database
    const role = await prisma.role.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: { users: true }
        }
      }
    });

    if (!role) {
      return NextResponse.json({ error: 'Role not found' }, { status: 404 });
    }

    return NextResponse.json(role);
  } catch (error) {
    console.error('Error fetching role:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// PUT handler - Update a role
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Check if the user has permission to update roles
    if (session.user.role?.name !== 'admin' && !session.user.role?.permissions.includes('roles:write')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse request body
    const data = await request.json();
    
    // Validate input
    const validationResult = RoleUpdateSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Check if role exists
    const existingRole = await prisma.role.findUnique({
      where: { id: params.id }
    });
    
    if (!existingRole) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      );
    }
    
    // Prevent modifying built-in admin role permissions
    if (existingRole.name === 'admin' && data.permissions) {
      return NextResponse.json(
        { error: 'Cannot modify admin role permissions' },
        { status: 400 }
      );
    }
    
    // Check if role name already exists (if changing)
    if (data.name && data.name !== existingRole.name) {
      const duplicateRole = await prisma.role.findFirst({
        where: {
          name: data.name,
          NOT: { id: params.id }
        }
      });
      
      if (duplicateRole) {
        return NextResponse.json(
          { error: 'Role name already exists' },
          { status: 400 }
        );
      }
    }
    
    // Prepare update data
    const updateData: any = {
      name: data.name,
      description: data.description,
      permissions: data.permissions
    };
    
    // Only include defined fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key] === undefined) {
        delete updateData[key];
      }
    });
    
    // Update role
    const role = await prisma.role.update({
      where: { id: params.id },
      data: updateData
    });
    
    // Log activity
    await logActivity(
      session.user.id,
      'role_updated',
      `Role ${role.name} updated`,
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      request.headers.get('user-agent') || 'unknown',
      'role',
      role.id
    );
    
    return NextResponse.json(role);
  } catch (error) {
    console.error('Error updating role:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// DELETE handler - Delete a role
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Check if the user has permission to delete roles
    if (session.user.role?.name !== 'admin' && !session.user.role?.permissions.includes('roles:write')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    
    // Get role
    const role = await prisma.role.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: { users: true }
        }
      }
    });
    
    if (!role) {
      return NextResponse.json({ error: 'Role not found' }, { status: 404 });
    }
    
    // Prevent deleting built-in roles
    if (['admin', 'editor', 'viewer'].includes(role.name)) {
      return NextResponse.json(
        { error: 'Cannot delete built-in roles' },
        { status: 400 }
      );
    }
    
    // Check if role is in use
    if (role._count.users > 0) {
      return NextResponse.json(
        { error: 'Cannot delete role that is assigned to users' },
        { status: 400 }
      );
    }
    
    // Delete role
    await prisma.role.delete({
      where: { id: params.id }
    });
    
    // Log activity
    await logActivity(
      session.user.id,
      'role_deleted',
      `Role ${role.name} deleted`,
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      request.headers.get('user-agent') || 'unknown',
      'role',
      params.id
    );
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting role:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
