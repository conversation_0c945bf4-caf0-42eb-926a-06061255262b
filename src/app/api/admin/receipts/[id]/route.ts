import { NextRequest, NextResponse } from 'next/server';
import * as receiptService from '@/services/receiptService';

/**
 * GET /api/admin/receipts/[id]
 * Get a receipt by ID
 */
export async function GET(req: NextRequest, context: { params: { id: string } }) {
  try {
    // Get receipt by ID
    const receipt = await receiptService.getReceiptById(context.params.id);

    if (!receipt) {
      return NextResponse.json({ error: 'Receipt not found' }, { status: 404 });
    }

    return NextResponse.json(receipt);
  } catch (error) {
    console.error(`Error getting receipt ${context.params.id}:`, error);
    return NextResponse.json({ error: 'Failed to get receipt' }, { status: 500 });
  }
}
