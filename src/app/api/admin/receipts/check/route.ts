import { NextRequest, NextResponse } from 'next/server';
import * as receiptService from '@/services/receiptService';

/**
 * POST /api/admin/receipts/check
 * Check if a receipt exists for a given M-Pesa transaction ID
 */
export async function POST(req: NextRequest) {
  try {
    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.mpesaTransactionId) {
      return NextResponse.json({ error: 'M-Pesa transaction ID is required' }, { status: 400 });
    }

    // Check if a receipt exists for this M-Pesa transaction ID
    const existingReceipt = await receiptService.checkReceiptByMpesaTransactionId(body.mpesaTransactionId);

    if (existingReceipt) {
      return NextResponse.json({
        exists: true,
        receiptId: existingReceipt.id,
        receiptNumber: existingReceipt.receiptNumber
      });
    }

    return NextResponse.json({ exists: false });
  } catch (error) {
    console.error('Error checking receipt:', error);
    return NextResponse.json({ error: 'Failed to check receipt' }, { status: 500 });
  }
}
