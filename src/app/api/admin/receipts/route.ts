import { NextRequest, NextResponse } from 'next/server';
import * as receiptService from '@/services/receiptService';
import prisma from '@/lib/prisma';

/**
 * GET /api/admin/receipts
 * Get all receipts
 */
export async function GET(req: NextRequest) {
  try {

    // Get all receipts
    const receipts = await receiptService.getAllReceipts();

    return NextResponse.json(receipts);
  } catch (error) {
    console.error('Error getting receipts:', error);
    return NextResponse.json({ error: 'Failed to get receipts' }, { status: 500 });
  }
}

/**
 * POST /api/admin/receipts
 * Create a new receipt
 */
export async function POST(req: NextRequest) {
  try {
    console.log('POST /api/admin/receipts - Starting receipt creation');

    // Get request body
    const body = await req.json();
    console.log('Receipt request body:', JSON.stringify(body));

    // Validate required fields
    if (!body.transactionId || !body.items || !Array.isArray(body.items) || body.items.length === 0) {
      console.log('Receipt validation failed: Missing transaction ID or items');
      return NextResponse.json({ error: 'Transaction ID and items are required' }, { status: 400 });
    }

    // Validate items
    for (const item of body.items) {
      if (!item.serviceId || item.quantity === undefined || item.quantity <= 0) {
        console.log('Receipt validation failed: Invalid item', item);
        return NextResponse.json({ error: 'Each item must have a service ID and quantity' }, { status: 400 });
      }
    }

    try {
      // Create receipt
      console.log('Creating receipt for transaction:', body.transactionId);
      const receipt = await receiptService.createReceipt({
        transactionId: body.transactionId,
        customerName: body.customerName,
        phoneNumber: body.phoneNumber,
        email: body.email,
        notes: body.notes,
        items: body.items
      });

      if (!receipt) {
        console.log('Receipt creation failed: No receipt returned from service');
        return NextResponse.json({ error: 'Failed to create receipt' }, { status: 500 });
      }

      console.log('Receipt created successfully:', receipt.id);
      return NextResponse.json(receipt);
    } catch (serviceError) {
      console.error('Error in receipt service:', serviceError);

      // Check for specific error types
      if (serviceError.code === 'P2002') {
        // Check if the error is related to the transactionId or receiptNumber
        const isTransactionIdError = serviceError.meta?.target?.includes('transactionId');
        const isReceiptNumberError = serviceError.meta?.target?.includes('receiptNumber');

        // Try to find the existing receipt for this transaction
        try {
          const existingReceipt = await prisma.receipt.findFirst({
            where: {
              transactionId: body.transactionId
            }
          });

          if (existingReceipt) {
            return NextResponse.json({
              error: 'A receipt with this transaction ID already exists',
              details: `Receipt number ${existingReceipt.receiptNumber} was already created for this transaction.`,
              receiptId: existingReceipt.id
            }, { status: 409 });
          }
        } catch (lookupError) {
          console.error('Error looking up existing receipt:', lookupError);
        }

        // Default message if we can't find the specific receipt
        return NextResponse.json({
          error: 'A receipt with this transaction ID already exists',
          details: isTransactionIdError
            ? 'This transaction has already been processed with a receipt.'
            : (isReceiptNumberError
                ? 'A receipt with this number already exists.'
                : serviceError.message)
        }, { status: 409 });
      }

      throw serviceError; // Re-throw to be caught by the outer catch
    }
  } catch (error) {
    console.error('Error creating receipt:', error);

    // Provide more detailed error message if available
    const errorMessage = error instanceof Error ? error.message : 'Failed to create receipt';
    return NextResponse.json({
      error: 'Failed to create receipt',
      details: errorMessage
    }, { status: 500 });
  }
}
