import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { z } from 'zod';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { logActivity } from '@/utils/passwordUtils';

// Validation schema for site scripts
const SiteScriptSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  scriptType: z.enum(['head', 'body', 'footer']),
  content: z.string().min(1),
  isActive: z.boolean().default(true),
  position: z.number().int().default(0),
});

/**
 * GET handler for individual script API
 * Returns a specific script by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get script from database
    const script = await prisma.siteScript.findUnique({
      where: { id },
    });

    if (!script) {
      return NextResponse.json({ error: 'Script not found' }, { status: 404 });
    }

    return NextResponse.json(script);
  } catch (error) {
    console.error('Error fetching script:', error);
    return NextResponse.json(
      { error: 'Failed to fetch script' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for individual script API
 * Updates a specific script by ID
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Check if script exists
    const existingScript = await prisma.siteScript.findUnique({
      where: { id },
    });

    if (!existingScript) {
      return NextResponse.json({ error: 'Script not found' }, { status: 404 });
    }

    // Parse request body
    const data = await request.json();

    // Validate data
    const validatedData = SiteScriptSchema.parse(data);

    // Update script in database
    const updatedScript = await prisma.siteScript.update({
      where: { id },
      data: validatedData,
    });

    // Log activity
    if (session.user?.id) {
      await logActivity(
        session.user.id,
        'update',
        `Updated script: ${updatedScript.name}`,
        request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        request.headers.get('user-agent') || 'unknown',
        'script',
        updatedScript.id
      );
    }

    return NextResponse.json(updatedScript);
  } catch (error) {
    console.error('Error updating script:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to update script' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for individual script API
 * Deletes a specific script by ID
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Check if script exists
    const existingScript = await prisma.siteScript.findUnique({
      where: { id },
    });

    if (!existingScript) {
      return NextResponse.json({ error: 'Script not found' }, { status: 404 });
    }

    // Delete script from database
    await prisma.siteScript.delete({
      where: { id },
    });

    // Log activity
    if (session.user?.id) {
      await logActivity(
        session.user.id,
        'delete',
        `Deleted script: ${existingScript.name}`,
        request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        request.headers.get('user-agent') || 'unknown',
        'script',
        id
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting script:', error);
    return NextResponse.json(
      { error: 'Failed to delete script' },
      { status: 500 }
    );
  }
}
