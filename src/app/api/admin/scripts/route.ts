import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { z } from 'zod';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { logActivity } from '@/utils/passwordUtils';

// Validation schema for site scripts
const SiteScriptSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  scriptType: z.enum(['head', 'body', 'footer']),
  content: z.string().min(1),
  isActive: z.boolean().default(true),
  position: z.number().int().default(0),
});

/**
 * GET handler for scripts API
 * Returns all scripts
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get scripts from database
    const scripts = await prisma.siteScript.findMany({
      orderBy: [
        { scriptType: 'asc' },
        { position: 'asc' },
        { name: 'asc' }
      ],
    });

    return NextResponse.json(scripts);
  } catch (error) {
    console.error('Error fetching scripts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch scripts' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for scripts API
 * Creates a new script
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const data = await request.json();

    // Validate data
    const validatedData = SiteScriptSchema.parse(data);

    // Create script in database
    const script = await prisma.siteScript.create({
      data: validatedData,
    });

    // Log activity
    if (session.user?.id) {
      await logActivity(
        session.user.id,
        'create',
        `Created script: ${script.name}`,
        request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        request.headers.get('user-agent') || 'unknown',
        'script',
        script.id
      );
    }

    return NextResponse.json(script, { status: 201 });
  } catch (error) {
    console.error('Error creating script:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to create script' },
      { status: 500 }
    );
  }
}
