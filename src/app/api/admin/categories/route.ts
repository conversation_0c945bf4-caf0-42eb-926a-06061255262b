import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import slugify from 'slugify';
import * as categoryService from '@/services/categoryService';

export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    const categories = await categoryService.getAllCategories();
    
    // Add cache control headers to prevent caching
    return NextResponse.json(categories, {
      headers: {
        'Cache-Control': 'no-store, max-age=0, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}

export async function POST(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    // Get request body
    const body = await request.json();
    const { name, description } = body;

    if (!name) {
      return new NextResponse(JSON.stringify({ error: 'Name is required' }), {
        status: 400,
      });
    }

    // Generate slug from name
    const slug = slugify(name, { lower: true, strict: true });

    // Check if category with same slug exists
    const existingCategory = await categoryService.getCategoryBySlug(slug);

    if (existingCategory) {
      return new NextResponse(JSON.stringify({ error: 'Category already exists' }), {
        status: 400,
      });
    }

    // Create category
    const newCategory = await categoryService.createCategory({
      name,
      slug,
      description
    });

    return NextResponse.json(newCategory);
  } catch (error) {
    console.error('Error creating category:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}