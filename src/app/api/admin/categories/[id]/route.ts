import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import slugify from 'slugify';
import * as categoryService from '@/services/categoryService';

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    // Await params before accessing properties
    const id = params.id;
    const category = await categoryService.getCategoryById(id);

    if (!category) {
      return new NextResponse(JSON.stringify({ error: 'Category not found' }), {
        status: 404,
      });
    }

    // Add cache control headers
    return NextResponse.json(category, {
      headers: {
        'Cache-Control': 'no-store, max-age=0, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error(`Error fetching category ${params.id}:`, error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    // Await params before accessing properties
    const id = params.id;
    const body = await request.json();
    const { name, description } = body;

    if (!name) {
      return new NextResponse(JSON.stringify({ error: 'Name is required' }), {
        status: 400,
      });
    }

    // Generate slug from name
    const slug = slugify(name, { lower: true, strict: true });

    // Check if another category with the same slug exists (excluding this one)
    const existingCategory = await categoryService.getCategoryBySlug(slug);
    if (existingCategory && existingCategory.id !== id) {
      return new NextResponse(JSON.stringify({ error: 'Category with this name already exists' }), {
        status: 400,
      });
    }

    // Update the category
    const updatedCategory = await categoryService.updateCategory(id, {
      name,
      slug,
      description,
    });

    if (!updatedCategory) {
      return new NextResponse(JSON.stringify({ error: 'Category not found' }), {
        status: 404,
      });
    }

    // Add cache control headers
    return NextResponse.json(updatedCategory, {
      headers: {
        'Cache-Control': 'no-store, max-age=0, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error('Error updating category:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    // Await params before accessing properties
    const id = params.id;
    const success = await categoryService.deleteCategory(id);

    if (!success) {
      return new NextResponse(JSON.stringify({ error: 'Failed to delete category' }), {
        status: 500,
      });
    }

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error('Error deleting category:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}