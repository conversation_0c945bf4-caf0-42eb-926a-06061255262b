import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// Initialize S3 client with the correct environment variables
const s3Client = new S3Client({
  region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
  endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
  credentials: {
    accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
    secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
  },
  forcePathStyle: true // Required for Linode Object Storage
});

export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Log environment variables for debugging
    console.log('S3 Config:', {
      region: process.env.NEXT_PUBLIC_S3_REGION,
      endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT,
      bucket: process.env.NEXT_PUBLIC_S3_BUCKET,
    });

    // Verify required environment variables
    if (!process.env.NEXT_PUBLIC_S3_BUCKET) {
      console.error('Missing required environment variables');
      return NextResponse.json({ error: 'Storage configuration error' }, { status: 500 });
    }

    // List objects in the bucket
    const command = new ListObjectsV2Command({
      Bucket: process.env.NEXT_PUBLIC_S3_BUCKET,
      Prefix: 'images/portfolio/', // Only get images from the portfolio directory
    });

    const response = await s3Client.send(command);
    
    if (!response.Contents) {
      return NextResponse.json([]);
    }

    // Filter and format the images
    const images = response.Contents
      .filter(obj => {
        // Only include files with image extensions
        const key = obj.Key || '';
        return /\.(jpg|jpeg|png|gif)$/i.test(key);
      })
      .map(obj => {
        const key = obj.Key || '';
        // Extract category from the path (second part after /portfolio/)
        const pathParts = key.split('/');
        const category = pathParts.length > 2 ? pathParts[2] : 'uncategorized';
        // Extract filename (last part of the key)
        const filename = pathParts[pathParts.length - 1];
        
        return {
          key,
          url: `${process.env.NEXT_PUBLIC_S3_ENDPOINT}/${process.env.NEXT_PUBLIC_S3_BUCKET}/${key}`,
          category,
          filename,
          lastModified: obj.LastModified?.toISOString()
        };
      })
      .sort((a, b) => {
        // Sort by last modified date, most recent first
        const dateA = a.lastModified ? new Date(a.lastModified).getTime() : 0;
        const dateB = b.lastModified ? new Date(b.lastModified).getTime() : 0;
        return dateB - dateA;
      });

    return NextResponse.json(images);
  } catch (error) {
    console.error('Error fetching images:', error);
    return NextResponse.json({ error: 'Failed to fetch images' }, { status: 500 });
  }
} 