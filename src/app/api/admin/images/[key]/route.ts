import { NextRequest, NextResponse } from 'next/server';
import { S3Client, DeleteObjectCommand, CopyObjectCommand } from '@aws-sdk/client-s3';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

const s3Client = new S3Client({
  region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
  endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
  credentials: {
    accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
    secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
  },
  forcePathStyle: true // Required for Linode Object Storage
});

export async function DELETE(
  request: NextRequest,
  { params }: { params: { key: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify required environment variables
    if (!process.env.NEXT_PUBLIC_S3_BUCKET) {
      console.error('Missing S3_BUCKET environment variable');
      return NextResponse.json({ error: 'Storage configuration error' }, { status: 500 });
    }

    // Decode and clean the key
    const key = decodeURIComponent(params.key).trim();
    console.log('Attempting to delete image with key:', key);
    console.log('Using bucket:', process.env.NEXT_PUBLIC_S3_BUCKET);

    // Create and send delete command
    const command = new DeleteObjectCommand({
      Bucket: process.env.NEXT_PUBLIC_S3_BUCKET,
      Key: key,
    });

    const result = await s3Client.send(command);
    console.log('Delete operation result:', result);

    return NextResponse.json({ 
      message: 'Image deleted successfully',
      key: key
    });
  } catch (error: any) {
    console.error('Error deleting image:', {
      error: error.message,
      stack: error.stack,
      key: params.key
    });
    return NextResponse.json(
      { 
        error: 'Failed to delete image',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { key: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify required environment variables
    if (!process.env.NEXT_PUBLIC_S3_BUCKET) {
      console.error('Missing required environment variables');
      return NextResponse.json({ error: 'Storage configuration error' }, { status: 500 });
    }

    const key = decodeURIComponent(params.key);
    const { category } = await request.json();

    if (!category) {
      return NextResponse.json(
        { error: 'Category is required' },
        { status: 400 }
      );
    }

    // Get the old object
    const oldKey = key;
    const newKey = `${category}/${key.split('/').pop()}`;

    // Copy the object to the new location
    const copyCommand = new CopyObjectCommand({
      Bucket: process.env.NEXT_PUBLIC_S3_BUCKET,
      CopySource: `${process.env.NEXT_PUBLIC_S3_BUCKET}/${oldKey}`,
      Key: newKey,
    });

    await s3Client.send(copyCommand);

    // Delete the old object
    const deleteCommand = new DeleteObjectCommand({
      Bucket: process.env.NEXT_PUBLIC_S3_BUCKET,
      Key: oldKey,
    });

    await s3Client.send(deleteCommand);

    return NextResponse.json({
      message: 'Image category updated successfully',
      newKey,
    });
  } catch (error) {
    console.error('Error updating image category:', error);
    return NextResponse.json(
      { error: 'Failed to update image category' },
      { status: 500 }
    );
  }
} 