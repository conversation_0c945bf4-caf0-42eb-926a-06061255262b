import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

/**
 * GET handler for page stats API
 * Returns page view statistics
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate') as string) : undefined;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate') as string) : undefined;

    // If endDate is provided, set it to the end of the day
    if (endDate) {
      endDate.setHours(23, 59, 59, 999);
    }

    // Get page view counts
    const pageViews = await prisma.$queryRaw`
      SELECT "url", COUNT(*) as "views"
      FROM "event_tracking"
      WHERE "eventType" = 'pageView'
        AND "url" IS NOT NULL
        AND (${startDate} IS NULL OR "created_at" >= ${startDate})
        AND (${endDate} IS NULL OR "created_at" <= ${endDate})
      GROUP BY "url"
      ORDER BY "views" DESC
      LIMIT 20
    `;

    // For each page, get unique visitor count
    const pageStats = [];
    for (const { url, views } of pageViews as { url: string, views: number }[]) {
      // Get unique session count for this page
      const uniqueVisitors = await prisma.$queryRaw`
        SELECT COUNT(DISTINCT "sessionId") as "uniqueVisitors"
        FROM "event_tracking"
        WHERE "eventType" = 'pageView'
          AND "url" = ${url}
          AND "sessionId" IS NOT NULL
          AND (${startDate} IS NULL OR "created_at" >= ${startDate})
          AND (${endDate} IS NULL OR "created_at" <= ${endDate})
      `;

      // Add to page stats
      pageStats.push({
        url,
        views: Number(views),
        uniqueVisitors: Number(uniqueVisitors[0]?.uniqueVisitors || 0),
      });
    }

    // Return the page stats
    return NextResponse.json(pageStats);
  } catch (error) {
    console.error('Error fetching page stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch page stats' },
      { status: 500 }
    );
  }
}
