import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

/**
 * GET handler for form stats API
 * Returns form submission and abandonment statistics
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate') as string) : undefined;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate') as string) : undefined;

    // If endDate is provided, set it to the end of the day
    if (endDate) {
      endDate.setHours(23, 59, 59, 999);
    }

    // Get all form names from both submissions and abandonments
    const formNames = await prisma.$queryRaw`
      SELECT DISTINCT "eventName" as "formName"
      FROM "event_tracking"
      WHERE ("eventType" = 'formSubmission' OR "eventType" = 'formAbandonment')
        AND (${startDate} IS NULL OR "created_at" >= ${startDate})
        AND (${endDate} IS NULL OR "created_at" <= ${endDate})
    `;

    // For each form, get submission and abandonment stats
    const formStats = [];
    for (const { formName } of formNames as { formName: string }[]) {
      // Get submission count
      const submissionCount = await prisma.eventTracking.count({
        where: {
          eventName: formName,
          eventType: 'formSubmission',
          ...(startDate && endDate && {
            created_at: {
              gte: startDate,
              lte: endDate,
            },
          }),
        },
      });

      // Get abandonment count
      const abandonmentCount = await prisma.eventTracking.count({
        where: {
          eventName: formName,
          eventType: 'formAbandonment',
          ...(startDate && endDate && {
            created_at: {
              gte: startDate,
              lte: endDate,
            },
          }),
        },
      });

      // Get average completion percentage for abandonments
      const abandonments = await prisma.eventTracking.findMany({
        where: {
          eventName: formName,
          eventType: 'formAbandonment',
          ...(startDate && endDate && {
            created_at: {
              gte: startDate,
              lte: endDate,
            },
          }),
        },
        select: {
          metadata: true,
        },
      });

      let avgCompletionPercentage = 0;
      if (abandonments.length > 0) {
        const totalCompletionPercentage = abandonments.reduce((sum, event) => {
          const completionPercentage = event.metadata?.completionPercentage || 0;
          return sum + (typeof completionPercentage === 'number' ? completionPercentage : 0);
        }, 0);
        avgCompletionPercentage = totalCompletionPercentage / abandonments.length;
      }

      // Calculate total forms and abandonment rate
      const totalForms = submissionCount + abandonmentCount;
      const abandonmentRate = totalForms > 0 ? (abandonmentCount / totalForms) * 100 : 0;

      // Add to form stats
      formStats.push({
        formName,
        totalForms,
        submissions: submissionCount,
        abandonments: abandonmentCount,
        abandonmentRate,
        avgCompletionPercentage,
      });
    }

    // Sort by total forms descending
    formStats.sort((a, b) => b.totalForms - a.totalForms);

    // Return the form stats
    return NextResponse.json(formStats);
  } catch (error) {
    console.error('Error fetching form stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch form stats' },
      { status: 500 }
    );
  }
}
