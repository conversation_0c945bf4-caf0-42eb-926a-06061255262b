import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

/**
 * GET handler for event count API
 * Returns counts of events by type
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate') as string) : undefined;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate') as string) : undefined;

    // If endDate is provided, set it to the end of the day
    if (endDate) {
      endDate.setHours(23, 59, 59, 999);
    }

    // Get event counts by type
    const eventCounts = await prisma.$queryRaw`
      SELECT "eventType", COUNT(*) as count
      FROM "event_tracking"
      WHERE (${startDate} IS NULL OR "created_at" >= ${startDate})
        AND (${endDate} IS NULL OR "created_at" <= ${endDate})
      GROUP BY "eventType"
      ORDER BY count DESC
    `;

    // Convert BigInt to Number for JSON serialization
    const serializedEventCounts = (eventCounts as any[]).map(item => ({
      eventType: item.eventType,
      count: Number(item.count)
    }));

    // Return the event counts
    return NextResponse.json(serializedEventCounts);
  } catch (error) {
    console.error('Error fetching event counts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch event counts' },
      { status: 500 }
    );
  }
}
