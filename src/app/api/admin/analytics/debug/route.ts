import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getAnalyticsConfig, shouldUseMockData } from '@/utils/analyticsConfig';

/**
 * GET handler for debugging Google Analytics configuration
 */
export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const config = getAnalyticsConfig();
    const useMockData = shouldUseMockData();

    return NextResponse.json({
      timestamp: new Date().toISOString(),
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        hasGoogleCredentials: !!(process.env.GOOGLE_APPLICATION_CREDENTIALS || process.env.GOOGLE_ANALYTICS_PRIVATE_KEY),
      },
      configuration: {
        isConfigured: config.isConfigured,
        isValid: config.isValid,
        shouldUseMockData: useMockData,
        measurementId: config.measurementId,
        propertyId: config.propertyId,
        clientEmail: config.clientEmail,
        privateKeyLength: config.privateKey ? config.privateKey.length : 0,
        privateKeyStartsWith: config.privateKey ? config.privateKey.substring(0, 50) + '...' : 'Not set',
      },
      validation: {
        measurementIdValid: /^G-[A-Z0-9]{10}$/.test(config.measurementId),
        propertyIdValid: /^properties\/\d+$/.test(config.propertyId),
        clientEmailValid: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.iam\.gserviceaccount\.com$/.test(config.clientEmail),
        privateKeyValid: config.privateKey.includes('-----BEGIN PRIVATE KEY-----') && config.privateKey.includes('-----END PRIVATE KEY-----'),
      },
      nextSteps: useMockData ? [
        'Enable Google Analytics Data API in Google Cloud Console',
        'Add service account to Google Analytics property with Viewer role',
        'Verify all environment variables are correctly set',
        'Restart the application after making changes'
      ] : [
        'Configuration appears valid',
        'Check Google Cloud Console for API quotas and usage',
        'Verify service account has proper permissions'
      ]
    });

  } catch (error: any) {
    console.error('Error in debug endpoint:', error);
    return NextResponse.json({
      error: 'Debug endpoint failed',
      details: error.message || 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
