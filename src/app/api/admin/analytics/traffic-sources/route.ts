import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getTrafficSources } from '@/services/googleAnalyticsService';

/**
 * GET handler for traffic sources API
 * Returns data about where website traffic is coming from
 */
export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get traffic sources data
    const trafficData = await getTrafficSources();

    // Return the data
    return NextResponse.json(trafficData);
  } catch (error) {
    console.error('Error fetching traffic sources:', error);
    return NextResponse.json(
      { error: 'Failed to fetch traffic source data' },
      { status: 500 }
    );
  }
}
