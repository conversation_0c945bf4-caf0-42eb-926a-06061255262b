import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { BetaAnalyticsDataClient } from '@google-analytics/data';
import { getAnalyticsConfig } from '@/utils/analyticsConfig';

/**
 * GET handler for listing available Google Analytics properties
 * This helps verify which properties the service account has access to
 */
export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const config = getAnalyticsConfig();
    
    // Check configuration
    if (!config.isValid) {
      return NextResponse.json({
        success: false,
        error: 'Google Analytics not properly configured',
        details: 'Please check environment variables'
      });
    }

    // Test different property IDs to see which one works
    const propertyIdsToTest = [
      'properties/*********', // Current from env
      'properties/*********', // Same as current
    ];

    const analyticsDataClient = new BetaAnalyticsDataClient({
      credentials: {
        client_email: config.clientEmail,
        private_key: config.privateKey.replace(/\\n/g, '\n'),
      },
      projectId: config.clientEmail.split('@')[1].split('.')[0],
    });

    const results = [];

    for (const propertyId of propertyIdsToTest) {
      try {
        // Try to make a simple API call to test access
        const [response] = await analyticsDataClient.runReport({
          property: propertyId,
          dateRanges: [
            {
              startDate: '7daysAgo',
              endDate: 'today',
            },
          ],
          metrics: [
            { name: 'screenPageViews' },
          ],
          limit: 1,
        });

        results.push({
          propertyId,
          status: 'success',
          hasData: !!(response.rows && response.rows.length > 0),
          rowCount: response.rows?.length || 0,
        });

      } catch (error: any) {
        results.push({
          propertyId,
          status: 'error',
          error: error.message || 'Unknown error',
          errorCode: error.code,
        });
      }
    }

    return NextResponse.json({
      success: true,
      currentPropertyId: config.propertyId,
      measurementId: config.measurementId,
      serviceAccount: config.clientEmail,
      testResults: results,
      instructions: {
        step1: 'Check which property IDs work (status: success)',
        step2: 'If none work, verify service account has access in Google Analytics',
        step3: 'Update GOOGLE_ANALYTICS_PROPERTY_ID in .env with working property ID',
        step4: 'To find your property ID: Go to Google Analytics > Admin > Property Settings'
      }
    });

  } catch (error: any) {
    console.error('Error listing properties:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to list properties',
      details: error.message || 'Unknown error'
    }, { status: 500 });
  }
}
