import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getTopPages } from '@/services/googleAnalyticsService';
import { NextRequest } from 'next/server';

/**
 * GET handler for top pages API
 * Returns data about the most viewed pages on the website
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get limit parameter from query string, default to 10
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '10', 10);

    // Get top pages data
    const topPagesData = await getTopPages(limit);

    // Return the data
    return NextResponse.json(topPagesData);
  } catch (error) {
    console.error('Error fetching top pages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch top pages data' },
      { status: 500 }
    );
  }
}
