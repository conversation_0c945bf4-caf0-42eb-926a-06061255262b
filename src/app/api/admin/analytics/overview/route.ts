import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getAnalyticsOverview } from '@/services/googleAnalyticsService';

/**
 * GET handler for analytics overview API
 * Returns basic analytics metrics like page views, visitors, etc.
 */
export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get analytics overview data
    const overviewData = await getAnalyticsOverview();

    // Return the data
    return NextResponse.json(overviewData);
  } catch (error) {
    console.error('Error fetching analytics overview:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics data' },
      { status: 500 }
    );
  }
}
