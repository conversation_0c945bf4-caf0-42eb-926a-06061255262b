import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getDeviceData } from '@/services/googleAnalyticsService';

/**
 * GET handler for device data API
 * Returns data about device usage (mobile, desktop, tablet)
 */
export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get device data
    const deviceData = await getDeviceData();

    // Return the data
    return NextResponse.json(deviceData);
  } catch (error) {
    console.error('Error fetching device data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch device data' },
      { status: 500 }
    );
  }
}
