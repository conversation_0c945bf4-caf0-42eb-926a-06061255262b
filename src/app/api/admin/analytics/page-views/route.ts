import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getPageViewsOverTime } from '@/services/googleAnalyticsService';
import { NextRequest } from 'next/server';

/**
 * GET handler for page views API
 * Returns data about page views over time
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get days parameter from query string, default to 30
    const searchParams = request.nextUrl.searchParams;
    const days = parseInt(searchParams.get('days') || '30', 10);

    // Get page views data
    const pageViewsData = await getPageViewsOverTime(days);

    // Return the data
    return NextResponse.json(pageViewsData);
  } catch (error) {
    console.error('Error fetching page views:', error);
    return NextResponse.json(
      { error: 'Failed to fetch page views data' },
      { status: 500 }
    );
  }
}
