import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getCountryData } from '@/services/googleAnalyticsService';
import { NextRequest } from 'next/server';

/**
 * GET handler for country data API
 * Returns data about visitor countries
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get limit parameter from query string, default to 5
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '5', 10);

    // Get country data
    const countryData = await getCountryData(limit);

    // Return the data
    return NextResponse.json(countryData);
  } catch (error) {
    console.error('Error fetching country data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch country data' },
      { status: 500 }
    );
  }
}
