import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { generateBlogPost, BlogPostGenerationOptions } from '@/utils/openaiUtils';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const token = await getToken({ req: request });
    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const data = await request.json();

    // Validate required fields
    if (!data.topic) {
      return NextResponse.json(
        { error: 'Missing required field: topic' },
        { status: 400 }
      );
    }

    // Prepare options for blog post generation
    const options: BlogPostGenerationOptions = {
      topic: data.topic,
      tone: data.tone || 'professional',
      length: data.length || 'medium',
      targetAudience: data.targetAudience || 'general readers',
      includeHeadings: data.includeHeadings !== false,
      includeBulletPoints: data.includeBulletPoints !== false,
      includeConclusion: data.includeConclusion !== false
    };

    // Generate blog post
    const result = await generateBlogPost(options);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to generate blog post' },
        { status: 500 }
      );
    }

    // Return the generated blog post
    return NextResponse.json({
      title: result.title,
      content: result.content,
      excerpt: result.excerpt
    });
  } catch (error) {
    console.error('Error in POST blog post generation:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to generate blog post' },
      { status: 500 }
    );
  }
}
