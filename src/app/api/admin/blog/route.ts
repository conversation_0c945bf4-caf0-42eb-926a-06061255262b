import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { BlogPost } from '@/types/blog';
import slugify from 'slugify';
import * as blogService from '@/services/blogService';

// GET handler - Get all blog posts
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const tag = searchParams.get('tag');
    const status = searchParams.get('status');

    // Get all blog posts from the database
    let blogPosts = await blogService.getAllBlogPosts();

    // Apply filters if provided
    if (category) {
      blogPosts = blogPosts.filter(post => post.category === category);
    }

    if (tag) {
      blogPosts = blogPosts.filter(post => post.tags.includes(tag));
    }

    if (status) {
      blogPosts = blogPosts.filter(post => post.status === status);
    }

    // Sort by createdAt (newest first)
    blogPosts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return NextResponse.json(blogPosts, {
      headers: {
        'Cache-Control': 'no-store',
      },
    });
  } catch (error) {
    console.error('Error in GET blog posts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch blog posts' },
      { status: 500 }
    );
  }
}

// POST handler - Create a new blog post
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const token = await getToken({ req: request });
    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const data = await request.json();

    // Validate required fields
    if (!data.title || !data.content) {
      return NextResponse.json(
        { error: 'Missing required fields: title or content' },
        { status: 400 }
      );
    }

    // Generate slug from title
    let slug = slugify(data.title, {
      lower: true,
      strict: true,
      trim: true,
    });

    // Create new blog post
    const newPost = await blogService.createBlogPost({
      title: data.title,
      slug,
      content: data.content,
      excerpt: data.excerpt || data.content.substring(0, 150) + '...',
      author: data.author || 'Admin',
      category: data.category || 'uncategorized',
      tags: data.tags || [],
      status: data.status || 'draft',
      publishedAt: data.status === 'published' ? new Date().toISOString() : undefined,
    });

    return NextResponse.json(newPost, { status: 201 });
  } catch (error) {
    console.error('Error in POST blog post:', error);
    return NextResponse.json(
      { error: 'Failed to create blog post' },
      { status: 500 }
    );
  }
}
