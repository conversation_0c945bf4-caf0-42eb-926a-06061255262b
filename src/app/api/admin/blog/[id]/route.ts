import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { BlogPost } from '@/types/blog';
import slugify from 'slugify';
import * as blogService from '@/services/blogService';

// GET handler - Get a specific blog post
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Use the params object directly without destructuring
    const id = params.id;

    // Get the blog post from the database by ID
    const blogPost = await blogService.getBlogPostById(id);

    if (!blogPost) {
      return NextResponse.json(
        { error: 'Blog post not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(blogPost);
  } catch (error) {
    console.error('Error in GET blog post:', error);
    return NextResponse.json(
      { error: 'Failed to fetch blog post' },
      { status: 500 }
    );
  }
}

// PUT handler - Update a blog post
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const token = await getToken({ req: request });
    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Use the params object directly without destructuring
    const id = params.id;
    const data = await request.json();

    // Validate required fields
    if (!data.title || !data.content) {
      return NextResponse.json(
        { error: 'Missing required fields: title or content' },
        { status: 400 }
      );
    }

    // Get the existing blog post
    const existingPost = await blogService.getBlogPostById(id);

    if (!existingPost) {
      return NextResponse.json(
        { error: 'Blog post not found' },
        { status: 404 }
      );
    }

    // Generate slug from title if title has changed
    let slug = existingPost.slug;
    if (data.title !== existingPost.title) {
      slug = slugify(data.title, {
        lower: true,
        strict: true,
        trim: true,
      });
    }

    // Update the blog post in the database
    const updatedPost = await blogService.updateBlogPost(id, {
      title: data.title,
      slug,
      content: data.content,
      excerpt: data.excerpt || data.content.substring(0, 150) + '...',
      author: data.author,
      category: data.category,
      tags: data.tags,
      status: data.status,
    });

    if (!updatedPost) {
      return NextResponse.json(
        { error: 'Failed to update blog post' },
        { status: 500 }
      );
    }

    return NextResponse.json(updatedPost);
  } catch (error) {
    console.error('Error in PUT blog post:', error);
    return NextResponse.json(
      { error: 'Failed to update blog post' },
      { status: 500 }
    );
  }
}

// DELETE handler - Delete a blog post
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const token = await getToken({ req: request });
    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Use the params object directly without destructuring
    const id = params.id;

    // Delete the blog post from the database
    const success = await blogService.deleteBlogPost(id);

    if (!success) {
      return NextResponse.json(
        { error: 'Blog post not found or could not be deleted' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE blog post:', error);
    return NextResponse.json(
      { error: 'Failed to delete blog post' },
      { status: 500 }
    );
  }
}
