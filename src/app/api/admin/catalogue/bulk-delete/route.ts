import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import * as catalogueService from '@/services/catalogueService';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    // Get request body
    const body = await request.json();
    const { ids } = body;

    // Validate IDs
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return new NextResponse(JSON.stringify({ error: 'No IDs provided for deletion' }), {
        status: 400,
      });
    }

    // Bulk delete catalogue items
    const result = await catalogueService.bulkDeleteCatalogue(ids);

    if (!result.success) {
      return new NextResponse(JSON.stringify({ error: 'Failed to delete catalogue items' }), {
        status: 500,
      });
    }

    return NextResponse.json({
      message: `Successfully deleted ${result.count} catalogue items`,
      count: result.count
    });
  } catch (error) {
    console.error('Error bulk deleting catalogue items:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}
