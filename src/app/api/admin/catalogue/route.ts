import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import * as catalogueService from '@/services/catalogueService';

export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    const catalogueItems = await catalogueService.getAllCatalogue();

    // Add cache control headers to prevent caching
    return NextResponse.json(catalogueItems, {
      headers: {
        'Cache-Control': 'no-store, max-age=0, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error('Error fetching catalogue items:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}

export async function POST(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    // Get request body
    const body = await request.json();
    const {
      service,
      price,
      description,
      icon,
      popular,
      features,
      imageUrl,
      imageUrl2,
      imageUrl3,
      category
    } = body;

    if (!service) {
      return new NextResponse(JSON.stringify({ error: 'Service name is required' }), {
        status: 400,
      });
    }

    if (price === undefined || price === null) {
      return new NextResponse(JSON.stringify({ error: 'Price is required' }), {
        status: 400,
      });
    }

    // Create catalogue item
    const newCatalogue = await catalogueService.createCatalogue({
      service,
      price: Number(price),
      description,
      icon,
      popular: popular || false,
      features: Array.isArray(features) ? features : [],
      imageUrl,
      imageUrl2,
      imageUrl3,
      category: category || 'Other'
    });

    return NextResponse.json(newCatalogue);
  } catch (error) {
    console.error('Error creating catalogue item:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}
