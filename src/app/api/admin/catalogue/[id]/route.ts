import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import * as catalogueService from '@/services/catalogueService';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: Request, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    const { id } = params;
    const catalogueItem = await catalogueService.getCatalogueById(id);

    if (!catalogueItem) {
      return new NextResponse(JSON.stringify({ error: 'Catalogue item not found' }), {
        status: 404,
      });
    }

    return NextResponse.json(catalogueItem);
  } catch (error) {
    console.error(`Error fetching catalogue item ${params.id}:`, error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}

export async function PUT(request: Request, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    const { id } = params;
    const body = await request.json();
    const {
      service,
      price,
      description,
      icon,
      popular,
      features,
      imageUrl,
      imageUrl2,
      imageUrl3,
      category
    } = body;

    // Validate required fields
    if (!service) {
      return new NextResponse(JSON.stringify({ error: 'Service name is required' }), {
        status: 400,
      });
    }

    if (price === undefined || price === null) {
      return new NextResponse(JSON.stringify({ error: 'Price is required' }), {
        status: 400,
      });
    }

    const updatedCatalogue = await catalogueService.updateCatalogue(id, {
      service,
      price: Number(price),
      description,
      icon,
      popular,
      features: Array.isArray(features) ? features : undefined,
      imageUrl,
      imageUrl2,
      imageUrl3,
      category
    });

    if (!updatedCatalogue) {
      return new NextResponse(JSON.stringify({ error: 'Catalogue item not found' }), {
        status: 404,
      });
    }

    return NextResponse.json(updatedCatalogue);
  } catch (error) {
    console.error(`Error updating catalogue item ${params.id}:`, error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}

export async function DELETE(request: Request, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    const { id } = params;
    const success = await catalogueService.deleteCatalogue(id);

    if (!success) {
      return new NextResponse(JSON.stringify({ error: 'Catalogue item not found' }), {
        status: 404,
      });
    }

    return NextResponse.json({ message: 'Catalogue item deleted successfully' });
  } catch (error) {
    console.error(`Error deleting catalogue item ${params.id}:`, error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}
