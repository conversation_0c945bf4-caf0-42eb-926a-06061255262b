import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { getToken } from 'next-auth/jwt';
import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { PortfolioItem, PortfolioCategory } from '@/types/portfolio';

// Cache for portfolio items
let cachedPortfolioItems: PortfolioItem[] | null = null;
let cacheTime = 0;
const CACHE_DURATION = 1000 * 60 * 5; // 5 minutes

// Path to the portfolio data file
const dataFilePath = path.join(process.cwd(), 'src', 'data', 'portfolio.json');

// Base path for all portfolio images
const PORTFOLIO_BASE_PATH = 'images/portfolio';

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
  endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
  credentials: {
    accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
    secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
  },
  forcePathStyle: true // Required for Linode Object Storage
});

// Helper function to fetch images from S3 by category
async function fetchImagesFromS3(category?: string): Promise<PortfolioItem[]> {
  try {
    if (!process.env.NEXT_PUBLIC_S3_BUCKET) {
      console.warn('S3 bucket not configured');
      return [];
    }

    // Construct the prefix to include images/portfolio/ directory
    const prefix = category ? `${PORTFOLIO_BASE_PATH}/${category}/` : PORTFOLIO_BASE_PATH;
    console.log('Using S3 prefix:', prefix);

    const command = new ListObjectsV2Command({
      Bucket: process.env.NEXT_PUBLIC_S3_BUCKET,
      Prefix: prefix,
      MaxKeys: 1000, // Increase max keys to ensure we get all images
    });

    const response = await s3Client.send(command);
    console.log(`Found ${response.Contents?.length || 0} items in S3`);

    if (!response.Contents || response.Contents.length === 0) {
      console.warn(`No images found in S3. Using fallback data.`);
      return [];
    }

    const portfolioItems: PortfolioItem[] = [];

    if (response.Contents) {
      for (const item of response.Contents) {
        if (!item.Key) {
          console.log('Skipping item with no key');
          continue;
        }

        // Skip if not an image or if it's just the folder itself
        if (!/\.(jpg|jpeg|png|webp|gif)$/i.test(item.Key) || item.Key === prefix) {
          console.log('Skipping non-image or folder:', item.Key);
          continue;
        }

        // Extract category from the key
        const keyParts = item.Key.split('/');
        if (keyParts.length < 3) continue; // Skip if not in the expected format

        const itemCategory = keyParts[2] as PortfolioCategory;

        // Extract filename from the key
        const filename = item.Key.split('/').pop() || '';
        console.log('Processing image:', filename);

        // Construct the URL properly
        const imageUrl = `${process.env.NEXT_PUBLIC_S3_ENDPOINT}/${process.env.NEXT_PUBLIC_S3_BUCKET}/${item.Key}`;
        console.log('Image URL:', imageUrl);

        portfolioItems.push({
          id: uuidv4(),
          title: filename.replace(/\.(jpg|jpeg|png|webp|gif)$/i, '').replace(/-/g, ' '),
          description: '',
          category: itemCategory,
          imageSrc: imageUrl,
          alt: filename.replace(/\.(jpg|jpeg|png|webp|gif)$/i, '').replace(/-/g, ' '),
          featured: false,
          createdAt: item.LastModified?.toISOString() || new Date().toISOString(),
          updatedAt: item.LastModified?.toISOString() || new Date().toISOString(),
        });
      }
    }

    console.log(`Processed ${portfolioItems.length} valid images`);
    return portfolioItems;
  } catch (error) {
    console.error('Error fetching images from S3:', error);
    return [];
  }
}

// Helper function to read portfolio data
async function readPortfolioData(): Promise<PortfolioItem[]> {
  try {
    // Check if we have a valid cache
    const now = Date.now();
    if (cachedPortfolioItems && now - cacheTime < CACHE_DURATION) {
      return cachedPortfolioItems;
    }

    // Read the data file
    const fileData = await fs.readFile(dataFilePath, 'utf8');
    const portfolioItems: PortfolioItem[] = JSON.parse(fileData);

    // Update cache
    cachedPortfolioItems = portfolioItems;
    cacheTime = now;

    return portfolioItems;
  } catch (error) {
    // If file doesn't exist, create it with empty array
    if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
      await fs.mkdir(path.dirname(dataFilePath), { recursive: true });
      await fs.writeFile(dataFilePath, JSON.stringify([], null, 2));
      return [];
    }
    console.error('Error reading portfolio data:', error);
    return [];
  }
}

// Helper function to write portfolio data
async function writePortfolioData(data: PortfolioItem[]): Promise<void> {
  try {
    await fs.mkdir(path.dirname(dataFilePath), { recursive: true });
    await fs.writeFile(dataFilePath, JSON.stringify(data, null, 2));

    // Update cache
    cachedPortfolioItems = data;
    cacheTime = Date.now();
  } catch (error) {
    console.error('Error writing portfolio data:', error);
    throw new Error('Failed to write portfolio data');
  }
}

// GET handler - Get all portfolio items or filter by category
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const useS3 = searchParams.get('useS3') !== 'false'; // Default to true

    let portfolioItems: PortfolioItem[] = [];

    if (useS3) {
      // Try to fetch from S3 first
      try {
        // If category is provided and valid, fetch only that category
        if (category && category !== 'all') {
          portfolioItems = await fetchImagesFromS3(category);
        } else {
          // Otherwise fetch all portfolio items from S3
          portfolioItems = await fetchImagesFromS3();
        }
      } catch (s3Error) {
        console.error('Error fetching from S3, falling back to local data:', s3Error);
      }
    }

    // If no items from S3 or useS3 is false, fall back to local data
    if (portfolioItems.length === 0) {
      // Read portfolio data from local file
      portfolioItems = await readPortfolioData();

      // Filter by category if provided
      if (category && category !== 'all') {
        portfolioItems = portfolioItems.filter(item => item.category === category);
      }
    }

    return NextResponse.json(portfolioItems, {
      headers: {
        'Cache-Control': 'public, max-age=300, s-maxage=600',
      },
    });
  } catch (error) {
    console.error('Error in GET portfolio items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch portfolio items' },
      { status: 500 }
    );
  }
}

// POST handler - Create a new portfolio item
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const token = await getToken({ req: request });
    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const data = await request.json();

    // Validate required fields
    if (!data.title || !data.category || !data.imageSrc) {
      return NextResponse.json(
        { error: 'Missing required fields: title, category, or imageSrc' },
        { status: 400 }
      );
    }

    // Read existing portfolio data
    const portfolioItems = await readPortfolioData();

    // Create new portfolio item
    const newItem: PortfolioItem = {
      id: uuidv4(),
      title: data.title,
      description: data.description || '',
      category: data.category,
      imageSrc: data.imageSrc,
      alt: data.alt || data.title,
      featured: data.featured || false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Add to portfolio items
    portfolioItems.push(newItem);

    // Write updated data
    await writePortfolioData(portfolioItems);

    return NextResponse.json(newItem, { status: 201 });
  } catch (error) {
    console.error('Error in POST portfolio item:', error);
    return NextResponse.json(
      { error: 'Failed to create portfolio item' },
      { status: 500 }
    );
  }
}
