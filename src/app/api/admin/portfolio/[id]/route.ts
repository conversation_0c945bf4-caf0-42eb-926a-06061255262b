import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { getToken } from 'next-auth/jwt';
import { PortfolioItem } from '@/types/portfolio';

// Path to the portfolio data file
const dataFilePath = path.join(process.cwd(), 'src', 'data', 'portfolio.json');

// Helper function to read portfolio data
async function readPortfolioData(): Promise<PortfolioItem[]> {
  try {
    const fileData = await fs.readFile(dataFilePath, 'utf8');
    return JSON.parse(fileData);
  } catch (error) {
    if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
      await fs.mkdir(path.dirname(dataFilePath), { recursive: true });
      await fs.writeFile(dataFilePath, JSON.stringify([], null, 2));
      return [];
    }
    console.error('Error reading portfolio data:', error);
    return [];
  }
}

// Helper function to write portfolio data
async function writePortfolioData(data: PortfolioItem[]): Promise<void> {
  try {
    await fs.mkdir(path.dirname(dataFilePath), { recursive: true });
    await fs.writeFile(dataFilePath, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error writing portfolio data:', error);
    throw new Error('Failed to write portfolio data');
  }
}

// GET handler - Get a specific portfolio item by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Await params before accessing properties
    const id = params.id;

    // Read portfolio data
    const portfolioItems = await readPortfolioData();

    // Find the item with the specified ID
    const item = portfolioItems.find(item => item.id === id);

    if (!item) {
      return NextResponse.json(
        { error: 'Portfolio item not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(item);
  } catch (error) {
    console.error('Error in GET portfolio item:', error);
    return NextResponse.json(
      { error: 'Failed to fetch portfolio item' },
      { status: 500 }
    );
  }
}

// PUT handler - Update a specific portfolio item by ID
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const token = await getToken({ req: request });
    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Await params before accessing properties
    const id = params.id;

    // Parse request body
    const data = await request.json();

    // Read portfolio data
    const portfolioItems = await readPortfolioData();

    // Find the index of the item with the specified ID
    const itemIndex = portfolioItems.findIndex(item => item.id === id);

    if (itemIndex === -1) {
      return NextResponse.json(
        { error: 'Portfolio item not found' },
        { status: 404 }
      );
    }

    // Update the item
    const updatedItem: PortfolioItem = {
      ...portfolioItems[itemIndex],
      title: data.title || portfolioItems[itemIndex].title,
      description: data.description !== undefined ? data.description : portfolioItems[itemIndex].description,
      category: data.category || portfolioItems[itemIndex].category,
      imageSrc: data.imageSrc || portfolioItems[itemIndex].imageSrc,
      alt: data.alt || portfolioItems[itemIndex].alt || data.title || portfolioItems[itemIndex].title,
      featured: data.featured !== undefined ? data.featured : portfolioItems[itemIndex].featured,
      updatedAt: new Date().toISOString(),
    };

    // Replace the item in the array
    portfolioItems[itemIndex] = updatedItem;

    // Write updated data
    await writePortfolioData(portfolioItems);

    return NextResponse.json(updatedItem);
  } catch (error) {
    console.error('Error in PUT portfolio item:', error);
    return NextResponse.json(
      { error: 'Failed to update portfolio item' },
      { status: 500 }
    );
  }
}

// DELETE handler - Delete a specific portfolio item by ID
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const token = await getToken({ req: request });
    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Await params before accessing properties
    const id = params.id;

    // Read portfolio data
    const portfolioItems = await readPortfolioData();

    // Filter out the item with the specified ID
    const updatedItems = portfolioItems.filter(item => item.id !== id);

    // If the length is the same, the item was not found
    if (updatedItems.length === portfolioItems.length) {
      return NextResponse.json(
        { error: 'Portfolio item not found' },
        { status: 404 }
      );
    }

    // Write updated data
    await writePortfolioData(updatedItems);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE portfolio item:', error);
    return NextResponse.json(
      { error: 'Failed to delete portfolio item' },
      { status: 500 }
    );
  }
}
