import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { z } from 'zod';
import seoService from '@/services/seoService';
import { logActivity } from '@/utils/passwordUtils';

// Validation schema for updating SEO page
const UpdateSeoPageSchema = z.object({
  title: z.string().min(1).optional(),
  description: z.string().optional(),
  keywords: z.array(z.string()).optional(),
});

/**
 * GET handler to retrieve a specific SEO page
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get the SEO page
    const page = await seoService.getSeoPageById(id);

    if (!page) {
      return NextResponse.json({ error: 'SEO page not found' }, { status: 404 });
    }

    return NextResponse.json(page);
  } catch (error) {
    console.error('Error fetching SEO page:', error);
    return NextResponse.json(
      { error: 'Failed to fetch SEO page' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler to update a SEO page
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get the existing page
    const existingPage = await seoService.getSeoPageById(id);
    if (!existingPage) {
      return NextResponse.json({ error: 'SEO page not found' }, { status: 404 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = UpdateSeoPageSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { title, description, keywords } = validationResult.data;

    // Update the SEO page
    const updatedPage = await prisma.seoPage.update({
      where: { id },
      data: {
        ...(title && { title }),
        ...(description !== undefined && { description }),
        ...(keywords && { keywords }),
        updatedAt: new Date(),
      },
    });

    // Log the activity
    await logActivity({
      userId: session.user.id,
      action: 'update_seo_page',
      details: `Updated SEO page for URL: ${existingPage.url}`,
      resourceType: 'seo_page',
      resourceId: id,
    });

    return NextResponse.json(updatedPage);
  } catch (error) {
    console.error('Error updating SEO page:', error);
    return NextResponse.json(
      { error: 'Failed to update SEO page' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler to delete a SEO page
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get the existing page
    const existingPage = await seoService.getSeoPageById(id);
    if (!existingPage) {
      return NextResponse.json({ error: 'SEO page not found' }, { status: 404 });
    }

    // Delete the SEO page
    await seoService.deleteSeoPage(id);

    // Log the activity
    await logActivity({
      userId: session.user.id,
      action: 'delete_seo_page',
      details: `Deleted SEO page for URL: ${existingPage.url}`,
      resourceType: 'seo_page',
      resourceId: id,
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting SEO page:', error);
    return NextResponse.json(
      { error: 'Failed to delete SEO page' },
      { status: 500 }
    );
  }
}
