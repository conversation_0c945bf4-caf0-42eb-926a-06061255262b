import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { z } from 'zod';
import seoService from '@/services/seoService';
import { logActivity } from '@/utils/passwordUtils';

// Validation schema for SEO page
const SeoPageSchema = z.object({
  url: z.string().url(),
  title: z.string().min(1),
  description: z.string().optional(),
  keywords: z.array(z.string()).optional(),
});

/**
 * GET handler to retrieve all SEO pages
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all SEO pages
    const pages = await seoService.getAllSeoPages();

    return NextResponse.json(pages);
  } catch (error) {
    console.error('Error fetching SEO pages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch SEO pages' },
      { status: 500 }
    );
  }
}

/**
 * POST handler to create a new SEO page
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = SeoPageSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { url, title, description, keywords } = validationResult.data;

    // Create or update the SEO page
    const page = await seoService.createOrUpdateSeoPage({
      url,
      title,
      description,
      keywords,
    });

    // Log the activity
    await logActivity({
      userId: session.user.id,
      action: 'create_seo_page',
      details: `Created SEO page for URL: ${url}`,
      resourceType: 'seo_page',
      resourceId: page.id,
    });

    return NextResponse.json(page, { status: 201 });
  } catch (error) {
    console.error('Error creating SEO page:', error);
    return NextResponse.json(
      { error: 'Failed to create SEO page' },
      { status: 500 }
    );
  }
}
