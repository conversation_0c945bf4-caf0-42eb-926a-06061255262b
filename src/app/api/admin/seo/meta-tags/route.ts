import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';

/**
 * GET handler to retrieve all meta tags
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all meta tags with page information
    const metaTags = await prisma.seoMetaTag.findMany({
      include: {
        page: {
          select: {
            id: true,
            url: true,
            title: true,
          },
        },
      },
      orderBy: [
        { name: 'asc' },
        { updatedAt: 'desc' },
      ],
    });

    return NextResponse.json(metaTags);
  } catch (error) {
    console.error('Error fetching meta tags:', error);
    return NextResponse.json(
      { error: 'Failed to fetch meta tags' },
      { status: 500 }
    );
  }
}
