import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import { withAuth } from '@/utils/apiAuth';
import { logActivity } from '@/utils/passwordUtils';

const execPromise = promisify(exec);

/**
 * POST handler to set up automated SEO scanning
 */
export const POST = withAuth(
  async (request: NextRequest, session: any) => {
    try {
      // Get the project root directory
      const projectRoot = process.cwd();

      // Path to the setup script
      const scriptPath = path.join(projectRoot, 'scripts', 'setup-seo-cron.sh');

      // Execute the script
      const { stdout, stderr } = await execPromise(`bash ${scriptPath}`);

      if (stderr) {
        console.error('Error setting up SEO cron job:', stderr);
        return NextResponse.json(
          { error: 'Failed to set up SEO cron job', details: stderr },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'SEO cron job set up successfully',
        output: stdout,
      });
    } catch (error) {
      console.error('Error setting up SEO cron job:', error);
      return NextResponse.json(
        { error: 'Failed to set up SEO cron job', details: error.message },
        { status: 500 }
      );
    }
  },
  'seo:read',
  {
    logAction: 'setup_seo_cron',
    resourceType: 'seo'
  }
);

/**
 * PUT handler to run a manual SEO scan
 */
export const PUT = withAuth(
  async (request: NextRequest, session: any) => {
    try {
      // Get the project root directory
      const projectRoot = process.cwd();

      // Execute the scan script
      const { stdout, stderr } = await execPromise(`cd ${projectRoot} && node scripts/seo-scan.js --all`);

      if (stderr) {
        console.error('Error running SEO scan:', stderr);
        return NextResponse.json(
          { error: 'Failed to run SEO scan', details: stderr },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'SEO scan completed successfully',
        output: stdout,
      });
    } catch (error) {
      console.error('Error running SEO scan:', error);
      return NextResponse.json(
        { error: 'Failed to run SEO scan', details: error.message },
        { status: 500 }
      );
    }
  },
  'seo:read',
  {
    logAction: 'run_seo_scan',
    resourceType: 'seo'
  }
);
