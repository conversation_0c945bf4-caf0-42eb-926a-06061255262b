import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';
import { z } from 'zod';
import seoService from '@/services/seoService';
import { logActivity } from '@/utils/passwordUtils';

// Validation schema for fixing broken link
const FixBrokenLinkSchema = z.object({
  fixedUrl: z.string().url(),
});

/**
 * PUT handler to fix a broken link
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get the existing broken link
    const brokenLink = await prisma.brokenLink.findUnique({
      where: { id },
      include: { page: true },
    });

    if (!brokenLink) {
      return NextResponse.json({ error: 'Broken link not found' }, { status: 404 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = FixBrokenLinkSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { fixedUrl } = validationResult.data;

    // Fix the broken link
    const updatedLink = await seoService.fixBrokenLink(id, fixedUrl);

    // Log the activity
    await logActivity({
      userId: session.user.id,
      action: 'fix_broken_link',
      details: `Fixed broken link: ${brokenLink.url} -> ${fixedUrl}`,
      resourceType: 'broken_link',
      resourceId: id,
    });

    return NextResponse.json(updatedLink);
  } catch (error) {
    console.error('Error fixing broken link:', error);
    return NextResponse.json(
      { error: 'Failed to fix broken link' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler to delete a broken link
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get the existing broken link
    const brokenLink = await prisma.brokenLink.findUnique({
      where: { id },
    });

    if (!brokenLink) {
      return NextResponse.json({ error: 'Broken link not found' }, { status: 404 });
    }

    // Delete the broken link
    await prisma.brokenLink.delete({
      where: { id },
    });

    // Log the activity
    await logActivity({
      userId: session.user.id,
      action: 'delete_broken_link',
      details: `Deleted broken link: ${brokenLink.url}`,
      resourceType: 'broken_link',
      resourceId: id,
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting broken link:', error);
    return NextResponse.json(
      { error: 'Failed to delete broken link' },
      { status: 500 }
    );
  }
}
