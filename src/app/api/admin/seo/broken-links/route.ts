import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';

/**
 * GET handler to retrieve all broken links
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all broken links with page information
    const brokenLinks = await prisma.brokenLink.findMany({
      include: {
        page: {
          select: {
            id: true,
            url: true,
            title: true,
          },
        },
      },
      orderBy: [
        { fixed: 'asc' },
        { updatedAt: 'desc' },
      ],
    });

    return NextResponse.json(brokenLinks);
  } catch (error) {
    console.error('Error fetching broken links:', error);
    return NextResponse.json(
      { error: 'Failed to fetch broken links' },
      { status: 500 }
    );
  }
}
