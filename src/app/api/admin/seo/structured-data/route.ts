import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';

/**
 * GET handler to retrieve all structured data
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all structured data with page information
    const structuredData = await prisma.seoStructuredData.findMany({
      include: {
        page: {
          select: {
            id: true,
            url: true,
            title: true,
          },
        },
      },
      orderBy: [
        { type: 'asc' },
        { updatedAt: 'desc' },
      ],
    });

    return NextResponse.json(structuredData);
  } catch (error) {
    console.error('Error fetching structured data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch structured data' },
      { status: 500 }
    );
  }
}
