import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { z } from 'zod';
import seoService from '@/services/seoService';
import seoAnalyzerService from '@/services/seoAnalyzerService';
import { logActivity } from '@/utils/passwordUtils';

// Validation schema for scan request
const ScanRequestSchema = z.object({
  url: z.string().url(),
});

/**
 * POST handler to scan a URL for SEO issues
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = ScanRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { url } = validationResult.data;

    // Create a new scan record
    const scan = await seoService.createSeoScan();

    try {
      // Analyze the page
      const results = await seoAnalyzerService.analyzePage(url);

      // Create or update the SEO page
      const page = await seoService.createOrUpdateSeoPage({
        url,
        title: results.pageData.title,
        description: results.pageData.description,
        keywords: results.pageData.keywords,
      });

      // Save the scan results
      await seoService.saveSeoScanResults(page.id, results);

      // Update the scan record
      await seoService.updateSeoScan(scan.id, {
        status: 'completed',
        endedAt: new Date(),
        summary: {
          url,
          issuesCount: results.issues.length,
          brokenLinksCount: results.brokenLinks.length,
          healthScore: page.healthScore,
        },
      });

      // Log the activity
      await logActivity({
        userId: session.user.id,
        action: 'scan_seo_page',
        details: `Scanned URL: ${url}`,
        resourceType: 'seo_page',
        resourceId: page.id,
      });

      return NextResponse.json({
        success: true,
        scan,
        page,
        results,
      });
    } catch (error) {
      // Update the scan record with error
      await seoService.updateSeoScan(scan.id, {
        status: 'failed',
        endedAt: new Date(),
        summary: {
          url,
          error: error.message,
        },
      });

      throw error;
    }
  } catch (error) {
    console.error('Error scanning URL:', error);
    return NextResponse.json(
      { error: 'Failed to scan URL', details: error.message },
      { status: 500 }
    );
  }
}

/**
 * GET handler to retrieve all scans
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all scans
    const scans = await seoService.getAllSeoScans();

    return NextResponse.json(scans);
  } catch (error) {
    console.error('Error fetching SEO scans:', error);
    return NextResponse.json(
      { error: 'Failed to fetch SEO scans' },
      { status: 500 }
    );
  }
}
