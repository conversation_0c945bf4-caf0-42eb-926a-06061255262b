import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { z } from 'zod';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { 
  updateStorageConfig, 
  deleteStorageConfig, 
  StorageConfigInput
} from '@/lib/storageConfig';

// Validation schema for storage configuration update
const StorageConfigUpdateSchema = z.object({
  provider: z.enum(['S3', 'LOCAL', 'LINODE', 'CLOUDINARY']).optional(),
  region: z.string().min(1).max(100).optional(),
  endpoint: z.string().url().max(255).optional(),
  bucketName: z.string().min(1).max(100).optional(),
  accessKeyId: z.string().min(1).max(255).optional(),
  secretAccessKey: z.string().min(1).max(255).optional(),
  isDefault: z.boolean().optional(),
});

// Get the ID from the URL
function getIdFromUrl(request: NextRequest): string {
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  return pathParts[pathParts.length - 1];
}

export async function PUT(request: NextRequest) {
  try {
    // Authenticate the request
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the configuration ID from the URL
    const id = getIdFromUrl(request);

    // Parse and validate the request body
    const body = await request.json();
    
    const result = StorageConfigUpdateSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid input', issues: result.error.issues },
        { status: 400 }
      );
    }
    
    // Update the storage configuration
    const config = await updateStorageConfig(id, result.data);
    
    return NextResponse.json(config);
  } catch (error) {
    console.error('Error updating storage configuration:', error);
    return NextResponse.json(
      { error: 'Failed to update storage configuration' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Authenticate the request
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the configuration ID from the URL
    const id = getIdFromUrl(request);
    
    // Delete the storage configuration
    await deleteStorageConfig(id);
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error deleting storage configuration:', error);
    
    // Check if this is trying to delete a default configuration
    if (error.message === 'Cannot delete the default storage configuration') {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to delete storage configuration' },
      { status: 500 }
    );
  }
} 