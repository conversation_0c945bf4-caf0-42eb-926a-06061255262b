import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { updateStorageConfig } from '@/lib/storageConfig';

// Get the ID from the URL
function getIdFromUrl(request: NextRequest): string {
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  return pathParts[pathParts.length - 2]; // ID is second-to-last segment in /storage/[id]/default
}

export async function PUT(request: NextRequest) {
  try {
    // Authenticate the request
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the configuration ID from the URL
    const id = getIdFromUrl(request);
    
    // Update the storage configuration to be the default
    const config = await updateStorageConfig(id, { isDefault: true });
    
    return NextResponse.json(config);
  } catch (error) {
    console.error('Error setting default storage configuration:', error);
    return NextResponse.json(
      { error: 'Failed to set default storage configuration' },
      { status: 500 }
    );
  }
} 