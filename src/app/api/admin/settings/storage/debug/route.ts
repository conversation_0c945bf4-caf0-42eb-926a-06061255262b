import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET() {
  try {
    console.log('GET /api/admin/settings/storage/debug - Fetching debug information');

    let configs = [];
    let dbError = null;

    try {
      // Check if prisma and storageConfig model exist
      if (!prisma || !prisma.storageConfig) {
        throw new Error('StorageConfig model not available in Prisma client');
      }

      // Directly query the database, bypassing auth for debugging
      // @ts-ignore - Using the StorageConfig table that's defined in our schema
      configs = await prisma.storageConfig.findMany({
        orderBy: { createdAt: 'desc' },
      });

      // Ensure configs is always an array
      configs = Array.isArray(configs) ? configs : [];

      console.log(`Successfully fetched ${configs.length} storage configurations for debug`);
    } catch (error) {
      console.error('Database error in debug route:', error);
      dbError = error instanceof Error ? error.message : String(error);
      // Ensure configs is always an array even on error
      configs = [];
    }

    // Check if environment variables are available
    const envVarsAvailable = !!(
      process.env.NEXT_PUBLIC_S3_REGION &&
      process.env.NEXT_PUBLIC_S3_ENDPOINT &&
      process.env.NEXT_PUBLIC_S3_BUCKET &&
      process.env.NEXT_PUBLIC_S3_ACCESS_KEY &&
      process.env.NEXT_PUBLIC_S3_SECRET_KEY
    );

    // Add extra debugging information
    const response = {
      configs: configs || [], // Ensure configs is always an array
      count: configs ? configs.length : 0,
      dbError,
      envVarsAvailable,
      debug: {
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        databaseUrl: process.env.DATABASE_URL ?
          `${process.env.DATABASE_URL.split('@')[0].split(':')[0]}:***@${process.env.DATABASE_URL.split('@')[1]}` :
          'Not available',
        prismaClientInitialized: !!prisma,
        storageConfigModelExists: !!prisma.storageConfig,
      }
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error in debug route:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch storage configurations',
        details: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}