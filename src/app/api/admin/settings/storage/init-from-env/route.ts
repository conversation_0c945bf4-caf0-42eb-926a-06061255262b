import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import prisma from '@/lib/prisma';

export async function POST() {
  try {
    // Authenticate the request
    const session = await getServerSession(authOptions);

    // Check if this is a development environment
    const isDevelopment = process.env.NODE_ENV === 'development';

    // In development, allow initialization without authentication
    if (!isDevelopment && (!session || !session.user)) {
      console.error('Authentication failed: No valid session found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (session?.user) {
      console.log('Authentication successful for user:', session.user.email || session.user.name);
    } else if (isDevelopment) {
      console.log('Bypassing authentication in development environment');
    }

    // Check if required environment variables are available
    if (!process.env.NEXT_PUBLIC_S3_REGION ||
        !process.env.NEXT_PUBLIC_S3_ENDPOINT ||
        !process.env.NEXT_PUBLIC_S3_BUCKET ||
        !process.env.NEXT_PUBLIC_S3_ACCESS_KEY ||
        !process.env.NEXT_PUBLIC_S3_SECRET_KEY) {
      return NextResponse.json(
        { error: 'Required environment variables are missing' },
        { status: 400 }
      );
    }

    // Check if a config already exists
    // @ts-ignore - Using the StorageConfig table that's defined in our schema
    const existingConfig = await prisma.storageConfig.findFirst({
      where: { isDefault: true }
    });

    if (existingConfig) {
      // Update existing config with environment values
      // @ts-ignore - Using the StorageConfig table that's defined in our schema
      const updatedConfig = await prisma.storageConfig.update({
        where: { id: existingConfig.id },
        data: {
          provider: 'S3',
          region: process.env.NEXT_PUBLIC_S3_REGION,
          endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT,
          bucketName: process.env.NEXT_PUBLIC_S3_BUCKET,
          accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY,
          secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY,
          isDefault: true
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Updated existing configuration with environment variables',
        config: {
          id: updatedConfig.id,
          provider: updatedConfig.provider,
          region: updatedConfig.region,
          endpoint: updatedConfig.endpoint,
          bucketName: updatedConfig.bucketName,
          isDefault: updatedConfig.isDefault
        }
      });
    } else {
      // Create new config from environment variables
      // @ts-ignore - Using the StorageConfig table that's defined in our schema
      const newConfig = await prisma.storageConfig.create({
        data: {
          provider: 'S3',
          region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
          endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || '',
          bucketName: process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky',
          accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
          secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
          isDefault: true
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Created new configuration from environment variables',
        config: {
          id: newConfig.id,
          provider: newConfig.provider,
          region: newConfig.region,
          endpoint: newConfig.endpoint,
          bucketName: newConfig.bucketName,
          isDefault: newConfig.isDefault
        }
      });
    }
  } catch (error) {
    console.error('Error initializing storage config from env:', error);
    return NextResponse.json(
      {
        error: 'Failed to initialize storage configuration',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}