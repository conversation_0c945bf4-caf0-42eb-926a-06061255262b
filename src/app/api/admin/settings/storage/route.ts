import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { z } from 'zod';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import {
  getAllStorageConfigs,
  createStorageConfig,
  StorageConfigInput
} from '@/lib/storageConfig';
import prisma from '@/lib/prisma';

// Validation schema for storage configuration input
const StorageConfigSchema = z.object({
  provider: z.enum(['S3', 'LOCAL', 'LINODE', 'CLOUDINARY']),
  region: z.string().min(1).max(100),
  endpoint: z.string().url().max(255),
  bucketName: z.string().min(1).max(100),
  accessKeyId: z.string().min(1).max(255),
  secretAccessKey: z.string().min(1).max(255),
  isDefault: z.boolean().default(false),
});

export async function GET(request: Request) {
  try {
    console.log('GET /api/admin/settings/storage - Fetching storage configurations');

    // Check if this is a debug request with a special header
    const url = new URL(request.url);
    const isDebug = url.searchParams.get('debug') === 'true';

    // Check if this is a production environment
    const isProduction = process.env.NODE_ENV === 'production';

    // Authenticate the request
    const session = await getServerSession(authOptions);

    if (!session && !isDebug && isProduction) {
      console.log('Authentication failed: No valid session found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('Authentication check passed, proceeding to fetch configurations');

    try {
      // Check if prisma and storageConfig model exist
      if (!prisma || !prisma.storageConfig) {
        throw new Error('StorageConfig model not available in Prisma client');
      }

      // Get all storage configurations using direct Prisma call for reliability
      // @ts-ignore - Using the StorageConfig table that's defined in our schema
      let configs = await prisma.storageConfig.findMany({
        orderBy: { createdAt: 'desc' },
      });

      // Ensure configs is always an array
      configs = Array.isArray(configs) ? configs : [];

      console.log(`Successfully fetched ${configs.length} storage configurations`);
      return NextResponse.json(configs);
    } catch (dbError) {
      console.error('Database error fetching storage configurations:', dbError);

      // Fallback to environment variables if DB lookup fails
      if (process.env.NEXT_PUBLIC_S3_BUCKET) {
        console.log('Falling back to environment variables for storage configuration');
        const envConfig = {
          id: 'env-default',
          provider: 'S3',
          region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
          endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || '',
          bucketName: process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky',
          accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
          secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
          isDefault: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        return NextResponse.json([envConfig]);
      }

      // Return empty array instead of throwing to prevent forEach errors
      return NextResponse.json([]);
    }
  } catch (error) {
    console.error('Error fetching storage configurations:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch storage configurations',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate the request
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate the request body
    const body = await request.json();

    const result = StorageConfigSchema.safeParse(body);

    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid input', issues: result.error.issues },
        { status: 400 }
      );
    }

    // Create the storage configuration
    const config = await createStorageConfig(result.data as StorageConfigInput);

    return NextResponse.json(config, { status: 201 });
  } catch (error) {
    console.error('Error creating storage configuration:', error);
    return NextResponse.json(
      { error: 'Failed to create storage configuration' },
      { status: 500 }
    );
  }
}