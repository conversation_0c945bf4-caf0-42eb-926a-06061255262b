import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { z } from 'zod';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { getSiteSettings, upsertSiteSettings } from '@/services/siteSettingsService';

// Validation schema for site settings
const SiteSettingsSchema = z.object({
  siteName: z.string().min(1).max(100).optional(),
  siteDescription: z.string().max(255).optional(),
  contactEmail: z.string().email().max(100).optional(),
  phoneNumber: z.string().max(50).optional(),
  address: z.string().max(255).optional(),
  facebookUrl: z.string().url().max(255).optional(),
  twitterUrl: z.string().url().max(255).optional(),
  instagramUrl: z.string().url().max(255).optional(),
  tiktokUrl: z.string().url().max(255).optional(),
  linkedinUrl: z.string().url().max(255).optional(),
  metaTitle: z.string().max(255).optional(),
  metaDescription: z.string().optional(),
  googleAnalyticsId: z.string().max(50).optional(),
});

export async function GET(request: Request) {
  try {
    // Check if this is a debug request with a special header
    const url = new URL(request.url);
    const isDebug = url.searchParams.get('debug') === 'true';
    
    // Authenticate the request for admin routes
    if (url.pathname.startsWith('/api/admin')) {
      const session = await getServerSession(authOptions);
      
      if (!session && !isDebug) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
    }

    // Get site settings
    const settings = await getSiteSettings();
    
    // If no settings exist, return empty object
    if (!settings) {
      return NextResponse.json({});
    }
    
    return NextResponse.json(settings);
  } catch (error) {
    console.error('Error fetching site settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch site settings' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Authenticate the request
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate the request body
    const body = await request.json();
    
    const result = SiteSettingsSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid input', issues: result.error.issues },
        { status: 400 }
      );
    }
    
    // Update site settings
    const updatedSettings = await upsertSiteSettings(result.data);
    
    return NextResponse.json(updatedSettings);
  } catch (error) {
    console.error('Error updating site settings:', error);
    return NextResponse.json(
      { error: 'Failed to update site settings' },
      { status: 500 }
    );
  }
}
