import { NextRequest, NextResponse } from 'next/server';
import * as invoiceService from '@/services/invoiceService';

/**
 * GET /api/admin/invoices/[id]
 * Get an invoice by ID
 */
export async function GET(req: NextRequest, context: { params: { id: string } }) {
  try {
    // Get invoice by ID
    const invoice = await invoiceService.getInvoiceById(context.params.id);

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    return NextResponse.json(invoice);
  } catch (error) {
    console.error(`Error getting invoice ${context.params.id}:`, error);
    return NextResponse.json({ error: 'Failed to get invoice' }, { status: 500 });
  }
}
