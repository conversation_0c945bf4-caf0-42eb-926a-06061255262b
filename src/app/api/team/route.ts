import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export const dynamic = 'force-dynamic';
export const revalidate = 0; // Disable cache for now

export async function GET() {
  try {
    console.log('Fetching team members from database...');

    // Use direct database query with Prisma
    const teamMembers = await prisma.$transaction(async (tx) => {
      return await tx.$queryRaw`SELECT * FROM team_members ORDER BY "order" ASC`;
    });

    console.log(`Found ${Array.isArray(teamMembers) ? teamMembers.length : 0} team members`);

    // Return the team members
    return NextResponse.json(teamMembers);
  } catch (error) {
    console.error('Error fetching team members:', error);
    return NextResponse.json(
      { error: 'Failed to fetch team members' },
      { status: 500 }
    );
  }
}
