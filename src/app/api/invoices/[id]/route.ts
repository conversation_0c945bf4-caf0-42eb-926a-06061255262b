import { NextRequest, NextResponse } from 'next/server';
import { getInvoiceById } from '@/services/invoiceService';
import { promises as fs } from 'fs';
import path from 'path';

/**
 * Generate HTML for an invoice
 * @param invoice The invoice data
 * @returns HTML string
 */
function generateInvoiceHtml(invoice: any): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invoice ${invoice.invoiceNumber}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        .header-container {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 30px;
          position: relative;
        }
        .logo-container {
          flex: 0 0 25%;
          text-align: left;
        }
        .company-info {
          flex: 0 0 25%;
          text-align: right;
        }
        .invoice-center {
          flex: 0 0 50%;
          text-align: center;
          padding: 0 15px;
        }
        .invoice-title {
          margin: 0 0 15px 0;
        }
        .invoice-details {
          text-align: center;
          margin-bottom: 10px;
        }
        .customer-info {
          margin-bottom: 20px;
        }
        .customer-info p {
          margin: 0 0 5px 0;
        }
        .section-title {
          font-weight: bold;
          margin-bottom: 10px;
          color: #0F2557;
          font-size: 16px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }
        th, td {
          padding: 8px;
          text-align: left;
          border-bottom: 1px solid #ddd;
        }
        th {
          background-color: #f2f2f2;
        }
        .summary {
          margin-left: auto;
          width: 300px;
          text-align: right;
          margin-bottom: 20px;
        }
        .summary p {
          margin: 5px 0;
        }
        .summary p span {
          display: inline-block;
          width: 120px;
          text-align: right;
        }
        .total {
          font-weight: bold;
          font-size: 16px;
          color: #0F2557;
        }
        .notes {
          margin-bottom: 20px;
        }
        .footer {
          text-align: center;
          margin-top: 30px;
          color: #666;
        }
        .print-button {
          display: block;
          margin: 20px auto;
          padding: 10px 20px;
          background-color: #0F2557;
          color: white;
          border: none;
          border-radius: 5px;
          cursor: pointer;
          font-size: 16px;
        }
        /* Responsive scaling for different screen sizes */
        @media screen and (max-width: 600px) {
          body {
            padding: 10px;
            font-size: 14px;
          }
          .header-container {
            flex-direction: column;
            align-items: center;
          }
          .logo-container, .company-info, .invoice-center {
            flex: 0 0 100%;
            text-align: center;
            margin-bottom: 15px;
          }
          .summary {
            width: 100%;
          }
        }
        /* Print-specific styles with auto-scaling */
        @media print {
          @page {
            size: auto;  /* auto is the default value */
            margin: 0mm; /* this affects the margin in the printer settings */
          }
          html {
            background-color: #FFFFFF;
            margin: 0; /* this affects the margin on the html before sending to printer */
          }
          body {
            padding: 10mm 15mm; /* margin you want for the content */
            margin: 0;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
            width: 100%;
            height: 100%;
            box-sizing: border-box;
          }
          .print-button {
            display: none;
          }
          /* Scale content to fit the page */
          table, .summary {
            max-width: 100%;
            width: 100%;
            font-size: 10pt;
          }
          th, td {
            padding: 5px;
          }
          h1 {
            font-size: 18pt;
          }
          h2 {
            font-size: 14pt;
          }
          h3 {
            font-size: 12pt;
          }
          p {
            font-size: 10pt;
          }
        }
      </style>
    </head>
    <body>
      <div class="header-container">
        <div class="logo-container">
          <img src="/images/logo.png" alt="Mocky Digital Logo" style="max-width: 120px; height: auto;">
        </div>

        <div class="invoice-center">
          <div class="invoice-title">
            <h1 style="margin: 10px 0;">INVOICE</h1>
          </div>

          <div class="invoice-details">
            <p style="margin: 5px 0;">Invoice #: <strong>${invoice.invoiceNumber}</strong></p>
            <p style="margin: 5px 0;">Date: ${new Date(invoice.issuedAt).toLocaleDateString()}</p>
            <p style="margin: 5px 0;">Due Date: ${new Date(invoice.dueDate).toLocaleDateString()}</p>
            <p style="margin: 5px 0;">Status: ${invoice.status.toUpperCase()}</p>
          </div>
        </div>

        <div class="company-info">
          <h2 style="margin-top: 0;">Mocky Digital</h2>
          <p style="margin: 5px 0;">Nairobi, Kenya</p>
          <p style="margin: 5px 0;">Phone: +*********** 670</p>
          <p style="margin: 5px 0;">Email: <EMAIL></p>
          <p style="margin: 5px 0;">Tax PIN: P052373324V</p>
        </div>
      </div>

        <div class="customer-info">
          <div class="section-title">CUSTOMER INFORMATION</div>
          <p>Name: ${invoice.customerName}</p>
          <p>Phone: ${invoice.phoneNumber}</p>
          ${invoice.email ? `<p>Email: ${invoice.email}</p>` : ''}
        </div>

        <div class="section-title">ITEMS</div>
        <table>
          <thead>
            <tr>
              <th>Description</th>
              <th>Quantity</th>
              <th>Unit Price (KES)</th>
              <th>Total (KES)</th>
            </tr>
          </thead>
          <tbody>
            ${invoice.items.map(item => `
              <tr>
                <td>${item.description || item.serviceName || ''}</td>
                <td>${item.quantity}</td>
                <td>KES ${item.unitPrice.toLocaleString()}</td>
                <td>KES ${item.totalPrice.toLocaleString()}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div class="summary">
          <p>Total Amount: <span>KES ${invoice.totalAmount.toLocaleString()}</span></p>
          <p>Amount Paid: <span>KES ${invoice.amountPaid.toLocaleString()}</span></p>
          <p class="total">Balance Due: <span>KES ${invoice.balance.toLocaleString()}</span></p>
        </div>

        ${invoice.notes ? `
          <div class="notes">
            <div class="section-title">NOTES</div>
            <p>${invoice.notes}</p>
          </div>
        ` : ''}

        <div class="footer">
          <p>Thank you for your business!</p>
          <p>This is a computer-generated invoice and does not require a signature.</p>
        </div>

        <button class="print-button">Print Invoice</button>
      </div>

      <script>
        function printInvoice() {
          window.print();
        }

        document.addEventListener('DOMContentLoaded', function() {
          setTimeout(function() {
            // Add click event to print button
            const printButton = document.querySelector('.print-button');
            if (printButton) {
              printButton.addEventListener('click', function(e) {
                e.preventDefault();
                printInvoice();
              });
            }
          }, 500);
        });
      </script>
    </body>
    </html>
  `;
}

/**
 * GET /api/invoices/[id]
 * Generate and serve an invoice as PDF or HTML
 */
export async function GET(
  req: NextRequest,
  context: { params: { id: string } }
) {
  try {
    // Get the ID from params
    const id = context.params.id;
    console.log(`Invoice download requested for ID: ${id}`);

    if (!id || id === 'undefined' || id === 'null') {
      console.error('Invalid invoice ID provided:', id);
      return NextResponse.json({ error: 'Invalid invoice ID' }, { status: 400 });
    }

    // Get the invoice
    const invoice = await getInvoiceById(id);

    if (!invoice) {
      console.error(`Invoice not found: ${id}`);
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // Check if the invoice has a PDF URL
    if (invoice.pdfUrl) {
      console.log(`Invoice has PDF URL: ${invoice.pdfUrl}`);

      try {
        // Get the PDF file path
        const pdfPath = path.join(process.cwd(), 'public', invoice.pdfUrl);

        // Check if the file exists
        try {
          await fs.access(pdfPath);
          console.log(`PDF file exists: ${pdfPath}`);
        } catch (accessError) {
          console.error(`PDF file does not exist: ${pdfPath}`);

          // Generate HTML invoice
          const html = generateInvoiceHtml(invoice);

          // Return the HTML content
          return new NextResponse(html, {
            headers: {
              'Content-Type': 'text/html',
            },
          });
        }

        // Generate HTML invoice instead of serving PDF
        console.log('Returning HTML invoice instead of PDF');
        const html = generateInvoiceHtml(invoice);

        // Return the HTML content
        return new NextResponse(html, {
          headers: {
            'Content-Type': 'text/html',
          },
        });
      } catch (error) {
        console.error(`Error serving invoice PDF: ${error}`);

        // Generate HTML invoice as fallback
        const html = generateInvoiceHtml(invoice);

        // Return the HTML content
        return new NextResponse(html, {
          headers: {
            'Content-Type': 'text/html',
          },
        });
      }
    } else {
      console.log(`Invoice does not have a PDF URL, will return HTML version`);

      // Generate HTML invoice
      const html = generateInvoiceHtml(invoice);

      // Return the HTML content
      return new NextResponse(html, {
        headers: {
          'Content-Type': 'text/html',
        },
      });
    }
  } catch (error) {
    console.error('Error generating invoice:', error);
    return NextResponse.json({ error: 'Failed to generate invoice' }, { status: 500 });
  }
}
