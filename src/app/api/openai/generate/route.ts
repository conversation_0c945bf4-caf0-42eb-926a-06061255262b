import { NextRequest, NextResponse } from 'next/server';
import { OpenAI } from 'openai';

// Create an OpenAI client instance
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    
    // Validate the request
    if (!body.prompt) {
      return NextResponse.json(
        { error: 'Missing required field: prompt' },
        { status: 400 }
      );
    }
    
    // Set default values for optional parameters
    const model = body.model || 'gpt-4o-mini';
    const temperature = body.temperature || 0.7;
    const maxTokens = body.maxTokens || 1000;
    const store = body.store !== undefined ? body.store : true;
    
    // Create a chat completion
    const completion = await openai.chat.completions.create({
      model,
      store,
      messages: [
        { role: 'user', content: body.prompt },
      ],
      temperature,
      max_tokens: maxTokens,
    });
    
    // Return the response
    return NextResponse.json({
      success: true,
      model: completion.model,
      content: completion.choices[0].message.content,
      usage: completion.usage,
    });
    
  } catch (error) {
    console.error('Error generating OpenAI response:', error);
    
    // Handle API errors
    if (error instanceof OpenAI.APIError) {
      return NextResponse.json(
        { 
          success: false,
          error: error.message,
          type: error.type,
          code: error.code,
        },
        { status: error.status || 500 }
      );
    }
    
    // Handle other errors
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
