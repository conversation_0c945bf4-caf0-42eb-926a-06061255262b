import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

// Create an OpenAI client instance with the API key from .env
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function GET(request: NextRequest) {
  try {
    // Create a chat completion using the provided code
    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      store: true,
      messages: [
        {"role": "user", "content": "write a haiku about ai"},
      ],
    });
    
    // Return the response
    return NextResponse.json({
      success: true,
      haiku: completion.choices[0].message.content,
      model: completion.model,
      usage: completion.usage,
    });
    
  } catch (error) {
    console.error('Error generating haiku:', error);
    
    // Handle API errors
    if (error instanceof OpenAI.APIError) {
      return NextResponse.json(
        { 
          success: false,
          error: error.message,
          type: error.type,
          code: error.code,
        },
        { status: error.status || 500 }
      );
    }
    
    // Handle other errors
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
