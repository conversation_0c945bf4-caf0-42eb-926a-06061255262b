import { NextRequest, NextResponse } from 'next/server';
import { getActiveScriptsByType } from '@/services/scriptService';

/**
 * GET handler for public scripts API
 * Returns active scripts of the specified type
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const type = url.searchParams.get('type') as 'head' | 'body' | 'footer';

    if (!type || !['head', 'body', 'footer'].includes(type)) {
      return NextResponse.json(
        { error: 'Invalid script type. Must be one of: head, body, footer' },
        { status: 400 }
      );
    }

    const scripts = await getActiveScriptsByType(type);
    return NextResponse.json(scripts);
  } catch (error) {
    console.error('Error fetching scripts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch scripts' },
      { status: 500 }
    );
  }
}
