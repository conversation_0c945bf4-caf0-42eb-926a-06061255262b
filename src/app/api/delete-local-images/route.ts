import { NextRequest, NextResponse } from 'next/server';
import { deleteLocalImages } from '@/utils/s3';

export async function POST(request: NextRequest) {
  try {
    // Get the path parameter from the request body
    const body = await request.json();
    const { path } = body;
    
    if (!path) {
      return NextResponse.json(
        { error: 'Path parameter is required' },
        { status: 400 }
      );
    }
    
    console.log(`Deleting local images from path: ${path}`);
    
    // Delete local images
    const result = await deleteLocalImages(path);
    
    return NextResponse.json({
      success: true,
      message: `Successfully processed ${result.total} images. Found ${result.existInS3} in S3, deleted ${result.deleted} local files, ${result.missing} were missing from S3.`,
      ...result
    });
  } catch (error) {
    console.error('Error deleting local images:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to delete local images', 
        details: (error as Error).message 
      },
      { status: 500 }
    );
  }
} 