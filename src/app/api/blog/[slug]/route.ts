import { NextRequest, NextResponse } from 'next/server';
import { BlogPost } from '@/types/blog';
import * as blogService from '@/services/blogService';

// GET handler - Get a specific blog post by slug
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Await params before accessing properties
    const slug = params.slug;
    console.log(`API route: Fetching blog post with slug: ${slug} from database`);

    // Get the blog post from database
    const post = await blogService.getBlogPostBySlug(slug);

    if (!post) {
      console.log(`Blog post not found or not published: ${slug}`);
      return NextResponse.json(
        { error: 'Blog post not found' },
        { status: 404 }
      );
    }

    console.log(`Successfully fetched blog post: ${post.title}`);

    return NextResponse.json(post, {
      headers: {
        'Cache-Control': 'no-store',
      },
    });
  } catch (error) {
    console.error('Error in GET blog post:', error);
    return NextResponse.json(
      { error: 'Failed to fetch blog post' },
      { status: 500 }
    );
  }
}
