import { NextRequest, NextResponse } from 'next/server';
import * as catalogueService from '@/services/simpleCatalogueService';

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    // Get the catalogue items from the database
    console.log('API: Fetching catalogue items from database...');
    const catalogueItems = await catalogueService.getAllCatalogue();

    // Check if we have any catalogue items
    if (!catalogueItems || catalogueItems.length === 0) {
      console.log('API: No catalogue items found in database');
      return NextResponse.json([], {
        headers: {
          'Cache-Control': 'public, max-age=60, s-maxage=120', // Cache for 1 minute
          'Expires': new Date(Date.now() + 60000).toUTCString() // 1 minute
        }
      });
    }

    console.log(`API: Retrieved ${catalogueItems.length} catalogue items from database`);

    // Filter by category if provided
    let filteredItems = catalogueItems;
    if (category) {
      console.log(`API: Filtering by category: ${category}`);
      filteredItems = catalogueItems.filter(item =>
        (item.category || 'Other') === category
      );
      console.log(`API: Found ${filteredItems.length} items in category "${category}"`);
    }

    // Add cache control headers - shorter cache time to ensure fresh data
    return NextResponse.json(filteredItems, {
      headers: {
        'Cache-Control': 'public, max-age=60, s-maxage=120', // Cache for 1 minute
        'Expires': new Date(Date.now() + 60000).toUTCString() // 1 minute
      }
    });
  } catch (error) {
    console.error('Error fetching catalogue items:', error);
    return new NextResponse(JSON.stringify({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}
