import { NextRequest, NextResponse } from 'next/server';
import * as catalogueService from '@/services/catalogueService';

// GET handler - Get a specific catalogue item by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    console.log(`API route: Fetching catalogue item with ID: ${id}`);

    // Get the catalogue item from database
    const catalogueItem = await catalogueService.getCatalogueById(id);

    if (!catalogueItem) {
      console.log(`Catalogue item not found: ${id}`);
      return NextResponse.json(
        { error: 'Catalogue item not found' },
        { status: 404 }
      );
    }

    console.log(`Successfully fetched catalogue item: ${catalogueItem.service}`);

    return NextResponse.json(catalogueItem, {
      headers: {
        'Cache-Control': 'no-store',
      },
    });
  } catch (error) {
    console.error(`Error fetching catalogue item ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
