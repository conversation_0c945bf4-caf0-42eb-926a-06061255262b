import { NextRequest, NextResponse } from 'next/server';
import { createLead, recordInteraction } from '@/services/leadManagementService';
import { trackEvent } from '@/services/eventTrackingService';

export async function POST(request: Request) {
  try {
    // Get form data
    const formData = await request.formData();
    const name = formData.get('name') as string;
    const email = formData.get('email') as string;
    const phone = formData.get('phone') as string;
    const service = formData.get('service') as string;
    const subject = formData.get('subject') as string;
    const message = formData.get('message') as string;
    const company = formData.get('company') as string;
    const source = formData.get('source') as string || 'contact_form';
    const sessionId = formData.get('sessionId') as string;

    // Validate required fields
    if (!name || !email || !message) {
      return NextResponse.json(
        { success: false, message: 'Name, email and message are required' },
        { status: 400 }
      );
    }

    // Log the submission
    console.log('Contact form submission received:', {
      name,
      email,
      phone,
      service,
      subject,
      message,
      company,
      source,
    });

    // Create a lead in the database
    const lead = await createLead({
      name,
      email,
      phone,
      company,
      source: source || 'website_contact',
      notes: message,
    });

    // Record the initial interaction
    await recordInteraction({
      leadId: lead.id,
      type: 'form_submission',
      details: `Contact form submission: ${subject || 'General inquiry'}`,
    });

    // Track the event
    await trackEvent({
      eventName: 'contact_form_submission',
      eventType: 'formSubmission',
      sessionId,
      leadId: lead.id,
      metadata: {
        name,
        email,
        phone,
        service,
        subject,
        company,
        source,
      },
    });

    // Return success with the lead ID
    return NextResponse.json(
      {
        success: true,
        message: 'Your message has been sent. We will get back to you soon!',
        leadId: lead.id
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error processing contact form:', error);
    return NextResponse.json(
      { success: false, message: 'There was an error processing your request. Please try again later.' },
      { status: 500 }
    );
  }
}