import { NextRequest, NextResponse } from 'next/server';
import { getReceiptById, Receipt } from '@/services/receiptService';
import { promises as fs } from 'fs';
import path from 'path';

/**
 * Generate HTML receipt
 * @param receipt The receipt data
 * @returns HTML string
 */
function generateReceiptHtml(receipt: Receipt): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Receipt #${receipt.receiptNumber}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        .header-container {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 30px;
          position: relative;
        }
        .logo-container {
          flex: 0 0 25%;
          text-align: left;
        }
        .company-info {
          flex: 0 0 25%;
          text-align: right;
        }
        .receipt-center {
          flex: 0 0 50%;
          text-align: center;
          padding: 0 15px;
        }
        .receipt-title {
          margin: 0 0 15px 0;
        }
        .receipt-details {
          text-align: center;
          margin-bottom: 10px;
        }
        .receipt-info {
          margin-bottom: 20px;
        }
        .customer-info {
          margin-bottom: 30px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
        }
        th, td {
          padding: 10px;
          text-align: left;
          border-bottom: 1px solid #ddd;
        }
        th {
          background-color: #f2f2f2;
        }
        .totals {
          margin-left: auto;
          width: 300px;
        }
        .totals td:last-child {
          text-align: right;
        }
        .footer {
          text-align: center;
          margin-top: 50px;
          font-size: 14px;
          color: #666;
        }
        /* Responsive scaling for different screen sizes */
        @media screen and (max-width: 600px) {
          body {
            padding: 10px;
            font-size: 14px;
          }
          .header-container {
            flex-direction: column;
            align-items: center;
          }
          .logo-container, .company-info, .receipt-center {
            flex: 0 0 100%;
            text-align: center;
            margin-bottom: 15px;
          }
          .totals {
            width: 100%;
          }
        }
        /* Print-specific styles with auto-scaling */
        @media print {
          @page {
            size: auto;  /* auto is the default value */
            margin: 0mm; /* this affects the margin in the printer settings */
          }
          html {
            background-color: #FFFFFF;
            margin: 0; /* this affects the margin on the html before sending to printer */
          }
          body {
            padding: 10mm 15mm; /* margin you want for the content */
            margin: 0;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
            width: 100%;
            height: 100%;
            box-sizing: border-box;
          }
          button {
            display: none;
          }
          .header-container {
            display: flex !important;
          }
          .logo-container img {
            display: block !important;
            visibility: visible !important;
            max-width: 100px;
            height: auto;
          }
          .print-button {
            display: none !important;
          }
          /* Scale content to fit the page */
          table, .totals {
            max-width: 100%;
            width: 100%;
            font-size: 10pt;
          }
          th, td {
            padding: 5px;
          }
          h1 {
            font-size: 18pt;
          }
          h2 {
            font-size: 14pt;
          }
          h3 {
            font-size: 12pt;
          }
          p {
            font-size: 10pt;
          }
        }
      </style>
    </head>
    <body>
      <div class="header-container">
        <div class="logo-container">
          <img src="/logo.png" alt="Mocky Digital Logo" style="max-width: 120px; height: auto;">
        </div>

        <div class="receipt-center">
          <div class="receipt-title">
            <h1 style="margin: 10px 0;">RECEIPT</h1>
          </div>

          <div class="receipt-details">
            <p style="margin: 5px 0;">Transaction ID: <strong>${receipt.receiptNumber}</strong></p>
            <p style="margin: 5px 0;">Date: ${new Date(receipt.issuedAt).toLocaleDateString()}</p>
            <p style="margin: 5px 0;">Status: ${receipt.status}</p>
          </div>
        </div>

        <div class="company-info">
          <h2 style="margin-top: 0;">Mocky Digital</h2>
          <p style="margin: 5px 0;">Nairobi, Kenya</p>
          <p style="margin: 5px 0;">Phone: +*********** 670</p>
          <p style="margin: 5px 0;">Email: <EMAIL></p>
          <p style="margin: 5px 0;">Tax PIN: P052373324V</p>
        </div>
      </div>

      <div class="customer-info">
        <h3>Customer Information</h3>
        <p>Name: ${receipt.customerName}</p>
        <p>Phone: ${receipt.phoneNumber}</p>
        ${receipt.email ? `<p>Email: ${receipt.email}</p>` : ''}
      </div>

      <h3>Receipt Items</h3>
      <table>
        <thead>
          <tr>
            <th>Description</th>
            <th>Quantity</th>
            <th>Unit Price</th>
            <th>Total</th>
          </tr>
        </thead>
        <tbody>
          ${receipt.items.map(item => `
            <tr>
              <td>${item.description || 'Service'}</td>
              <td>${item.quantity}</td>
              <td>KES ${item.unitPrice.toLocaleString()}</td>
              <td>KES ${item.totalPrice.toLocaleString()}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      <table class="totals">
        <tr>
          <td>Subtotal:</td>
          <td>KES ${receipt.totalAmount.toLocaleString()}</td>
        </tr>
        <tr>
          <td>Amount Paid:</td>
          <td>KES ${receipt.amountPaid.toLocaleString()}</td>
        </tr>
        <tr>
          <td><strong>Balance:</strong></td>
          <td><strong>KES ${receipt.balance.toLocaleString()}</strong></td>
        </tr>
      </table>

      ${receipt.notes ? `
        <div class="notes">
          <h3>Notes</h3>
          <p>${receipt.notes}</p>
        </div>
      ` : ''}

      <div class="footer">
        <p>Thank you for your business!</p>
        <p>This is a computer-generated receipt and does not require a signature.</p>
      </div>

      <button class="print-button" style="display: block; margin: 30px auto; padding: 10px 20px; background-color: #3182ce; color: white; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;">
        Print Receipt
      </button>

      <script>
        // Function to handle printing with scaling
        function printReceipt() {
          // Set print-specific styles
          const style = document.createElement('style');
          style.textContent =
            "@media print {" +
              "@page {" +
                "size: auto;" +
                "margin: 0;" +
              "}" +
              "body {" +
                "zoom: 1;" +
                "transform-origin: top left;" +
                "transform: scale(1);" +
              "}" +
            "}";
          document.head.appendChild(style);

          // Trigger print dialog
          window.print();

          // Remove the style after printing
          setTimeout(() => {
            document.head.removeChild(style);
          }, 1000);
        }

        // Auto-trigger print dialog when the page loads
        window.addEventListener('load', function() {
          // Add a small delay to ensure the page is fully rendered
          setTimeout(function() {
            // Check if the logo is loaded
            var logo = document.querySelector('.logo-container img');
            if (logo) {
              logo.onload = function() {
                // Logo is loaded, now we can print
                // Uncomment the line below to auto-print when page loads
                // printReceipt();
              };

              // If logo is already loaded or fails to load, still allow printing
              if (logo.complete) {
                // Uncomment the line below to auto-print when page loads
                // printReceipt();
              }
            } else {
              // No logo found, print anyway
              // Uncomment the line below to auto-print when page loads
              // printReceipt();
            }

            // Add click event to print button
            const printButton = document.querySelector('.print-button');
            if (printButton) {
              printButton.addEventListener('click', function(e) {
                e.preventDefault();
                printReceipt();
              });
            }
          }, 500);
        });
      </script>
    </body>
    </html>
  `;
}

/**
 * GET /api/receipts/[id]
 * Generate and serve a receipt as PDF or HTML
 */
export async function GET(
  req: NextRequest,
  context: { params: { id: string } }
) {
  try {
    // Get the ID from params
    const id = context.params.id;
    console.log(`Receipt download requested for ID: ${id}`);

    if (!id || id === 'undefined' || id === 'null') {
      console.error('Invalid receipt ID provided:', id);
      return NextResponse.json({ error: 'Invalid receipt ID' }, { status: 400 });
    }

    // Get the receipt data
    console.log(`Fetching receipt with ID: ${id}`);
    const receipt = await getReceiptById(id);

    if (!receipt) {
      console.error(`Receipt not found for ID: ${id}`);

      // Return a more user-friendly error page instead of JSON
      const errorHtml = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Receipt Not Found</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            .header-container {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 30px;
              position: relative;
            }
            .logo-container {
              flex: 0 0 25%;
              text-align: left;
            }
            .company-info {
              flex: 0 0 25%;
              text-align: right;
            }
            .receipt-center {
              flex: 0 0 50%;
              text-align: center;
              padding: 0 15px;
            }
            .error-container {
              margin-top: 20px;
              padding: 30px;
              border: 1px solid #ddd;
              border-radius: 8px;
              background-color: #f9f9f9;
              text-align: center;
            }
            h1 {
              color: #e53e3e;
            }
            .back-button {
              display: inline-block;
              margin-top: 20px;
              padding: 10px 20px;
              background-color: #3182ce;
              color: white;
              text-decoration: none;
              border-radius: 5px;
            }
          </style>
        </head>
        <body>
          <div class="header-container">
            <div class="logo-container">
              <img src="/logo.png" alt="Mocky Digital Logo" style="max-width: 120px; height: auto;">
            </div>

            <div class="receipt-center">
              <h1 style="color: #e53e3e; margin: 10px 0;">Receipt Not Found</h1>
            </div>

            <div class="company-info">
              <h2 style="margin-top: 0;">Mocky Digital</h2>
              <p style="margin: 5px 0;">Nairobi, Kenya</p>
              <p style="margin: 5px 0;">Phone: +*********** 670</p>
              <p style="margin: 5px 0;">Email: <EMAIL></p>
              <p style="margin: 5px 0;">Tax PIN: P052373324V</p>
            </div>
          </div>

          <div class="error-container">
            <p>Sorry, we couldn't find the receipt you're looking for.</p>
            <p>The receipt ID <strong>${id}</strong> does not exist in our system.</p>
            <a href="/admin/receipts" class="back-button">Back to Receipts</a>
          </div>
        </body>
        </html>
      `;

      return new NextResponse(errorHtml, {
        headers: {
          'Content-Type': 'text/html',
        },
        status: 404
      });
    }

    console.log(`Receipt found: ${receipt.receiptNumber}`);


    // Check if the receipt has a PDF URL
    if (receipt.pdfUrl) {
      console.log(`Receipt has PDF URL: ${receipt.pdfUrl}`);

      // Get the PDF file path
      const pdfPath = path.join(process.cwd(), 'public', receipt.pdfUrl);
      console.log(`Looking for PDF file at: ${pdfPath}`);

      try {
        // Check if the file exists
        await fs.access(pdfPath);
        console.log(`PDF file exists at: ${pdfPath}`);

        // Read the PDF file
        const pdfBuffer = await fs.readFile(pdfPath);
        console.log(`PDF file read successfully, size: ${pdfBuffer.length} bytes`);

        // Generate a filename for download using the transaction ID (receipt number)
        const transactionId = receipt.receiptNumber;
        const filename = `${transactionId}.pdf`;

        // Verify the PDF buffer is valid
        if (pdfBuffer.length === 0) {
          console.error('PDF file exists but is empty');
          throw new Error('PDF file is empty');
        }

        // Return the PDF file with appropriate headers for reliable download
        return new NextResponse(pdfBuffer, {
          headers: {
            'Content-Type': 'application/pdf',
            'Content-Disposition': `attachment; filename="${filename}"`,
            'Content-Length': pdfBuffer.length.toString(),
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
          },
        });
      } catch (fileError) {
        console.error(`PDF file not found at ${pdfPath}:`, fileError);
        console.log(`Will attempt to generate a new PDF file for receipt: ${receipt.receiptNumber}`);

        try {
          // Verify receipt is defined before proceeding
          if (!receipt) {
            console.error('Receipt is undefined, cannot generate receipt');
            throw new Error('Receipt is undefined');
          }

          console.log('Skipping PDF generation due to font issues, using HTML receipt instead');

          // Use a fallback URL that points to the HTML version
          const pdfUrl = `/api/receipts/${receipt.id}`;
          console.log(`Using HTML receipt URL: ${pdfUrl}`);

          // Update the receipt with the new PDF URL if receipt exists
          if (receipt && receipt.id) {
            const { prisma } = await import('@/lib/prisma');
            await prisma.receipt.update({
              where: { id: receipt.id },
              data: { pdfUrl }
            });
            console.log(`Updated receipt with new PDF URL: ${pdfUrl}`);
          } else {
            console.error('Cannot update receipt: receipt or receipt.id is undefined');
          }

          // Get the new PDF file path
          const newPdfPath = path.join(process.cwd(), 'public', pdfUrl);

          // Skip PDF file reading and return HTML receipt directly
          console.log('Returning HTML receipt instead of PDF');

          // Generate HTML receipt
          const html = generateReceiptHtml(receipt);

          // Return the HTML content
          return new NextResponse(html, {
            headers: {
              'Content-Type': 'text/html',
            },
          });
        } catch (genError) {
          console.error(`Failed to generate new PDF:`, genError);
          // Fall back to HTML if PDF generation fails
        }
      }
    } else {
      console.log(`Receipt does not have a PDF URL, will attempt to generate one`);

      try {
        // Verify receipt is defined before proceeding
        if (!receipt) {
          console.error('Receipt is undefined, cannot generate receipt');
          throw new Error('Receipt is undefined');
        }

        console.log('Skipping PDF generation due to font issues, using HTML receipt instead');

        // Use a fallback URL that points to the HTML version
        const pdfUrl = `/api/receipts/${receipt.id}`;
        console.log(`Using HTML receipt URL: ${pdfUrl}`);

        // Update the receipt with the new PDF URL if receipt exists
        if (receipt && receipt.id) {
          const { prisma } = await import('@/lib/prisma');
          await prisma.receipt.update({
            where: { id: receipt.id },
            data: { pdfUrl }
          });
          console.log(`Updated receipt with new PDF URL: ${pdfUrl}`);
        } else {
          console.error('Cannot update receipt: receipt or receipt.id is undefined');
        }

        // Get the new PDF file path
        const newPdfPath = path.join(process.cwd(), 'public', pdfUrl);

        // Skip PDF file reading and return HTML receipt directly
        console.log('Returning HTML receipt instead of PDF');

        // Generate HTML receipt
        const html = generateReceiptHtml(receipt);

        // Return the HTML content
        return new NextResponse(html, {
          headers: {
            'Content-Type': 'text/html',
          },
        });
      } catch (genError) {
        console.error(`Failed to generate new PDF:`, genError);
        // Fall back to HTML if PDF generation fails
      }
    }

    // If we reach here, return the HTML receipt as a fallback
    console.log('Using HTML receipt as fallback');
    const html = generateReceiptHtml(receipt);

    // Return the HTML content
    return new NextResponse(html, {
      headers: {
        'Content-Type': 'text/html',
      },
    });
  } catch (error) {
    console.error('Error generating receipt:', error);
    return NextResponse.json({ error: 'Failed to generate receipt' }, { status: 500 });
  }
}
