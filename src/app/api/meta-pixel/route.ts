import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';

// Define the event data interface
interface MetaPixelEvent {
  event_name: string;
  event_time: number;
  event_id?: string;
  event_source_url?: string;
  user_data?: {
    client_ip_address?: string;
    client_user_agent?: string;
    em?: string; // Hashed email
    ph?: string; // Hashed phone
    fn?: string; // Hashed first name
    ln?: string; // Hashed last name
    external_id?: string;
    fbp?: string; // Facebook Browser ID
    fbc?: string; // Facebook Click ID
  };
  custom_data?: Record<string, any>;
  action_source?: 'website' | 'app' | 'email' | 'phone_call' | 'chat' | 'other';
}

/**
 * Hash a string using SHA-256
 * @param value The string to hash
 * @returns The hashed string
 */
function hashValue(value: string): string {
  if (!value) return '';
  return crypto.createHash('sha256').update(value.toLowerCase().trim()).digest('hex');
}

/**
 * Send an event to Meta's Conversion API
 * @param pixelId The Meta Pixel ID
 * @param accessToken The Meta Pixel access token
 * @param event The event data
 * @returns The response from Meta's API
 */
async function sendToMetaConversionAPI(
  pixelId: string,
  accessToken: string,
  event: MetaPixelEvent
): Promise<any> {
  const url = `https://graph.facebook.com/v17.0/${pixelId}/events`;

  const data = {
    data: [event],
    access_token: accessToken,
  };

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    return await response.json();
  } catch (error) {
    console.error('Error sending event to Meta Conversion API:', error);
    throw error;
  }
}

/**
 * POST handler for Meta Pixel server-side events
 * This endpoint allows sending events to Meta's Conversion API
 */
export async function POST(request: NextRequest) {
  try {
    // Get the Meta Pixel ID and access token from environment variables
    const pixelId = process.env.NEXT_PUBLIC_META_PIXEL_ID;
    const accessToken = process.env.META_CONVERSION_API_ACCESS_TOKEN;

    // Check if the required environment variables are set
    if (!pixelId) {
      return NextResponse.json(
        { error: 'Meta Pixel ID not configured' },
        { status: 500 }
      );
    }

    // Check if the access token is set and not a placeholder
    if (!accessToken || accessToken === "your-meta-conversion-api-access-token") {
      console.warn('Meta Conversion API access token not properly configured. Falling back to client-side tracking only.');
      return NextResponse.json(
        {
          warning: 'Meta Conversion API not configured. Event not sent server-side.',
          success: false,
          fallback: 'client-side-only'
        },
        { status: 200 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate the request body
    if (!body.event_name) {
      return NextResponse.json(
        { error: 'Missing required field: event_name' },
        { status: 400 }
      );
    }

    // Get client IP and user agent
    const clientIp = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Prepare the event data
    const event: MetaPixelEvent = {
      event_name: body.event_name,
      event_time: Math.floor(Date.now() / 1000),
      event_id: body.event_id || crypto.randomUUID(),
      event_source_url: body.event_source_url || request.headers.get('referer') || undefined,
      action_source: 'website',
      user_data: {
        client_ip_address: clientIp,
        client_user_agent: userAgent,
        ...body.user_data,
      },
      custom_data: body.custom_data || {},
    };

    // Hash user data if provided
    if (body.user_data) {
      if (body.user_data.email) {
        event.user_data!.em = hashValue(body.user_data.email);
      }
      if (body.user_data.phone) {
        event.user_data!.ph = hashValue(body.user_data.phone);
      }
      if (body.user_data.firstName) {
        event.user_data!.fn = hashValue(body.user_data.firstName);
      }
      if (body.user_data.lastName) {
        event.user_data!.ln = hashValue(body.user_data.lastName);
      }
    }

    // Send the event to Meta's Conversion API
    const response = await sendToMetaConversionAPI(pixelId, accessToken, event);

    // Return the response
    return NextResponse.json(response);
  } catch (error) {
    console.error('Error processing Meta Pixel event:', error);
    return NextResponse.json(
      { error: 'Failed to process Meta Pixel event' },
      { status: 500 }
    );
  }
}
