import { NextAuthOptions } from 'next-auth';
import NextAuth from 'next-auth/next';
import CredentialsProvider from 'next-auth/providers/credentials';
import { findUserByUsernameOrEmail, updateUserLastLogin, verifyPassword, logActivity } from '@/utils/passwordUtils';

// We'll handle admin user creation in a separate process to avoid issues during initialization

declare module "next-auth" {
  interface User {
    id: string;
    username: string;
    email?: string;
    name?: string;
    role?: {
      id: string;
      name: string;
      permissions: string[];
    };
  }

  interface Session {
    user: {
      id: string;
      username: string;
      email?: string;
      name?: string;
      role?: {
        id: string;
        name: string;
        permissions: string[];
      };
    };
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials, req) {
        if (!credentials?.username || !credentials?.password) {
          throw new Error('Username and password are required');
        }

        try {
          // Find user in database
          const user = await findUserByUsernameOrEmail(credentials.username);

          if (!user) {
            console.log(`User not found: ${credentials.username}`);
            throw new Error('Invalid credentials');
          }

          // Verify password
          const isValid = await verifyPassword(credentials.password, user.passwordHash);

          if (!isValid) {
            console.log(`Invalid password for user: ${credentials.username}`);
            throw new Error('Invalid credentials');
          }

          // Update last login time
          await updateUserLastLogin(user.id);

          // Log login activity
          const ipAddress = req?.headers?.['x-forwarded-for'] as string ||
                           req?.headers?.['x-real-ip'] as string ||
                           'unknown';

          const userAgent = req?.headers?.['user-agent'] as string || 'unknown';

          await logActivity(
            user.id,
            'login',
            'User logged in successfully',
            ipAddress,
            userAgent
          );

          // Return user data for token
          return {
            id: user.id,
            username: user.username,
            email: user.email,
            name: user.name || user.username,
            role: user.role,
          };
        } catch (error) {
          console.error('Authentication error:', error);
          throw new Error('Invalid credentials');
        }
      }
    })
  ],
  pages: {
    signIn: '/admin/login',
    error: '/admin/login',
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.username = user.username;
        token.email = user.email;
        token.name = user.name;
        token.role = user.role;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user = {
          id: token.id as string,
          username: token.username as string,
          email: token.email as string,
          name: token.name as string,
          role: token.role as {
            id: string;
            name: string;
            permissions: string[];
          },
        };
      }
      return session;
    }
  },
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 hours
  },
  secret: process.env.NEXTAUTH_SECRET,
};

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST };