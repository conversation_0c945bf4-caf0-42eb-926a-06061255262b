'use client';

import { useState } from 'react';

export default function HaikuPage() {
  const [loading, setLoading] = useState(false);
  const [haiku, setHaiku] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const generateHaiku = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/openai/haiku');
      const data = await response.json();
      
      if (data.success) {
        setHaiku(data.haiku);
      } else {
        setError(data.error || 'Failed to generate haiku');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">AI Haiku Generator</h1>
      
      <button
        onClick={generateHaiku}
        disabled={loading}
        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400 transition-colors"
      >
        {loading ? 'Generating...' : 'Generate Haiku'}
      </button>
      
      {haiku && (
        <div className="mt-8 p-6 bg-gray-50 rounded-lg border border-gray-200">
          <h2 className="text-xl font-semibold mb-4">Your AI-Generated Haiku</h2>
          <p className="text-lg italic whitespace-pre-wrap">{haiku}</p>
        </div>
      )}
      
      {error && (
        <div className="mt-4 p-4 bg-red-50 text-red-700 rounded-md border border-red-200">
          <p className="font-medium">Error:</p>
          <p>{error}</p>
        </div>
      )}
    </div>
  );
}
