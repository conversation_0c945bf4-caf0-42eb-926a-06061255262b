'use client';

import dynamic from 'next/dynamic';
import { motion } from 'framer-motion';
import PageHero from '@/components/PageHero';

// Dynamic import for ColorSelector
const ColorSelector = dynamic(() => import('@/components/ColorSelector'), {
  loading: () => <div className="flex items-center justify-center p-12">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
  </div>,
  ssr: false
});

export default function BrandColors() {
  return (
    <div className="pt-24 bg-gray-50">
      <PageHero 
        title="Brand Color Selection"
        subtitle="Choose the perfect colors to represent your brand identity"
        bgImage="/images/brand-colors-hero.jpg" // Add an appropriate hero image
      />

      {/* Color Selection Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">Brand Color Explorer</h2>
            <p className="mt-4 text-gray-600 max-w-3xl mx-auto">
              Discover the perfect color combination that reflects your brand's personality and values. Choose from our curated palettes or create your own unique color scheme.
            </p>
          </motion.div>

          {/* Color Theory Guide */}
          <div className="mb-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-gray-50 p-6 rounded-xl"
            >
              <h3 className="text-xl font-semibold mb-3">Color Psychology</h3>
              <p className="text-gray-600">
                Colors evoke emotions and convey messages. Understanding color psychology helps create a meaningful brand identity.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-gray-50 p-6 rounded-xl"
            >
              <h3 className="text-xl font-semibold mb-3">Color Harmony</h3>
              <p className="text-gray-600">
                Well-balanced color combinations create visual harmony and make your brand more memorable and appealing.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-gray-50 p-6 rounded-xl"
            >
              <h3 className="text-xl font-semibold mb-3">Brand Consistency</h3>
              <p className="text-gray-600">
                Consistent use of colors across all platforms strengthens brand recognition and professional appearance.
              </p>
            </motion.div>
          </div>
          
          <ColorSelector />

          {/* Color Tips Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="mt-16 bg-gray-50 p-8 rounded-xl"
          >
            <h3 className="text-2xl font-semibold mb-6">Tips for Choosing Brand Colors</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-2">Consider Your Industry</h4>
                <p className="text-gray-600">Different industries have different color expectations. Choose colors that align with your sector while standing out.</p>
              </div>
              <div>
                <h4 className="font-medium mb-2">Think About Application</h4>
                <p className="text-gray-600">Your colors will be used across various mediums. Ensure they work well in both digital and print formats.</p>
              </div>
              <div>
                <h4 className="font-medium mb-2">Test for Accessibility</h4>
                <p className="text-gray-600">Make sure your color combinations are accessible and readable for all users.</p>
              </div>
              <div>
                <h4 className="font-medium mb-2">Future-Proof Your Palette</h4>
                <p className="text-gray-600">Choose timeless colors that won't feel outdated as trends change.</p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Need Help with Your Brand Identity?</h2>
          <p className="mb-8 text-gray-300">
            Our design experts can help you create a complete brand identity that stands out.
          </p>
          <a 
            href="https://wa.me/254741590670"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 px-8 py-3 bg-primary text-white rounded-full hover:bg-primary-dark transition-all"
          >
            Contact Our Design Team <i className="fas fa-arrow-right"></i>
          </a>
        </div>
      </section>
    </div>
  );
} 