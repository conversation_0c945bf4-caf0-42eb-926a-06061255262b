'use client';

import { useState } from 'react';
import PageHero from '@/components/PageHero';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';

const trainingPackages = [
  {
    title: "Graphics Design (Photoshop & Illustrator)",
    description: "Master professional graphic design with Adobe's creative tools",
    icon: "images/training/graphics.png",
    packages: [
      {
        name: "Basic Package",
        price: "25,000",
        duration: "1 Month",
        sessions: "12 Sessions",
        features: [
          "Adobe Photoshop Essentials",
          "Adobe Illustrator Fundamentals",
          "Photo Editing & Manipulation",
          "Vector Graphics Creation",
          "Layer Management",
          "Typography & Color Theory",
          "Logo Design Basics",
          "Social Media Graphics",
          "Print Design Basics",
          "Practice Projects"
        ]
      },
      {
        name: "Professional Package",
        price: "35,000",
        duration: "2 Months",
        sessions: "16 Sessions",
        popular: true,
        features: [
          "All Basic Package Features",
          "Advanced Photo Manipulation",
          "Complex Vector Illustrations",
          "Brand Identity Design",
          "Advanced Logo Design",
          "Print & Digital Marketing Materials",
          "UI/UX Design Elements",
          "Portfolio Development",
          "1 Month Post-Training Support"
        ]
      }
    ]
  },
  {
    title: "Adobe Premiere Pro",
    description: "Professional video editing and production",
    icon: "images/training/premiere.png",
    packages: [
      {
        name: "Basic Package",
        price: "20,000",
        duration: "2 Weeks",
        sessions: "6 Sessions",
        features: [
          "Interface Overview",
          "Basic Video Editing",
          "Audio Management",
          "Transitions & Effects",
          "Title Creation",
          "Video Export",
          "Practice Projects"
        ]
      },
      {
        name: "Professional Package",
        price: "35,000",
        duration: "1 Month",
        sessions: "12 Sessions",
        popular: true,
        features: [
          "All Basic Package Features",
          "Advanced Editing Techniques",
          "Color Grading",
          "Motion Graphics Integration",
          "Multi-camera Editing",
          "Audio Enhancement",
          "Portfolio Development",
          "1 Month Post-Training Support"
        ]
      }
    ]
  }
];

export default function Training() {
  const [selectedPackage, setSelectedPackage] = useState<any>(null);
  const [showRequestForm, setShowRequestForm] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50">
      <PageHero
        title="Adobe Creative Suite Training"
        description="Master industry-standard creative tools with our professional training programs"
      />

      {/* Training Programs Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          {trainingPackages.map((tool, index) => (
            <div key={tool.title} className={`mb-20 ${index !== trainingPackages.length - 1 ? 'border-b border-gray-200 pb-20' : ''}`}>
              <div className="text-center mb-12">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{tool.title}</h2>
                  <p className="text-xl text-gray-600">{tool.description}</p>
                </motion.div>
              </div>

              <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
                {tool.packages.map((pkg, pkgIndex) => (
                  <motion.div
                    key={pkg.name}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: pkgIndex * 0.1 }}
                    viewport={{ once: true }}
                    className={`bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden relative ${
                      pkg.popular ? 'border-2 border-primary' : ''
                    }`}
                  >
                    {pkg.popular && (
                      <div className="absolute top-0 right-0">
                        <div className="bg-primary text-white text-xs font-semibold px-3 py-1 rounded-bl-lg">
                          Most Popular
                        </div>
                      </div>
                    )}

                    <div className="p-8">
                      <h3 className="text-2xl font-bold text-gray-900 mb-4">{pkg.name}</h3>
                      <div className="flex items-baseline gap-1 mb-6">
                        <span className="text-sm text-gray-500">KSH</span>
                        <span className="text-4xl font-bold text-primary">{pkg.price}</span>
                      </div>

                      <div className="flex items-center gap-4 mb-6">
                        <div className="flex items-center gap-2">
                          <i className="fas fa-clock text-primary"></i>
                          <span className="text-gray-600">{pkg.duration}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <i className="fas fa-chalkboard-teacher text-primary"></i>
                          <span className="text-gray-600">{pkg.sessions}</span>
                        </div>
                      </div>

                      <ul className="space-y-3 mb-8">
                        {pkg.features.map((feature, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <i className="fas fa-check-circle text-green-500 mt-1"></i>
                            <span className="text-gray-600">{feature}</span>
                          </li>
                        ))}
                      </ul>

                      <button
                        onClick={() => {
                          setSelectedPackage({
                            tool: tool.title,
                            ...pkg
                          });
                          setShowRequestForm(true);
                        }}
                        className={`w-full py-4 rounded-xl font-medium transition-all duration-300 ${
                          pkg.popular
                            ? 'bg-primary text-white hover:bg-primary/90'
                            : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                        }`}
                      >
                        Enroll Now
                      </button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Why Choose Our Training?</h2>
            <p className="text-xl text-gray-600">Learn from industry professionals with years of experience</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {[
              {
                icon: "fas fa-user-tie",
                title: "Expert Instructors",
                description: "Learn from professionals with real-world experience in creative industries."
              },
              {
                icon: "fas fa-users",
                title: "Small Class Size",
                description: "Maximum attention with limited students per class for better learning."
              },
              {
                icon: "fas fa-laptop",
                title: "Hands-on Practice",
                description: "Work on real projects to build your portfolio during training."
              },
              {
                icon: "fas fa-headset",
                title: "Post-Training Support",
                description: "Get support even after completing your training program."
              },
              {
                icon: "fas fa-briefcase",
                title: "Job Assistance",
                description: "Get guidance on freelancing and finding creative work."
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300"
              >
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <i className={`${feature.icon} text-primary text-xl`}></i>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* System Requirements Section */}
      <section className="py-16 bg-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-blue-100">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <i className="fas fa-laptop-code text-primary mr-3"></i>
                System Requirements for Online Classes
              </h2>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0 mt-1">
                    <i className="fas fa-laptop text-primary"></i>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">Computer Requirements</h3>
                    <p className="text-gray-600">A laptop or desktop computer with at least:</p>
                    <ul className="mt-2 space-y-1 text-gray-600 ml-4">
                      <li>• 8GB RAM or higher</li>
                      <li>• Intel Core i5/AMD Ryzen 5 processor or better</li>
                      <li>• 256GB SSD storage or higher</li>
                      <li>• 1920x1080 screen resolution or higher</li>
                    </ul>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0 mt-1">
                    <i className="fas fa-wifi text-primary"></i>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">Internet Connection</h3>
                    <p className="text-gray-600">Reliable high-speed internet connection:</p>
                    <ul className="mt-2 space-y-1 text-gray-600 ml-4">
                      <li>• Minimum 10 Mbps download speed</li>
                      <li>• Minimum 5 Mbps upload speed</li>
                      <li>• Stable connection for video calls</li>
                    </ul>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0 mt-1">
                    <i className="fas fa-headset text-primary"></i>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">Additional Requirements</h3>
                    <ul className="mt-2 space-y-1 text-gray-600 ml-4">
                      <li>• Audio capability (speakers/headphones) required</li>
                      <li>• Microphone for class participation required</li>
                      <li>• Webcam recommended but optional</li>
                      <li>• Quiet workspace for attending classes</li>
                    </ul>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <i className="fas fa-info-circle mr-2"></i>
                    Note: These requirements ensure you can participate fully in the online training sessions and complete practical exercises effectively. While webcam is recommended for better interaction, it's not mandatory. If you have any concerns about meeting these requirements, please contact us before enrolling.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enrollment Form Modal */}
      {showRequestForm && selectedPackage && (
        <div
          className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4 backdrop-blur-sm"
          onClick={() => setShowRequestForm(false)}
        >
          <div
            className="bg-white rounded-2xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            onClick={e => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-2xl font-bold text-gray-900">Enroll in Training Program</h3>
              <button
                onClick={() => setShowRequestForm(false)}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            <EnrollmentForm package={selectedPackage} onClose={() => setShowRequestForm(false)} />
          </div>
        </div>
      )}
    </div>
  );
}

function EnrollmentForm({ package: selectedPackage, onClose }: { package: any, onClose: () => void }) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    preferredSchedule: 'weekday-morning',
    message: '',
    experience: 'beginner'
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Format WhatsApp message
    const message = `
*Training Program Enrollment Request*

*Selected Program:* ${selectedPackage.tool} - ${selectedPackage.name}
*Price:* KSH ${selectedPackage.price}
*Duration:* ${selectedPackage.duration}

*Student Details:*
Name: ${formData.name}
Email: ${formData.email}
Phone: ${formData.phone}
Experience Level: ${formData.experience}
Preferred Schedule: ${formData.preferredSchedule}

*Additional Message:*
${formData.message}
    `.trim();

    // Open WhatsApp with the message
    window.open(`https://wa.me/254741590670?text=${encodeURIComponent(message)}`, '_blank');
    onClose();
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <h4 className="font-semibold text-gray-900">Selected Package:</h4>
        <p className="text-primary">{selectedPackage.tool} - {selectedPackage.name}</p>
        <p className="text-gray-600">KSH {selectedPackage.price} • {selectedPackage.duration}</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
          <input
            type="text"
            required
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
          <input
            type="email"
            required
            value={formData.email}
            onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
          <input
            type="tel"
            required
            value={formData.phone}
            onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Experience Level</label>
          <select
            value={formData.experience}
            onChange={(e) => setFormData(prev => ({ ...prev, experience: e.target.value }))}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
          >
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">Preferred Schedule</label>
          <select
            value={formData.preferredSchedule}
            onChange={(e) => setFormData(prev => ({ ...prev, preferredSchedule: e.target.value }))}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
          >
            <option value="weekday-morning">Weekday Mornings (9 AM - 12 PM)</option>
            <option value="weekday-afternoon">Weekday Afternoons (2 PM - 5 PM)</option>
            <option value="weekday-evening">Weekday Evenings (6 PM - 9 PM)</option>
            <option value="weekend-morning">Weekend Mornings (9 AM - 12 PM)</option>
            <option value="weekend-afternoon">Weekend Afternoons (2 PM - 5 PM)</option>
          </select>
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">Additional Message (Optional)</label>
          <textarea
            value={formData.message}
            onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
            rows={4}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
            placeholder="Any specific requirements or questions..."
          ></textarea>
        </div>
      </div>

      <div className="flex justify-end gap-4 pt-4">
        <button
          type="button"
          onClick={onClose}
          className="px-6 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
        >
          Submit Enrollment
        </button>
      </div>
    </form>
  );
}