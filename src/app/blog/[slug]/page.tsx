import Link from 'next/link';
import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { BlogPost } from '@/types/blog';
import { CalendarIcon, UserIcon, TagIcon } from '@heroicons/react/24/outline';
import { sanitizeHtml } from '@/utils/sanitizeHtml';

interface BlogPostPageProps {
  params: {
    slug: string;
  };
}

// Generate metadata for the page
export async function generateMetadata(props: BlogPostPageProps): Promise<Metadata> {
  // Use the params object directly without destructuring
  const post = await getBlogPost(props.params.slug);

  if (!post) {
    return {
      title: 'Post Not Found | Mocky Digital',
      description: 'The requested blog post could not be found.',
    };
  }

  return {
    title: `${post.title} | Mocky Digital Blog`,
    description: post.excerpt,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      type: 'article',
      publishedTime: post.publishedAt || post.createdAt,
      modifiedTime: post.updatedAt,
      authors: [post.author],
      tags: post.tags,
    },
  };
}

async function getBlogPost(slug: string): Promise<BlogPost | null> {
  try {
    console.log('Getting blog post from database');

    // Import the service directly - this is the recommended approach for server components
    const { getBlogPostBySlug } = await import('@/services/blogService');
    const post = await getBlogPostBySlug(slug);

    if (post) {
      console.log(`Successfully fetched blog post: ${post.title}`);
      return post;
    } else {
      console.log(`Blog post not found with slug: ${slug}`);
      return null;
    }
  } catch (error) {
    console.error('Error fetching blog post:', error);
    return null;
  }
}

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  // Use the params object directly without destructuring
  const post = await getBlogPost(params.slug);

  if (!post) {
    notFound();
  }

  return (
    <main className="pt-16 md:pt-20 bg-white">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header Section */}
        <div className="mb-6 mt-12">
          <Link
            href="/blog"
            className="text-gray-500 hover:text-gray-700 text-sm font-medium transition-colors flex items-center mb-8"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Blog
          </Link>

          <h1 className="text-3xl md:text-5xl font-bold mb-6 leading-tight text-gray-900">{post.title}</h1>

          <div className="flex items-center text-gray-500 text-sm space-x-6 mb-8">
            <div className="flex items-center">
              <span className="text-gray-400 capitalize mr-2">
                {post.category}
              </span>
              <span className="mx-2 text-gray-300">•</span>
            </div>
            <div className="flex items-center">
              <span>{formatDate(post.publishedAt || post.createdAt)}</span>
            </div>
            <div className="flex items-center">
              <span>By {post.author}</span>
            </div>
          </div>
        </div>



        {/* Blog Content */}
        <section className="mb-16">
          <article className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-headings:font-semibold prose-a:text-gray-700 prose-a:font-medium prose-a:underline hover:prose-a:text-black blog-content">
            <div dangerouslySetInnerHTML={{ __html: sanitizeHtml(post.content) }} />
          </article>

          {/* Tags */}
          {post.tags && post.tags.length > 0 && (
            <div className="mt-12 pt-6 border-t border-gray-200">
              <div className="flex items-center flex-wrap gap-2">
                {post.tags.map((tag) => (
                  <Link
                    key={tag}
                    href={`/blog?tag=${encodeURIComponent(tag)}`}
                    className="inline-block px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm transition-colors"
                  >
                    {tag}
                  </Link>
                ))}
              </div>
            </div>
          )}
        </section>

        {/* Call to Action */}
        <section className="mb-20 border-t border-gray-200 pt-12">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Ready to start your project?</h2>
            <p className="text-lg text-gray-600 mb-8">
              Let's discuss how we can help bring your vision to life.
            </p>
            <Link
              href="/contact"
              className="inline-block px-8 py-3 bg-gray-900 hover:bg-black text-white font-medium transition-colors"
            >
              Get in Touch
            </Link>
          </div>
        </section>
      </div>
    </main>
  );
}
