import Link from 'next/link';
import { BlogPost } from '@/types/blog';
import { Metadata } from 'next';
import BlogSearch from '@/components/BlogSearch';

// Set the cache revalidation to 0 to prevent caching
export const revalidate = 0;

export const metadata: Metadata = {
  title: 'Blog | Mocky Digital',
  description: 'Read our latest articles on web design, graphic design, digital marketing, and more.',
};

async function getBlogPosts(): Promise<BlogPost[]> {
  try {
    // Use the blog service to get published posts from the database
    console.log('Blog page: Fetching published blog posts from database');
    
    // Use direct API call with cache busting parameter
    const timestamp = new Date().getTime();
    const apiUrl = `http://0.0.0.0:3000/api/blog?t=${timestamp}`;
    console.log(`Blog page debug - Using API URL: ${apiUrl}`);
    
    const response = await fetch(apiUrl, {
      cache: 'no-store',
      next: { revalidate: 0 }
    });
    
    if (!response.ok) {
      console.error(`Blog page debug - API error: ${response.status} ${response.statusText}`);
      return [];
    }
    
    const posts = await response.json();
    console.log(`Blog page debug - Fetched ${posts.length} posts from API`);
    
    if (posts.length > 0) {
      console.log(`Blog page debug - First post: ${posts[0].title}, Published: ${posts[0].publishedAt}`);
    }
    
    return posts;
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    // Return empty array to prevent page from crashing
    return [];
  }
}

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

export default async function BlogPage() {
  console.log('Blog page: Rendering started');
  const posts = await getBlogPosts();
  console.log(`Blog page: Render with ${posts.length} posts`);

  return (
    <main className="pt-16 md:pt-20 bg-white">
      {/* Header Section */}
      <section className="py-16 md:py-20 border-b border-gray-100">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">Our Blog</h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl">
              Insights, tips, and news from the world of digital design and marketing
            </p>
            <div className="max-w-xl">
              <BlogSearch initialValue="" />
            </div>
          </div>
        </div>
      </section>

      {/* Blog Posts Section */}
      <section className="py-16 md:py-20">
        <div className="container mx-auto px-4">
          {posts.length === 0 ? (
            <div className="text-center py-16">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">No posts found</h2>
              <p className="text-gray-600">Check back soon for new content!</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-12 max-w-7xl mx-auto">
              {posts.map((post) => (
                <article key={post.id} className="flex flex-col border-t pt-6">
                  <div className="flex-1">
                    <div className="flex items-center mb-3 text-xs text-gray-500">
                      <span className="text-gray-400 capitalize">
                        {post.category}
                      </span>
                      <span className="mx-2 text-gray-300">•</span>
                      <span>{formatDate(post.publishedAt || post.createdAt)}</span>
                    </div>

                    <Link href={`/blog/${post.slug}`} className="block group">
                      <h2 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-gray-700 transition-colors line-clamp-2">
                        {post.title}
                      </h2>
                    </Link>

                    <p className="text-gray-600 mb-4 line-clamp-2 text-base">
                      {post.excerpt}
                    </p>

                    <div className="mt-auto">
                      <Link
                        href={`/blog/${post.slug}`}
                        className="inline-flex items-center text-gray-900 hover:text-gray-700 font-medium text-sm transition-colors"
                      >
                        Read Article
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          )}
        </div>
      </section>
    </main>
  );
}
