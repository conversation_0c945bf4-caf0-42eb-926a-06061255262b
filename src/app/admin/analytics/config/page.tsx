'use client';

import { useState, useEffect } from 'react';
import { 
  CheckCircleIcon, 
  ExclamationTriangleIcon, 
  XCircleIcon,
  DocumentTextIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { getConfigurationStatus, getAnalyticsConfig } from '@/utils/analyticsConfig';

interface ConfigStatus {
  status: 'valid' | 'configured' | 'missing';
  message: string;
  details: string[];
}

export default function AnalyticsConfigPage() {
  const [configStatus, setConfigStatus] = useState<ConfigStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const checkConfiguration = () => {
    setIsLoading(true);
    try {
      const status = getConfigurationStatus();
      setConfigStatus(status);
    } catch (error) {
      console.error('Error checking configuration:', error);
      setConfigStatus({
        status: 'missing',
        message: 'Error checking configuration',
        details: ['Unable to validate configuration']
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkConfiguration();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'valid':
        return <CheckCircleIcon className="h-6 w-6 text-green-500" />;
      case 'configured':
        return <ExclamationTriangleIcon className="h-6 w-6 text-yellow-500" />;
      case 'missing':
        return <XCircleIcon className="h-6 w-6 text-red-500" />;
      default:
        return <XCircleIcon className="h-6 w-6 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'valid':
        return 'bg-green-50 border-green-200';
      case 'configured':
        return 'bg-yellow-50 border-yellow-200';
      case 'missing':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="flex items-center">
          <div className="p-2 bg-blue-50 text-blue-500 rounded-lg mr-3">
            <DocumentTextIcon className="h-6 w-6" />
          </div>
          <h1 className="text-2xl sm:text-3xl font-semibold text-slate-800">
            Analytics Configuration
          </h1>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 text-center">
          <ArrowPathIcon className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-4" />
          <p className="text-slate-600">Checking configuration...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="p-2 bg-blue-50 text-blue-500 rounded-lg mr-3">
            <DocumentTextIcon className="h-6 w-6" />
          </div>
          <h1 className="text-2xl sm:text-3xl font-semibold text-slate-800">
            Analytics Configuration
          </h1>
        </div>
        <button
          onClick={checkConfiguration}
          className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          <ArrowPathIcon className="h-4 w-4 mr-2" />
          Recheck
        </button>
      </div>

      {configStatus && (
        <div className={`rounded-lg border p-6 ${getStatusColor(configStatus.status)}`}>
          <div className="flex items-start">
            <div className="flex-shrink-0">
              {getStatusIcon(configStatus.status)}
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-lg font-medium text-slate-800 mb-2">
                Configuration Status: {configStatus.status.charAt(0).toUpperCase() + configStatus.status.slice(1)}
              </h3>
              <p className="text-slate-700 mb-4">
                {configStatus.message}
              </p>

              {configStatus.details.length > 0 && (
                <div>
                  <h4 className="font-medium text-slate-800 mb-2">Details:</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm text-slate-600">
                    {configStatus.details.map((detail, index) => (
                      <li key={index}>{detail}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Setup Instructions */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-semibold text-slate-800 mb-4">Setup Instructions</h2>
        
        <div className="prose max-w-none text-slate-600">
          <p className="mb-4">
            To enable real Google Analytics data, you need to configure the following environment variables in your <code>.env</code> file:
          </p>

          <div className="bg-slate-50 rounded-lg p-4 mb-4">
            <pre className="text-sm overflow-x-auto">
{`# Google Analytics Configuration
NEXT_PUBLIC_GA_MEASUREMENT_ID="G-XXXXXXXXXX"
GOOGLE_ANALYTICS_CLIENT_EMAIL="<EMAIL>"
GOOGLE_ANALYTICS_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\\nYour Private Key\\n-----END PRIVATE KEY-----"
GOOGLE_ANALYTICS_PROPERTY_ID="properties/123456789"`}
            </pre>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-medium text-blue-800 mb-2">📖 Complete Setup Guide</h3>
            <p className="text-blue-700 text-sm">
              For detailed step-by-step instructions on how to obtain these values, 
              see the <code>GOOGLE_ANALYTICS_SETUP.md</code> file in your project root.
            </p>
          </div>
        </div>
      </div>

      {/* Current Configuration (for debugging) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold text-slate-800 mb-4">
            Current Configuration (Development Only)
          </h2>
          
          <div className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span className="font-medium">Measurement ID:</span>
              <span className="font-mono text-slate-600">
                {process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || 'Not set'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Property ID:</span>
              <span className="font-mono text-slate-600">
                {process.env.GOOGLE_ANALYTICS_PROPERTY_ID || 'Not set'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Client Email:</span>
              <span className="font-mono text-slate-600">
                {process.env.GOOGLE_ANALYTICS_CLIENT_EMAIL || 'Not set'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Private Key:</span>
              <span className="font-mono text-slate-600">
                {process.env.GOOGLE_ANALYTICS_PRIVATE_KEY ? 
                  `${process.env.GOOGLE_ANALYTICS_PRIVATE_KEY.substring(0, 50)}...` : 
                  'Not set'
                }
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
