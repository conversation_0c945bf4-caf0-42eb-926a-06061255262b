'use client';

import { ChartBarIcon, CogIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import OverviewStats from '@/components/admin/analytics/OverviewStats';
import PageViewsChart from '@/components/admin/analytics/PageViewsChart';
import TrafficSourcesChart from '@/components/admin/analytics/TrafficSourcesChart';
import TopPagesTable from '@/components/admin/analytics/TopPagesTable';
import Device<PERSON>hart from '@/components/admin/analytics/DeviceChart';
import CountryChart from '@/components/admin/analytics/CountryChart';
import AnalyticsErrorBoundary from '@/components/admin/analytics/AnalyticsErrorBoundary';

export default function AnalyticsDashboard() {
  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="p-2 bg-blue-50 text-blue-500 rounded-lg mr-3">
            <ChartBarIcon className="h-6 w-6" />
          </div>
          <h1 className="text-2xl sm:text-3xl font-semibold text-slate-800">Analytics Dashboard</h1>
        </div>

        <Link
          href="/admin/analytics/config"
          className="inline-flex items-center px-4 py-2 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors"
        >
          <CogIcon className="h-4 w-4 mr-2" />
          Configuration
        </Link>
      </div>

      {/* Overview Stats */}
      <AnalyticsErrorBoundary>
        <OverviewStats />
      </AnalyticsErrorBoundary>

      {/* Page Views Chart */}
      <AnalyticsErrorBoundary>
        <PageViewsChart />
      </AnalyticsErrorBoundary>

      {/* Two Column Layout for Traffic Sources and Devices */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <AnalyticsErrorBoundary>
          <TrafficSourcesChart />
        </AnalyticsErrorBoundary>
        <AnalyticsErrorBoundary>
          <DeviceChart />
        </AnalyticsErrorBoundary>
      </div>

      {/* Top Pages Table */}
      <AnalyticsErrorBoundary>
        <TopPagesTable />
      </AnalyticsErrorBoundary>

      {/* Country Chart */}
      <AnalyticsErrorBoundary>
        <CountryChart />
      </AnalyticsErrorBoundary>
    </div>
  );
}
