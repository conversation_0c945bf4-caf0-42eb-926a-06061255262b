'use client';

import React, { useState, useRef, FormEvent, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeftIcon, PhotoIcon, GlobeAltIcon } from '@heroicons/react/24/outline';
import { WebsitePortfolioItem, WEBSITE_CATEGORIES } from '@/types/portfolio';
import { useNotification } from '@/contexts/NotificationContext';

interface EditWebsitePortfolioItemPageProps {
  params: {
    id: string;
  };
}

export default function EditWebsitePortfolioItemPage({ params }: EditWebsitePortfolioItemPageProps) {
  // Access id safely without using React.use()
  const id = params?.id;

  const router = useRouter();
  const { showNotification } = useNotification();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState<{
    title: string;
    description: string;
    category: string;
    image: File | null;
    url: string;
    featured: boolean;
    imageSrc: string;
  }>({
    title: '',
    description: '',
    category: 'e-commerce',
    image: null,
    url: '',
    featured: false,
    imageSrc: '',
  });

  const [preview, setPreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    const fetchWebsitePortfolioItem = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/admin/website-portfolio/${id}`);

        if (!response.ok) {
          throw new Error('Failed to fetch website portfolio item');
        }

        const item: WebsitePortfolioItem = await response.json();

        setFormData({
          title: item.title,
          description: item.description || '',
          category: item.category,
          image: null,
          url: item.url,
          featured: item.featured || false,
          imageSrc: item.imageSrc,
        });

        setPreview(item.imageSrc);
      } catch (error) {
        console.error('Error fetching website portfolio item:', error);
        showNotification('error', 'Error', 'Failed to load website portfolio item');
      } finally {
        setIsLoading(false);
      }
    };

    fetchWebsitePortfolioItem();
  }, [id, showNotification]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setFormData(prev => ({ ...prev, image: file }));

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(file);

      // Clear error
      if (errors.image) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.image;
          return newErrors;
        });
      }
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.url.trim()) {
      newErrors.url = 'Website URL is required';
    } else if (!/^https?:\/\/.+\..+/.test(formData.url)) {
      newErrors.url = 'Please enter a valid URL (starting with http:// or https://)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      let imageSrc = formData.imageSrc;

      // If a new image was selected, upload it
      if (formData.image) {
        const imageFormData = new FormData();
        imageFormData.append('file', formData.image);
        imageFormData.append('category', 'websites');

        const uploadResponse = await fetch('/api/admin/upload', {
          method: 'POST',
          body: imageFormData,
        });

        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json();
          throw new Error(errorData.error || 'Failed to upload image');
        }

        const uploadResult = await uploadResponse.json();
        imageSrc = uploadResult.url;
      }

      // Update the website portfolio item
      const websitePortfolioItemData = {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        imageSrc,
        url: formData.url,
        featured: formData.featured,
      };

      const updateResponse = await fetch(`/api/admin/website-portfolio/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(websitePortfolioItemData),
      });

      if (!updateResponse.ok) {
        const errorData = await updateResponse.json();
        throw new Error(errorData.error || 'Failed to update website portfolio item');
      }

      showNotification('success', 'Success', 'Website portfolio item updated successfully');
      router.push('/admin/website-portfolio');
    } catch (error) {
      console.error('Error updating website portfolio item:', error);
      showNotification('error', 'Error', error instanceof Error ? error.message : 'Failed to update website portfolio item');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href="/admin/website-portfolio"
            className="mr-4 p-2 rounded-full text-slate-500 hover:text-slate-700 hover:bg-slate-100"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <h1 className="text-2xl font-bold text-slate-800">Edit Website Portfolio Item</h1>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="bg-white shadow-sm rounded-lg p-6">
        <div className="space-y-6">
          {/* Title */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700">
              Website Title <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className={`mt-1 block w-full rounded-md border ${errors.title ? 'border-red-300' : 'border-gray-300'} shadow-sm p-2 focus:border-orange-500 focus:ring-orange-500`}
              placeholder="E.g., Homestore.co.ke"
            />
            {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
              Description <span className="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              name="description"
              rows={3}
              value={formData.description}
              onChange={handleInputChange}
              className={`mt-1 block w-full rounded-md border ${errors.description ? 'border-red-300' : 'border-gray-300'} shadow-sm p-2 focus:border-orange-500 focus:ring-orange-500`}
              placeholder="E.g., Modern E-commerce Platform for Household Items"
            />
            {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
          </div>

          {/* URL */}
          <div>
            <label htmlFor="url" className="block text-sm font-medium text-gray-700">
              Website URL <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="url"
              name="url"
              value={formData.url}
              onChange={handleInputChange}
              className={`mt-1 block w-full rounded-md border ${errors.url ? 'border-red-300' : 'border-gray-300'} shadow-sm p-2 focus:border-orange-500 focus:ring-orange-500`}
              placeholder="https://example.com"
            />
            {errors.url && <p className="mt-1 text-sm text-red-600">{errors.url}</p>}
          </div>

          {/* Category */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700">
              Category <span className="text-red-500">*</span>
            </label>
            <select
              id="category"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2 focus:border-orange-500 focus:ring-orange-500"
            >
              {WEBSITE_CATEGORIES.map(category => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
          </div>

          {/* Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Website Screenshot
            </label>
            <div
              className={`mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-dashed rounded-md ${
                errors.image ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-orange-400'
              }`}
              onClick={() => fileInputRef.current?.click()}
            >
              <div className="space-y-1 text-center">
                {preview ? (
                  <div className="relative w-full h-48 mx-auto">
                    <Image
                      src={preview}
                      alt="Preview"
                      fill
                      className="object-contain"
                    />
                  </div>
                ) : (
                  <div className="mx-auto h-12 w-12 text-gray-400">
                    <GlobeAltIcon className="h-12 w-12" aria-hidden="true" />
                  </div>
                )}
                <div className="flex text-sm text-gray-600">
                  <label
                    htmlFor="image"
                    className="relative cursor-pointer rounded-md bg-white font-medium text-orange-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-orange-500 focus-within:ring-offset-2 hover:text-orange-500"
                  >
                    <span>Upload a new image</span>
                    <input
                      id="image"
                      name="image"
                      type="file"
                      className="sr-only"
                      accept="image/*"
                      ref={fileInputRef}
                      onChange={handleImageChange}
                    />
                  </label>
                  <p className="pl-1">or drag and drop</p>
                </div>
                <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
              </div>
            </div>
            {errors.image && <p className="mt-1 text-sm text-red-600">{errors.image}</p>}
          </div>

          {/* Featured Checkbox */}
          <div className="flex items-start">
            <div className="flex items-center h-5">
              <input
                id="featured"
                name="featured"
                type="checkbox"
                checked={formData.featured}
                onChange={handleCheckboxChange}
                className="h-4 w-4 rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="featured" className="font-medium text-gray-700">
                Featured
              </label>
              <p className="text-gray-500">Mark this website as featured to highlight it on the portfolio page</p>
            </div>
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <Link
            href="/admin/website-portfolio"
            className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
          >
            Cancel
          </Link>
          <button
            type="submit"
            disabled={isSubmitting}
            className="inline-flex justify-center rounded-md border border-transparent bg-orange-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:bg-orange-300 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Updating...' : 'Update Website Portfolio Item'}
          </button>
        </div>
      </form>
    </div>
  );
}
