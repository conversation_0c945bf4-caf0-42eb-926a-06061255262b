'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import AdminHeader from '@/components/admin/AdminHeader';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Loader2, Plus, Pencil, Trash2 } from 'lucide-react';
import Link from 'next/link';
import { formatDate } from '@/lib/utils';

interface CompanyStory {
  id: string;
  title: string;
  subtitle: string;
  founderName: string;
  founderRole: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function CompanyStoryPage() {
  const [stories, setStories] = useState<CompanyStory[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    fetchStories();
  }, []);

  const fetchStories = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/company-story');
      if (!response.ok) throw new Error('Failed to fetch company stories');
      
      const data = await response.json();
      setStories(data);
    } catch (error) {
      console.error('Error fetching company stories:', error);
      toast.error('Failed to load company stories');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this company story?')) return;
    
    try {
      setDeleting(id);
      const response = await fetch(`/api/admin/company-story/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) throw new Error('Failed to delete company story');
      
      toast.success('Company story deleted successfully');
      setStories(stories.filter(story => story.id !== id));
    } catch (error) {
      console.error('Error deleting company story:', error);
      toast.error('Failed to delete company story');
    } finally {
      setDeleting(null);
    }
  };

  return (
    <div className="p-6">
      <AdminHeader
        title="Company Story Management"
        description="Manage your company's story section on the About page"
      />
      
      <div className="mb-6 flex justify-end">
        <Button asChild>
          <Link href="/admin/company-story/new">
            <Plus className="mr-2 h-4 w-4" /> Add New Story
          </Link>
        </Button>
      </div>
      
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex justify-center items-center p-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : stories.length === 0 ? (
            <div className="text-center p-8 text-muted-foreground">
              <p>No company stories found. Create your first one!</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Founder</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Updated</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {stories.map((story) => (
                  <TableRow key={story.id}>
                    <TableCell className="font-medium">{story.title}</TableCell>
                    <TableCell>{story.founderName} ({story.founderRole})</TableCell>
                    <TableCell>
                      {story.isActive ? (
                        <Badge variant="default" className="bg-green-500">Active</Badge>
                      ) : (
                        <Badge variant="outline">Inactive</Badge>
                      )}
                    </TableCell>
                    <TableCell>{formatDate(story.updatedAt)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => router.push(`/admin/company-story/${story.id}`)}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="icon"
                          onClick={() => handleDelete(story.id)}
                          disabled={deleting === story.id}
                        >
                          {deleting === story.id ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
