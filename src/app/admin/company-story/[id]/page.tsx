'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import AdminHeader from '@/components/admin/AdminHeader';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Loader2, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import ImageUploader from '@/components/admin/ImageUploader';
import React, { use } from 'react';

interface CompanyStory {
  id: string;
  title: string;
  subtitle: string;
  imageSrc: string;
  quote1: string;
  quote2?: string;
  founderName: string;
  founderRole: string;
  linkedinUrl?: string;
  twitterUrl?: string;
  instagramUrl?: string;
  tiktokUrl?: string;
  isActive: boolean;
}

export default function EditCompanyStory({ params }: { params: { id: string } }) {
  // Use React.use() to unwrap the params Promise
  const resolvedParams = React.use(params);
  const isNew = resolvedParams.id === 'new';
  const router = useRouter();
  const [loading, setLoading] = useState(!isNew);
  const [saving, setSaving] = useState(false);
  const [story, setStory] = useState<CompanyStory>({
    id: '',
    title: '',
    subtitle: '',
    imageSrc: '',
    quote1: '',
    quote2: '',
    founderName: '',
    founderRole: '',
    linkedinUrl: '',
    twitterUrl: '',
    instagramUrl: '',
    tiktokUrl: '',
    isActive: true
  });

  useEffect(() => {
    if (!isNew) {
      fetchStory();
    }
  }, [isNew, resolvedParams.id]);

  const fetchStory = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/company-story/${resolvedParams.id}`);
      if (!response.ok) throw new Error('Failed to fetch company story');

      const data = await response.json();
      setStory(data);
    } catch (error) {
      console.error('Error fetching company story:', error);
      toast.error('Failed to load company story');
      router.push('/admin/company-story');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setStory(prev => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (checked: boolean) => {
    setStory(prev => ({ ...prev, isActive: checked }));
  };

  const handleImageUpload = (url: string) => {
    setStory(prev => ({ ...prev, imageSrc: url }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!story.title || !story.subtitle || !story.imageSrc || !story.quote1 || !story.founderName || !story.founderRole) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setSaving(true);
      const url = isNew
        ? '/api/admin/company-story'
        : `/api/admin/company-story/${resolvedParams.id}`;

      const method = isNew ? 'POST' : 'PUT';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(story),
      });

      if (!response.ok) throw new Error('Failed to save company story');

      toast.success(`Company story ${isNew ? 'created' : 'updated'} successfully`);
      router.push('/admin/company-story');
    } catch (error) {
      console.error('Error saving company story:', error);
      toast.error('Failed to save company story');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6 flex justify-center items-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="p-6">
      <AdminHeader
        title={isNew ? "Add Company Story" : "Edit Company Story"}
        description={isNew ? "Create a new company story" : "Update your company story"}
      />

      <div className="mb-6">
        <Button variant="outline" asChild>
          <Link href="/admin/company-story">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Stories
          </Link>
        </Button>
      </div>

      <form onSubmit={handleSubmit}>
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="grid gap-6">
              <div className="grid gap-3">
                <Label htmlFor="title">Title <span className="text-red-500">*</span></Label>
                <Input
                  id="title"
                  name="title"
                  value={story.title}
                  onChange={handleChange}
                  placeholder="e.g., The Journey Behind Mocky Digital"
                  required
                />
              </div>

              <div className="grid gap-3">
                <Label htmlFor="subtitle">Subtitle <span className="text-red-500">*</span></Label>
                <Input
                  id="subtitle"
                  name="subtitle"
                  value={story.subtitle}
                  onChange={handleChange}
                  placeholder="e.g., From humble beginnings to becoming a trusted digital partner..."
                  required
                />
              </div>

              <div className="grid gap-3">
                <Label>Founder Image <span className="text-red-500">*</span></Label>
                <ImageUploader
                  currentImage={story.imageSrc}
                  onImageUploaded={handleImageUpload}
                  folder="about"
                />
              </div>

              <div className="grid gap-3">
                <Label htmlFor="founderName">Founder Name <span className="text-red-500">*</span></Label>
                <Input
                  id="founderName"
                  name="founderName"
                  value={story.founderName}
                  onChange={handleChange}
                  placeholder="e.g., Don Omondi"
                  required
                />
              </div>

              <div className="grid gap-3">
                <Label htmlFor="founderRole">Founder Role <span className="text-red-500">*</span></Label>
                <Input
                  id="founderRole"
                  name="founderRole"
                  value={story.founderRole}
                  onChange={handleChange}
                  placeholder="e.g., Founder & CEO"
                  required
                />
              </div>

              <div className="grid gap-3">
                <Label htmlFor="quote1">Quote 1 <span className="text-red-500">*</span></Label>
                <Textarea
                  id="quote1"
                  name="quote1"
                  value={story.quote1}
                  onChange={handleChange}
                  placeholder="First quote from the founder..."
                  rows={4}
                  required
                />
              </div>

              <div className="grid gap-3">
                <Label htmlFor="quote2">Quote 2 (Optional)</Label>
                <Textarea
                  id="quote2"
                  name="quote2"
                  value={story.quote2 || ''}
                  onChange={handleChange}
                  placeholder="Second quote from the founder (optional)..."
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="grid gap-3">
                  <Label htmlFor="linkedinUrl">LinkedIn URL (Optional)</Label>
                  <Input
                    id="linkedinUrl"
                    name="linkedinUrl"
                    value={story.linkedinUrl || ''}
                    onChange={handleChange}
                    placeholder="e.g., https://www.linkedin.com/in/username/"
                  />
                </div>

                <div className="grid gap-3">
                  <Label htmlFor="twitterUrl">Twitter URL (Optional)</Label>
                  <Input
                    id="twitterUrl"
                    name="twitterUrl"
                    value={story.twitterUrl || ''}
                    onChange={handleChange}
                    placeholder="e.g., https://twitter.com/username"
                  />
                </div>

                <div className="grid gap-3">
                  <Label htmlFor="instagramUrl">Instagram URL (Optional)</Label>
                  <Input
                    id="instagramUrl"
                    name="instagramUrl"
                    value={story.instagramUrl || ''}
                    onChange={handleChange}
                    placeholder="e.g., https://instagram.com/username"
                  />
                </div>

                <div className="grid gap-3">
                  <Label htmlFor="tiktokUrl">TikTok URL (Optional)</Label>
                  <Input
                    id="tiktokUrl"
                    name="tiktokUrl"
                    value={story.tiktokUrl || ''}
                    onChange={handleChange}
                    placeholder="e.g., https://tiktok.com/@username"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={story.isActive}
                  onCheckedChange={handleSwitchChange}
                />
                <Label htmlFor="isActive">Set as active story (only one story can be active)</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end">
          <Button type="submit" disabled={saving}>
            {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isNew ? 'Create Story' : 'Update Story'}
          </Button>
        </div>
      </form>
    </div>
  );
}
