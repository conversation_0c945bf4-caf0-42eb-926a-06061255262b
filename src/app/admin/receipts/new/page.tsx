'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  ReceiptRefundIcon,
  PlusIcon,
  MinusIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { useNotification } from '@/contexts/NotificationContext';

interface Transaction {
  id: string;
  transactionId: string;
  amount: number;
  customerName: string;
  phoneNumber: string;
  transactionDate: string;
  status: string;
}

interface Service {
  id: string;
  name: string;
  description: string | null;
  price: number;
  category: string;
}

interface ReceiptItem {
  serviceId: string;
  quantity: number;
  unitPrice: number;
  description?: string;
}

export default function NewReceiptPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const transactionId = searchParams.get('transactionId');
  const { showNotification } = useNotification();

  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    customerName: '',
    phoneNumber: '',
    email: '',
    notes: '',
    items: [{ serviceId: '', quantity: 1, unitPrice: 0, description: '' }]
  });

  // Fetch transaction and services
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch services
      const servicesResponse = await fetch('/api/admin/services', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-store'
      });

      if (!servicesResponse.ok) {
        const errorData = await servicesResponse.json().catch(() => ({}));
        console.error('Services response error:', servicesResponse.status, errorData);
        throw new Error(errorData.error || 'Failed to fetch services');
      }

      const servicesData = await servicesResponse.json();
      console.log('Services loaded:', servicesData.length);
      setServices(servicesData || []);

      // If transaction ID is provided, fetch transaction
      if (transactionId) {
        const transactionResponse = await fetch(`/api/admin/transactions/${transactionId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          cache: 'no-store'
        });

        if (!transactionResponse.ok) {
          const errorData = await transactionResponse.json().catch(() => ({}));
          console.error('Transaction response error:', transactionResponse.status, errorData);
          throw new Error(errorData.error || 'Failed to fetch transaction');
        }

        const transactionData = await transactionResponse.json();
        console.log('Transaction loaded:', transactionData.id);
        setTransaction(transactionData);

        // Pre-fill form with transaction data
        setFormData(prev => ({
          ...prev,
          customerName: transactionData.customerName,
          phoneNumber: transactionData.phoneNumber
        }));
      }
    } catch (err) {
      console.error('Error fetching data:', err);
      setError('Failed to load required data');
      showNotification('error', 'Failed to load required data');
    } finally {
      setLoading(false);
    }
  }, [transactionId, showNotification]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleItemChange = (index: number, field: string, value: any) => {
    setFormData(prev => {
      const newItems = [...prev.items];

      // If changing service, update unit price
      if (field === 'serviceId') {
        const service = services.find(s => s.id === value);
        if (service) {
          newItems[index] = {
            ...newItems[index],
            [field]: value,
            unitPrice: service.price,
            description: service.name
          };
        } else {
          newItems[index] = {
            ...newItems[index],
            [field]: value
          };
        }
      } else {
        newItems[index] = {
          ...newItems[index],
          [field]: value
        };
      }

      return { ...prev, items: newItems };
    });
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { serviceId: '', quantity: 1, unitPrice: 0, description: '' }]
    }));
  };

  const removeItem = (index: number) => {
    if (formData.items.length === 1) {
      return; // Keep at least one item
    }

    setFormData(prev => {
      const newItems = [...prev.items];
      newItems.splice(index, 1);
      return { ...prev, items: newItems };
    });
  };

  const calculateTotal = () => {
    return formData.items.reduce((total, item) => {
      return total + (item.quantity * item.unitPrice);
    }, 0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!transaction) {
      setError('No transaction selected');
      return;
    }

    // Validate items
    for (const item of formData.items) {
      if (!item.serviceId || item.quantity <= 0) {
        setError('Please select a service and specify a quantity for each item');
        return;
      }
    }

    try {
      setSubmitting(true);
      setError(null);

      const response = await fetch('/api/admin/receipts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionId: transaction.id,
          customerName: formData.customerName,
          phoneNumber: formData.phoneNumber,
          email: formData.email || undefined,
          notes: formData.notes || undefined,
          items: formData.items.map(item => ({
            serviceId: item.serviceId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            description: item.description
          }))
        }),
        cache: 'no-store'
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Receipt creation error:', response.status, errorData);
        throw new Error(errorData.error || 'Failed to create receipt');
      }

      const receipt = await response.json();

      showNotification('success', 'Receipt created successfully');

      // Navigate to the receipt page
      router.push(`/admin/receipts/${receipt.id}`);
    } catch (err) {
      console.error('Error creating receipt:', err);
      setError(err instanceof Error ? err.message : 'Failed to create receipt');
      showNotification('error', 'Failed to create receipt');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href="/admin/transactions"
            className="mr-4 p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <div className="p-2 bg-blue-50 text-blue-500 rounded-lg mr-3">
            <ReceiptRefundIcon className="h-6 w-6" />
          </div>
          <h1 className="text-2xl font-bold text-slate-800">Create Receipt</h1>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <XCircleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {loading ? (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex justify-center items-center h-40">
            <svg className="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        </div>
      ) : !transaction ? (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="text-center py-8">
            <p className="text-gray-600 mb-4">No transaction selected. Please select a transaction to create a receipt.</p>
            <Link
              href="/admin/transactions"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              Go to Transactions
            </Link>
          </div>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="bg-white shadow rounded-lg overflow-hidden">
          <div className="p-6 space-y-6">
            {/* Transaction Information */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h2 className="text-lg font-medium text-blue-800 mb-2">Transaction Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Transaction ID:</p>
                  <p className="font-medium">{transaction.transactionId}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Amount:</p>
                  <p className="font-medium">KES {transaction.amount.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Date:</p>
                  <p className="font-medium">{new Date(transaction.transactionDate).toLocaleDateString()}</p>
                </div>
              </div>
            </div>

            {/* Customer Information */}
            <div>
              <h2 className="text-lg font-medium text-gray-900 mb-4">Customer Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="customerName" className="block text-sm font-medium text-gray-700">
                    Customer Name
                  </label>
                  <input
                    type="text"
                    id="customerName"
                    name="customerName"
                    value={formData.customerName}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700">
                    Phone Number
                  </label>
                  <input
                    type="text"
                    id="phoneNumber"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    required
                  />
                </div>
                <div className="md:col-span-2">
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                    Email (Optional)
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>
            </div>

            {/* Receipt Items */}
            <div>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-medium text-gray-900">Receipt Items</h2>
                <button
                  type="button"
                  onClick={addItem}
                  className="inline-flex items-center px-3 py-1 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                >
                  <PlusIcon className="h-4 w-4 mr-1" />
                  Add Item
                </button>
              </div>

              {formData.items.map((item, index) => (
                <div key={index} className="bg-gray-50 p-4 rounded-lg mb-4">
                  <div className="flex justify-between items-center mb-3">
                    <h3 className="text-sm font-medium text-gray-700">Item {index + 1}</h3>
                    <button
                      type="button"
                      onClick={() => removeItem(index)}
                      className="text-red-600 hover:text-red-800"
                      disabled={formData.items.length === 1}
                    >
                      <MinusIcon className="h-4 w-4" />
                    </button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="md:col-span-2">
                      <label htmlFor={`service-${index}`} className="block text-sm font-medium text-gray-700">
                        Service
                      </label>
                      <select
                        id={`service-${index}`}
                        value={item.serviceId}
                        onChange={(e) => handleItemChange(index, 'serviceId', e.target.value)}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        required
                      >
                        <option value="">Select a service</option>
                        {services.map((service) => (
                          <option key={service.id} value={service.id}>
                            {service.name} - KES {service.price.toLocaleString()}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label htmlFor={`quantity-${index}`} className="block text-sm font-medium text-gray-700">
                        Quantity
                      </label>
                      <input
                        type="number"
                        id={`quantity-${index}`}
                        value={item.quantity}
                        onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value))}
                        min="1"
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor={`unitPrice-${index}`} className="block text-sm font-medium text-gray-700">
                        Unit Price (KES)
                      </label>
                      <input
                        type="number"
                        id={`unitPrice-${index}`}
                        value={item.unitPrice}
                        onChange={(e) => handleItemChange(index, 'unitPrice', parseFloat(e.target.value))}
                        min="0"
                        step="0.01"
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        required
                      />
                    </div>
                  </div>

                  <div className="mt-3">
                    <label htmlFor={`description-${index}`} className="block text-sm font-medium text-gray-700">
                      Description (Optional)
                    </label>
                    <input
                      type="text"
                      id={`description-${index}`}
                      value={item.description}
                      onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                  </div>
                </div>
              ))}

              {/* Totals */}
              <div className="bg-gray-100 p-4 rounded-lg mt-6">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Total Amount:</span>
                  <span className="font-bold">KES {calculateTotal().toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center mt-2">
                  <span className="font-medium">Amount Paid:</span>
                  <span className="font-bold">KES {transaction.amount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center mt-2">
                  <span className="font-medium">Balance:</span>
                  <span className={`font-bold ${calculateTotal() > transaction.amount ? 'text-red-600' : 'text-green-600'}`}>
                    KES {(calculateTotal() - transaction.amount).toLocaleString()}
                  </span>
                </div>
              </div>
            </div>

            {/* Notes */}
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                Notes (Optional)
              </label>
              <textarea
                id="notes"
                name="notes"
                rows={3}
                value={formData.notes}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
            <Link
              href="/admin/transactions"
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={submitting}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {submitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating...
                </>
              ) : (
                'Create Receipt'
              )}
            </button>
          </div>
        </form>
      )}
    </div>
  );
}
