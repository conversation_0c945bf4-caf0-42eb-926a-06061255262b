'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  ReceiptRefundIcon,
  XCircleIcon,
  CheckCircleIcon,
  ClockIcon,
  DocumentTextIcon,
  PrinterIcon
} from '@heroicons/react/24/outline';
import { useNotification } from '@/contexts/NotificationContext';
import { format } from 'date-fns';

interface ReceiptItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  description: string | null;
  serviceId: string;
  serviceName?: string;
}

interface Receipt {
  id: string;
  receiptNumber: string;
  totalAmount: number;
  amountPaid: number;
  balance: number;
  customerName: string;
  phoneNumber: string;
  email: string | null;
  status: string;
  notes: string | null;
  createdAt: string;
  issuedAt: string;
  paidAt: string | null;
  transactionId: string;
  items: ReceiptItem[];
  pdfUrl?: string;
}

export default function ReceiptDetailPage() {
  const params = useParams();
  const receiptId = params.id as string;
  const { showNotification } = useNotification();

  const [receipt, setReceipt] = useState<Receipt | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);


  const fetchReceipt = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/admin/receipts/${receiptId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-store'
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Response error:', response.status, errorData);
        throw new Error(errorData.error || 'Failed to fetch receipt');
      }

      const data = await response.json();
      console.log('Receipt loaded:', data.id);
      setReceipt(data);
    } catch (err) {
      console.error('Error fetching receipt:', err);
      setError(err instanceof Error ? err.message : 'Failed to load receipt');
      showNotification('error', 'Failed to load receipt');
    } finally {
      setLoading(false);
    }
  }, [receiptId, showNotification]);

  useEffect(() => {
    fetchReceipt();
  }, [fetchReceipt]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'issued':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <ClockIcon className="h-3 w-3 mr-1" />
            Issued
          </span>
        );
      case 'paid':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircleIcon className="h-3 w-3 mr-1" />
            Paid
          </span>
        );
      case 'cancelled':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircleIcon className="h-3 w-3 mr-1" />
            Cancelled
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  const handlePrint = () => {
    // Open the receipt in a new tab for printing
    window.open(`/api/receipts/${receipt?.id}`, '_blank');
  };



  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between print:hidden">
        <div className="flex items-center">
          <Link
            href="/admin/receipts"
            className="mr-4 p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <div className="p-2 bg-blue-50 text-blue-500 rounded-lg mr-3">
            <ReceiptRefundIcon className="h-6 w-6" />
          </div>
          <h1 className="text-2xl font-bold text-slate-800">Receipt Details</h1>
        </div>
        {receipt && (
          <div className="flex space-x-2">
            <a
              href={`/api/receipts/${receipt.id}`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
            >
              <DocumentTextIcon className="h-4 w-4 mr-1" />
              View Receipt
            </a>
            <button
              onClick={handlePrint}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              <PrinterIcon className="h-4 w-4 mr-1" />
              Print Receipt
            </button>
          </div>
        )}
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4 print:hidden">
          <div className="flex">
            <div className="flex-shrink-0">
              <XCircleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {loading ? (
        <div className="bg-white shadow rounded-lg p-6 print:shadow-none">
          <div className="flex justify-center items-center h-40">
            <svg className="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        </div>
      ) : !receipt ? (
        <div className="bg-white shadow rounded-lg p-6 print:shadow-none">
          <div className="text-center py-8">
            <p className="text-gray-600 mb-4">Receipt not found</p>
            <Link
              href="/admin/receipts"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              Go to Receipts
            </Link>
          </div>
        </div>
      ) : (
        <div className="bg-white shadow rounded-lg overflow-hidden print:shadow-none">
          <div className="p-6 space-y-6">
            {/* Receipt Header */}
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center border-b pb-6">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Transaction ID: {receipt.receiptNumber}</h2>
                <p className="text-gray-600">
                  Issued on {format(new Date(receipt.issuedAt), 'dd/MM/yyyy')}
                </p>
                <div className="mt-2">
                  {getStatusBadge(receipt.status)}
                </div>
              </div>
              <div className="mt-4 md:mt-0 text-right">
                <div className="text-lg font-bold text-gray-900">Mocky Digital</div>
                <div className="text-gray-600">Nairobi, Kenya</div>
                <div className="text-gray-600">Phone: +*********** 670</div>
                <div className="text-gray-600">Email: <EMAIL></div>
                <div className="text-gray-600">Tax PIN: P052373324V</div>
              </div>
            </div>

            {/* Customer Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">Customer Information</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Name:</p>
                    <p className="font-medium">{receipt.customerName}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Phone:</p>
                    <p className="font-medium">{receipt.phoneNumber}</p>
                  </div>
                  {receipt.email && (
                    <div>
                      <p className="text-sm text-gray-600">Email:</p>
                      <p className="font-medium">{receipt.email}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Receipt Items */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">Receipt Items</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unit Price
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {receipt.items.map((item) => (
                      <tr key={item.id}>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          {item.description || 'Service'}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500 text-center">
                          {item.quantity}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900 text-right">
                          KES {item.unitPrice.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900 text-right">
                          KES {item.totalPrice.toLocaleString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Totals */}
            <div className="border-t pt-4">
              <div className="flex justify-end">
                <div className="w-full md:w-1/3">
                  <div className="flex justify-between py-2">
                    <span className="font-medium">Subtotal:</span>
                    <span>KES {receipt.totalAmount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between py-2">
                    <span className="font-medium">Amount Paid:</span>
                    <span>KES {receipt.amountPaid.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between py-2 border-t border-gray-200 pt-2">
                    <span className="font-bold">Balance:</span>
                    <span className={`font-bold ${receipt.balance > 0 ? 'text-red-600' : 'text-green-600'}`}>
                      KES {receipt.balance.toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Notes */}
            {receipt.notes && (
              <div className="border-t pt-4">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Notes</h3>
                <p className="text-gray-600">{receipt.notes}</p>
              </div>
            )}

            {/* Footer */}
            <div className="border-t pt-4 text-center text-gray-500 text-sm">
              <p>Thank you for your business!</p>
              <p className="mt-1">This is a computer-generated receipt and does not require a signature.</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
