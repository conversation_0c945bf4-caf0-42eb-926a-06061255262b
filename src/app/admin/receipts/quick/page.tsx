'use client';

import Link from 'next/link';
import { ArrowLeftIcon, ReceiptRefundIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import QuickReceiptForm from '@/components/admin/QuickReceiptForm';

export default function QuickReceiptPage() {
  return (
    <div className="max-w-5xl mx-auto">
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href="/admin/receipts"
            className="mr-4 p-2 rounded-full text-[#0F2557] hover:text-[#FF5400] hover:bg-gray-100 transition-colors"
            title="Back to Receipts"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <h1 className="text-2xl font-bold text-[#0F2557]">Quick Receipt Generator</h1>
        </div>

        <div className="flex space-x-2">
          <Link
            href="/admin/receipts"
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF5400]"
          >
            <DocumentTextIcon className="-ml-0.5 mr-1 h-4 w-4" />
            View All Receipts
          </Link>
        </div>
      </div>

      <QuickReceiptForm />

      <div className="mt-8 text-center text-sm text-gray-500">
        <p>Generate receipts quickly by pasting M-Pesa transaction messages.</p>
        <p className="mt-1">The system will automatically parse the transaction details and create a receipt.</p>
      </div>
    </div>
  );
}
