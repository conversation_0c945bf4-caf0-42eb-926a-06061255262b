'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import {
  ChartBarIcon,
  ArrowPathIcon,
  CalendarIcon,
  FunnelIcon,
  ChevronDownIcon,
} from '@heroicons/react/24/outline';
import { useNotification } from '@/contexts/NotificationContext';

interface EventCount {
  eventType: string;
  count: number;
}

interface FormAbandonmentStats {
  formName: string;
  totalForms: number;
  submissions: number;
  abandonments: number;
  abandonmentRate: number;
  avgCompletionPercentage: number;
}

interface PageViewStats {
  url: string;
  views: number;
  uniqueVisitors: number;
}

export default function TrackingDashboardPage() {
  const { data: session } = useSession();
  const { showNotification } = useNotification();
  const [eventCounts, setEventCounts] = useState<EventCount[]>([]);
  const [formStats, setFormStats] = useState<FormAbandonmentStats[]>([]);
  const [pageStats, setPageStats] = useState<PageViewStats[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    endDate: new Date().toISOString().split('T')[0], // today
  });
  const [showFilters, setShowFilters] = useState(false);

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  // Fetch all tracking data
  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query string from date range
      const queryParams = new URLSearchParams({
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
      });

      // Fetch event counts
      const eventCountsResponse = await fetch(`/api/admin/tracking/events/count?${queryParams.toString()}`);
      if (!eventCountsResponse.ok) {
        throw new Error(`Error fetching event counts: ${eventCountsResponse.status}`);
      }
      const eventCountsData = await eventCountsResponse.json();
      setEventCounts(eventCountsData);

      // Fetch form abandonment stats
      const formStatsResponse = await fetch(`/api/admin/tracking/forms?${queryParams.toString()}`);
      if (!formStatsResponse.ok) {
        throw new Error(`Error fetching form stats: ${formStatsResponse.status}`);
      }
      const formStatsData = await formStatsResponse.json();
      setFormStats(formStatsData);

      // Fetch page view stats
      const pageStatsResponse = await fetch(`/api/admin/tracking/pages?${queryParams.toString()}`);
      if (!pageStatsResponse.ok) {
        throw new Error(`Error fetching page stats: ${pageStatsResponse.status}`);
      }
      const pageStatsData = await pageStatsResponse.json();
      setPageStats(pageStatsData);
    } catch (err) {
      setError('Failed to load tracking data. Please try again.');
      console.error('Error fetching tracking data:', err);
      showNotification('error', 'Failed to load tracking data');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle date range change
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setDateRange(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // Apply date range filter
  const applyDateRange = () => {
    fetchData();
  };

  // Get event type display name
  const getEventTypeDisplayName = (eventType: string) => {
    switch (eventType) {
      case 'pageView':
        return 'Page Views';
      case 'buttonClick':
        return 'Button Clicks';
      case 'formSubmission':
        return 'Form Submissions';
      case 'formAbandonment':
        return 'Form Abandonments';
      default:
        return eventType.charAt(0).toUpperCase() + eventType.slice(1).replace(/([A-Z])/g, ' $1');
    }
  };

  // Get event type color
  const getEventTypeColor = (eventType: string) => {
    switch (eventType) {
      case 'pageView':
        return 'bg-blue-100 text-blue-800';
      case 'buttonClick':
        return 'bg-green-100 text-green-800';
      case 'formSubmission':
        return 'bg-purple-100 text-purple-800';
      case 'formAbandonment':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <div className="p-2 bg-blue-50 text-blue-600 rounded-lg mr-3">
            <ChartBarIcon className="h-6 w-6" />
          </div>
          <h1 className="text-2xl sm:text-3xl font-semibold text-slate-800">Website Tracking Dashboard</h1>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FunnelIcon className="-ml-0.5 mr-2 h-4 w-4" />
            Date Range
            <ChevronDownIcon className="ml-1 h-4 w-4" />
          </button>

          <button
            onClick={fetchData}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <ArrowPathIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Date Range Filter */}
      {showFilters && (
        <div className="bg-white rounded-lg shadow p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <CalendarIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="date"
                  id="startDate"
                  name="startDate"
                  value={dateRange.startDate}
                  onChange={handleDateChange}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
            </div>
            <div>
              <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <CalendarIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="date"
                  id="endDate"
                  name="endDate"
                  value={dateRange.endDate}
                  onChange={handleDateChange}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
            </div>
            <div className="flex items-end">
              <button
                onClick={applyDateRange}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Apply Date Range
              </button>
            </div>
          </div>
        </div>
      )}

      {isLoading ? (
        <div className="p-8 text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-500 border-t-transparent"></div>
          <p className="mt-2 text-gray-600">Loading tracking data...</p>
        </div>
      ) : error ? (
        <div className="p-8 text-center text-red-500">{error}</div>
      ) : (
        <div className="space-y-6">
          {/* Event Counts */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Event Overview</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {eventCounts.map((event) => (
                <div key={event.eventType} className="bg-gray-50 rounded-lg p-4 border border-gray-100">
                  <div className="flex items-center">
                    <div className={`px-2 py-1 rounded-md ${getEventTypeColor(event.eventType)}`}>
                      {getEventTypeDisplayName(event.eventType)}
                    </div>
                  </div>
                  <div className="mt-2 text-3xl font-semibold text-gray-900">{event.count.toLocaleString()}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Form Abandonment Stats */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Form Abandonment Statistics</h2>
            {formStats.length === 0 ? (
              <p className="text-center text-gray-500 py-4">No form data available for the selected period.</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Form Name
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total Forms
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Submissions
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Abandonments
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Abandonment Rate
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Avg. Completion
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {formStats.map((form) => (
                      <tr key={form.formName} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {form.formName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {form.totalForms}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {form.submissions}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {form.abandonments}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {form.abandonmentRate.toFixed(1)}%
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {form.avgCompletionPercentage.toFixed(1)}%
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Page View Stats */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Top Pages</h2>
            {pageStats.length === 0 ? (
              <p className="text-center text-gray-500 py-4">No page view data available for the selected period.</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Page URL
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Views
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unique Visitors
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {pageStats.map((page) => (
                      <tr key={page.url} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {page.url}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {page.views}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {page.uniqueVisitors}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
