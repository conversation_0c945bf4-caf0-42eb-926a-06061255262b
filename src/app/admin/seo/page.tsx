'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import {
  MagnifyingGlassIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  LinkIcon,
  TagIcon,
  CodeBracketIcon,
} from '@heroicons/react/24/outline';
import { handleApiError, withErrorHandling } from '@/utils/errorHandling';

interface SeoPageData {
  id: string;
  url: string;
  title: string;
  description: string | null;
  healthScore: number;
  lastScanned: string | null;
  status: string;
  _count: {
    seoMetaTags: number;
    seoStructuredData: number;
  };
  seoIssues: {
    id: string;
    type: string;
    severity: string;
    description: string;
    status: string;
  }[];
  brokenLinks: {
    id: string;
    url: string;
    statusCode: number | null;
    fixed: boolean;
  }[];
}

export default function SeoDashboard() {
  const [pages, setPages] = useState<SeoPageData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [scanUrl, setScanUrl] = useState('');
  const [isScanning, setIsScanning] = useState(false);
  const [isSettingUpCron, setIsSettingUpCron] = useState(false);
  const [isRunningFullScan, setIsRunningFullScan] = useState(false);

  // Fetch SEO pages
  const fetchPages = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/admin/seo/pages');
      if (!response.ok) {
        throw new Error('Failed to fetch SEO pages');
      }

      const data = await response.json();
      setPages(data);
    } catch (err) {
      const errorMsg = handleApiError(err, 'Failed to load SEO pages');
      setError(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };

  // Scan a URL
  const handleScan = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!scanUrl) {
      toast.error('Please enter a URL to scan');
      return;
    }

    try {
      setIsScanning(true);

      const response = await fetch('/api/admin/seo/scan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: scanUrl }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to scan URL');
      }

      await response.json();
      toast.success('URL scanned successfully');
      setScanUrl('');
      fetchPages(); // Refresh the page list
    } catch (err) {
      handleApiError(err, 'Failed to scan URL');
    } finally {
      setIsScanning(false);
    }
  };

  // Set up automated scanning
  const handleSetupCron = async () => {
    if (!confirm('This will set up a weekly automated SEO scan that runs every Sunday at 2:00 AM. Continue?')) {
      return;
    }

    try {
      setIsSettingUpCron(true);

      const response = await fetch('/api/admin/seo/setup-cron', {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to set up automated scanning');
      }

      await response.json();
      toast.success('Automated SEO scanning set up successfully');
    } catch (err) {
      handleApiError(err, 'Failed to set up automated scanning');
    } finally {
      setIsSettingUpCron(false);
    }
  };

  // Run a full scan of all pages
  const handleFullScan = async () => {
    if (!confirm('This will scan all pages in the sitemap. This may take several minutes. Continue?')) {
      return;
    }

    try {
      setIsRunningFullScan(true);

      // Call the correct endpoint for running a full scan
      const response = await fetch('/api/admin/seo/scan', {
        method: 'PUT',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to run full scan');
      }

      await response.json();
      toast.success('Full SEO scan completed successfully');
      fetchPages(); // Refresh the page list
    } catch (err) {
      handleApiError(err, 'Failed to run full scan');
    } finally {
      setIsRunningFullScan(false);
    }
  };

  // Load pages on component mount
  useEffect(() => {
    fetchPages();
  }, []);

  // Get health score color
  const getHealthScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-500';
    if (score >= 70) return 'text-yellow-500';
    if (score >= 50) return 'text-orange-500';
    return 'text-red-500';
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-wrap justify-between items-center gap-4">
        <div className="flex items-center">
          <div className="p-2 bg-blue-50 text-blue-500 rounded-lg mr-3">
            <MagnifyingGlassIcon className="h-6 w-6" />
          </div>
          <h1 className="text-2xl sm:text-3xl font-semibold text-slate-800">SEO Management</h1>
        </div>
      </div>

      {/* Scan URL Form */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-medium text-slate-800 mb-4">Scan a URL</h2>
        <form onSubmit={handleScan} className="flex flex-col sm:flex-row gap-3">
          <input
            type="url"
            value={scanUrl}
            onChange={(e) => setScanUrl(e.target.value)}
            placeholder="https://mocky.co.ke/page-to-scan"
            className="flex-1 px-4 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
          <button
            type="submit"
            disabled={isScanning}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isScanning ? 'Scanning...' : 'Scan URL'}
          </button>
        </form>
      </div>

      {/* Automated Scanning */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-medium text-slate-800 mb-4">Automated Scanning</h2>
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={handleSetupCron}
              disabled={isSettingUpCron}
              className="flex-1 px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSettingUpCron ? 'Setting Up...' : 'Set Up Weekly Automated Scanning'}
            </button>
            <button
              onClick={handleFullScan}
              disabled={isRunningFullScan}
              className="flex-1 px-4 py-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isRunningFullScan ? 'Scanning All Pages...' : 'Run Full Site Scan Now'}
            </button>
          </div>
          <div className="text-sm text-slate-600">
            <p><strong>Set Up Weekly Automated Scanning:</strong> Configure a cron job to automatically scan all pages every Sunday at 2:00 AM. <span className="text-xs text-slate-500">(Runs <code>npm run seo:setup-cron</code>)</span></p>
            <p className="mt-1"><strong>Run Full Site Scan Now:</strong> Immediately scan all pages in the sitemap. This may take several minutes.</p>
          </div>
        </div>
      </div>

      {/* SEO Pages */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="px-6 py-4 border-b border-slate-200">
          <h2 className="text-lg font-medium text-slate-800">Monitored Pages</h2>
        </div>

        {isLoading ? (
          <div className="p-8 text-center">
            <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent"></div>
            <p className="mt-2 text-slate-600">Loading pages...</p>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-500">{error}</p>
          </div>
        ) : pages.length === 0 ? (
          <div className="p-8 text-center">
            <DocumentTextIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <p className="text-slate-600">No pages have been scanned yet.</p>
            <p className="text-slate-500 mt-2">Use the form above to scan your first page.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-slate-200">
              <thead className="bg-slate-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">URL</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Health Score</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Issues</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Broken Links</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Last Scanned</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-slate-200">
                {pages.map((page) => (
                  <tr key={page.id} className="hover:bg-slate-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center bg-blue-100 text-blue-500 rounded-lg">
                          <DocumentTextIcon className="h-5 w-5" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-slate-900 truncate max-w-xs">
                            {page.title}
                          </div>
                          <div className="text-sm text-slate-500 truncate max-w-xs">
                            <a href={page.url} target="_blank" rel="noopener noreferrer" className="hover:underline">
                              {page.url}
                            </a>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-sm font-medium ${getHealthScoreColor(page.healthScore)}`}>
                        {page.healthScore}/100
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-slate-900">
                        {page.seoIssues.length} issues
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-slate-900">
                        {page.brokenLinks.length} broken links
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-slate-500">
                        {page.lastScanned ? new Date(page.lastScanned).toLocaleString() : 'Never'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Link
                        href={`/admin/seo/pages/${page.id}`}
                        className="text-blue-500 hover:text-blue-700 mr-4"
                      >
                        View Details
                      </Link>
                      <button
                        onClick={() => {
                          setScanUrl(page.url);
                          window.scrollTo({ top: 0, behavior: 'smooth' });
                        }}
                        className="text-green-500 hover:text-green-700"
                      >
                        Rescan
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* SEO Tools */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Link
          href="/admin/seo/keywords"
          className="bg-white p-6 rounded-lg shadow-sm border border-slate-100 hover:shadow-md transition-all duration-300 hover:-translate-y-1"
        >
          <div className="flex items-center mb-4">
            <div className="p-3 rounded-lg bg-purple-50 text-purple-500 mr-4">
              <TagIcon className="h-6 w-6" />
            </div>
            <h3 className="text-lg font-medium text-slate-800">Keyword Tracking</h3>
          </div>
          <p className="text-slate-600">Track keyword rankings and get optimization suggestions.</p>
        </Link>

        <Link
          href="/admin/seo/meta-tags"
          className="bg-white p-6 rounded-lg shadow-sm border border-slate-100 hover:shadow-md transition-all duration-300 hover:-translate-y-1"
        >
          <div className="flex items-center mb-4">
            <div className="p-3 rounded-lg bg-green-50 text-green-500 mr-4">
              <CodeBracketIcon className="h-6 w-6" />
            </div>
            <h3 className="text-lg font-medium text-slate-800">Meta Tag Manager</h3>
          </div>
          <p className="text-slate-600">Manage meta tags and structured data for better SEO.</p>
        </Link>

        <Link
          href="/admin/seo/broken-links"
          className="bg-white p-6 rounded-lg shadow-sm border border-slate-100 hover:shadow-md transition-all duration-300 hover:-translate-y-1"
        >
          <div className="flex items-center mb-4">
            <div className="p-3 rounded-lg bg-red-50 text-red-500 mr-4">
              <LinkIcon className="h-6 w-6" />
            </div>
            <h3 className="text-lg font-medium text-slate-800">Broken Link Checker</h3>
          </div>
          <p className="text-slate-600">Find and fix broken links across your website.</p>
        </Link>
      </div>
    </div>
  );
}
