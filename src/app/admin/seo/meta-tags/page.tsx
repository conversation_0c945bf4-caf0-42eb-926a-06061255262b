'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import {
  ArrowLeftIcon,
  CodeBracketIcon,
  ExclamationTriangleIcon,
  MagnifyingGlassIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';

interface MetaTag {
  id: string;
  name: string;
  content: string;
  page: {
    id: string;
    url: string;
    title: string;
  };
}

interface StructuredData {
  id: string;
  type: string;
  data: any;
  page: {
    id: string;
    url: string;
    title: string;
  };
}

export default function MetaTagsManager() {
  const [metaTags, setMetaTags] = useState<MetaTag[]>([]);
  const [structuredData, setStructuredData] = useState<StructuredData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<'meta-tags' | 'structured-data'>('meta-tags');

  // Fetch meta tags and structured data
  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch meta tags
      const metaTagsResponse = await fetch('/api/admin/seo/meta-tags');
      if (!metaTagsResponse.ok) {
        throw new Error('Failed to fetch meta tags');
      }
      const metaTagsData = await metaTagsResponse.json();
      setMetaTags(metaTagsData);

      // Fetch structured data
      const structuredDataResponse = await fetch('/api/admin/seo/structured-data');
      if (!structuredDataResponse.ok) {
        throw new Error('Failed to fetch structured data');
      }
      const structuredDataData = await structuredDataResponse.json();
      setStructuredData(structuredDataData);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError('Failed to load data');
      toast.error('Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  // Filter meta tags
  const filteredMetaTags = metaTags.filter((tag) => 
    tag.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tag.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tag.page.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filter structured data
  const filteredStructuredData = structuredData.filter((item) => 
    item.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    JSON.stringify(item.data).toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.page.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-8">
      <div className="flex flex-wrap justify-between items-center gap-4">
        <div className="flex items-center">
          <Link href="/admin/seo" className="p-2 text-slate-500 hover:text-slate-700 mr-2">
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <div className="p-2 bg-green-50 text-green-500 rounded-lg mr-3">
            <CodeBracketIcon className="h-6 w-6" />
          </div>
          <h1 className="text-2xl sm:text-3xl font-semibold text-slate-800">Meta Tag Manager</h1>
        </div>
        <div className="flex gap-2">
          <button
            onClick={fetchData}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-5 w-5 text-slate-400" />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search meta tags, structured data, or pages..."
            className="pl-10 px-4 py-2 w-full border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="border-b border-slate-200">
          <nav className="flex -mb-px">
            <button
              onClick={() => setActiveTab('meta-tags')}
              className={`py-4 px-6 text-sm font-medium border-b-2 ${
                activeTab === 'meta-tags'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              }`}
            >
              Meta Tags
            </button>
            <button
              onClick={() => setActiveTab('structured-data')}
              className={`py-4 px-6 text-sm font-medium border-b-2 ${
                activeTab === 'structured-data'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              }`}
            >
              Structured Data
            </button>
          </nav>
        </div>

        {isLoading ? (
          <div className="p-8 text-center">
            <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent"></div>
            <p className="mt-2 text-slate-600">Loading data...</p>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-500">{error}</p>
          </div>
        ) : activeTab === 'meta-tags' ? (
          <div className="p-6">
            <h2 className="text-lg font-medium text-slate-800 mb-4">Meta Tags</h2>
            {filteredMetaTags.length === 0 ? (
              <div className="text-center py-8">
                <CodeBracketIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                <p className="text-slate-600">No meta tags found.</p>
                <p className="text-slate-500 mt-2">Scan pages to extract meta tags.</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-slate-200">
                  <thead className="bg-slate-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                        Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                        Content
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                        Page
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-slate-200">
                    {filteredMetaTags.map((tag) => (
                      <tr key={tag.id} className="hover:bg-slate-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">
                          {tag.name}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-500 break-all">
                          {tag.content}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                          <Link href={`/admin/seo/pages/${tag.page.id}`} className="text-blue-500 hover:underline">
                            {tag.page.title}
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        ) : (
          <div className="p-6">
            <h2 className="text-lg font-medium text-slate-800 mb-4">Structured Data</h2>
            {filteredStructuredData.length === 0 ? (
              <div className="text-center py-8">
                <CodeBracketIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                <p className="text-slate-600">No structured data found.</p>
                <p className="text-slate-500 mt-2">Scan pages to extract structured data.</p>
              </div>
            ) : (
              <div className="space-y-6">
                {filteredStructuredData.map((item) => (
                  <div key={item.id} className="p-4 bg-slate-50 rounded-lg">
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        <DocumentTextIcon className="h-5 w-5 text-blue-500" />
                      </div>
                      <div className="ml-3 flex-1">
                        <div className="flex flex-wrap items-center justify-between">
                          <h3 className="text-sm font-medium text-slate-800">{item.type}</h3>
                          <Link href={`/admin/seo/pages/${item.page.id}`} className="text-xs text-blue-500 hover:underline">
                            {item.page.title}
                          </Link>
                        </div>
                        <pre className="mt-2 p-3 bg-slate-100 rounded text-xs text-slate-800 overflow-x-auto">
                          {JSON.stringify(item.data, null, 2)}
                        </pre>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
