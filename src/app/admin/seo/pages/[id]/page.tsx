'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  LinkIcon,
  TagIcon,
  CodeBracketIcon,
  CheckCircleIcon,
  XCircleIcon,
  PencilIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';

interface SeoPageDetail {
  id: string;
  url: string;
  title: string;
  description: string | null;
  keywords: string[];
  healthScore: number;
  lastScanned: string | null;
  status: string;
  seoIssues: {
    id: string;
    type: string;
    severity: string;
    description: string;
    status: string;
  }[];
  seoKeywords: {
    id: string;
    keyword: string;
    position: number | null;
    volume: number | null;
    difficulty: number | null;
  }[];
  seoMetaTags: {
    id: string;
    name: string;
    content: string;
  }[];
  seoStructuredData: {
    id: string;
    type: string;
    data: any;
  }[];
  brokenLinks: {
    id: string;
    url: string;
    statusCode: number | null;
    description: string | null;
    fixed: boolean;
    fixedUrl: string | null;
  }[];
}

export default function SeoPageDetail({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [page, setPage] = useState<SeoPageDetail | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('issues');
  const [isScanning, setIsScanning] = useState(false);
  const [fixUrl, setFixUrl] = useState('');
  const [selectedBrokenLink, setSelectedBrokenLink] = useState<string | null>(null);

  // Fetch page details
  const fetchPageDetails = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/admin/seo/pages/${params.id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch page details');
      }

      const data = await response.json();
      setPage(data);
    } catch (err) {
      console.error('Error fetching page details:', err);
      setError('Failed to load page details');
      toast.error('Failed to load page details');
    } finally {
      setIsLoading(false);
    }
  };

  // Scan the page
  const handleScan = async () => {
    if (!page) return;

    try {
      setIsScanning(true);

      const response = await fetch('/api/admin/seo/scan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: page.url }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to scan page');
      }

      toast.success('Page scanned successfully');
      fetchPageDetails(); // Refresh the page details
    } catch (err) {
      console.error('Error scanning page:', err);
      toast.error(err.message || 'Failed to scan page');
    } finally {
      setIsScanning(false);
    }
  };

  // Fix broken link
  const handleFixBrokenLink = async (id: string) => {
    if (!fixUrl) {
      toast.error('Please enter a URL to fix the broken link');
      return;
    }

    try {
      const response = await fetch(`/api/admin/seo/broken-links/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ fixedUrl: fixUrl }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fix broken link');
      }

      toast.success('Broken link fixed successfully');
      setFixUrl('');
      setSelectedBrokenLink(null);
      fetchPageDetails(); // Refresh the page details
    } catch (err) {
      console.error('Error fixing broken link:', err);
      toast.error(err.message || 'Failed to fix broken link');
    }
  };

  // Delete page
  const handleDeletePage = async () => {
    if (!page) return;

    if (!confirm('Are you sure you want to delete this page? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/seo/pages/${page.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete page');
      }

      toast.success('Page deleted successfully');
      router.push('/admin/seo');
    } catch (err) {
      console.error('Error deleting page:', err);
      toast.error(err.message || 'Failed to delete page');
    }
  };

  // Load page details on component mount
  useEffect(() => {
    fetchPageDetails();
  }, [params.id]);

  // Get health score color
  const getHealthScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-500';
    if (score >= 70) return 'text-yellow-500';
    if (score >= 50) return 'text-orange-500';
    return 'text-red-500';
  };

  // Get severity badge color
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-slate-100 text-slate-800';
    }
  };

  if (isLoading) {
    return (
      <div className="p-8 text-center">
        <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent"></div>
        <p className="mt-2 text-slate-600">Loading page details...</p>
      </div>
    );
  }

  if (error || !page) {
    return (
      <div className="p-8 text-center">
        <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <p className="text-red-500">{error || 'Page not found'}</p>
        <Link href="/admin/seo" className="mt-4 inline-flex items-center text-blue-500 hover:underline">
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Back to SEO Dashboard
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-wrap justify-between items-center gap-4">
        <div className="flex items-center">
          <Link href="/admin/seo" className="p-2 text-slate-500 hover:text-slate-700 mr-2">
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <div className="p-2 bg-blue-50 text-blue-500 rounded-lg mr-3">
            <DocumentTextIcon className="h-6 w-6" />
          </div>
          <div>
            <h1 className="text-2xl font-semibold text-slate-800">{page.title}</h1>
            <p className="text-slate-500">
              <a href={page.url} target="_blank" rel="noopener noreferrer" className="hover:underline">
                {page.url}
              </a>
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <button
            onClick={handleScan}
            disabled={isScanning}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isScanning ? 'Scanning...' : 'Rescan Page'}
          </button>
          <button
            onClick={handleDeletePage}
            className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
          >
            Delete
          </button>
        </div>
      </div>

      {/* Page Overview */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="p-4 bg-slate-50 rounded-lg">
            <h3 className="text-sm font-medium text-slate-500 mb-1">Health Score</h3>
            <p className={`text-2xl font-bold ${getHealthScoreColor(page.healthScore)}`}>
              {page.healthScore}/100
            </p>
          </div>
          <div className="p-4 bg-slate-50 rounded-lg">
            <h3 className="text-sm font-medium text-slate-500 mb-1">Issues</h3>
            <p className="text-2xl font-bold text-slate-800">{page.seoIssues.length}</p>
          </div>
          <div className="p-4 bg-slate-50 rounded-lg">
            <h3 className="text-sm font-medium text-slate-500 mb-1">Broken Links</h3>
            <p className="text-2xl font-bold text-slate-800">{page.brokenLinks.length}</p>
          </div>
          <div className="p-4 bg-slate-50 rounded-lg">
            <h3 className="text-sm font-medium text-slate-500 mb-1">Last Scanned</h3>
            <p className="text-lg font-medium text-slate-800">
              {page.lastScanned ? new Date(page.lastScanned).toLocaleString() : 'Never'}
            </p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="border-b border-slate-200">
          <nav className="flex -mb-px">
            <button
              onClick={() => setActiveTab('issues')}
              className={`py-4 px-6 text-sm font-medium border-b-2 ${
                activeTab === 'issues'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              }`}
            >
              Issues
            </button>
            <button
              onClick={() => setActiveTab('keywords')}
              className={`py-4 px-6 text-sm font-medium border-b-2 ${
                activeTab === 'keywords'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              }`}
            >
              Keywords
            </button>
            <button
              onClick={() => setActiveTab('meta-tags')}
              className={`py-4 px-6 text-sm font-medium border-b-2 ${
                activeTab === 'meta-tags'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              }`}
            >
              Meta Tags
            </button>
            <button
              onClick={() => setActiveTab('broken-links')}
              className={`py-4 px-6 text-sm font-medium border-b-2 ${
                activeTab === 'broken-links'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              }`}
            >
              Broken Links
            </button>
          </nav>
        </div>

        <div className="p-6">
          {/* Issues Tab */}
          {activeTab === 'issues' && (
            <div>
              <h2 className="text-lg font-medium text-slate-800 mb-4">SEO Issues</h2>
              {page.seoIssues.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircleIcon className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <p className="text-slate-600">No issues found. Great job!</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {page.seoIssues.map((issue) => (
                    <div key={issue.id} className="p-4 bg-slate-50 rounded-lg">
                      <div className="flex items-start">
                        <div className="flex-shrink-0">
                          <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
                        </div>
                        <div className="ml-3">
                          <div className="flex items-center">
                            <h3 className="text-sm font-medium text-slate-800">{issue.type.replace(/_/g, ' ')}</h3>
                            <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${getSeverityColor(issue.severity)}`}>
                              {issue.severity}
                            </span>
                          </div>
                          <p className="mt-1 text-sm text-slate-600">{issue.description}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Keywords Tab */}
          {activeTab === 'keywords' && (
            <div>
              <h2 className="text-lg font-medium text-slate-800 mb-4">Keywords</h2>
              {page.seoKeywords.length === 0 ? (
                <div className="text-center py-8">
                  <TagIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <p className="text-slate-600">No keywords found.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-slate-200">
                    <thead className="bg-slate-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Keyword</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Position</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Volume</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Difficulty</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-slate-200">
                      {page.seoKeywords.map((keyword) => (
                        <tr key={keyword.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">{keyword.keyword}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">{keyword.position || 'N/A'}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">{keyword.volume || 'N/A'}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">{keyword.difficulty || 'N/A'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}

          {/* Meta Tags Tab */}
          {activeTab === 'meta-tags' && (
            <div>
              <h2 className="text-lg font-medium text-slate-800 mb-4">Meta Tags</h2>
              {page.seoMetaTags.length === 0 ? (
                <div className="text-center py-8">
                  <CodeBracketIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <p className="text-slate-600">No meta tags found.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-slate-200">
                    <thead className="bg-slate-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Name</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Content</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-slate-200">
                      {page.seoMetaTags.map((tag) => (
                        <tr key={tag.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">{tag.name}</td>
                          <td className="px-6 py-4 text-sm text-slate-500 break-all">{tag.content}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}

          {/* Broken Links Tab */}
          {activeTab === 'broken-links' && (
            <div>
              <h2 className="text-lg font-medium text-slate-800 mb-4">Broken Links</h2>
              {page.brokenLinks.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircleIcon className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <p className="text-slate-600">No broken links found. Great job!</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {page.brokenLinks.map((link) => (
                    <div key={link.id} className="p-4 bg-slate-50 rounded-lg">
                      <div className="flex items-start">
                        <div className="flex-shrink-0">
                          {link.fixed ? (
                            <CheckCircleIcon className="h-5 w-5 text-green-500" />
                          ) : (
                            <XCircleIcon className="h-5 w-5 text-red-500" />
                          )}
                        </div>
                        <div className="ml-3 flex-1">
                          <div className="flex flex-wrap items-center justify-between">
                            <h3 className="text-sm font-medium text-slate-800 break-all">{link.url}</h3>
                            {link.statusCode && (
                              <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-red-100 text-red-800">
                                {link.statusCode}
                              </span>
                            )}
                          </div>
                          {link.description && <p className="mt-1 text-sm text-slate-600">{link.description}</p>}
                          {link.fixed && link.fixedUrl && (
                            <p className="mt-1 text-sm text-green-600">
                              Fixed with: <span className="font-medium break-all">{link.fixedUrl}</span>
                            </p>
                          )}
                          {!link.fixed && (
                            <div className="mt-3">
                              {selectedBrokenLink === link.id ? (
                                <div className="flex flex-col sm:flex-row gap-2">
                                  <input
                                    type="url"
                                    value={fixUrl}
                                    onChange={(e) => setFixUrl(e.target.value)}
                                    placeholder="https://example.com/fixed-url"
                                    className="flex-1 px-3 py-1 text-sm border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  />
                                  <div className="flex gap-2">
                                    <button
                                      onClick={() => handleFixBrokenLink(link.id)}
                                      className="px-3 py-1 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500"
                                    >
                                      Fix
                                    </button>
                                    <button
                                      onClick={() => {
                                        setSelectedBrokenLink(null);
                                        setFixUrl('');
                                      }}
                                      className="px-3 py-1 text-sm bg-slate-500 text-white rounded-lg hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-slate-500"
                                    >
                                      Cancel
                                    </button>
                                  </div>
                                </div>
                              ) : (
                                <button
                                  onClick={() => setSelectedBrokenLink(link.id)}
                                  className="px-3 py-1 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                  Fix Link
                                </button>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
