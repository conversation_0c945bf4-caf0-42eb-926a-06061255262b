'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import {
  ArrowLeftIcon,
  LinkIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';

interface BrokenLink {
  id: string;
  url: string;
  statusCode: number | null;
  description: string | null;
  fixed: boolean;
  fixedUrl: string | null;
  page: {
    id: string;
    url: string;
    title: string;
  };
}

export default function BrokenLinks() {
  const [brokenLinks, setBrokenLinks] = useState<BrokenLink[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fixUrl, setFixUrl] = useState('');
  const [selectedBrokenLink, setSelectedBrokenLink] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'fixed' | 'unfixed'>('all');

  // Fetch all broken links
  const fetchBrokenLinks = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/admin/seo/broken-links');
      if (!response.ok) {
        throw new Error('Failed to fetch broken links');
      }

      const data = await response.json();
      setBrokenLinks(data);
    } catch (err) {
      console.error('Error fetching broken links:', err);
      setError('Failed to load broken links');
      toast.error('Failed to load broken links');
    } finally {
      setIsLoading(false);
    }
  };

  // Fix broken link
  const handleFixBrokenLink = async (id: string) => {
    if (!fixUrl) {
      toast.error('Please enter a URL to fix the broken link');
      return;
    }

    try {
      const response = await fetch(`/api/admin/seo/broken-links/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ fixedUrl: fixUrl }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fix broken link');
      }

      toast.success('Broken link fixed successfully');
      setFixUrl('');
      setSelectedBrokenLink(null);
      fetchBrokenLinks(); // Refresh the list
    } catch (err) {
      console.error('Error fixing broken link:', err);
      toast.error(err.message || 'Failed to fix broken link');
    }
  };

  // Load broken links on component mount
  useEffect(() => {
    fetchBrokenLinks();
  }, []);

  // Filter broken links
  const filteredLinks = brokenLinks.filter((link) => {
    if (filter === 'fixed') return link.fixed;
    if (filter === 'unfixed') return !link.fixed;
    return true;
  });

  return (
    <div className="space-y-8">
      <div className="flex flex-wrap justify-between items-center gap-4">
        <div className="flex items-center">
          <Link href="/admin/seo" className="p-2 text-slate-500 hover:text-slate-700 mr-2">
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <div className="p-2 bg-red-50 text-red-500 rounded-lg mr-3">
            <LinkIcon className="h-6 w-6" />
          </div>
          <h1 className="text-2xl sm:text-3xl font-semibold text-slate-800">Broken Links</h1>
        </div>
        <div className="flex gap-2">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as 'all' | 'fixed' | 'unfixed')}
            className="px-4 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Links</option>
            <option value="fixed">Fixed Links</option>
            <option value="unfixed">Unfixed Links</option>
          </select>
          <button
            onClick={fetchBrokenLinks}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Broken Links */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="px-6 py-4 border-b border-slate-200">
          <h2 className="text-lg font-medium text-slate-800">
            {filter === 'all' ? 'All Broken Links' : filter === 'fixed' ? 'Fixed Links' : 'Unfixed Links'}
          </h2>
        </div>

        {isLoading ? (
          <div className="p-8 text-center">
            <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent"></div>
            <p className="mt-2 text-slate-600">Loading broken links...</p>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-500">{error}</p>
          </div>
        ) : filteredLinks.length === 0 ? (
          <div className="p-8 text-center">
            <CheckCircleIcon className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <p className="text-slate-600">
              {filter === 'all'
                ? 'No broken links found. Great job!'
                : filter === 'fixed'
                ? 'No fixed links found.'
                : 'No unfixed links found.'}
            </p>
          </div>
        ) : (
          <div className="p-6 space-y-4">
            {filteredLinks.map((link) => (
              <div key={link.id} className="p-4 bg-slate-50 rounded-lg">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    {link.fixed ? (
                      <CheckCircleIcon className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircleIcon className="h-5 w-5 text-red-500" />
                    )}
                  </div>
                  <div className="ml-3 flex-1">
                    <div className="flex flex-wrap items-center justify-between">
                      <h3 className="text-sm font-medium text-slate-800 break-all">{link.url}</h3>
                      {link.statusCode && (
                        <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-red-100 text-red-800">
                          {link.statusCode}
                        </span>
                      )}
                    </div>
                    {link.description && <p className="mt-1 text-sm text-slate-600">{link.description}</p>}
                    <p className="mt-1 text-sm text-slate-500">
                      Found on:{' '}
                      <Link href={`/admin/seo/pages/${link.page.id}`} className="text-blue-500 hover:underline">
                        {link.page.title}
                      </Link>
                    </p>
                    {link.fixed && link.fixedUrl && (
                      <p className="mt-1 text-sm text-green-600">
                        Fixed with: <span className="font-medium break-all">{link.fixedUrl}</span>
                      </p>
                    )}
                    {!link.fixed && (
                      <div className="mt-3">
                        {selectedBrokenLink === link.id ? (
                          <div className="flex flex-col sm:flex-row gap-2">
                            <input
                              type="url"
                              value={fixUrl}
                              onChange={(e) => setFixUrl(e.target.value)}
                              placeholder="https://example.com/fixed-url"
                              className="flex-1 px-3 py-1 text-sm border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                            <div className="flex gap-2">
                              <button
                                onClick={() => handleFixBrokenLink(link.id)}
                                className="px-3 py-1 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500"
                              >
                                Fix
                              </button>
                              <button
                                onClick={() => {
                                  setSelectedBrokenLink(null);
                                  setFixUrl('');
                                }}
                                className="px-3 py-1 text-sm bg-slate-500 text-white rounded-lg hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-slate-500"
                              >
                                Cancel
                              </button>
                            </div>
                          </div>
                        ) : (
                          <button
                            onClick={() => setSelectedBrokenLink(link.id)}
                            className="px-3 py-1 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            Fix Link
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
