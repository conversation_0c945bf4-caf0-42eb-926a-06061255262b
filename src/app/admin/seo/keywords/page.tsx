'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import {
  ArrowLeftIcon,
  TagIcon,
  ExclamationTriangleIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';

interface Keyword {
  id: string;
  keyword: string;
  position: number | null;
  volume: number | null;
  difficulty: number | null;
  lastChecked: string | null;
  page: {
    id: string;
    url: string;
    title: string;
  };
}

export default function KeywordsTracking() {
  const [keywords, setKeywords] = useState<Keyword[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'keyword' | 'position' | 'volume' | 'difficulty'>('keyword');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Fetch all keywords
  const fetchKeywords = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/admin/seo/keywords');
      if (!response.ok) {
        throw new Error('Failed to fetch keywords');
      }

      const data = await response.json();
      setKeywords(data);
    } catch (err) {
      console.error('Error fetching keywords:', err);
      setError('Failed to load keywords');
      toast.error('Failed to load keywords');
    } finally {
      setIsLoading(false);
    }
  };

  // Load keywords on component mount
  useEffect(() => {
    fetchKeywords();
  }, []);

  // Filter and sort keywords
  const filteredAndSortedKeywords = keywords
    .filter((keyword) => 
      keyword.keyword.toLowerCase().includes(searchTerm.toLowerCase()) ||
      keyword.page.title.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      if (sortBy === 'keyword') {
        return sortOrder === 'asc'
          ? a.keyword.localeCompare(b.keyword)
          : b.keyword.localeCompare(a.keyword);
      } else if (sortBy === 'position') {
        if (a.position === null) return sortOrder === 'asc' ? 1 : -1;
        if (b.position === null) return sortOrder === 'asc' ? -1 : 1;
        return sortOrder === 'asc' ? a.position - b.position : b.position - a.position;
      } else if (sortBy === 'volume') {
        if (a.volume === null) return sortOrder === 'asc' ? 1 : -1;
        if (b.volume === null) return sortOrder === 'asc' ? -1 : 1;
        return sortOrder === 'asc' ? a.volume - b.volume : b.volume - a.volume;
      } else if (sortBy === 'difficulty') {
        if (a.difficulty === null) return sortOrder === 'asc' ? 1 : -1;
        if (b.difficulty === null) return sortOrder === 'asc' ? -1 : 1;
        return sortOrder === 'asc' ? a.difficulty - b.difficulty : b.difficulty - a.difficulty;
      }
      return 0;
    });

  // Toggle sort order
  const toggleSort = (column: 'keyword' | 'position' | 'volume' | 'difficulty') => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  // Get sort indicator
  const getSortIndicator = (column: 'keyword' | 'position' | 'volume' | 'difficulty') => {
    if (sortBy !== column) return null;
    return sortOrder === 'asc' ? '↑' : '↓';
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-wrap justify-between items-center gap-4">
        <div className="flex items-center">
          <Link href="/admin/seo" className="p-2 text-slate-500 hover:text-slate-700 mr-2">
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <div className="p-2 bg-purple-50 text-purple-500 rounded-lg mr-3">
            <TagIcon className="h-6 w-6" />
          </div>
          <h1 className="text-2xl sm:text-3xl font-semibold text-slate-800">Keyword Tracking</h1>
        </div>
        <div className="flex gap-2">
          <button
            onClick={fetchKeywords}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-5 w-5 text-slate-400" />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search keywords or pages..."
            className="pl-10 px-4 py-2 w-full border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Keywords */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="px-6 py-4 border-b border-slate-200">
          <h2 className="text-lg font-medium text-slate-800">Keywords</h2>
        </div>

        {isLoading ? (
          <div className="p-8 text-center">
            <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent"></div>
            <p className="mt-2 text-slate-600">Loading keywords...</p>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <ExclamationTriangleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-500">{error}</p>
          </div>
        ) : keywords.length === 0 ? (
          <div className="p-8 text-center">
            <TagIcon className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <p className="text-slate-600">No keywords found.</p>
            <p className="text-slate-500 mt-2">Scan pages to extract keywords.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-slate-200">
              <thead className="bg-slate-50">
                <tr>
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => toggleSort('keyword')}
                  >
                    Keyword {getSortIndicator('keyword')}
                  </th>
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => toggleSort('position')}
                  >
                    Position {getSortIndicator('position')}
                  </th>
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => toggleSort('volume')}
                  >
                    Volume {getSortIndicator('volume')}
                  </th>
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => toggleSort('difficulty')}
                  >
                    Difficulty {getSortIndicator('difficulty')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                    Page
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                    Last Checked
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-slate-200">
                {filteredAndSortedKeywords.map((keyword) => (
                  <tr key={keyword.id} className="hover:bg-slate-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">
                      {keyword.keyword}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                      {keyword.position !== null ? keyword.position : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                      {keyword.volume !== null ? keyword.volume : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                      {keyword.difficulty !== null ? keyword.difficulty : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                      <Link href={`/admin/seo/pages/${keyword.page.id}`} className="text-blue-500 hover:underline">
                        {keyword.page.title}
                      </Link>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                      {keyword.lastChecked ? new Date(keyword.lastChecked).toLocaleString() : 'Never'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
