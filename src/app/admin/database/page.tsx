'use client';

import { useState, useEffect } from 'react';
import { ArrowDownTrayIcon, TrashIcon } from '@heroicons/react/24/outline';
import { CircleStackIcon as DatabaseIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import DatabaseFileDropzone from '@/components/admin/DatabaseFileDropzone';

interface DatabaseBackup {
  id: string;
  filename: string;
  description: string | null;
  size: number;
  path: string;
  type: string;
  status: string;
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
}

export default function DatabaseManagementPage() {
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);
  const [restoreError, setRestoreError] = useState<string | null>(null);
  const [restoreSuccess, setRestoreSuccess] = useState<string | null>(null);
  const [backups, setBackups] = useState<DatabaseBackup[]>([]);
  const [isLoadingBackups, setIsLoadingBackups] = useState(false);
  const [backupError, setBackupError] = useState<string | null>(null);

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Fetch backups
  const fetchBackups = async () => {
    setIsLoadingBackups(true);
    setBackupError(null);

    try {
      const response = await fetch('/api/admin/database/backups');

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch backups');
      }

      const data = await response.json();
      setBackups(data);
    } catch (error) {
      console.error('Error fetching backups:', error);
      setBackupError(error instanceof Error ? error.message : 'Failed to fetch backups');
    } finally {
      setIsLoadingBackups(false);
    }
  };

  // Delete backup
  const handleDeleteBackup = async (id: string) => {
    const confirmDelete = window.confirm('Are you sure you want to delete this backup?');
    if (!confirmDelete) return;

    const toastId = toast.loading('Deleting backup...');

    try {
      const response = await fetch(`/api/admin/database/backups?id=${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete backup');
      }

      toast.success('Backup deleted successfully', { id: toastId });

      // Refresh the backup list
      fetchBackups();
    } catch (error) {
      console.error('Error deleting backup:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete backup', { id: toastId });
    }
  };

  // Load backups on component mount
  useEffect(() => {
    fetchBackups();
  }, []);

  // Handle database backup
  const handleBackup = async () => {
    const toastId = toast.loading('Creating database backup...');

    try {
      setIsBackingUp(true);

      // Call the backup API endpoint
      const response = await fetch('/api/admin/database/backup', {
        method: 'GET',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to backup database');
      }

      // Get the filename from the Content-Disposition header
      const contentDisposition = response.headers.get('Content-Disposition');
      const filenameMatch = contentDisposition && contentDisposition.match(/filename="(.+)"/);
      const filename = filenameMatch ? filenameMatch[1] : 'database-backup.dump';

      toast.loading('Downloading backup file...', { id: toastId });

      // Convert the response to a blob and download it
      const blob = await response.blob();

      // Check if the blob has content
      if (blob.size === 0) {
        throw new Error('Backup file is empty. Please try again.');
      }

      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('Database backup created and downloaded successfully', { id: toastId });

      // Refresh the backup list
      fetchBackups();
    } catch (error) {
      console.error('Backup error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to backup database';
      toast.error(errorMessage, { id: toastId });
    } finally {
      setIsBackingUp(false);
    }
  };

  // Handle database restore via drag and drop
  const handleFileDrop = async (file: File) => {
    const toastId = toast.loading(`Processing file: ${file.name}`);

    try {
      setIsRestoring(true);
      setRestoreError(null);
      setRestoreSuccess(null);

      // Validate file type (basic check)
      if (!file.name.endsWith('.dump') && !file.name.endsWith('.sql')) {
        const errorMsg = 'Invalid file type. Please upload a .dump or .sql file';
        setRestoreError(errorMsg);
        toast.error(errorMsg, { id: toastId });
        return;
      }

      // Validate file size (basic check)
      const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
      if (file.size > MAX_FILE_SIZE) {
        const errorMsg = `File size exceeds maximum allowed (${Math.round(file.size / 1024 / 1024)}MB > 100MB)`;
        setRestoreError(errorMsg);
        toast.error(errorMsg, { id: toastId });
        return;
      }

      toast.loading('Creating backup before restore...', { id: toastId });

      // Create form data with the file
      const formData = new FormData();
      formData.append('file', file);

      // Call the restore API endpoint
      toast.loading('Uploading and restoring database...', { id: toastId });
      const response = await fetch('/api/admin/database/restore', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to restore database');
      }

      setRestoreSuccess(result.message || 'Database restored successfully');

      // Show success message with backup info
      if (result.preRestoreBackup) {
        toast.success(
          <div>
            <p>Database restored successfully!</p>
            <p className="text-xs mt-1">Pre-restore backup created: {result.preRestoreBackup}</p>
          </div>,
          { id: toastId, duration: 5000 }
        );
      } else {
        toast.success('Database restored successfully!', { id: toastId });
      }

      // Refresh the backup list
      fetchBackups();
    } catch (error) {
      console.error('Restore error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to restore database';
      setRestoreError(errorMessage);
      toast.error(errorMessage, { id: toastId });
    } finally {
      setIsRestoring(false);
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-slate-800">Database Management</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Backup Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center mb-4">
            <DatabaseIcon className="h-6 w-6 text-blue-500 mr-2" />
            <h2 className="text-lg font-medium text-slate-800">Backup Database</h2>
          </div>

          <p className="text-slate-600 mb-6">
            Create a backup of your database. The backup file will be downloaded to your computer.
          </p>

          <button
            onClick={handleBackup}
            disabled={isBackingUp}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            {isBackingUp ? 'Creating Backup...' : 'Download Backup'}
          </button>
        </div>

        {/* Restore Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center mb-4">
            <DatabaseIcon className="h-6 w-6 text-green-500 mr-2" />
            <h2 className="text-lg font-medium text-slate-800">Restore Database</h2>
          </div>

          <p className="text-slate-600 mb-6">
            Restore your database from a backup file. Drag and drop a backup file or click to select.
          </p>

          <DatabaseFileDropzone
            onFileDrop={handleFileDrop}
            isProcessing={isRestoring}
            error={restoreError}
            success={restoreSuccess}
          />
        </div>
      </div>

      <div className="bg-amber-50 border-l-4 border-amber-400 p-4 rounded-md">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-amber-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-amber-700">
              <strong>Warning:</strong> Restoring a database will overwrite all existing data. Make sure you have a backup before proceeding.
            </p>
          </div>
        </div>
      </div>

      {/* Recent Backups Section */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <DatabaseIcon className="h-6 w-6 text-indigo-500 mr-2" />
            <h2 className="text-lg font-medium text-slate-800">Recent Backups</h2>
          </div>

          <button
            onClick={fetchBackups}
            disabled={isLoadingBackups}
            className="text-sm text-indigo-600 hover:text-indigo-800"
          >
            {isLoadingBackups ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>

        {backupError && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-md mb-4">
            <p className="text-sm text-red-700">{backupError}</p>
          </div>
        )}

        {isLoadingBackups ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
            <span className="ml-2 text-gray-500">Loading backups...</span>
          </div>
        ) : backups.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No backups found. Create your first backup using the button above.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Filename
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Size
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {backups.map((backup) => (
                  <tr key={backup.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      <div className="truncate max-w-xs" title={backup.filename}>
                        {backup.filename}
                      </div>
                      {backup.description && (
                        <div className="text-xs text-gray-500 truncate max-w-xs" title={backup.description}>
                          {backup.description}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        backup.type === 'manual'
                          ? 'bg-blue-100 text-blue-800'
                          : backup.type === 'automatic'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {backup.type}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatFileSize(backup.size)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(backup.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        backup.status === 'completed'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {backup.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleDeleteBackup(backup.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete backup"
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
