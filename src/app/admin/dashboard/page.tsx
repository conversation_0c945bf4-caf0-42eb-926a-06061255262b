'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import {
  ChartBarIcon,
  DocumentTextIcon,
  FolderIcon,
  TagIcon,
  UserGroupIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import ServerStats from '@/components/admin/ServerStats';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  href: string;
}

const StatCard = ({ title, value, icon, href }: StatCardProps) => (
  <Link
    href={href}
    className="bg-white p-5 sm:p-6 rounded-lg shadow-sm border border-slate-100 hover:shadow-md transition-all duration-300 hover:-translate-y-1 relative overflow-hidden group"
  >
    <div className="flex items-center">
      <div className="p-3 sm:p-4 rounded-lg bg-blue-50 text-blue-500 mr-4 sm:mr-5 group-hover:bg-blue-100 transition-colors duration-300">
        {icon}
      </div>
      <div>
        <p className="text-xs sm:text-sm text-slate-500 mb-1">{title}</p>
        <p className="text-2xl sm:text-3xl font-semibold text-slate-800">{value}</p>
      </div>
    </div>
  </Link>
);

export default function AdminDashboard() {
  const [stats, setStats] = useState({
    posts: 0,
    categories: 0,
    tags: 0,
    authors: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Use the full URL with the correct port
      const response = await fetch(`${window.location.origin}/api/admin/stats/dashboard`);

      if (!response.ok) {
        throw new Error('Failed to fetch dashboard statistics');
      }

      const data = await response.json();
      setStats(data);
    } catch (err) {
      console.error('Error fetching dashboard stats:', err);
      setError('Failed to load dashboard statistics');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Refresh stats manually
  const handleRefresh = () => {
    fetchStats();
  };

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return (
    <div className="space-y-8">
      <div className="flex flex-wrap justify-between items-center gap-4">
        <div className="flex items-center">
          <div className="p-2 bg-blue-50 text-blue-500 rounded-lg mr-3">
            <ChartBarIcon className="h-6 w-6" />
          </div>
          <h1 className="text-2xl sm:text-3xl font-semibold text-slate-800">Dashboard</h1>
        </div>
        <button
          onClick={handleRefresh}
          disabled={isLoading}
          className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100 transition-colors"
          aria-label="Refresh statistics"
        >
          <ArrowPathIcon className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 p-4 rounded-xl shadow-sm">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-2 bg-red-100 rounded-full">
              <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Loading Dashboard</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Welcome Card */}
      <div className="bg-white rounded-lg shadow-sm p-6 sm:p-8 relative overflow-hidden">
        <div className="relative">
          <h2 className="text-xl sm:text-2xl font-semibold text-slate-800 mb-2 sm:mb-4">Welcome to the Admin Dashboard</h2>
          <p className="text-slate-600 text-sm sm:text-base max-w-2xl">
            This is your central hub for managing content on the Mocky Digital website. Monitor server performance, manage blog posts, and update your website content all from one place.
            {/* Different instructions for mobile vs desktop */}
            <span className="md:hidden block mt-3 text-slate-700 font-medium">
              Use the menu button in the top-left to navigate to different sections.
            </span>
            <span className="hidden md:block mt-3 text-slate-700 font-medium">
              Use the sidebar to navigate to different sections.
            </span>
          </p>
        </div>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="bg-white p-5 sm:p-6 rounded-lg shadow-sm border border-slate-100 animate-pulse">
              <div className="flex items-center">
                <div className="p-3 sm:p-4 rounded-lg bg-slate-200 mr-4 sm:mr-5 h-12 w-12 sm:h-14 sm:w-14"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-slate-200 rounded w-20 sm:w-24"></div>
                  <div className="h-7 sm:h-8 bg-slate-200 rounded w-16 sm:w-20"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          <StatCard
            title="Blog Posts"
            value={stats.posts}
            icon={<DocumentTextIcon className="h-5 w-5 sm:h-6 sm:w-6" />}
            href="/admin/blog"
          />
          <StatCard
            title="Categories"
            value={stats.categories}
            icon={<FolderIcon className="h-5 w-5 sm:h-6 sm:w-6" />}
            href="/admin/categories"
          />
          <StatCard
            title="Tags"
            value={stats.tags}
            icon={<TagIcon className="h-5 w-5 sm:h-6 sm:w-6" />}
            href="/admin/tags"
          />
          <StatCard
            title="Authors"
            value={stats.authors}
            icon={<UserGroupIcon className="h-5 w-5 sm:h-6 sm:w-6" />}
            href="/admin/authors"
          />
        </div>
      )}

      {/* Server Stats Section */}
      <ServerStats refreshInterval={60000} />
    </div>
  );
}
