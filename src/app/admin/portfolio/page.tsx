'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {
  PlusIcon,
  ArrowPathIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { PortfolioItem, PORTFOLIO_CATEGORIES, PortfolioCategory } from '@/types/portfolio';
import { useNotification } from '@/contexts/NotificationContext';

export default function PortfolioPage() {
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [deleteItemId, setDeleteItemId] = useState<string | null>(null);
  const { showNotification } = useNotification();

  const fetchPortfolioItems = async (category?: string) => {
    try {
      setLoading(true);
      setError('');

      // Build URL with parameters
      let url = '/api/admin/portfolio';
      const params = new URLSearchParams();

      // Add category parameter if provided and not 'all'
      if (category && category !== 'all') {
        params.append('category', category);
      }

      // Always use S3 storage
      params.append('useS3', 'true');

      // Add timestamp to prevent caching
      params.append('t', Date.now().toString());

      // Append parameters to URL
      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      console.log('Fetching portfolio items from:', url);

      const response = await fetch(url, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch portfolio items: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`Received ${data.length} portfolio items`);
      setPortfolioItems(data);
    } catch (err) {
      console.error('Error fetching portfolio items:', err);
      setError('Failed to load portfolio items. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPortfolioItems(selectedCategory);
  }, [selectedCategory]);

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  };

  const handleDeleteClick = (id: string) => {
    setDeleteItemId(id);
  };

  const handleConfirmDelete = async () => {
    if (!deleteItemId) return;

    try {
      setLoading(true);

      const response = await fetch(`/api/admin/portfolio/${deleteItemId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete portfolio item: ${response.statusText}`);
      }

      // Remove the deleted item from the state
      setPortfolioItems(prevItems => prevItems.filter(item => item.id !== deleteItemId));
      showNotification('success', 'Success', 'Portfolio item deleted successfully');
    } catch (err) {
      console.error('Error deleting portfolio item:', err);
      showNotification('error', 'Error', 'Failed to delete portfolio item');
    } finally {
      setLoading(false);
      setDeleteItemId(null);
    }
  };

  const handleCancelDelete = () => {
    setDeleteItemId(null);
  };

  const handleToggleFeatured = async (id: string, currentFeatured: boolean) => {
    try {
      setLoading(true);

      const item = portfolioItems.find(item => item.id === id);
      if (!item) return;

      const response = await fetch(`/api/admin/portfolio/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...item,
          featured: !currentFeatured,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update portfolio item: ${response.statusText}`);
      }

      const updatedItem = await response.json();

      // Update the item in the state
      setPortfolioItems(prevItems =>
        prevItems.map(item => item.id === id ? updatedItem : item)
      );

      showNotification(
        'success',
        'Success',
        `Item ${updatedItem.featured ? 'marked as featured' : 'removed from featured'}`
      );
    } catch (err) {
      console.error('Error updating portfolio item:', err);
      showNotification('error', 'Error', 'Failed to update portfolio item');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center mb-6 mt-2">
        <h1 className="text-2xl font-bold text-slate-800">Portfolio Management</h1>
        <div className="flex space-x-3">
          <button
            onClick={() => fetchPortfolioItems(selectedCategory)}
            className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100 transition-colors"
            disabled={loading}
            aria-label="Refresh portfolio items"
          >
            <ArrowPathIcon className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
          <Link
            href="/admin/portfolio/new"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 transition-colors"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Portfolio Item
          </Link>
        </div>
      </div>

      {/* Category Filter */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center gap-4">
          <label htmlFor="category-filter" className="text-sm font-medium text-gray-700 min-w-[100px]">
            Filter by Category:
          </label>
          <select
            id="category-filter"
            value={selectedCategory}
            onChange={handleCategoryChange}
            className="block w-full sm:w-auto px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm"
          >
            <option value="all">All Categories</option>
            {PORTFOLIO_CATEGORIES.map(category => (
              <option key={category.value} value={category.value}>
                {category.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-5 rounded-md shadow-sm mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700 font-medium">{error}</p>
            </div>
          </div>
        </div>
      )}

      {loading && portfolioItems.length === 0 ? (
        <div className="text-center py-16 bg-white rounded-lg shadow-sm border border-gray-200">
          <svg className="animate-spin h-10 w-10 text-orange-500 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <p className="mt-4 text-sm font-medium text-slate-500">Loading portfolio items...</p>
        </div>
      ) : portfolioItems.length === 0 ? (
        <div className="text-center py-16 bg-white rounded-lg shadow-sm border border-gray-200">
          <svg className="mx-auto h-16 w-16 text-slate-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <h3 className="mt-4 text-lg font-medium text-slate-800">No portfolio items found</h3>
          <p className="mt-2 text-slate-500 max-w-md mx-auto">
            {selectedCategory !== 'all'
              ? `No items found in the "${PORTFOLIO_CATEGORIES.find(c => c.value === selectedCategory)?.label || selectedCategory}" category.`
              : 'Get started by adding your first portfolio item.'}
          </p>
          <div className="mt-8">
            <Link
              href="/admin/portfolio/new"
              className="inline-flex items-center px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 transition-colors"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Your First Item
            </Link>
          </div>
        </div>
      ) : (
        <div className="bg-white shadow-sm rounded-lg overflow-hidden border border-gray-200">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-6">
            {portfolioItems.map((item) => (
              <div
                key={item.id}
                className="relative bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
              >
                <div className="relative aspect-square bg-gray-100">
                  <Image
                    src={item.imageSrc}
                    alt={item.alt || item.title}
                    fill
                    className="object-cover"
                    sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                  />
                  <div className="absolute top-2 right-2 flex space-x-1">
                    <button
                      onClick={() => handleToggleFeatured(item.id, !!item.featured)}
                      className={`p-1.5 rounded-full ${item.featured ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-100 text-gray-500'} hover:bg-gray-200 transition-colors`}
                      title={item.featured ? 'Remove from featured' : 'Mark as featured'}
                    >
                      {item.featured ? (
                        <StarIconSolid className="h-4 w-4" />
                      ) : (
                        <StarIcon className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="text-lg font-medium text-gray-900 truncate">{item.title}</h3>
                  <p className="text-sm text-gray-500 mt-1 capitalize">{item.category}</p>

                  <div className="mt-4 flex justify-between items-center">
                    <span className="text-xs text-gray-500">
                      {new Date(item.createdAt).toLocaleDateString()}
                    </span>
                    <div className="flex space-x-2">
                      <Link
                        href={`/portfolio?item=${item.id}`}
                        target="_blank"
                        className="text-slate-400 hover:text-slate-600 transition-colors p-1 rounded-full hover:bg-slate-100"
                        aria-label="View portfolio item"
                      >
                        <EyeIcon className="h-5 w-5" />
                      </Link>
                      <Link
                        href={`/admin/portfolio/${item.id}/edit`}
                        className="text-blue-400 hover:text-blue-600 transition-colors p-1 rounded-full hover:bg-blue-50"
                        aria-label="Edit portfolio item"
                      >
                        <PencilIcon className="h-5 w-5" />
                      </Link>
                      <button
                        onClick={() => handleDeleteClick(item.id)}
                        className="text-red-400 hover:text-red-600 transition-colors p-1 rounded-full hover:bg-red-50"
                        aria-label="Delete portfolio item"
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {deleteItemId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6 shadow-xl">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
            <p className="text-gray-500 mb-6">
              Are you sure you want to delete this portfolio item? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={handleCancelDelete}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
