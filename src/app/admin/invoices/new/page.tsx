'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  PlusIcon,
  MinusIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { useNotification } from '@/contexts/NotificationContext';
import { addDays, format } from 'date-fns';

interface Service {
  id: string;
  name: string;
  description: string | null;
  price: number;
  category: string;
}

interface InvoiceItem {
  serviceId: string;
  quantity: number;
  unitPrice: number;
  description?: string;
}

export default function NewInvoicePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const quoteId = searchParams.get('quoteId');
  const { showNotification } = useNotification();

  const [customerName, setCustomerName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [email, setEmail] = useState('');
  const [notes, setNotes] = useState('');
  const [dueDate, setDueDate] = useState(format(addDays(new Date(), 14), 'yyyy-MM-dd'));
  const [items, setItems] = useState<InvoiceItem[]>([{ serviceId: '', quantity: 1, unitPrice: 0 }]);
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [quoteData, setQuoteData] = useState<any | null>(null);

  const fetchServices = useCallback(async () => {
    try {
      const response = await fetch('/api/admin/services');
      
      if (!response.ok) {
        throw new Error('Failed to fetch services');
      }

      const data = await response.json();
      setServices(data);
    } catch (err) {
      console.error('Error fetching services:', err);
      showNotification('error', 'Failed to load services');
    } finally {
      setLoading(false);
    }
  }, [showNotification]);

  const fetchQuoteData = useCallback(async () => {
    if (!quoteId) return;
    
    try {
      const response = await fetch(`/api/admin/quotes/${quoteId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch quote');
      }

      const data = await response.json();
      setQuoteData(data);
      
      // Pre-fill form with quote data
      setCustomerName(data.customerName);
      setPhoneNumber(data.phoneNumber);
      setEmail(data.email || '');
      setNotes(data.notes || '');
      
      // Convert quote items to invoice items
      const invoiceItems = data.items.map((item: any) => ({
        serviceId: item.serviceId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        description: item.description || ''
      }));
      
      setItems(invoiceItems);
    } catch (err) {
      console.error('Error fetching quote:', err);
      showNotification('error', 'Failed to load quote data');
    }
  }, [quoteId, showNotification]);

  useEffect(() => {
    fetchServices();
    fetchQuoteData();
  }, [fetchServices, fetchQuoteData]);

  const handleServiceChange = (index: number, serviceId: string) => {
    const newItems = [...items];
    const service = services.find(s => s.id === serviceId);
    
    if (service) {
      newItems[index] = {
        ...newItems[index],
        serviceId,
        unitPrice: service.price,
        description: service.description || undefined
      };
    } else {
      newItems[index] = {
        ...newItems[index],
        serviceId,
        unitPrice: 0
      };
    }
    
    setItems(newItems);
  };

  const handleQuantityChange = (index: number, quantity: number) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], quantity };
    setItems(newItems);
  };

  const handleUnitPriceChange = (index: number, unitPrice: number) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], unitPrice };
    setItems(newItems);
  };

  const handleDescriptionChange = (index: number, description: string) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], description };
    setItems(newItems);
  };

  const addItem = () => {
    setItems([...items, { serviceId: '', quantity: 1, unitPrice: 0 }]);
  };

  const removeItem = (index: number) => {
    if (items.length === 1) {
      return;
    }
    
    const newItems = [...items];
    newItems.splice(index, 1);
    setItems(newItems);
  };

  const calculateTotal = () => {
    return items.reduce((total, item) => total + (item.quantity * item.unitPrice), 0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);

    // Validate form
    if (!customerName || !phoneNumber || items.some(item => !item.serviceId)) {
      setError('Please fill in all required fields');
      setSubmitting(false);
      return;
    }

    try {
      const response = await fetch('/api/admin/invoices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerName,
          phoneNumber,
          email: email || undefined,
          notes: notes || undefined,
          dueDate,
          items,
          quoteId: quoteId || undefined
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create invoice');
      }

      const invoice = await response.json();
      showNotification('success', 'Invoice created successfully');
      router.push(`/admin/invoices/${invoice.id}`);
    } catch (err) {
      console.error('Error creating invoice:', err);
      setError(err.message || 'Failed to create invoice');
      showNotification('error', 'Failed to create invoice');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Link
          href="/admin/invoices"
          className="mr-4 p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100"
        >
          <ArrowLeftIcon className="h-5 w-5" />
        </Link>
        <div className="p-2 bg-blue-50 text-blue-500 rounded-lg mr-3">
          <DocumentTextIcon className="h-6 w-6" />
        </div>
        <h1 className="text-2xl font-bold text-slate-800">
          {quoteId ? 'Convert Quote to Invoice' : 'Create New Invoice'}
        </h1>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <XCircleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {quoteData && (
        <div className="bg-blue-50 border-l-4 border-blue-400 p-4">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                Creating invoice from Quote #{quoteData.quoteNumber}
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h2 className="text-lg font-medium text-gray-900 mb-4">Customer Information</h2>
              <div className="space-y-4">
                <div>
                  <label htmlFor="customerName" className="block text-sm font-medium text-gray-700">
                    Customer Name *
                  </label>
                  <input
                    type="text"
                    id="customerName"
                    value={customerName}
                    onChange={(e) => setCustomerName(e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700">
                    Phone Number *
                  </label>
                  <input
                    type="text"
                    id="phoneNumber"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>
            </div>

            <div>
              <h2 className="text-lg font-medium text-gray-900 mb-4">Invoice Details</h2>
              <div className="space-y-4">
                <div>
                  <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700">
                    Due Date *
                  </label>
                  <input
                    type="date"
                    id="dueDate"
                    value={dueDate}
                    onChange={(e) => setDueDate(e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                    Notes
                  </label>
                  <textarea
                    id="notes"
                    rows={3}
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>
            </div>
          </div>

          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-4">Items</h2>
            <div className="space-y-4">
              {items.map((item, index) => (
                <div key={index} className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-2 p-4 border border-gray-200 rounded-md">
                  <div className="flex-grow">
                    <label htmlFor={`service-${index}`} className="block text-sm font-medium text-gray-700">
                      Service *
                    </label>
                    <select
                      id={`service-${index}`}
                      value={item.serviceId}
                      onChange={(e) => handleServiceChange(index, e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      required
                    >
                      <option value="">Select a service</option>
                      {services.map((service) => (
                        <option key={service.id} value={service.id}>
                          {service.name} - KES {service.price.toLocaleString()}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="w-24">
                    <label htmlFor={`quantity-${index}`} className="block text-sm font-medium text-gray-700">
                      Quantity *
                    </label>
                    <input
                      type="number"
                      id={`quantity-${index}`}
                      value={item.quantity}
                      onChange={(e) => handleQuantityChange(index, parseInt(e.target.value))}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      min="1"
                      required
                    />
                  </div>
                  <div className="w-32">
                    <label htmlFor={`unitPrice-${index}`} className="block text-sm font-medium text-gray-700">
                      Unit Price *
                    </label>
                    <input
                      type="number"
                      id={`unitPrice-${index}`}
                      value={item.unitPrice}
                      onChange={(e) => handleUnitPriceChange(index, parseFloat(e.target.value))}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      min="0"
                      step="0.01"
                      required
                    />
                  </div>
                  <div className="flex-grow">
                    <label htmlFor={`description-${index}`} className="block text-sm font-medium text-gray-700">
                      Description
                    </label>
                    <input
                      type="text"
                      id={`description-${index}`}
                      value={item.description || ''}
                      onChange={(e) => handleDescriptionChange(index, e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                  </div>
                  <div className="flex items-end pb-1">
                    <button
                      type="button"
                      onClick={() => removeItem(index)}
                      className="ml-2 p-1 text-red-600 hover:text-red-800"
                      disabled={items.length === 1}
                      title="Remove item"
                    >
                      <MinusIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              ))}
              <div>
                <button
                  type="button"
                  onClick={addItem}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  <PlusIcon className="-ml-0.5 mr-2 h-4 w-4" />
                  Add Item
                </button>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-200 pt-4">
            <div className="flex justify-between items-center">
              <div className="text-lg font-medium text-gray-900">
                Total: KES {calculateTotal().toLocaleString()}
              </div>
              <div className="flex space-x-2">
                <Link
                  href="/admin/invoices"
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={submitting || loading}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  {submitting ? 'Creating...' : 'Create Invoice'}
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
