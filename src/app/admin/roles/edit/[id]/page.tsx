'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ShieldCheckIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { useNotification } from '@/contexts/NotificationContext';
import PermissionSelector from '@/components/admin/PermissionSelector';
import { format } from 'date-fns';

interface Role {
  id: string;
  name: string;
  description: string | null;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
  _count?: {
    users: number;
  };
}

export default function EditRolePage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { showNotification } = useNotification();
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    permissions: [] as string[],
  });
  const [originalData, setOriginalData] = useState<Role | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch role on component mount
  useEffect(() => {
    const fetchRole = async () => {
      try {
        setIsFetching(true);
        
        const response = await fetch(`${window.location.origin}/api/admin/roles/${params.id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch role');
        }
        
        const roleData = await response.json();
        setOriginalData(roleData);
        
        // Set form data
        setFormData({
          name: roleData.name,
          description: roleData.description || '',
          permissions: roleData.permissions || [],
        });
      } catch (error) {
        console.error('Error fetching role:', error);
        showNotification('error', 'Error', 'Failed to load role data');
        router.push('/admin/roles');
      } finally {
        setIsFetching(false);
      }
    };
    
    fetchRole();
  }, [params.id, router, showNotification]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Role name is required';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Role name must be at least 2 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handlePermissionsChange = (permissions: string[]) => {
    setFormData(prev => ({
      ...prev,
      permissions
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      setIsLoading(true);
      
      // Prepare data for update (only include changed fields)
      const updateData: any = {};
      
      if (formData.name !== originalData?.name) {
        updateData.name = formData.name;
      }
      
      if (formData.description !== (originalData?.description || '')) {
        updateData.description = formData.description || null;
      }
      
      // Always include permissions as they might have changed
      updateData.permissions = formData.permissions;
      
      const response = await fetch(`${window.location.origin}/api/admin/roles/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update role');
      }
      
      showNotification('success', 'Success', 'Role updated successfully');
      router.push('/admin/roles');
    } catch (error: any) {
      console.error('Error updating role:', error);
      showNotification('error', 'Error', error.message || 'Failed to update role');
    } finally {
      setIsLoading(false);
    }
  };

  if (isFetching) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  const isBuiltInRole = originalData?.name && ['admin', 'editor', 'viewer'].includes(originalData.name);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="p-2 bg-blue-50 text-blue-500 rounded-lg mr-3">
            <ShieldCheckIcon className="h-6 w-6" />
          </div>
          <h1 className="text-2xl font-bold text-slate-800">Edit Role</h1>
        </div>
        <Link
          href="/admin/roles"
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          Back to Roles
        </Link>
      </div>

      {originalData && (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="mb-6 pb-6 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Role Information</h2>
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Role ID:</span>
                <span className="ml-2 text-gray-900">{originalData.id}</span>
              </div>
              <div>
                <span className="text-gray-500">Created:</span>
                <span className="ml-2 text-gray-900">
                  {format(new Date(originalData.createdAt), 'MMM d, yyyy HH:mm')}
                </span>
              </div>
              <div>
                <span className="text-gray-500">Last Updated:</span>
                <span className="ml-2 text-gray-900">
                  {format(new Date(originalData.updatedAt), 'MMM d, yyyy HH:mm')}
                </span>
              </div>
              <div>
                <span className="text-gray-500">Users with this role:</span>
                <span className="ml-2 text-gray-900">
                  {originalData._count?.users || 0}
                </span>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Role Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="name"
                  id="name"
                  value={formData.name}
                  onChange={handleChange}
                  disabled={isBuiltInRole}
                  className={`mt-1 block w-full rounded-md shadow-sm sm:text-sm ${
                    errors.name ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-orange-500 focus:ring-orange-500'
                  } ${isBuiltInRole ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                )}
                {isBuiltInRole && (
                  <p className="mt-1 text-xs text-gray-500">Built-in role names cannot be changed</p>
                )}
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description
                </label>
                <textarea
                  name="description"
                  id="description"
                  rows={3}
                  value={formData.description}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Permissions
                </label>
                <PermissionSelector 
                  selectedPermissions={formData.permissions} 
                  onChange={handlePermissionsChange}
                  disabled={originalData.name === 'admin'} 
                />
                {originalData.name === 'admin' && (
                  <p className="mt-1 text-xs text-gray-500">Admin role always has all permissions</p>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <Link
                href="/admin/roles"
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
}
