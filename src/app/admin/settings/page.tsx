'use client';

import { useState, useEffect } from 'react';
import {
  ArrowPathIcon,
  CheckIcon,
  ExclamationCircleIcon,
  Cog6ToothIcon,
  ServerIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';

interface SettingsFormData {
  id?: string;
  siteName: string;
  siteDescription: string;
  contactEmail: string;
  phoneNumber: string;
  address: string;
  facebookUrl: string;
  twitterUrl: string;
  instagramUrl: string;
  tiktokUrl: string;
  linkedinUrl: string;
  metaTitle: string;
  metaDescription: string;
  googleAnalyticsId: string;
}

export default function SettingsPage() {
  const [formData, setFormData] = useState<SettingsFormData>({
    siteName: '',
    siteDescription: '',
    contactEmail: '',
    phoneNumber: '',
    address: '',
    facebookUrl: '',
    twitterUrl: '',
    instagramUrl: '',
    tiktokUrl: '',
    linkedinUrl: '',
    metaTitle: '',
    metaDescription: '',
    googleAnalyticsId: '',
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError('');

      // Fetch settings from the API
      const response = await fetch('/api/admin/settings');

      if (!response.ok) {
        throw new Error('Failed to fetch settings');
      }

      const data = await response.json();

      // If we have settings data, use it
      if (data && Object.keys(data).length > 0) {
        setFormData({
          id: data.id || '',
          siteName: data.siteName || '',
          siteDescription: data.siteDescription || '',
          contactEmail: data.contactEmail || '',
          phoneNumber: data.phoneNumber || '',
          address: data.address || '',
          facebookUrl: data.facebookUrl || '',
          twitterUrl: data.twitterUrl || '',
          instagramUrl: data.instagramUrl || '',
          tiktokUrl: data.tiktokUrl || '',
          linkedinUrl: data.linkedinUrl || '',
          metaTitle: data.metaTitle || '',
          metaDescription: data.metaDescription || '',
          googleAnalyticsId: data.googleAnalyticsId || '',
        });
      } else {
        // Use default values if no settings exist
        setFormData({
          siteName: 'Mocky Digital',
          siteDescription: 'Professional web design and digital marketing services',
          contactEmail: '<EMAIL>',
          phoneNumber: '+*********** 670',
          address: 'Nairobi, Kenya',
          facebookUrl: 'https://facebook.com/mockydigital',
          twitterUrl: 'https://x.com/mockydigital',
          instagramUrl: 'https://instagram.com/mockydigital',
          tiktokUrl: 'https://tiktok.com/@mocky_digital',
          linkedinUrl: 'https://linkedin.com/company/mockydigital',
          metaTitle: 'Mocky Digital - Web Design & Digital Marketing',
          metaDescription: 'Professional web design, development, and digital marketing services in Nairobi, Kenya.',
          googleAnalyticsId: '',
        });
      }
    } catch (err) {
      console.error('Error fetching settings:', err);
      setError('Failed to load settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Direct property update (no nested objects anymore)
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);
      setError('');
      setSuccess('');

      // Send data to the API
      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save settings');
      }

      // Get the updated settings
      const updatedSettings = await response.json();

      // Update the form with the latest data
      setFormData(updatedSettings);

      setSuccess('Settings saved successfully!');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess('');
      }, 3000);
    } catch (err: any) {
      console.error('Error saving settings:', err);
      setError(err.message || 'Failed to save settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-slate-800">Settings</h1>
        <button
          onClick={fetchSettings}
          className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100"
          disabled={loading}
        >
          <ArrowPathIcon className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {/* Settings Navigation */}
      <div className="bg-white shadow-sm rounded-lg p-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        <Link href="/admin/settings" className="p-4 border rounded-lg hover:bg-orange-50 hover:border-orange-200 transition-colors">
          <div className="flex items-start">
            <Cog6ToothIcon className="h-6 w-6 text-orange-500 mr-3 mt-0.5" />
            <div>
              <h3 className="font-medium text-slate-800">General Settings</h3>
              <p className="text-sm text-slate-500 mt-1">Configure basic site information and contact details</p>
            </div>
          </div>
        </Link>
        <Link href="/admin/settings/storage" className="p-4 border rounded-lg hover:bg-orange-50 hover:border-orange-200 transition-colors">
          <div className="flex items-start">
            <ServerIcon className="h-6 w-6 text-orange-500 mr-3 mt-0.5" />
            <div>
              <h3 className="font-medium text-slate-800">Storage Settings</h3>
              <p className="text-sm text-slate-500 mt-1">Configure file storage providers and buckets</p>
            </div>
          </div>
        </Link>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <ExclamationCircleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border-l-4 border-green-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <CheckIcon className="h-5 w-5 text-green-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        </div>
      )}

      {loading ? (
        <div className="text-center py-12">
          <svg className="animate-spin h-8 w-8 text-orange-500 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <p className="mt-2 text-sm text-slate-500">Loading settings...</p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* General Settings */}
          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <div className="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
              <h3 className="text-lg font-medium leading-6 text-gray-900">General Settings</h3>
              <p className="mt-1 text-sm text-gray-500">Basic information about your website.</p>
            </div>
            <div className="px-4 py-5 sm:p-6 space-y-4">
              <div>
                <label htmlFor="siteName" className="block text-sm font-medium text-gray-700">
                  Site Name
                </label>
                <input
                  type="text"
                  name="siteName"
                  id="siteName"
                  value={formData.siteName}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                />
              </div>
              <div>
                <label htmlFor="siteDescription" className="block text-sm font-medium text-gray-700">
                  Site Description
                </label>
                <textarea
                  name="siteDescription"
                  id="siteDescription"
                  rows={3}
                  value={formData.siteDescription}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                />
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <div className="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
              <h3 className="text-lg font-medium leading-6 text-gray-900">Contact Information</h3>
              <p className="mt-1 text-sm text-gray-500">How visitors can reach you.</p>
            </div>
            <div className="px-4 py-5 sm:p-6 space-y-4">
              <div>
                <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700">
                  Email Address
                </label>
                <input
                  type="email"
                  name="contactEmail"
                  id="contactEmail"
                  value={formData.contactEmail}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                />
              </div>
              <div>
                <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700">
                  Phone Number
                </label>
                <input
                  type="text"
                  name="phoneNumber"
                  id="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                />
              </div>
              <div>
                <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                  Address
                </label>
                <textarea
                  name="address"
                  id="address"
                  rows={2}
                  value={formData.address}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                />
              </div>
            </div>
          </div>

          {/* Social Media Links */}
          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <div className="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
              <h3 className="text-lg font-medium leading-6 text-gray-900">Social Media</h3>
              <p className="mt-1 text-sm text-gray-500">Your social media profiles.</p>
            </div>
            <div className="px-4 py-5 sm:p-6 space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <label htmlFor="facebookUrl" className="block text-sm font-medium text-gray-700">
                    Facebook
                  </label>
                  <input
                    type="url"
                    name="facebookUrl"
                    id="facebookUrl"
                    value={formData.facebookUrl}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label htmlFor="twitterUrl" className="block text-sm font-medium text-gray-700">
                    Twitter/X
                  </label>
                  <input
                    type="url"
                    name="twitterUrl"
                    id="twitterUrl"
                    value={formData.twitterUrl}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label htmlFor="instagramUrl" className="block text-sm font-medium text-gray-700">
                    Instagram
                  </label>
                  <input
                    type="url"
                    name="instagramUrl"
                    id="instagramUrl"
                    value={formData.instagramUrl}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label htmlFor="tiktokUrl" className="block text-sm font-medium text-gray-700">
                    TikTok
                  </label>
                  <input
                    type="url"
                    name="tiktokUrl"
                    id="tiktokUrl"
                    value={formData.tiktokUrl}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label htmlFor="linkedinUrl" className="block text-sm font-medium text-gray-700">
                    LinkedIn
                  </label>
                  <input
                    type="url"
                    name="linkedinUrl"
                    id="linkedinUrl"
                    value={formData.linkedinUrl}
                    onChange={handleChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* SEO Settings */}
          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <div className="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
              <h3 className="text-lg font-medium leading-6 text-gray-900">SEO Settings</h3>
              <p className="mt-1 text-sm text-gray-500">Search engine optimization settings.</p>
            </div>
            <div className="px-4 py-5 sm:p-6 space-y-4">
              <div>
                <label htmlFor="metaTitle" className="block text-sm font-medium text-gray-700">
                  Default Meta Title
                </label>
                <input
                  type="text"
                  name="metaTitle"
                  id="metaTitle"
                  value={formData.metaTitle}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                />
              </div>
              <div>
                <label htmlFor="metaDescription" className="block text-sm font-medium text-gray-700">
                  Default Meta Description
                </label>
                <textarea
                  name="metaDescription"
                  id="metaDescription"
                  rows={3}
                  value={formData.metaDescription}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                />
              </div>
              <div>
                <label htmlFor="googleAnalyticsId" className="block text-sm font-medium text-gray-700">
                  Google Analytics ID
                </label>
                <input
                  type="text"
                  name="googleAnalyticsId"
                  id="googleAnalyticsId"
                  value={formData.googleAnalyticsId}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 sm:text-sm"
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={saving}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-70 disabled:cursor-not-allowed"
            >
              {saving ? 'Saving...' : 'Save Settings'}
            </button>
          </div>
        </form>
      )}
    </div>
  );
}
