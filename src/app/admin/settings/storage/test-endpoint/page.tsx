'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function TestEndpointPage() {
  const [response, setResponse] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const testEndpoints = async () => {
    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      const urls = [
        '/api/admin/settings/storage?debug=true',
        '/api/admin/settings/storage/debug'
      ];

      const results = await Promise.all(
        urls.map(async (url) => {
          try {
            const res = await fetch(url);
            let data;

            try {
              data = await res.json();
            } catch (jsonError) {
              console.error(`Error parsing JSON from ${url}:`, jsonError);
              return {
                url,
                status: res.status,
                error: 'Failed to parse response as JSON',
                rawText: await res.text()
              };
            }

            // Ensure data.configs is always an array if it exists
            if (data && data.configs && !Array.isArray(data.configs)) {
              data.configs = [];
              data._fixed = true;
            }

            return { url, status: res.status, data };
          } catch (err) {
            console.error(`Error fetching ${url}:`, err);
            return { url, error: err instanceof Error ? err.message : String(err) };
          }
        })
      );

      setResponse(results);
    } catch (err) {
      console.error('Error in test endpoints function:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <div className="flex justify-between mb-8">
        <h1 className="text-2xl font-bold">Storage API Test</h1>
        <Link
          href="/admin/settings/storage"
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Back to Storage Settings
        </Link>
      </div>

      <div className="mb-8">
        <button
          onClick={testEndpoints}
          disabled={loading}
          className="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test API Endpoints'}
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <p className="text-red-700">Error: {error}</p>
        </div>
      )}

      {response && (
        <div className="space-y-8">
          {response.map((result: any, index: number) => (
            <div key={index} className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-semibold mb-2">{result.url}</h2>

              {result.error ? (
                <div className="bg-red-50 p-4 rounded">
                  <p className="text-red-700">Error: {result.error}</p>
                  {result.rawText && (
                    <div className="mt-4">
                      <p className="text-sm text-gray-500 mb-2">Raw response:</p>
                      <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-96 text-sm">
                        {result.rawText}
                      </pre>
                    </div>
                  )}
                </div>
              ) : (
                <div>
                  <p className="mb-2">
                    Status:
                    <span className={`ml-2 font-medium ${
                      result.status >= 200 && result.status < 300
                        ? 'text-green-600'
                        : 'text-red-600'
                    }`}>
                      {result.status}
                    </span>
                  </p>

                  {result.data && result.data._fixed && (
                    <div className="bg-yellow-50 p-3 rounded mb-4">
                      <p className="text-yellow-700 text-sm">
                        Note: Response data was fixed to prevent errors (configs was not an array)
                      </p>
                    </div>
                  )}

                  <div className="mt-4">
                    <p className="text-sm text-gray-500 mb-2">Response data:</p>
                    <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-96 text-sm">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}