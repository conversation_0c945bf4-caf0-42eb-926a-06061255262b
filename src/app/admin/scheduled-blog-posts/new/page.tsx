'use client';

import { useState, FormEvent } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import AdminLayout from '@/components/admin/AdminLayout';
import { ArrowLeftIcon, CalendarIcon } from '@heroicons/react/24/outline';

// Blog categories
const BLOG_CATEGORIES = [
  { value: 'web-design', label: 'Web Design' },
  { value: 'graphic-design', label: 'Graphic Design' },
  { value: 'branding', label: 'Branding' },
  { value: 'marketing', label: 'Marketing' },
  { value: 'ui-ux', label: 'UI/UX Design' },
  { value: 'design-trends', label: 'Design Trends' },
  { value: 'business', label: 'Business' },
  { value: 'technology', label: 'Technology' },
];

// Tone options
const TONE_OPTIONS = [
  { value: 'professional', label: 'Professional' },
  { value: 'casual', label: 'Casual' },
  { value: 'humorous', label: 'Humorous' },
  { value: 'technical', label: 'Technical' },
  { value: 'conversational', label: 'Conversational' },
];

// Length options
const LENGTH_OPTIONS = [
  { value: 'short', label: 'Short (600-800 words)' },
  { value: 'medium', label: 'Medium (1000-1200 words)' },
  { value: 'long', label: 'Long (1500-2000 words)' },
];

// Target audience options
const TARGET_AUDIENCE_OPTIONS = [
  { value: 'business owners', label: 'Business Owners' },
  { value: 'marketing professionals', label: 'Marketing Professionals' },
  { value: 'designers', label: 'Designers' },
  { value: 'entrepreneurs', label: 'Entrepreneurs' },
  { value: 'general readers', label: 'General Readers' },
];

export default function NewScheduledBlogPostPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    category: '',
    tone: 'professional',
    length: 'medium',
    targetAudience: 'business owners',
    scheduledDate: '',
    scheduledTime: '06:00',
  });
  const [submitting, setSubmitting] = useState(false);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    try {
      setSubmitting(true);
      
      // Combine date and time
      const scheduledDateTime = new Date(`${formData.scheduledDate}T${formData.scheduledTime}`);
      
      if (isNaN(scheduledDateTime.getTime())) {
        toast.error('Please select a valid date and time');
        return;
      }
      
      // Prepare data for API
      const apiData = {
        category: formData.category || undefined,
        tone: formData.tone,
        length: formData.length,
        targetAudience: formData.targetAudience,
        scheduledDate: scheduledDateTime.toISOString(),
      };
      
      // Submit to API
      const response = await fetch('/api/admin/scheduled-blog-posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to schedule blog post');
      }
      
      toast.success('Blog post scheduled successfully');
      router.push('/admin/scheduled-blog-posts');
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setSubmitting(false);
    }
  };

  // Get tomorrow's date in YYYY-MM-DD format for the default value
  const getTomorrowDate = () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split('T')[0];
  };

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <button
            type="button"
            onClick={() => router.push('/admin/scheduled-blog-posts')}
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back to Scheduled Posts
          </button>
        </div>
        
        <div className="md:flex md:items-center md:justify-between mb-6">
          <div className="min-w-0 flex-1">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl">
              Schedule New Blog Post
            </h2>
          </div>
        </div>
        
        <div className="bg-white shadow rounded-lg">
          <form onSubmit={handleSubmit} className="p-6">
            <div className="space-y-6">
              {/* Date and Time */}
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label htmlFor="scheduledDate" className="block text-sm font-medium text-gray-700">
                    Date
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <CalendarIcon className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="date"
                      name="scheduledDate"
                      id="scheduledDate"
                      className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md"
                      value={formData.scheduledDate || getTomorrowDate()}
                      onChange={handleChange}
                      min={new Date().toISOString().split('T')[0]}
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="scheduledTime" className="block text-sm font-medium text-gray-700">
                    Time
                  </label>
                  <div className="mt-1">
                    <input
                      type="time"
                      name="scheduledTime"
                      id="scheduledTime"
                      className="focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      value={formData.scheduledTime}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>
              </div>
              
              {/* Category */}
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                  Category (optional)
                </label>
                <div className="mt-1">
                  <select
                    id="category"
                    name="category"
                    className="focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    value={formData.category}
                    onChange={handleChange}
                  >
                    <option value="">Random (system will choose)</option>
                    {BLOG_CATEGORIES.map((category) => (
                      <option key={category.value} value={category.value}>
                        {category.label}
                      </option>
                    ))}
                  </select>
                </div>
                <p className="mt-1 text-sm text-gray-500">
                  If no category is selected, the system will randomly choose one
                </p>
              </div>
              
              {/* Tone */}
              <div>
                <label htmlFor="tone" className="block text-sm font-medium text-gray-700">
                  Tone
                </label>
                <div className="mt-1">
                  <select
                    id="tone"
                    name="tone"
                    className="focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    value={formData.tone}
                    onChange={handleChange}
                    required
                  >
                    {TONE_OPTIONS.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              {/* Length */}
              <div>
                <label htmlFor="length" className="block text-sm font-medium text-gray-700">
                  Length
                </label>
                <div className="mt-1">
                  <select
                    id="length"
                    name="length"
                    className="focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    value={formData.length}
                    onChange={handleChange}
                    required
                  >
                    {LENGTH_OPTIONS.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              {/* Target Audience */}
              <div>
                <label htmlFor="targetAudience" className="block text-sm font-medium text-gray-700">
                  Target Audience
                </label>
                <div className="mt-1">
                  <select
                    id="targetAudience"
                    name="targetAudience"
                    className="focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    value={formData.targetAudience}
                    onChange={handleChange}
                    required
                  >
                    {TARGET_AUDIENCE_OPTIONS.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
            
            <div className="mt-6 flex justify-end">
              <button
                type="button"
                onClick={() => router.push('/admin/scheduled-blog-posts')}
                className="mr-3 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={submitting}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-indigo-400"
              >
                {submitting ? 'Scheduling...' : 'Schedule Blog Post'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}
