@layer components {
  .shape {
    @apply absolute rounded-full mix-blend-multiply filter blur-xl opacity-70;
  }

  .shape-1 {
    @apply bg-accent/60 w-72 h-72 -top-10 -right-10;
    animation: float 8s infinite;
  }

  .shape-2 {
    @apply bg-secondary/60 w-96 h-96 top-1/2 right-1/4;
    animation: float 12s infinite;
  }

  .shape-3 {
    @apply bg-primary/60 w-80 h-80 bottom-1/4 right-1/3;
    animation: float 10s infinite;
  }

  .service-card {
    @apply relative bg-white rounded-2xl p-6 sm:p-8 shadow-lg transition-all duration-500 hover:-translate-y-2 hover:shadow-xl overflow-hidden;
  }

  .service-card::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 opacity-0 transition-all duration-500;
  }

  .service-card:hover::before {
    @apply opacity-100;
  }

  .service-card .icon-wrapper {
    @apply relative w-12 h-12 sm:w-16 sm:h-16 rounded-2xl flex items-center justify-center mb-6 bg-primary/10 transition-transform duration-500 z-10;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-20px);
    }
  }
} 