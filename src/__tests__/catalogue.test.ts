import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import {
  getAllCatalogue,
  getCatalogueById,
  createCatalogue,
  updateCatalogue,
  deleteCatalogue,
  bulkDeleteCatalogue,
  getCatalogueStats,
  searchCatalogue,
  CatalogueNotFoundError,
  CatalogueDuplicateError
} from '../services/catalogueServiceImproved';
import {
  validateCatalogueCreate,
  validateCatalogueUpdate,
  CatalogueValidationError
} from '../utils/catalogueValidation';

// Mock Prisma
jest.mock('../lib/prisma', () => ({
  catalogue: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    findFirst: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn(),
    count: jest.fn(),
    groupBy: jest.fn(),
    aggregate: jest.fn(),
  },
  $transaction: jest.fn(),
}));

// Mock ErrorTracker
jest.mock('../services/errorTracking', () => ({
  ErrorTracker: {
    trackError: jest.fn(),
  },
}));

describe('Catalogue Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Validation', () => {
    it('should validate correct catalogue data', () => {
      const validData = {
        service: 'Web Development',
        price: 5000,
        description: 'Professional web development service',
        features: ['Responsive Design', 'SEO Optimized'],
        category: 'Web Services'
      };

      expect(() => validateCatalogueCreate(validData)).not.toThrow();
    });

    it('should reject invalid service names', () => {
      const invalidData = {
        service: '', // Empty service name
        price: 5000
      };

      expect(() => validateCatalogueCreate(invalidData)).toThrow(CatalogueValidationError);
    });

    it('should reject negative prices', () => {
      const invalidData = {
        service: 'Web Development',
        price: -100
      };

      expect(() => validateCatalogueCreate(invalidData)).toThrow(CatalogueValidationError);
    });

    it('should reject too many features', () => {
      const invalidData = {
        service: 'Web Development',
        price: 5000,
        features: new Array(15).fill('Feature') // Too many features
      };

      expect(() => validateCatalogueCreate(invalidData)).toThrow(CatalogueValidationError);
    });

    it('should reject invalid URLs', () => {
      const invalidData = {
        service: 'Web Development',
        price: 5000,
        imageUrl: 'not-a-url'
      };

      expect(() => validateCatalogueCreate(invalidData)).toThrow(CatalogueValidationError);
    });
  });

  describe('getAllCatalogue', () => {
    it('should return paginated catalogue items', async () => {
      const mockItems = [
        {
          id: 1,
          service: 'Web Development',
          price: 5000,
          description: 'Professional web development',
          features: ['Responsive', 'SEO'],
          category: 'Web Services',
          popular: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      const mockPrisma = require('../lib/prisma');
      mockPrisma.$transaction.mockResolvedValue([1, mockItems]);

      const result = await getAllCatalogue({}, { page: 1, limit: 10 });

      expect(result).toEqual({
        items: expect.arrayContaining([
          expect.objectContaining({
            id: '1',
            service: 'Web Development',
            price: 5000
          })
        ]),
        total: 1,
        hasMore: false,
        page: 1,
        limit: 10
      });
    });

    it('should apply filters correctly', async () => {
      const mockPrisma = require('../lib/prisma');
      mockPrisma.$transaction.mockResolvedValue([0, []]);

      await getAllCatalogue({
        category: 'Web Services',
        minPrice: 1000,
        maxPrice: 10000,
        popular: true,
        search: 'web'
      });

      expect(mockPrisma.$transaction).toHaveBeenCalledWith([
        expect.any(Object), // count query
        expect.objectContaining({
          where: expect.objectContaining({
            category: 'Web Services',
            price: { gte: 1000, lte: 10000 },
            popular: true,
            OR: expect.arrayContaining([
              { service: { contains: 'web', mode: 'insensitive' } }
            ])
          })
        })
      ]);
    });
  });

  describe('createCatalogue', () => {
    it('should create a new catalogue item', async () => {
      const newItem = {
        service: 'Logo Design',
        price: 2000,
        description: 'Professional logo design',
        category: 'Design Services'
      };

      const mockCreatedItem = {
        id: 1,
        ...newItem,
        features: [],
        popular: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const mockPrisma = require('../lib/prisma');
      mockPrisma.catalogue.findFirst.mockResolvedValue(null); // No duplicate
      mockPrisma.catalogue.create.mockResolvedValue(mockCreatedItem);

      const result = await createCatalogue(newItem);

      expect(result).toEqual(expect.objectContaining({
        id: '1',
        service: 'Logo Design',
        price: 2000
      }));
    });

    it('should reject duplicate service names', async () => {
      const newItem = {
        service: 'Existing Service',
        price: 2000
      };

      const mockPrisma = require('../lib/prisma');
      mockPrisma.catalogue.findFirst.mockResolvedValue({ id: 1 }); // Duplicate found

      await expect(createCatalogue(newItem)).rejects.toThrow(CatalogueDuplicateError);
    });
  });

  describe('updateCatalogue', () => {
    it('should update an existing catalogue item', async () => {
      const updateData = {
        service: 'Updated Service',
        price: 3000
      };

      const existingItem = {
        id: 1,
        service: 'Original Service',
        price: 2000,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const updatedItem = {
        ...existingItem,
        ...updateData,
        updatedAt: new Date()
      };

      const mockPrisma = require('../lib/prisma');
      mockPrisma.catalogue.findUnique.mockResolvedValue(existingItem);
      mockPrisma.catalogue.findFirst.mockResolvedValue(null); // No duplicate
      mockPrisma.catalogue.update.mockResolvedValue(updatedItem);

      const result = await updateCatalogue('1', updateData);

      expect(result).toEqual(expect.objectContaining({
        id: '1',
        service: 'Updated Service',
        price: 3000
      }));
    });

    it('should throw error for non-existent item', async () => {
      const mockPrisma = require('../lib/prisma');
      mockPrisma.catalogue.findUnique.mockResolvedValue(null);

      await expect(updateCatalogue('999', { price: 3000 })).rejects.toThrow(CatalogueNotFoundError);
    });
  });

  describe('deleteCatalogue', () => {
    it('should delete an existing catalogue item', async () => {
      const existingItem = {
        id: 1,
        service: 'Service to Delete',
        price: 2000
      };

      const mockPrisma = require('../lib/prisma');
      mockPrisma.catalogue.findUnique.mockResolvedValue(existingItem);
      mockPrisma.catalogue.delete.mockResolvedValue(existingItem);

      const result = await deleteCatalogue('1');

      expect(result).toBe(true);
      expect(mockPrisma.catalogue.delete).toHaveBeenCalledWith({
        where: { id: 1 }
      });
    });

    it('should throw error for non-existent item', async () => {
      const mockPrisma = require('../lib/prisma');
      mockPrisma.catalogue.findUnique.mockResolvedValue(null);

      await expect(deleteCatalogue('999')).rejects.toThrow(CatalogueNotFoundError);
    });
  });

  describe('bulkDeleteCatalogue', () => {
    it('should delete multiple catalogue items', async () => {
      const existingItems = [
        { id: 1, service: 'Service 1' },
        { id: 2, service: 'Service 2' }
      ];

      const mockPrisma = require('../lib/prisma');
      mockPrisma.catalogue.findMany.mockResolvedValue(existingItems);
      mockPrisma.catalogue.deleteMany.mockResolvedValue({ count: 2 });

      const result = await bulkDeleteCatalogue({ ids: ['1', '2'] });

      expect(result).toEqual({
        success: true,
        count: 2,
        errors: []
      });
    });

    it('should handle partial failures', async () => {
      const existingItems = [
        { id: 1, service: 'Service 1' }
      ];

      const mockPrisma = require('../lib/prisma');
      mockPrisma.catalogue.findMany.mockResolvedValue(existingItems);
      mockPrisma.catalogue.deleteMany.mockResolvedValue({ count: 1 });

      const result = await bulkDeleteCatalogue({ ids: ['1', '999'] });

      expect(result).toEqual({
        success: true,
        count: 1,
        errors: ['Items not found: 999']
      });
    });
  });

  describe('getCatalogueStats', () => {
    it('should return catalogue statistics', async () => {
      const mockStats = [
        5, // total count
        [{ category: 'Web Services', _count: { category: 3 } }], // categories
        { _min: { price: 1000 }, _max: { price: 10000 }, _avg: { price: 5000 } }, // price stats
        2 // popular count
      ];

      const mockPrisma = require('../lib/prisma');
      mockPrisma.$transaction.mockResolvedValue(mockStats);

      const result = await getCatalogueStats();

      expect(result).toEqual({
        total: 5,
        categories: [{ name: 'Web Services', count: 3 }],
        priceRange: { min: 1000, max: 10000, avg: 5000 },
        popularCount: 2
      });
    });
  });

  describe('searchCatalogue', () => {
    it('should return search results', async () => {
      const mockResults = [
        {
          id: 1,
          service: 'Web Development',
          price: 5000,
          description: 'Professional web development',
          popular: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      const mockPrisma = require('../lib/prisma');
      mockPrisma.catalogue.findMany.mockResolvedValue(mockResults);

      const result = await searchCatalogue('web', 10);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(expect.objectContaining({
        id: '1',
        service: 'Web Development'
      }));
    });

    it('should return empty array for short queries', async () => {
      const result = await searchCatalogue('w', 10);
      expect(result).toEqual([]);
    });
  });
});
