import { PrismaClient } from '@prisma/client';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

const execPromise = promisify(exec);
const prisma = new PrismaClient();

// Base directory for backups
const BACKUP_DIR = path.join(process.cwd(), 'backups');

// Ensure backup directory exists
const ensureBackupDir = async () => {
  if (!fs.existsSync(BACKUP_DIR)) {
    await fs.promises.mkdir(BACKUP_DIR, { recursive: true });
  }
  return BACKUP_DIR;
};

// Parse database connection string
const parseDatabaseUrl = (databaseUrl: string) => {
  const dbRegex = /postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/([^?]+)/;
  const dbMatch = databaseUrl.match(dbRegex);

  if (!dbMatch) {
    throw new Error('Invalid database connection string format');
  }

  const [, dbUser, dbPassword, dbHost, dbPort, dbName] = dbMatch;
  return { dbUser, dbPassword, dbHost, dbPort, dbName };
};

/**
 * Create a database backup
 * @param description Optional description for the backup
 * @param type Type of backup ('automatic', 'manual', 'pre-restore')
 * @param username Username of the user creating the backup
 * @returns Information about the created backup
 */
export async function createDatabaseBackup(
  description?: string,
  type: 'automatic' | 'manual' | 'pre-restore' = 'manual',
  username?: string
) {
  // Ensure backup directory exists
  const backupDir = await ensureBackupDir();

  // Get database connection details
  const databaseUrl = process.env.DATABASE_URL;
  if (!databaseUrl) {
    throw new Error('Database connection string not found');
  }

  const { dbUser, dbPassword, dbHost, dbPort, dbName } = parseDatabaseUrl(databaseUrl);

  // Create a unique filename for the backup
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFilename = `backup-${dbName}-${timestamp}-${uuidv4().substring(0, 8)}.dump`;
  const backupPath = path.join(backupDir, backupFilename);

  try {
    // Set environment variable for pg_dump password
    process.env.PGPASSWORD = dbPassword;

    // Execute pg_dump command
    const pgDumpCommand = `pg_dump -h ${dbHost} -p ${dbPort} -U ${dbUser} -d ${dbName} -F c --no-owner --verbose -f "${backupPath}"`;
    
    console.log(`Creating database backup: ${backupFilename}`);
    const { stdout, stderr } = await execPromise(pgDumpCommand);

    if (stderr) {
      console.log('pg_dump stderr (may include warnings):', stderr);
    }

    // Get file size
    const stats = await fs.promises.stat(backupPath);
    if (stats.size === 0) {
      throw new Error('Backup file was created but is empty');
    }

    // Record the backup in the database
    const backup = await prisma.databaseBackup.create({
      data: {
        filename: backupFilename,
        description: description || `${type} backup created on ${new Date().toLocaleString()}`,
        size: stats.size,
        path: backupPath,
        type,
        createdBy: username || 'system',
        status: 'completed',
      },
    });

    console.log(`Backup created successfully: ${backupFilename} (${stats.size} bytes)`);
    return { backup, backupPath };
  } catch (error) {
    console.error('Database backup error:', error);

    // Record the failed backup attempt
    await prisma.databaseBackup.create({
      data: {
        filename: backupFilename,
        description: `Failed backup: ${error instanceof Error ? error.message : String(error)}`,
        size: 0,
        path: backupPath,
        type,
        createdBy: username || 'system',
        status: 'failed',
      },
    });

    // Check if the backup file exists and delete it if it does
    try {
      if (fs.existsSync(backupPath)) {
        await fs.promises.unlink(backupPath);
        console.log(`Deleted failed backup file: ${backupPath}`);
      }
    } catch (unlinkError) {
      console.error('Error deleting failed backup file:', unlinkError);
    }

    throw error;
  } finally {
    // Clear the password environment variable
    process.env.PGPASSWORD = '';
  }
}

/**
 * Get all database backups
 * @param limit Maximum number of backups to return
 * @param type Optional filter by backup type
 * @returns List of database backups
 */
export async function getDatabaseBackups(limit = 50, type?: string) {
  const where = type ? { type } : {};
  
  return prisma.databaseBackup.findMany({
    where,
    orderBy: {
      createdAt: 'desc',
    },
    take: limit,
  });
}

/**
 * Get a specific database backup by ID
 * @param id Backup ID
 * @returns Database backup or null if not found
 */
export async function getDatabaseBackupById(id: string) {
  return prisma.databaseBackup.findUnique({
    where: { id },
  });
}

/**
 * Delete a database backup
 * @param id Backup ID
 * @returns True if deleted successfully, false otherwise
 */
export async function deleteDatabaseBackup(id: string) {
  try {
    // Get the backup
    const backup = await prisma.databaseBackup.findUnique({
      where: { id },
    });

    if (!backup) {
      return false;
    }

    // Delete the backup file if it exists
    if (fs.existsSync(backup.path)) {
      await fs.promises.unlink(backup.path);
    }

    // Delete the database record
    await prisma.databaseBackup.delete({
      where: { id },
    });

    return true;
  } catch (error) {
    console.error('Error deleting database backup:', error);
    return false;
  }
}

/**
 * Restore database from a backup file
 * @param filePath Path to the backup file
 * @param username Username of the user performing the restore
 * @returns Information about the restore operation
 */
export async function restoreDatabaseFromBackup(filePath: string, username?: string) {
  // Create a backup before restoring
  const { backup: preRestoreBackup } = await createDatabaseBackup(
    'Automatic backup before restore',
    'pre-restore',
    username
  );

  // Get database connection details
  const databaseUrl = process.env.DATABASE_URL;
  if (!databaseUrl) {
    throw new Error('Database connection string not found');
  }

  const { dbUser, dbPassword, dbHost, dbPort, dbName } = parseDatabaseUrl(databaseUrl);

  try {
    // Set environment variable for pg_restore password
    process.env.PGPASSWORD = dbPassword;

    // Execute pg_restore command
    const pgRestoreCommand = `pg_restore -h ${dbHost} -p ${dbPort} -U ${dbUser} -d ${dbName} --clean --if-exists --no-owner --verbose "${filePath}"`;
    
    console.log(`Restoring database from: ${filePath}`);
    const { stdout, stderr } = await execPromise(pgRestoreCommand);

    if (stderr) {
      console.log('pg_restore stderr (may include warnings):', stderr);
    }

    // Check for critical errors in stderr
    if (stderr && stderr.includes('ERROR:') && 
        !stderr.includes('ERROR: schema') && 
        !stderr.includes('ERROR: role') &&
        !stderr.includes('ERROR: permission denied')) {
      throw new Error(`Database restore encountered errors: ${stderr}`);
    }

    return { 
      success: true, 
      message: 'Database restored successfully',
      preRestoreBackupId: preRestoreBackup.id
    };
  } catch (error) {
    console.error('Database restore error:', error);
    throw error;
  } finally {
    // Clear the password environment variable
    process.env.PGPASSWORD = '';
  }
}
