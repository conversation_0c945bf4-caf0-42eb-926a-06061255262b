/**
 * Event Tracking Service
 * 
 * This service provides functions to track user events and interactions:
 * - Page views
 * - <PERSON><PERSON> clicks
 * - Form submissions and abandonments
 * - Custom events
 */

import { prisma } from '@/lib/prisma';
import { ErrorTracker } from './errorTracking';

// Define types for event tracking
export interface EventData {
  eventName: string;
  eventType: string;
  url?: string;
  userAgent?: string;
  ipAddress?: string;
  sessionId?: string;
  userId?: string;
  leadId?: string;
  metadata?: Record<string, any>;
}

/**
 * Track a user event
 * @param eventData The event data to track
 * @returns The created event
 */
export async function trackEvent(eventData: EventData) {
  try {
    // Create the event
    const event = await prisma.eventTracking.create({
      data: {
        eventName: eventData.eventName,
        eventType: eventData.eventType,
        url: eventData.url,
        userAgent: eventData.userAgent,
        ipAddress: eventData.ipAddress,
        sessionId: eventData.sessionId,
        userId: eventData.userId,
        leadId: eventData.leadId,
        metadata: eventData.metadata || {},
      },
    });

    return event;
  } catch (error) {
    console.error('Error tracking event:', error);
    ErrorTracker.trackError(error as Error, 'trackEvent');
    throw error;
  }
}

/**
 * Track a page view event
 * @param url The URL of the page
 * @param sessionId The session ID
 * @param leadId Optional lead ID
 * @param userId Optional user ID
 * @param metadata Optional additional metadata
 * @returns The created event
 */
export async function trackPageView(
  url: string,
  sessionId: string,
  leadId?: string,
  userId?: string,
  metadata?: Record<string, any>
) {
  return trackEvent({
    eventName: 'pageView',
    eventType: 'pageView',
    url,
    sessionId,
    leadId,
    userId,
    metadata,
  });
}

/**
 * Track a button click event
 * @param buttonName The name of the button
 * @param url The URL where the button was clicked
 * @param sessionId The session ID
 * @param leadId Optional lead ID
 * @param userId Optional user ID
 * @param metadata Optional additional metadata
 * @returns The created event
 */
export async function trackButtonClick(
  buttonName: string,
  url: string,
  sessionId: string,
  leadId?: string,
  userId?: string,
  metadata?: Record<string, any>
) {
  return trackEvent({
    eventName: buttonName,
    eventType: 'buttonClick',
    url,
    sessionId,
    leadId,
    userId,
    metadata,
  });
}

/**
 * Track a form submission event
 * @param formName The name of the form
 * @param url The URL where the form was submitted
 * @param sessionId The session ID
 * @param leadId Optional lead ID
 * @param userId Optional user ID
 * @param metadata Optional additional metadata
 * @returns The created event
 */
export async function trackFormSubmission(
  formName: string,
  url: string,
  sessionId: string,
  leadId?: string,
  userId?: string,
  metadata?: Record<string, any>
) {
  return trackEvent({
    eventName: formName,
    eventType: 'formSubmission',
    url,
    sessionId,
    leadId,
    userId,
    metadata,
  });
}

/**
 * Track a form abandonment event
 * @param formName The name of the form
 * @param url The URL where the form was abandoned
 * @param sessionId The session ID
 * @param completionPercentage The percentage of the form that was completed
 * @param leadId Optional lead ID
 * @param userId Optional user ID
 * @param metadata Optional additional metadata
 * @returns The created event
 */
export async function trackFormAbandonment(
  formName: string,
  url: string,
  sessionId: string,
  completionPercentage: number,
  leadId?: string,
  userId?: string,
  metadata?: Record<string, any>
) {
  return trackEvent({
    eventName: formName,
    eventType: 'formAbandonment',
    url,
    sessionId,
    leadId,
    userId,
    metadata: {
      ...metadata,
      completionPercentage,
    },
  });
}

/**
 * Get events for a specific session
 * @param sessionId The session ID
 * @returns The events for the session
 */
export async function getEventsBySession(sessionId: string) {
  try {
    const events = await prisma.eventTracking.findMany({
      where: {
        sessionId,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    return events;
  } catch (error) {
    console.error(`Error getting events for session ${sessionId}:`, error);
    ErrorTracker.trackError(error as Error, 'getEventsBySession');
    throw error;
  }
}

/**
 * Get events for a specific lead
 * @param leadId The lead ID
 * @returns The events for the lead
 */
export async function getEventsByLead(leadId: string) {
  try {
    const events = await prisma.eventTracking.findMany({
      where: {
        leadId,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return events;
  } catch (error) {
    console.error(`Error getting events for lead ${leadId}:`, error);
    ErrorTracker.trackError(error as Error, 'getEventsByLead');
    throw error;
  }
}

/**
 * Get events by type
 * @param eventType The event type
 * @param limit Optional limit on the number of events to return
 * @returns The events of the specified type
 */
export async function getEventsByType(eventType: string, limit?: number) {
  try {
    const events = await prisma.eventTracking.findMany({
      where: {
        eventType,
      },
      orderBy: {
        createdAt: 'desc',
      },
      ...(limit && { take: limit }),
    });

    return events;
  } catch (error) {
    console.error(`Error getting events of type ${eventType}:`, error);
    ErrorTracker.trackError(error as Error, 'getEventsByType');
    throw error;
  }
}

/**
 * Get form abandonment statistics
 * @param formName Optional form name to filter by
 * @param startDate Optional start date for the statistics
 * @param endDate Optional end date for the statistics
 * @returns The form abandonment statistics
 */
export async function getFormAbandonmentStats(
  formName?: string,
  startDate?: Date,
  endDate?: Date
) {
  try {
    // Get form submission events
    const submissions = await prisma.eventTracking.findMany({
      where: {
        eventType: 'formSubmission',
        ...(formName && { eventName: formName }),
        ...(startDate && endDate && {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        }),
      },
    });

    // Get form abandonment events
    const abandonments = await prisma.eventTracking.findMany({
      where: {
        eventType: 'formAbandonment',
        ...(formName && { eventName: formName }),
        ...(startDate && endDate && {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        }),
      },
    });

    // Calculate statistics
    const totalForms = submissions.length + abandonments.length;
    const abandonmentRate = totalForms > 0 ? (abandonments.length / totalForms) * 100 : 0;

    // Calculate average completion percentage for abandoned forms
    const avgCompletionPercentage = abandonments.length > 0
      ? abandonments.reduce((sum, event) => {
          const completionPercentage = event.metadata?.completionPercentage || 0;
          return sum + completionPercentage;
        }, 0) / abandonments.length
      : 0;

    return {
      totalForms,
      submissions: submissions.length,
      abandonments: abandonments.length,
      abandonmentRate,
      avgCompletionPercentage,
    };
  } catch (error) {
    console.error('Error getting form abandonment statistics:', error);
    ErrorTracker.trackError(error as Error, 'getFormAbandonmentStats');
    throw error;
  }
}
