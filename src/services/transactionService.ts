/**
 * Service for managing transactions
 */
import prisma from '@/lib/prisma';
import { parseTransactionMessage, parseTransactionMessages } from '@/utils/transactionParser';

export interface Transaction {
  id: string;
  transactionId: string;
  amount: number;
  customerName: string;
  phoneNumber: string;
  transactionDate: Date;
  rawMessage: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  processedAt: Date | null;
}

/**
 * Create a new transaction from a raw M-PESA message
 * @param message The raw M-PESA message
 * @returns The created transaction or null if parsing fails
 */
export async function createTransactionFromMessage(message: string): Promise<Transaction | null> {
  try {
    // Parse the message
    const parsedData = parseTransactionMessage(message);
    
    if (!parsedData) {
      return null;
    }
    
    // Check if transaction already exists
    const existingTransaction = await prisma.transaction.findUnique({
      where: {
        transactionId: parsedData.transactionId
      }
    });
    
    if (existingTransaction) {
      console.log(`Transaction ${parsedData.transactionId} already exists`);
      return formatTransaction(existingTransaction);
    }
    
    // Create the transaction
    const transaction = await prisma.transaction.create({
      data: {
        transactionId: parsedData.transactionId,
        amount: parsedData.amount,
        customerName: parsedData.customerName,
        phoneNumber: parsedData.phoneNumber,
        transactionDate: parsedData.transactionDate,
        rawMessage: parsedData.rawMessage,
        status: 'pending'
      }
    });
    
    return formatTransaction(transaction);
  } catch (error) {
    console.error('Error creating transaction from message:', error);
    return null;
  }
}

/**
 * Create multiple transactions from raw M-PESA messages
 * @param messages Array of raw M-PESA messages
 * @returns Array of created transactions
 */
export async function createTransactionsFromMessages(messages: string[]): Promise<Transaction[]> {
  const results: Transaction[] = [];
  
  // Parse all messages
  const parsedData = parseTransactionMessages(messages);
  
  // Process each parsed transaction
  for (const data of parsedData) {
    try {
      // Check if transaction already exists
      const existingTransaction = await prisma.transaction.findUnique({
        where: {
          transactionId: data.transactionId
        }
      });
      
      if (existingTransaction) {
        console.log(`Transaction ${data.transactionId} already exists`);
        results.push(formatTransaction(existingTransaction));
        continue;
      }
      
      // Create the transaction
      const transaction = await prisma.transaction.create({
        data: {
          transactionId: data.transactionId,
          amount: data.amount,
          customerName: data.customerName,
          phoneNumber: data.phoneNumber,
          transactionDate: data.transactionDate,
          rawMessage: data.rawMessage,
          status: 'pending'
        }
      });
      
      results.push(formatTransaction(transaction));
    } catch (error) {
      console.error(`Error creating transaction ${data.transactionId}:`, error);
    }
  }
  
  return results;
}

/**
 * Get all transactions
 * @returns Array of all transactions
 */
export async function getAllTransactions(): Promise<Transaction[]> {
  try {
    const transactions = await prisma.transaction.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    return transactions.map(formatTransaction);
  } catch (error) {
    console.error('Error getting all transactions:', error);
    throw error;
  }
}

/**
 * Get a transaction by ID
 * @param id The transaction ID
 * @returns The transaction or null if not found
 */
export async function getTransactionById(id: string): Promise<Transaction | null> {
  try {
    const transaction = await prisma.transaction.findUnique({
      where: {
        id
      }
    });
    
    if (!transaction) {
      return null;
    }
    
    return formatTransaction(transaction);
  } catch (error) {
    console.error(`Error getting transaction ${id}:`, error);
    throw error;
  }
}

/**
 * Get a transaction by M-PESA transaction ID
 * @param transactionId The M-PESA transaction ID
 * @returns The transaction or null if not found
 */
export async function getTransactionByTransactionId(transactionId: string): Promise<Transaction | null> {
  try {
    const transaction = await prisma.transaction.findUnique({
      where: {
        transactionId
      }
    });
    
    if (!transaction) {
      return null;
    }
    
    return formatTransaction(transaction);
  } catch (error) {
    console.error(`Error getting transaction ${transactionId}:`, error);
    throw error;
  }
}

/**
 * Update a transaction's status
 * @param id The transaction ID
 * @param status The new status
 * @returns The updated transaction
 */
export async function updateTransactionStatus(id: string, status: string): Promise<Transaction> {
  try {
    const updateData: any = {
      status
    };
    
    // If status is 'processed', set processedAt
    if (status === 'processed') {
      updateData.processedAt = new Date();
    }
    
    const transaction = await prisma.transaction.update({
      where: {
        id
      },
      data: updateData
    });
    
    return formatTransaction(transaction);
  } catch (error) {
    console.error(`Error updating transaction ${id}:`, error);
    throw error;
  }
}

/**
 * Format a transaction from the database
 * @param transaction The transaction from the database
 * @returns The formatted transaction
 */
function formatTransaction(transaction: any): Transaction {
  return {
    id: transaction.id,
    transactionId: transaction.transactionId,
    amount: Number(transaction.amount),
    customerName: transaction.customerName,
    phoneNumber: transaction.phoneNumber,
    transactionDate: new Date(transaction.transactionDate),
    rawMessage: transaction.rawMessage,
    status: transaction.status,
    createdAt: new Date(transaction.createdAt),
    updatedAt: new Date(transaction.updatedAt),
    processedAt: transaction.processedAt ? new Date(transaction.processedAt) : null
  };
}
