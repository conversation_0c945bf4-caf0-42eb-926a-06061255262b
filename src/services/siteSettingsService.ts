import { SiteSettings } from '@prisma/client';
import prisma from '@/lib/prisma';

// Format site settings for API response
function formatSiteSettings(settings: SiteSettings): SiteSettings {
  return {
    ...settings
  };
}

// Get site settings
export async function getSiteSettings(): Promise<SiteSettings | null> {
  try {
    // Get the first site settings record
    const settings = await prisma.siteSettings.findFirst();

    if (!settings) {
      // If no settings exist, try to initialize with defaults
      try {
        return await initializeSiteSettings();
      } catch (initError) {
        console.error('Error initializing site settings:', initError);
        return null;
      }
    }

    return formatSiteSettings(settings);
  } catch (error) {
    console.error('Error getting site settings:', error);
    // Return null instead of throwing to prevent build failures
    return null;
  }
}

// Create or update site settings
export async function upsertSiteSettings(data: Partial<SiteSettings>): Promise<SiteSettings> {
  try {
    // Get the first site settings record
    const existingSettings = await prisma.siteSettings.findFirst();

    if (existingSettings) {
      // Update existing settings
      const updatedSettings = await prisma.siteSettings.update({
        where: {
          id: existingSettings.id
        },
        data
      });

      return formatSiteSettings(updatedSettings);
    } else {
      // Create new settings
      // Ensure required fields are provided
      if (!data.siteName) {
        data.siteName = 'Mocky Digital';
      }

      const newSettings = await prisma.siteSettings.create({
        data: data as any // Cast to any to bypass TypeScript's strict checking
      });

      return formatSiteSettings(newSettings);
    }
  } catch (error) {
    console.error('Error upserting site settings:', error);
    throw error;
  }
}

// Initialize site settings with default values if none exist
export async function initializeSiteSettings(): Promise<SiteSettings> {
  try {
    // Check if settings already exist
    const existingSettings = await prisma.siteSettings.findFirst();

    if (existingSettings) {
      return existingSettings;
    }

    // Create default settings
    const defaultSettings = await prisma.siteSettings.create({
      data: {
        siteName: 'Mocky Digital',
        siteDescription: 'Professional web design and digital marketing services',
        contactEmail: '<EMAIL>',
        phoneNumber: '+*********** 670',
        address: 'Nairobi, Kenya',
        facebookUrl: 'https://facebook.com/mockydigital',
        twitterUrl: 'https://x.com/mockydigital',
        instagramUrl: 'https://instagram.com/mockydigital',
        tiktokUrl: 'https://tiktok.com/@mocky_digital',
        linkedinUrl: 'https://linkedin.com/company/mockydigital',
        metaTitle: 'Mocky Digital - Web Design & Digital Marketing',
        metaDescription: 'Professional web design, development, and digital marketing services in Nairobi, Kenya.',
        googleAnalyticsId: ''
      }
    });

    return defaultSettings;
  } catch (error) {
    console.error('Error initializing site settings:', error);
    throw error;
  }
}
