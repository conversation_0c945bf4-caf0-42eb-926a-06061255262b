import { <PERSON><PERSON><PERSON> } from 'jsdom';
import DOMPurify from 'dompurify';
import fetch from 'node-fetch';
import { SeoScanResult, SeoPageData, SeoIssueData, SeoMetaTagData, SeoStructuredDataItem, BrokenLinkData } from './seoService';

/**
 * Analyze a webpage for SEO issues
 */
export async function analyzePage(url: string): Promise<SeoScanResult> {
  try {
    // Fetch the page content
    const response = await fetch(url);
    const html = await response.text();
    
    // Parse the HTML
    const dom = new JSDOM(html);
    const document = dom.window.document;
    
    // Extract basic page data
    const pageData: SeoPageData = {
      url,
      title: document.title,
      description: getMetaContent(document, 'description'),
      keywords: getMetaContent(document, 'keywords')?.split(',').map(k => k.trim()) || [],
    };
    
    // Analyze the page
    const issues = analyzeIssues(document, url);
    const metaTags = extractMetaTags(document);
    const structuredData = extractStructuredData(document);
    const brokenLinks = await checkBrokenLinks(document, url);
    
    // Extract keywords from content
    const keywords = extractKeywords(document);
    
    return {
      pageData,
      issues,
      keywords,
      metaTags,
      structuredData,
      brokenLinks,
    };
  } catch (error) {
    console.error('Error analyzing page:', error);
    throw new Error(`Failed to analyze page: ${error.message}`);
  }
}

/**
 * Get meta tag content
 */
function getMetaContent(document: Document, name: string): string | undefined {
  const meta = document.querySelector(`meta[name="${name}"]`) || document.querySelector(`meta[property="og:${name}"]`);
  return meta ? meta.getAttribute('content') : undefined;
}

/**
 * Analyze page for SEO issues
 */
function analyzeIssues(document: Document, url: string): SeoIssueData[] {
  const issues: SeoIssueData[] = [];
  
  // Check title
  const title = document.title;
  if (!title) {
    issues.push({
      type: 'missing_title',
      severity: 'critical',
      description: 'Page is missing a title tag',
    });
  } else if (title.length < 10) {
    issues.push({
      type: 'short_title',
      severity: 'high',
      description: 'Page title is too short (less than 10 characters)',
    });
  } else if (title.length > 60) {
    issues.push({
      type: 'long_title',
      severity: 'medium',
      description: 'Page title is too long (more than 60 characters)',
    });
  }
  
  // Check meta description
  const description = getMetaContent(document, 'description');
  if (!description) {
    issues.push({
      type: 'missing_description',
      severity: 'high',
      description: 'Page is missing a meta description',
    });
  } else if (description.length < 50) {
    issues.push({
      type: 'short_description',
      severity: 'medium',
      description: 'Meta description is too short (less than 50 characters)',
    });
  } else if (description.length > 160) {
    issues.push({
      type: 'long_description',
      severity: 'low',
      description: 'Meta description is too long (more than 160 characters)',
    });
  }
  
  // Check headings
  const h1Elements = document.querySelectorAll('h1');
  if (h1Elements.length === 0) {
    issues.push({
      type: 'missing_h1',
      severity: 'high',
      description: 'Page is missing an H1 heading',
    });
  } else if (h1Elements.length > 1) {
    issues.push({
      type: 'multiple_h1',
      severity: 'medium',
      description: `Page has multiple H1 headings (${h1Elements.length})`,
    });
  }
  
  // Check images
  const images = document.querySelectorAll('img');
  let missingAltCount = 0;
  
  images.forEach(img => {
    if (!img.hasAttribute('alt')) {
      missingAltCount++;
    }
  });
  
  if (missingAltCount > 0) {
    issues.push({
      type: 'missing_alt',
      severity: 'medium',
      description: `${missingAltCount} images are missing alt attributes`,
    });
  }
  
  // Check canonical URL
  const canonical = document.querySelector('link[rel="canonical"]');
  if (!canonical) {
    issues.push({
      type: 'missing_canonical',
      severity: 'medium',
      description: 'Page is missing a canonical URL',
    });
  }
  
  // Check mobile viewport
  const viewport = document.querySelector('meta[name="viewport"]');
  if (!viewport) {
    issues.push({
      type: 'missing_viewport',
      severity: 'high',
      description: 'Page is missing a viewport meta tag',
    });
  }
  
  return issues;
}

/**
 * Extract meta tags from the document
 */
function extractMetaTags(document: Document): SeoMetaTagData[] {
  const metaTags: SeoMetaTagData[] = [];
  const tags = document.querySelectorAll('meta');
  
  tags.forEach(tag => {
    const name = tag.getAttribute('name') || tag.getAttribute('property') || '';
    const content = tag.getAttribute('content') || '';
    
    if (name && content) {
      metaTags.push({ name, content });
    }
  });
  
  return metaTags;
}

/**
 * Extract structured data from the document
 */
function extractStructuredData(document: Document): SeoStructuredDataItem[] {
  const structuredData: SeoStructuredDataItem[] = [];
  const scripts = document.querySelectorAll('script[type="application/ld+json"]');
  
  scripts.forEach(script => {
    try {
      const data = JSON.parse(script.textContent || '{}');
      const type = data['@type'] || 'Unknown';
      
      structuredData.push({
        type,
        data,
      });
    } catch (error) {
      console.error('Error parsing structured data:', error);
    }
  });
  
  return structuredData;
}

/**
 * Check for broken links on the page
 */
async function checkBrokenLinks(document: Document, baseUrl: string): Promise<BrokenLinkData[]> {
  const brokenLinks: BrokenLinkData[] = [];
  const links = document.querySelectorAll('a');
  const checkedUrls = new Set<string>();
  
  // Only check a limited number of links to avoid overloading
  const MAX_LINKS_TO_CHECK = 20;
  let checkedCount = 0;
  
  for (const link of links) {
    if (checkedCount >= MAX_LINKS_TO_CHECK) break;
    
    const href = link.getAttribute('href');
    if (!href || href.startsWith('#') || href.startsWith('mailto:') || href.startsWith('tel:')) {
      continue;
    }
    
    // Resolve relative URLs
    const absoluteUrl = new URL(href, baseUrl).toString();
    
    // Skip if already checked
    if (checkedUrls.has(absoluteUrl)) {
      continue;
    }
    
    checkedUrls.add(absoluteUrl);
    checkedCount++;
    
    try {
      const response = await fetch(absoluteUrl, { method: 'HEAD' });
      
      if (response.status >= 400) {
        brokenLinks.push({
          url: absoluteUrl,
          statusCode: response.status,
          description: `Broken link with status ${response.status}`,
        });
      }
    } catch (error) {
      brokenLinks.push({
        url: absoluteUrl,
        description: `Failed to fetch: ${error.message}`,
      });
    }
  }
  
  return brokenLinks;
}

/**
 * Extract keywords from the document content
 */
function extractKeywords(document: Document): { keyword: string }[] {
  // Get the text content of the page
  const bodyText = document.body.textContent || '';
  const cleanText = bodyText.toLowerCase().replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim();
  
  // Split into words
  const words = cleanText.split(' ');
  
  // Count word frequency
  const wordCounts: Record<string, number> = {};
  words.forEach(word => {
    if (word.length > 3) { // Skip short words
      wordCounts[word] = (wordCounts[word] || 0) + 1;
    }
  });
  
  // Convert to array and sort by frequency
  const sortedWords = Object.entries(wordCounts)
    .filter(([word, count]) => count > 2) // Only include words that appear more than twice
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10) // Get top 10 keywords
    .map(([word]) => ({ keyword: word }));
  
  return sortedWords;
}

export default {
  analyzePage,
};
