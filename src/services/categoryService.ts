import prisma from '@/lib/prisma';
import { Category } from '@/types/blog';

// Get all categories
export async function getAllCategories(): Promise<Category[]> {
  try {
    console.log('Fetching all categories from database');
    const categories = await prisma.category.findMany({
      orderBy: {
        name: 'asc'
      }
    });

    console.log(`Found ${categories.length} categories in database`);
    return categories.map(formatCategory);
  } catch (error) {
    console.error('Error getting all categories:', error);
    throw new Error('Failed to fetch categories from database');
  }
}

// Get a category by ID
export async function getCategoryById(id: string): Promise<Category | null> {
  try {
    const categoryId = parseInt(id);
    const category = await prisma.category.findUnique({
      where: {
        id: categoryId
      }
    });

    if (!category) {
      return null;
    }

    return formatCategory(category);
  } catch (error) {
    console.error(`Error getting category by ID ${id}:`, error);
    throw error;
  }
}

// Get a category by slug
export async function getCategoryBySlug(slug: string): Promise<Category | null> {
  try {
    const category = await prisma.category.findUnique({
      where: {
        slug
      }
    });

    if (!category) {
      return null;
    }

    return formatCategory(category);
  } catch (error) {
    console.error(`Error getting category by slug ${slug}:`, error);
    throw error;
  }
}

// Create a new category
export async function createCategory(data: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>): Promise<Category> {
  try {
    const newCategory = await prisma.category.create({
      data: {
        name: data.name,
        slug: data.slug,
        description: data.description || ''
      }
    });

    return formatCategory(newCategory);
  } catch (error) {
    console.error('Error creating category:', error);
    throw error;
  }
}

// Update a category
export async function updateCategory(id: string, data: Partial<Category>): Promise<Category | null> {
  try {
    const categoryId = parseInt(id);

    // Check if the category exists
    const existingCategory = await prisma.category.findUnique({
      where: {
        id: categoryId
      }
    });

    if (!existingCategory) {
      return null;
    }

    // Create an update data object with only the fields that are provided
    const updateData: any = {};
    if (data.name !== undefined) updateData.name = data.name;
    if (data.slug !== undefined) updateData.slug = data.slug;
    if (data.description !== undefined) updateData.description = data.description;

    const updatedCategory = await prisma.category.update({
      where: {
        id: categoryId
      },
      data: updateData
    });

    return formatCategory(updatedCategory);
  } catch (error) {
    console.error(`Error updating category ${id}:`, error);
    throw error;
  }
}

// Delete a category
export async function deleteCategory(id: string): Promise<boolean> {
  try {
    const categoryId = parseInt(id);

    await prisma.category.delete({
      where: {
        id: categoryId
      }
    });

    return true;
  } catch (error) {
    console.error(`Error deleting category ${id}:`, error);
    return false;
  }
}

// Helper function to format category data from the database
function formatCategory(category: any): Category {
  return {
    id: category.id.toString(),
    name: category.name,
    slug: category.slug,
    description: category.description || '',
    createdAt: category.createdAt.toISOString(),
    updatedAt: category.updatedAt.toISOString()
  };
}
