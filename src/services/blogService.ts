import prisma from '@/lib/prisma';
import { BlogPost } from '@/types/blog';

// Get all blog posts
export async function getAllBlogPosts(): Promise<BlogPost[]> {
  try {
    console.log('Fetching all blog posts from database');
    const posts = await prisma.blogPost.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`Found ${posts.length} blog posts in database`);
    return posts.map(formatBlogPost);
  } catch (error) {
    console.error('Error getting all blog posts:', error);
    throw new Error('Failed to fetch blog posts from database');
  }
}

// Get published blog posts
export async function getPublishedBlogPosts(): Promise<BlogPost[]> {
  try {
    console.log('Fetching published blog posts from database');
    
    const posts = await prisma.blogPost.findMany({
      where: {
        status: 'published'
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`Found ${posts.length} published blog posts in database`);
    
    // If no posts are found, return a mock post for debugging
    if (posts.length === 0) {
      console.log('No posts found, returning a mock post for debugging');
      return [createMockPost()];
    }
    
    return posts.map(formatBlogPost);
  } catch (error) {
    console.error('Error getting published blog posts:', error);
    
    // In case of error, return a mock post for debugging
    console.log('Error occurred, returning a mock post for debugging');
    return [createMockPost()];
  }
}

// Get a blog post by slug
export async function getBlogPostBySlug(slug: string): Promise<BlogPost | null> {
  try {
    console.log(`Fetching blog post with slug: ${slug} from database`);
    const post = await prisma.blogPost.findUnique({
      where: {
        slug
      }
    });

    if (!post) {
      console.log(`No blog post found with slug: ${slug}`);
      return null;
    }

    console.log(`Found blog post: ${post.title}`);
    return formatBlogPost(post);
  } catch (error) {
    console.error(`Error getting blog post by slug ${slug}:`, error);
    throw new Error(`Failed to fetch blog post with slug: ${slug}`);
  }
}

// Get a blog post by ID
export async function getBlogPostById(id: string): Promise<BlogPost | null> {
  try {
    const postId = parseInt(id);
    const post = await prisma.blogPost.findUnique({
      where: {
        id: postId
      }
    });

    if (!post) {
      return null;
    }

    return formatBlogPost(post);
  } catch (error) {
    console.error(`Error getting blog post by ID ${id}:`, error);
    throw error;
  }
}

// Create a new blog post
export async function createBlogPost(post: Omit<BlogPost, 'id' | 'createdAt' | 'updatedAt'>): Promise<BlogPost> {
  try {
    // Always use current date if status is published
    const publishedAt = post.status === 'published' ? new Date() : null;
    console.log(`Creating post with status: ${post.status}, publishedAt: ${publishedAt}`);

    const newPost = await prisma.blogPost.create({
      data: {
        title: post.title,
        slug: post.slug,
        content: post.content,
        excerpt: post.excerpt || '',
        author: post.author || '',
        category: post.category || '',
        tags: post.tags || [],
        status: post.status,
        publishedAt
      }
    });

    return formatBlogPost(newPost);
  } catch (error) {
    console.error('Error creating blog post:', error);
    throw error;
  }
}

// Update a blog post
export async function updateBlogPost(id: string, post: Partial<BlogPost>): Promise<BlogPost | null> {
  try {
    const postId = parseInt(id);

    // Check if the post exists
    const existingPost = await prisma.blogPost.findUnique({
      where: {
        id: postId
      }
    });

    if (!existingPost) {
      return null;
    }

    // Always set publishedAt to current date if status is being changed to published
    let publishedAt = undefined;
    if (post.status === 'published') {
      // Always use current date when publishing, regardless of whether it had a date before
      publishedAt = new Date();
      console.log(`Setting publishedAt to current date: ${publishedAt}`);
    }

    // Create an update data object with only the fields that are provided
    const updateData: any = {};
    if (post.title !== undefined) updateData.title = post.title;
    if (post.slug !== undefined) updateData.slug = post.slug;
    if (post.content !== undefined) updateData.content = post.content;
    if (post.excerpt !== undefined) updateData.excerpt = post.excerpt;
    if (post.author !== undefined) updateData.author = post.author;
    if (post.category !== undefined) updateData.category = post.category;
    if (post.tags !== undefined) updateData.tags = post.tags;
    if (post.status !== undefined) updateData.status = post.status;
    if (publishedAt !== undefined) updateData.publishedAt = publishedAt;

    const updatedPost = await prisma.blogPost.update({
      where: {
        id: postId
      },
      data: updateData
    });

    return formatBlogPost(updatedPost);
  } catch (error) {
    console.error(`Error updating blog post ${id}:`, error);
    throw error;
  }
}

// Delete a blog post
export async function deleteBlogPost(id: string): Promise<boolean> {
  try {
    const postId = parseInt(id);

    await prisma.blogPost.delete({
      where: {
        id: postId
      }
    });

    return true;
  } catch (error) {
    console.error(`Error deleting blog post ${id}:`, error);
    return false;
  }
}

// Helper function to format blog post data from the database
function formatBlogPost(post: any): BlogPost {
  // Get the current date to check against future dates
  const now = new Date();
  
  // Format dates, ensuring they aren't in the future
  const formatDate = (date: Date | null): string => {
    if (!date) return now.toISOString();
    
    // If date is in the future, use current date instead
    const dateToUse = date > now ? now : date;
    return dateToUse.toISOString();
  };
  
  return {
    id: post.id.toString(),
    title: post.title,
    slug: post.slug,
    content: post.content,
    excerpt: post.excerpt,
    author: post.author,
    category: post.category,
    tags: post.tags || [],
    status: post.status,
    createdAt: formatDate(post.createdAt),
    updatedAt: formatDate(post.updatedAt),
    publishedAt: post.publishedAt ? formatDate(post.publishedAt) : null
  };
}

// Helper function to create a mock post for debugging
function createMockPost(): BlogPost {
  const now = new Date();
  return {
    id: 'mock-1',
    title: 'Mock Blog Post (Debug)',
    slug: 'mock-blog-post-debug',
    content: '<p>This is a mock blog post created for debugging purposes.</p>',
    excerpt: 'This is a mock blog post created for debugging purposes.',
    author: 'Debug',
    category: 'debug',
    tags: ['debug', 'mock'],
    status: 'published',
    createdAt: now.toISOString(),
    updatedAt: now.toISOString(),
    publishedAt: now.toISOString()
  };
}
