/**
 * Service for managing service items
 */
import prisma from '@/lib/prisma';

export interface Service {
  id: string;
  name: string;
  description: string | null;
  price: number;
  category: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Get all services
 * @returns Array of all services
 */
export async function getAllServices(): Promise<Service[]> {
  try {
    const services = await prisma.service.findMany({
      orderBy: {
        name: 'asc'
      }
    });
    
    return services.map(formatService);
  } catch (error) {
    console.error('Error getting all services:', error);
    throw error;
  }
}

/**
 * Get services by category
 * @param category The service category
 * @returns Array of services in the category
 */
export async function getServicesByCategory(category: string): Promise<Service[]> {
  try {
    const services = await prisma.service.findMany({
      where: {
        category
      },
      orderBy: {
        name: 'asc'
      }
    });
    
    return services.map(formatService);
  } catch (error) {
    console.error(`Error getting services in category ${category}:`, error);
    throw error;
  }
}

/**
 * Get a service by ID
 * @param id The service ID
 * @returns The service or null if not found
 */
export async function getServiceById(id: string): Promise<Service | null> {
  try {
    const service = await prisma.service.findUnique({
      where: {
        id
      }
    });
    
    if (!service) {
      return null;
    }
    
    return formatService(service);
  } catch (error) {
    console.error(`Error getting service ${id}:`, error);
    throw error;
  }
}

/**
 * Create a new service
 * @param data The service data
 * @returns The created service
 */
export async function createService(data: Omit<Service, 'id' | 'createdAt' | 'updatedAt'>): Promise<Service> {
  try {
    const service = await prisma.service.create({
      data: {
        name: data.name,
        description: data.description,
        price: data.price,
        category: data.category
      }
    });
    
    return formatService(service);
  } catch (error) {
    console.error('Error creating service:', error);
    throw error;
  }
}

/**
 * Update a service
 * @param id The service ID
 * @param data The updated service data
 * @returns The updated service
 */
export async function updateService(id: string, data: Partial<Omit<Service, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Service> {
  try {
    const updateData: any = {};
    
    if (data.name !== undefined) updateData.name = data.name;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.price !== undefined) updateData.price = data.price;
    if (data.category !== undefined) updateData.category = data.category;
    
    const service = await prisma.service.update({
      where: {
        id
      },
      data: updateData
    });
    
    return formatService(service);
  } catch (error) {
    console.error(`Error updating service ${id}:`, error);
    throw error;
  }
}

/**
 * Delete a service
 * @param id The service ID
 * @returns True if the service was deleted, false otherwise
 */
export async function deleteService(id: string): Promise<boolean> {
  try {
    await prisma.service.delete({
      where: {
        id
      }
    });
    
    return true;
  } catch (error) {
    console.error(`Error deleting service ${id}:`, error);
    return false;
  }
}

/**
 * Format a service from the database
 * @param service The service from the database
 * @returns The formatted service
 */
function formatService(service: any): Service {
  return {
    id: service.id,
    name: service.name,
    description: service.description,
    price: Number(service.price),
    category: service.category,
    createdAt: new Date(service.createdAt),
    updatedAt: new Date(service.updatedAt)
  };
}
