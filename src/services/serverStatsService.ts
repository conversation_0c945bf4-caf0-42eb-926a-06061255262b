import os from 'os';
import fs from 'fs';
import { promisify } from 'util';
import { exec } from 'child_process';

const execPromise = promisify(exec);

/**
 * Interface for system stats
 */
export interface SystemStats {
  cpu: {
    usage: number;
    cores: number;
    model: string;
    loadAvg: number[];
  };
  memory: {
    total: number;
    free: number;
    used: number;
    usedPercent: number;
  };
  disk: {
    total: number;
    free: number;
    used: number;
    usedPercent: number;
  };
  uptime: {
    system: number;
    process: number;
  };
  network: {
    interfaces: string[];
  };
  os: {
    platform: string;
    release: string;
    hostname: string;
  };
}

/**
 * Get CPU usage percentage
 * @returns Promise with CPU usage percentage
 */
async function getCpuUsage(): Promise<number> {
  try {
    // For Linux systems, we can use the 'top' command
    if (os.platform() === 'linux') {
      const { stdout } = await execPromise("top -bn1 | grep 'Cpu(s)' | sed 's/.*, *\\([0-9.]*\\)%* id.*/\\1/' | awk '{print 100 - $1}'");
      return parseFloat(stdout.trim());
    } else {
      // Fallback method for other platforms
      const cpus = os.cpus();
      const totalIdle = cpus.reduce((acc, cpu) => acc + cpu.times.idle, 0);
      const totalTick = cpus.reduce((acc, cpu) => acc + Object.values(cpu.times).reduce((a, b) => a + b, 0), 0);
      
      // Wait a second to measure difference
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const cpusAfter = os.cpus();
      const totalIdleAfter = cpusAfter.reduce((acc, cpu) => acc + cpu.times.idle, 0);
      const totalTickAfter = cpusAfter.reduce((acc, cpu) => acc + Object.values(cpu.times).reduce((a, b) => a + b, 0), 0);
      
      const idleDiff = totalIdleAfter - totalIdle;
      const totalDiff = totalTickAfter - totalTick;
      
      return 100 - (100 * idleDiff / totalDiff);
    }
  } catch (error) {
    console.error('Error getting CPU usage:', error);
    return 0;
  }
}

/**
 * Get disk usage statistics
 * @returns Promise with disk usage information
 */
async function getDiskUsage(): Promise<{ total: number; free: number; used: number; usedPercent: number }> {
  try {
    if (os.platform() === 'linux') {
      const { stdout } = await execPromise("df -k / | tail -1 | awk '{print $2,$3,$4,$5}'");
      const [total, used, free, usedPercentStr] = stdout.trim().split(/\s+/);
      
      return {
        total: parseInt(total) * 1024, // Convert to bytes
        used: parseInt(used) * 1024,
        free: parseInt(free) * 1024,
        usedPercent: parseInt(usedPercentStr.replace('%', ''))
      };
    } else {
      // Fallback for non-Linux systems (less accurate)
      return {
        total: 0,
        free: 0,
        used: 0,
        usedPercent: 0
      };
    }
  } catch (error) {
    console.error('Error getting disk usage:', error);
    return {
      total: 0,
      free: 0,
      used: 0,
      usedPercent: 0
    };
  }
}

/**
 * Get system statistics
 * @returns Promise with system statistics
 */
export async function getSystemStats(): Promise<SystemStats> {
  try {
    // Get CPU usage
    const cpuUsage = await getCpuUsage();
    const cpuInfo = os.cpus();
    
    // Get memory info
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;
    const memUsedPercent = (usedMem / totalMem) * 100;
    
    // Get disk usage
    const diskUsage = await getDiskUsage();
    
    // Get network interfaces
    const networkInterfaces = Object.keys(os.networkInterfaces());
    
    return {
      cpu: {
        usage: parseFloat(cpuUsage.toFixed(2)),
        cores: cpuInfo.length,
        model: cpuInfo[0]?.model || 'Unknown',
        loadAvg: os.loadavg(),
      },
      memory: {
        total: totalMem,
        free: freeMem,
        used: usedMem,
        usedPercent: parseFloat(memUsedPercent.toFixed(2)),
      },
      disk: diskUsage,
      uptime: {
        system: os.uptime(),
        process: process.uptime(),
      },
      network: {
        interfaces: networkInterfaces,
      },
      os: {
        platform: os.platform(),
        release: os.release(),
        hostname: os.hostname(),
      },
    };
  } catch (error) {
    console.error('Error getting system stats:', error);
    throw error;
  }
}
