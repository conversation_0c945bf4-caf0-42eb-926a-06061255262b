import prisma from '@/lib/prisma';
import { ErrorTracker } from '@/services/errorTracking';
import {
  validateCatalogueCreate,
  validateCatalogueUpdate,
  validateCatalogueFilters,
  validatePagination,
  validateCatalogueId,
  validateBulkDelete,
  CatalogueValidationError,
  checkRateLimit,
  sanitizeSearchTerm
} from '@/utils/catalogueValidation';
import { Prisma } from '@prisma/client';

// Enhanced interfaces
export interface Catalogue {
  id: string;
  service: string;
  price: number;
  description?: string;
  features?: string[];
  icon?: string;
  popular?: boolean;
  imageUrl?: string;
  imageUrl2?: string;
  imageUrl3?: string;
  category?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CatalogueFilters {
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  popular?: boolean;
  search?: string;
}

export interface PaginationOptions {
  page: number;
  limit: number;
  sortBy?: 'service' | 'price' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export interface CatalogueResponse {
  items: Catalogue[];
  total: number;
  hasMore: boolean;
  page: number;
  limit: number;
}

// Custom errors
export class CatalogueNotFoundError extends Error {
  constructor(id: string) {
    super(`Catalogue item with ID ${id} not found`);
    this.name = 'CatalogueNotFoundError';
  }
}

export class CatalogueDuplicateError extends Error {
  constructor(service: string) {
    super(`Catalogue item with service name "${service}" already exists`);
    this.name = 'CatalogueDuplicateError';
  }
}

// Enhanced service functions
export async function getAllCatalogue(
  filters: CatalogueFilters = {},
  pagination?: PaginationOptions,
  clientId?: string
): Promise<CatalogueResponse> {
  try {
    // Rate limiting
    if (clientId && !checkRateLimit(clientId, 100, 60000)) {
      throw new Error('Rate limit exceeded');
    }

    // Validate inputs
    const validatedFilters = validateCatalogueFilters(filters);
    const validatedPagination = pagination ? validatePagination(pagination) : {
      page: 1,
      limit: 20,
      sortBy: 'service' as const,
      sortOrder: 'asc' as const
    };

    console.log('Service: Fetching catalogue items with filters:', validatedFilters);

    // Build where clause with proper typing
    const where: Prisma.CatalogueWhereInput = {};

    if (validatedFilters.category) {
      where.category = validatedFilters.category;
    }

    if (validatedFilters.minPrice !== undefined || validatedFilters.maxPrice !== undefined) {
      where.price = {};
      if (validatedFilters.minPrice !== undefined) {
        where.price.gte = validatedFilters.minPrice;
      }
      if (validatedFilters.maxPrice !== undefined) {
        where.price.lte = validatedFilters.maxPrice;
      }
    }

    if (validatedFilters.popular !== undefined) {
      where.popular = validatedFilters.popular;
    }

    if (validatedFilters.search) {
      const sanitizedSearch = sanitizeSearchTerm(validatedFilters.search);
      where.OR = [
        { service: { contains: sanitizedSearch, mode: 'insensitive' } },
        { description: { contains: sanitizedSearch, mode: 'insensitive' } },
        { category: { contains: sanitizedSearch, mode: 'insensitive' } }
      ];
    }

    // Build order by clause
    const orderBy: Prisma.CatalogueOrderByWithRelationInput = {
      [validatedPagination.sortBy]: validatedPagination.sortOrder
    };

    // Use transaction for consistency
    const [total, catalogueItems] = await prisma.$transaction([
      prisma.catalogue.count({ where }),
      prisma.catalogue.findMany({
        where,
        orderBy,
        skip: (validatedPagination.page - 1) * validatedPagination.limit,
        take: validatedPagination.limit
      })
    ]);

    console.log(`Service: Found ${catalogueItems.length} of ${total} catalogue items`);

    const formattedItems = catalogueItems.map(formatCatalogue);
    const hasMore = (validatedPagination.page - 1) * validatedPagination.limit + catalogueItems.length < total;

    return {
      items: formattedItems,
      total,
      hasMore,
      page: validatedPagination.page,
      limit: validatedPagination.limit
    };
  } catch (error) {
    if (error instanceof CatalogueValidationError) {
      throw error;
    }

    ErrorTracker.trackError(error as Error, 'getAllCatalogue', { filters, pagination });
    console.error('Error fetching catalogue items:', error);
    throw new Error('Failed to fetch catalogue items');
  }
}

export async function getCatalogueById(id: string): Promise<Catalogue | null> {
  try {
    const catalogueId = validateCatalogueId(id);

    const catalogue = await prisma.catalogue.findUnique({
      where: { id: catalogueId }
    });

    if (!catalogue) {
      return null;
    }

    return formatCatalogue(catalogue);
  } catch (error) {
    if (error instanceof CatalogueValidationError) {
      throw error;
    }

    ErrorTracker.trackError(error as Error, 'getCatalogueById', { id });
    console.error(`Error getting catalogue by ID ${id}:`, error);
    throw new Error('Failed to fetch catalogue item');
  }
}

export async function createCatalogue(
  data: unknown,
  clientId?: string
): Promise<Catalogue> {
  try {
    // Rate limiting for creation
    if (clientId && !checkRateLimit(`create_${clientId}`, 10, 60000)) {
      throw new Error('Creation rate limit exceeded');
    }

    // Validate input data
    const validatedData = validateCatalogueCreate(data);

    // Check for duplicate service names
    const existingCatalogue = await prisma.catalogue.findFirst({
      where: {
        service: {
          equals: validatedData.service,
          mode: 'insensitive'
        }
      }
    });

    if (existingCatalogue) {
      throw new CatalogueDuplicateError(validatedData.service);
    }

    // Create catalogue item
    const newCatalogue = await prisma.catalogue.create({
      data: {
        service: validatedData.service,
        price: validatedData.price,
        description: validatedData.description || '',
        features: validatedData.features || [],
        icon: validatedData.icon,
        popular: validatedData.popular || false,
        imageUrl: validatedData.imageUrl,
        imageUrl2: validatedData.imageUrl2,
        imageUrl3: validatedData.imageUrl3,
        category: validatedData.category || 'Other'
      }
    });

    console.log(`Service: Created catalogue item: ${newCatalogue.service}`);
    return formatCatalogue(newCatalogue);
  } catch (error) {
    if (error instanceof CatalogueValidationError || error instanceof CatalogueDuplicateError) {
      throw error;
    }

    ErrorTracker.trackError(error as Error, 'createCatalogue', { data });
    console.error('Error creating catalogue item:', error);
    throw new Error('Failed to create catalogue item');
  }
}

export async function updateCatalogue(
  id: string,
  data: unknown,
  clientId?: string
): Promise<Catalogue> {
  try {
    // Rate limiting for updates
    if (clientId && !checkRateLimit(`update_${clientId}`, 20, 60000)) {
      throw new Error('Update rate limit exceeded');
    }

    const catalogueId = validateCatalogueId(id);
    const validatedData = validateCatalogueUpdate(data);

    // Check if catalogue exists
    const existingCatalogue = await prisma.catalogue.findUnique({
      where: { id: catalogueId }
    });

    if (!existingCatalogue) {
      throw new CatalogueNotFoundError(id);
    }

    // Check for duplicate service names if service is being updated
    if (validatedData.service && validatedData.service !== existingCatalogue.service) {
      const duplicateCatalogue = await prisma.catalogue.findFirst({
        where: {
          service: {
            equals: validatedData.service,
            mode: 'insensitive'
          },
          id: { not: catalogueId }
        }
      });

      if (duplicateCatalogue) {
        throw new CatalogueDuplicateError(validatedData.service);
      }
    }

    // Update catalogue item
    const updatedCatalogue = await prisma.catalogue.update({
      where: { id: catalogueId },
      data: validatedData
    });

    console.log(`Service: Updated catalogue item: ${updatedCatalogue.service}`);
    return formatCatalogue(updatedCatalogue);
  } catch (error) {
    if (error instanceof CatalogueValidationError ||
        error instanceof CatalogueNotFoundError ||
        error instanceof CatalogueDuplicateError) {
      throw error;
    }

    ErrorTracker.trackError(error as Error, 'updateCatalogue', { id, data });
    console.error(`Error updating catalogue ${id}:`, error);
    throw new Error('Failed to update catalogue item');
  }
}

export async function deleteCatalogue(
  id: string,
  clientId?: string
): Promise<boolean> {
  try {
    // Rate limiting for deletion
    if (clientId && !checkRateLimit(`delete_${clientId}`, 5, 60000)) {
      throw new Error('Deletion rate limit exceeded');
    }

    const catalogueId = validateCatalogueId(id);

    // Check if catalogue exists
    const existingCatalogue = await prisma.catalogue.findUnique({
      where: { id: catalogueId }
    });

    if (!existingCatalogue) {
      throw new CatalogueNotFoundError(id);
    }

    await prisma.catalogue.delete({
      where: { id: catalogueId }
    });

    console.log(`Service: Deleted catalogue item: ${existingCatalogue.service}`);
    return true;
  } catch (error) {
    if (error instanceof CatalogueValidationError || error instanceof CatalogueNotFoundError) {
      throw error;
    }

    ErrorTracker.trackError(error as Error, 'deleteCatalogue', { id });
    console.error(`Error deleting catalogue ${id}:`, error);
    throw new Error('Failed to delete catalogue item');
  }
}

export async function bulkDeleteCatalogue(
  data: unknown,
  clientId?: string
): Promise<{ success: boolean; count: number; errors: string[] }> {
  try {
    // Rate limiting for bulk operations
    if (clientId && !checkRateLimit(`bulk_delete_${clientId}`, 2, 60000)) {
      throw new Error('Bulk deletion rate limit exceeded');
    }

    const validatedData = validateBulkDelete(data);
    const catalogueIds = validatedData.ids.map(id => parseInt(id, 10));

    // Check which items exist
    const existingItems = await prisma.catalogue.findMany({
      where: { id: { in: catalogueIds } },
      select: { id: true, service: true }
    });

    const existingIds = existingItems.map(item => item.id);
    const missingIds = catalogueIds.filter(id => !existingIds.includes(id));

    const errors: string[] = [];
    if (missingIds.length > 0) {
      errors.push(`Items not found: ${missingIds.join(', ')}`);
    }

    // Delete existing items
    const result = await prisma.catalogue.deleteMany({
      where: { id: { in: existingIds } }
    });

    console.log(`Service: Bulk deleted ${result.count} catalogue items`);

    return {
      success: result.count > 0,
      count: result.count,
      errors
    };
  } catch (error) {
    if (error instanceof CatalogueValidationError) {
      throw error;
    }

    ErrorTracker.trackError(error as Error, 'bulkDeleteCatalogue', { data });
    console.error('Error bulk deleting catalogue items:', error);
    throw new Error('Failed to bulk delete catalogue items');
  }
}

export async function getCatalogueStats(): Promise<{
  total: number;
  categories: { name: string; count: number }[];
  priceRange: { min: number; max: number; avg: number };
  popularCount: number;
}> {
  try {
    const [total, categories, priceStats, popularCount] = await prisma.$transaction([
      // Total count
      prisma.catalogue.count(),

      // Categories with counts
      prisma.catalogue.groupBy({
        by: ['category'],
        _count: { category: true },
        orderBy: { _count: { category: 'desc' } }
      }),

      // Price statistics
      prisma.catalogue.aggregate({
        _min: { price: true },
        _max: { price: true },
        _avg: { price: true }
      }),

      // Popular items count
      prisma.catalogue.count({ where: { popular: true } })
    ]);

    return {
      total,
      categories: categories.map(cat => ({
        name: cat.category || 'Other',
        count: cat._count.category
      })),
      priceRange: {
        min: priceStats._min.price || 0,
        max: priceStats._max.price || 0,
        avg: Math.round(priceStats._avg.price || 0)
      },
      popularCount
    };
  } catch (error) {
    ErrorTracker.trackError(error as Error, 'getCatalogueStats');
    console.error('Error getting catalogue stats:', error);
    throw new Error('Failed to get catalogue statistics');
  }
}

export async function searchCatalogue(
  query: string,
  limit: number = 10
): Promise<Catalogue[]> {
  try {
    const sanitizedQuery = sanitizeSearchTerm(query);

    if (sanitizedQuery.length < 2) {
      return [];
    }

    const catalogueItems = await prisma.catalogue.findMany({
      where: {
        OR: [
          { service: { contains: sanitizedQuery, mode: 'insensitive' } },
          { description: { contains: sanitizedQuery, mode: 'insensitive' } },
          { category: { contains: sanitizedQuery, mode: 'insensitive' } }
        ]
      },
      orderBy: [
        { popular: 'desc' },
        { service: 'asc' }
      ],
      take: Math.min(limit, 50) // Cap at 50 results
    });

    return catalogueItems.map(formatCatalogue);
  } catch (error) {
    ErrorTracker.trackError(error as Error, 'searchCatalogue', { query, limit });
    console.error('Error searching catalogue:', error);
    throw new Error('Failed to search catalogue');
  }
}

// Helper function to format catalogue data
function formatCatalogue(catalogue: any): Catalogue {
  return {
    id: catalogue.id.toString(),
    service: catalogue.service,
    price: catalogue.price,
    description: catalogue.description || '',
    features: catalogue.features || [],
    icon: catalogue.icon || undefined,
    popular: catalogue.popular || false,
    imageUrl: catalogue.imageUrl || undefined,
    imageUrl2: catalogue.imageUrl2 || undefined,
    imageUrl3: catalogue.imageUrl3 || undefined,
    category: catalogue.category || 'Other',
    createdAt: catalogue.createdAt.toISOString(),
    updatedAt: catalogue.updatedAt.toISOString()
  };
}
