import prisma from '@/lib/prisma';
import { JSDOM } from 'jsdom';
import DOMPurify from 'dompurify';
import fetch from 'node-fetch';

// Types
export interface SeoPageData {
  url: string;
  title: string;
  description?: string;
  keywords?: string[];
  healthScore?: number;
}

export interface SeoIssueData {
  type: string;
  severity: string;
  description: string;
}

export interface SeoKeywordData {
  keyword: string;
  position?: number;
  volume?: number;
  difficulty?: number;
}

export interface SeoMetaTagData {
  name: string;
  content: string;
}

export interface SeoStructuredDataItem {
  type: string;
  data: any;
}

export interface BrokenLinkData {
  url: string;
  statusCode?: number;
  description?: string;
}

export interface SeoScanResult {
  pageData: SeoPageData;
  issues: SeoIssueData[];
  keywords: SeoKeywordData[];
  metaTags: SeoMetaTagData[];
  structuredData: SeoStructuredDataItem[];
  brokenLinks: BrokenLinkData[];
}

/**
 * Create or update a SEO page record
 */
export async function createOrUpdateSeoPage(pageData: SeoPageData) {
  return prisma.seoPage.upsert({
    where: { url: pageData.url },
    update: {
      title: pageData.title,
      description: pageData.description,
      keywords: pageData.keywords || [],
      healthScore: pageData.healthScore || 0,
      updatedAt: new Date(),
    },
    create: {
      url: pageData.url,
      title: pageData.title,
      description: pageData.description,
      keywords: pageData.keywords || [],
      healthScore: pageData.healthScore || 0,
    },
  });
}

/**
 * Get all SEO pages
 */
export async function getAllSeoPages() {
  return prisma.seoPage.findMany({
    include: {
      seoIssues: true,
      seoKeywords: true,
      brokenLinks: true,
      _count: {
        select: {
          seoMetaTags: true,
          seoStructuredData: true,
        },
      },
    },
    orderBy: {
      updatedAt: 'desc',
    },
  });
}

/**
 * Get a SEO page by ID with all related data
 */
export async function getSeoPageById(id: string) {
  return prisma.seoPage.findUnique({
    where: { id },
    include: {
      seoIssues: true,
      seoKeywords: true,
      seoMetaTags: true,
      seoStructuredData: true,
      brokenLinks: true,
    },
  });
}

/**
 * Get a SEO page by URL with all related data
 */
export async function getSeoPageByUrl(url: string) {
  return prisma.seoPage.findUnique({
    where: { url },
    include: {
      seoIssues: true,
      seoKeywords: true,
      seoMetaTags: true,
      seoStructuredData: true,
      brokenLinks: true,
    },
  });
}

/**
 * Delete a SEO page and all related data
 */
export async function deleteSeoPage(id: string) {
  return prisma.seoPage.delete({
    where: { id },
  });
}

/**
 * Create a new SEO scan record
 */
export async function createSeoScan() {
  return prisma.seoScan.create({
    data: {
      status: 'running',
    },
  });
}

/**
 * Update a SEO scan record
 */
export async function updateSeoScan(id: string, data: { status: string; endedAt?: Date; summary?: any }) {
  return prisma.seoScan.update({
    where: { id },
    data,
  });
}

/**
 * Get all SEO scans
 */
export async function getAllSeoScans() {
  return prisma.seoScan.findMany({
    orderBy: {
      startedAt: 'desc',
    },
  });
}

/**
 * Save SEO scan results for a page
 */
export async function saveSeoScanResults(pageId: string, results: SeoScanResult) {
  // Update the page record
  await prisma.seoPage.update({
    where: { id: pageId },
    data: {
      lastScanned: new Date(),
      healthScore: calculateHealthScore(results),
      status: 'scanned',
    },
  });

  // Clear existing data
  await prisma.$transaction([
    prisma.seoIssue.deleteMany({ where: { pageId } }),
    prisma.seoKeyword.deleteMany({ where: { pageId } }),
    prisma.seoMetaTag.deleteMany({ where: { pageId } }),
    prisma.seoStructuredData.deleteMany({ where: { pageId } }),
    prisma.brokenLink.deleteMany({ where: { pageId } }),
  ]);

  // Save new data
  if (results.issues.length > 0) {
    await prisma.seoIssue.createMany({
      data: results.issues.map(issue => ({
        pageId,
        type: issue.type,
        severity: issue.severity,
        description: issue.description,
      })),
    });
  }

  if (results.keywords.length > 0) {
    await prisma.seoKeyword.createMany({
      data: results.keywords.map(keyword => ({
        pageId,
        keyword: keyword.keyword,
        position: keyword.position,
        volume: keyword.volume,
        difficulty: keyword.difficulty,
        lastChecked: new Date(),
      })),
    });
  }

  if (results.metaTags.length > 0) {
    await prisma.seoMetaTag.createMany({
      data: results.metaTags.map(tag => ({
        pageId,
        name: tag.name,
        content: tag.content,
      })),
    });
  }

  if (results.structuredData.length > 0) {
    await prisma.seoStructuredData.createMany({
      data: results.structuredData.map(item => ({
        pageId,
        type: item.type,
        data: item.data,
      })),
    });
  }

  if (results.brokenLinks.length > 0) {
    await prisma.brokenLink.createMany({
      data: results.brokenLinks.map(link => ({
        pageId,
        url: link.url,
        statusCode: link.statusCode,
        description: link.description,
        fixed: false,
      })),
    });
  }

  return true;
}

/**
 * Calculate health score based on scan results
 */
function calculateHealthScore(results: SeoScanResult): number {
  let score = 100;
  
  // Deduct points for issues based on severity
  results.issues.forEach(issue => {
    if (issue.severity === 'critical') {
      score -= 20;
    } else if (issue.severity === 'high') {
      score -= 10;
    } else if (issue.severity === 'medium') {
      score -= 5;
    } else if (issue.severity === 'low') {
      score -= 2;
    }
  });
  
  // Deduct points for broken links
  score -= results.brokenLinks.length * 5;
  
  // Ensure score doesn't go below 0
  return Math.max(0, score);
}

/**
 * Fix a broken link
 */
export async function fixBrokenLink(id: string, fixedUrl: string) {
  return prisma.brokenLink.update({
    where: { id },
    data: {
      fixed: true,
      fixedUrl,
      updatedAt: new Date(),
    },
  });
}

export default {
  createOrUpdateSeoPage,
  getAllSeoPages,
  getSeoPageById,
  getSeoPageByUrl,
  deleteSeoPage,
  createSeoScan,
  updateSeoScan,
  getAllSeoScans,
  saveSeoScanResults,
  fixBrokenLink,
};
