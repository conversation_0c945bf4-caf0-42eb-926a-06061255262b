import { SiteScript } from '@prisma/client';
import prisma from '@/lib/prisma';

/**
 * Get all scripts
 * @returns Array of scripts
 */
export async function getAllScripts(): Promise<SiteScript[]> {
  try {
    const scripts = await prisma.siteScript.findMany({
      orderBy: [
        { scriptType: 'asc' },
        { position: 'asc' },
        { name: 'asc' }
      ],
    });
    return scripts;
  } catch (error) {
    console.error('Error getting scripts:', error);
    throw error;
  }
}

/**
 * Get active scripts by type
 * @param scriptType The type of scripts to get (head, body, footer)
 * @returns Array of active scripts of the specified type
 */
export async function getActiveScriptsByType(scriptType: 'head' | 'body' | 'footer'): Promise<SiteScript[]> {
  try {
    const scripts = await prisma.siteScript.findMany({
      where: {
        scriptType,
        isActive: true,
      },
      orderBy: [
        { position: 'asc' },
        { name: 'asc' }
      ],
    });
    return scripts;
  } catch (error) {
    console.error(`<PERSON>rror getting ${scriptType} scripts:`, error);
    return []; // Return empty array instead of throwing to prevent build failures
  }
}

/**
 * Get a script by ID
 * @param id The ID of the script to get
 * @returns The script or null if not found
 */
export async function getScriptById(id: string): Promise<SiteScript | null> {
  try {
    const script = await prisma.siteScript.findUnique({
      where: { id },
    });
    return script;
  } catch (error) {
    console.error('Error getting script by ID:', error);
    throw error;
  }
}

/**
 * Create a new script
 * @param data The script data
 * @returns The created script
 */
export async function createScript(data: Omit<SiteScript, 'id' | 'createdAt' | 'updatedAt'>): Promise<SiteScript> {
  try {
    const script = await prisma.siteScript.create({
      data,
    });
    return script;
  } catch (error) {
    console.error('Error creating script:', error);
    throw error;
  }
}

/**
 * Update a script
 * @param id The ID of the script to update
 * @param data The updated script data
 * @returns The updated script
 */
export async function updateScript(id: string, data: Partial<Omit<SiteScript, 'id' | 'createdAt' | 'updatedAt'>>): Promise<SiteScript> {
  try {
    const script = await prisma.siteScript.update({
      where: { id },
      data,
    });
    return script;
  } catch (error) {
    console.error('Error updating script:', error);
    throw error;
  }
}

/**
 * Delete a script
 * @param id The ID of the script to delete
 * @returns The deleted script
 */
export async function deleteScript(id: string): Promise<SiteScript> {
  try {
    const script = await prisma.siteScript.delete({
      where: { id },
    });
    return script;
  } catch (error) {
    console.error('Error deleting script:', error);
    throw error;
  }
}
