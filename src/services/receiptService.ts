/**
 * Service for managing receipts
 */
import prisma from '@/lib/prisma';
import { generateReceiptPDF, ReceiptData, ReceiptItemData } from '@/utils/pdfGenerator';
import { getTransactionById } from './transactionService';
import { getServiceById } from './serviceItemService';
import path from 'path';
import { promises as fs } from 'fs';

export interface Receipt {
  id: string;
  receiptNumber: string;
  totalAmount: number;
  amountPaid: number;
  balance: number;
  customerName: string;
  phoneNumber: string;
  email: string | null;
  status: string;
  notes: string | null;
  createdAt: Date;
  updatedAt: Date;
  issuedAt: Date;
  paidAt: Date | null;
  transactionId: string;
  items: ReceiptItem[];
  pdfUrl?: string;
}

export interface ReceiptItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  description: string | null;
  createdAt: Date;
  updatedAt: Date;
  receiptId: string;
  serviceId: string;
  serviceName?: string;
}

/**
 * Generate a unique receipt number
 * @param mpesaTransactionId Optional M-Pesa transaction ID to use as the receipt number
 * @returns A unique receipt number
 */
async function generateReceiptNumber(mpesaTransactionId?: string): Promise<string> {
  // If M-Pesa transaction ID is provided, use it as the receipt number
  if (mpesaTransactionId && mpesaTransactionId.trim() !== '') {
    console.log(`Using M-Pesa transaction ID as receipt number: ${mpesaTransactionId}`);

    // Check if a receipt with this number already exists
    const existingReceipt = await prisma.receipt.findUnique({
      where: {
        receiptNumber: mpesaTransactionId
      }
    });

    if (existingReceipt) {
      console.log(`Receipt with number ${mpesaTransactionId} already exists, generating alternative number`);
      // If it exists, append a suffix to make it unique
      const timestamp = Date.now().toString().slice(-4);
      return `${mpesaTransactionId}-${timestamp}`;
    }

    return mpesaTransactionId;
  }

  // Otherwise, generate a receipt number in the traditional format
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');

  // Get the count of receipts for today
  const todayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const todayEnd = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);

  const count = await prisma.receipt.count({
    where: {
      createdAt: {
        gte: todayStart,
        lt: todayEnd
      }
    }
  });

  // Generate receipt number in format: MG-YYMMDD-XXX
  const sequence = (count + 1).toString().padStart(3, '0');
  const receiptNumber = `MG-${year}${month}${day}-${sequence}`;

  // Ensure the generated number is unique
  const existingReceipt = await prisma.receipt.findUnique({
    where: {
      receiptNumber
    }
  });

  if (existingReceipt) {
    // If it exists, append a timestamp to make it unique
    const timestamp = Date.now().toString().slice(-4);
    return `${receiptNumber}-${timestamp}`;
  }

  return receiptNumber;
}

/**
 * Create a new receipt
 * @param data The receipt data
 * @returns The created receipt
 */
export async function createReceipt(data: {
  transactionId: string;
  customerName?: string;
  phoneNumber?: string;
  email?: string;
  notes?: string;
  items: {
    serviceId: string;
    quantity: number;
    unitPrice?: number;
    description?: string;
  }[];
}): Promise<Receipt | null> {
  try {
    // Get the transaction
    const transaction = await getTransactionById(data.transactionId);

    if (!transaction) {
      throw new Error(`Transaction ${data.transactionId} not found`);
    }

    // Check if a receipt already exists for this transaction
    const existingReceipt = await prisma.receipt.findFirst({
      where: {
        transactionId: data.transactionId
      },
      include: {
        items: true
      }
    });

    if (existingReceipt) {
      console.log(`Receipt already exists for transaction ${data.transactionId}`);
      // Return the existing receipt
      return formatReceipt(existingReceipt);
    }

    // Get the M-Pesa transaction ID from the transaction
    const mpesaTransactionId = transaction.transactionId;

    // Generate receipt number using the M-Pesa transaction ID
    const receiptNumber = await generateReceiptNumber(mpesaTransactionId);

    // Calculate total amount
    let totalAmount = 0;
    const receiptItems = [];

    for (const item of data.items) {
      // Get the service
      const service = await getServiceById(item.serviceId);

      if (!service) {
        throw new Error(`Service ${item.serviceId} not found`);
      }

      // Use provided unit price or service price
      const unitPrice = item.unitPrice !== undefined ? item.unitPrice : service.price;

      // Calculate total price for this item
      const totalPrice = unitPrice * item.quantity;

      // Add to total amount
      totalAmount += totalPrice;

      // Add to receipt items
      receiptItems.push({
        serviceId: item.serviceId,
        quantity: item.quantity,
        unitPrice,
        totalPrice,
        description: item.description || service.name
      });
    }

    // Calculate balance
    const amountPaid = transaction.amount;
    const balance = totalAmount - amountPaid;

    // Determine receipt status
    const status = balance <= 0 ? 'paid' : 'issued';

    // Create the receipt
    const receipt = await prisma.receipt.create({
      data: {
        receiptNumber,
        totalAmount,
        amountPaid,
        balance,
        customerName: data.customerName || transaction.customerName,
        phoneNumber: data.phoneNumber || transaction.phoneNumber,
        email: data.email || null,
        status,
        notes: data.notes || null,
        issuedAt: new Date(),
        paidAt: status === 'paid' ? new Date() : null,
        transactionId: transaction.id,
        items: {
          create: receiptItems
        }
      },
      include: {
        items: true
      }
    });

    // Update transaction status
    await prisma.transaction.update({
      where: {
        id: transaction.id
      },
      data: {
        status: 'processed',
        processedAt: new Date()
      }
    });

    // Format the receipt
    const formattedReceipt = formatReceipt(receipt);

    // Generate PDF file
    try {
      // Convert receipt to receipt data for PDF generation
      const receiptData = mapToReceiptData(formattedReceipt);

      // Generate the PDF file
      const pdfUrl = await generateReceiptPDF(receiptData);

      // Update the receipt with the PDF URL
      await prisma.receipt.update({
        where: { id: receipt.id },
        data: { pdfUrl }
      });

      // Return the receipt with PDF URL
      return {
        ...formattedReceipt,
        pdfUrl
      };
    } catch (pdfError) {
      console.error('Error generating PDF for receipt:', pdfError);

      // Generate a consistent PDF filename based on receipt number and ID as fallback
      const pdfFilename = `receipt-${formattedReceipt.receiptNumber}-${formattedReceipt.id.substring(0, 8)}.pdf`;
      const pdfUrl = `/uploads/receipts/${pdfFilename}`;

      // Update the receipt with the PDF URL
      await prisma.receipt.update({
        where: { id: receipt.id },
        data: { pdfUrl }
      });

      // Return the receipt with PDF URL
      return {
        ...formattedReceipt,
        pdfUrl
      };
    }
  } catch (error) {
    console.error('Error creating receipt:', error);
    return null;
  }
}

/**
 * Get all receipts
 * @returns Array of all receipts
 */
export async function getAllReceipts(): Promise<Receipt[]> {
  try {
    const receipts = await prisma.receipt.findMany({
      include: {
        items: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Format receipts and add PDF URLs
    const formattedReceipts = receipts.map(receipt => {
      const formatted = formatReceipt(receipt);

      // Use existing PDF URL if available
      if (receipt.pdfUrl) {
        formatted.pdfUrl = receipt.pdfUrl;
      } else {
        // Generate a consistent PDF filename based on receipt number and ID
        const pdfFilename = `receipt-${formatted.receiptNumber}-${formatted.id.substring(0, 8)}.pdf`;
        const pdfUrl = `/uploads/receipts/${pdfFilename}`;
        formatted.pdfUrl = pdfUrl;
      }

      return formatted;
    });

    return formattedReceipts;
  } catch (error) {
    console.error('Error getting all receipts:', error);
    throw error;
  }
}

/**
 * Get a receipt by ID
 * @param id The receipt ID
 * @returns The receipt or null if not found
 */
export async function getReceiptById(id: string): Promise<Receipt | null> {
  try {
    const receipt = await prisma.receipt.findUnique({
      where: {
        id
      },
      include: {
        items: true
      }
    });

    if (!receipt) {
      return null;
    }

    // Format the receipt
    const formattedReceipt = formatReceipt(receipt);

    // Check if the receipt already has a PDF URL
    if (receipt.pdfUrl) {
      formattedReceipt.pdfUrl = receipt.pdfUrl;

      // Check if the PDF file exists
      const pdfPath = path.join(process.cwd(), 'public', receipt.pdfUrl);
      try {
        await fs.access(pdfPath);
        // PDF exists, return the receipt
        return formattedReceipt;
      } catch (fileError) {
        // PDF doesn't exist, we'll generate it below
        console.log(`PDF file not found at ${pdfPath}, will generate a new one`);
      }
    }

    // Generate PDF file
    try {
      // Convert receipt to receipt data for PDF generation
      const receiptData = mapToReceiptData(formattedReceipt);

      // Generate the PDF file
      const pdfUrl = await generateReceiptPDF(receiptData);

      // Update the receipt with the PDF URL
      await prisma.receipt.update({
        where: { id: receipt.id },
        data: { pdfUrl }
      });

      // Set the PDF URL
      formattedReceipt.pdfUrl = pdfUrl;
    } catch (pdfError) {
      console.error('Error generating PDF for receipt:', pdfError);

      // Generate a consistent PDF filename based on receipt number and ID as fallback
      const pdfFilename = `receipt-${formattedReceipt.receiptNumber}-${formattedReceipt.id.substring(0, 8)}.pdf`;
      const pdfUrl = `/uploads/receipts/${pdfFilename}`;

      // Set the PDF URL
      formattedReceipt.pdfUrl = pdfUrl;
    }

    return formattedReceipt;
  } catch (error) {
    console.error(`Error getting receipt ${id}:`, error);
    throw error;
  }
}

/**
 * Check if a receipt exists for a given M-Pesa transaction ID
 * @param mpesaTransactionId The M-Pesa transaction ID
 * @returns The receipt ID and receipt number if found, null otherwise
 */
export async function checkReceiptByMpesaTransactionId(mpesaTransactionId: string): Promise<{ id: string; receiptNumber: string } | null> {
  try {
    // First, find a receipt with this M-Pesa transaction ID as the receipt number
    // (since we use the M-Pesa transaction ID as the receipt number)
    const receipt = await prisma.receipt.findFirst({
      where: {
        receiptNumber: {
          startsWith: mpesaTransactionId
        }
      },
      select: {
        id: true,
        receiptNumber: true
      }
    });

    if (receipt) {
      return {
        id: receipt.id,
        receiptNumber: receipt.receiptNumber
      };
    }

    // If not found directly, try to find through the transaction
    // First find the transaction with this M-Pesa transaction ID
    const transaction = await prisma.transaction.findUnique({
      where: {
        transactionId: mpesaTransactionId
      },
      select: {
        id: true
      }
    });

    if (!transaction) {
      return null;
    }

    // Then check if there's a receipt for this transaction
    const receiptByTransaction = await prisma.receipt.findFirst({
      where: {
        transactionId: transaction.id
      },
      select: {
        id: true,
        receiptNumber: true
      }
    });

    if (receiptByTransaction) {
      return {
        id: receiptByTransaction.id,
        receiptNumber: receiptByTransaction.receiptNumber
      };
    }

    return null;
  } catch (error) {
    console.error(`Error checking receipt for M-Pesa transaction ID ${mpesaTransactionId}:`, error);
    return null;
  }
}

/**
 * Get a receipt by receipt number
 * @param receiptNumber The receipt number
 * @returns The receipt or null if not found
 */
export async function getReceiptByNumber(receiptNumber: string): Promise<Receipt | null> {
  try {
    const receipt = await prisma.receipt.findUnique({
      where: {
        receiptNumber
      },
      include: {
        items: true
      }
    });

    if (!receipt) {
      return null;
    }

    // Format the receipt
    const formattedReceipt = formatReceipt(receipt);

    // Check if the receipt already has a PDF URL
    if (receipt.pdfUrl) {
      formattedReceipt.pdfUrl = receipt.pdfUrl;

      // Check if the PDF file exists
      const pdfPath = path.join(process.cwd(), 'public', receipt.pdfUrl);
      try {
        await fs.access(pdfPath);
        // PDF exists, return the receipt
        return formattedReceipt;
      } catch (fileError) {
        // PDF doesn't exist, we'll generate it below
        console.log(`PDF file not found at ${pdfPath}, will generate a new one`);
      }
    }

    // Generate PDF file
    try {
      // Convert receipt to receipt data for PDF generation
      const receiptData = mapToReceiptData(formattedReceipt);

      // Generate the PDF file
      const pdfUrl = await generateReceiptPDF(receiptData);

      // Update the receipt with the PDF URL
      await prisma.receipt.update({
        where: { id: receipt.id },
        data: { pdfUrl }
      });

      // Set the PDF URL
      formattedReceipt.pdfUrl = pdfUrl;
    } catch (pdfError) {
      console.error('Error generating PDF for receipt:', pdfError);

      // Generate a consistent PDF filename based on receipt number and ID as fallback
      const pdfFilename = `receipt-${formattedReceipt.receiptNumber}-${formattedReceipt.id.substring(0, 8)}.pdf`;
      const pdfUrl = `/uploads/receipts/${pdfFilename}`;

      // Set the PDF URL
      formattedReceipt.pdfUrl = pdfUrl;
    }

    return formattedReceipt;
  } catch (error) {
    console.error(`Error getting receipt ${receiptNumber}:`, error);
    throw error;
  }
}

/**
 * Format a receipt from the database
 * @param receipt The receipt from the database
 * @returns The formatted receipt
 */
function formatReceipt(receipt: any): Receipt {
  return {
    id: receipt.id,
    receiptNumber: receipt.receiptNumber,
    totalAmount: Number(receipt.totalAmount),
    amountPaid: Number(receipt.amountPaid),
    balance: Number(receipt.balance),
    customerName: receipt.customerName,
    phoneNumber: receipt.phoneNumber,
    email: receipt.email,
    status: receipt.status,
    notes: receipt.notes,
    createdAt: new Date(receipt.createdAt),
    updatedAt: new Date(receipt.updatedAt),
    issuedAt: new Date(receipt.issuedAt),
    paidAt: receipt.paidAt ? new Date(receipt.paidAt) : null,
    transactionId: receipt.transactionId,
    pdfUrl: receipt.pdfUrl || undefined,
    items: receipt.items.map((item: any) => ({
      id: item.id,
      quantity: item.quantity,
      unitPrice: Number(item.unitPrice),
      totalPrice: Number(item.totalPrice),
      description: item.description,
      createdAt: new Date(item.createdAt),
      updatedAt: new Date(item.updatedAt),
      receiptId: item.receiptId,
      serviceId: item.serviceId
    }))
  };
}

/**
 * Map a receipt to receipt data for PDF generation
 * @param receipt The receipt
 * @returns The receipt data for PDF generation
 */
export function mapToReceiptData(receipt: Receipt): ReceiptData {
  return {
    receiptNumber: receipt.receiptNumber,
    customerName: receipt.customerName,
    phoneNumber: receipt.phoneNumber,
    email: receipt.email || undefined,
    transactionId: receipt.transactionId,
    transactionDate: receipt.issuedAt,
    items: receipt.items.map((item): ReceiptItemData => ({
      description: item.description || 'Service',
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      totalPrice: item.totalPrice
    })),
    totalAmount: receipt.totalAmount,
    amountPaid: receipt.amountPaid,
    balance: receipt.balance,
    notes: receipt.notes || undefined,
    issuedAt: receipt.issuedAt
  };
}
