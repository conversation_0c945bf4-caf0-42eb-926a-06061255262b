/**
 * Lead Management Service
 * 
 * This service provides functions to manage leads, including:
 * - Creating and updating leads
 * - Scoring leads based on interactions
 * - Managing lead status and follow-ups
 * - Tracking lead interactions
 */

import { prisma } from '@/lib/prisma';
import { ErrorTracker } from './errorTracking';

// Define types for lead management
export interface LeadData {
  id?: string;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  source?: string;
  status?: string;
  score?: number;
  notes?: string;
  assignedToUserId?: string;
  nextFollowUpDate?: Date;
}

export interface InteractionData {
  leadId: string;
  type: string;
  details?: string;
  createdBy?: string;
}

export interface LeadScoreFactors {
  formCompleteness: number;
  interactionFrequency: number;
  pageVisits: number;
  highValuePageVisits: number;
  conversionEvents: number;
}

/**
 * Create a new lead in the database
 * @param leadData The lead data to create
 * @returns The created lead
 */
export async function createLead(leadData: LeadData) {
  try {
    // Create the lead
    const lead = await prisma.lead.create({
      data: {
        name: leadData.name,
        email: leadData.email,
        phone: leadData.phone,
        company: leadData.company,
        source: leadData.source || 'website',
        status: leadData.status || 'new',
        score: leadData.score || 0,
        notes: leadData.notes,
        assignedToUserId: leadData.assignedToUserId,
        nextFollowUpDate: leadData.nextFollowUpDate,
      },
    });

    return lead;
  } catch (error) {
    console.error('Error creating lead:', error);
    ErrorTracker.trackError(error as Error, 'createLead');
    throw error;
  }
}

/**
 * Update an existing lead in the database
 * @param id The ID of the lead to update
 * @param leadData The lead data to update
 * @returns The updated lead
 */
export async function updateLead(id: string, leadData: Partial<LeadData>) {
  try {
    // Update the lead
    const lead = await prisma.lead.update({
      where: { id },
      data: leadData,
    });

    return lead;
  } catch (error) {
    console.error(`Error updating lead ${id}:`, error);
    ErrorTracker.trackError(error as Error, 'updateLead');
    throw error;
  }
}

/**
 * Get a lead by ID
 * @param id The ID of the lead to get
 * @returns The lead
 */
export async function getLeadById(id: string) {
  try {
    const lead = await prisma.lead.findUnique({
      where: { id },
      include: {
        interactions: {
          orderBy: {
            createdAt: 'desc',
          },
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
      },
    });

    return lead;
  } catch (error) {
    console.error(`Error getting lead ${id}:`, error);
    ErrorTracker.trackError(error as Error, 'getLeadById');
    throw error;
  }
}

/**
 * Get all leads with optional filtering
 * @param filters Optional filters for the leads
 * @returns The leads
 */
export async function getLeads(filters?: {
  status?: string;
  assignedToUserId?: string;
  minScore?: number;
  search?: string;
}) {
  try {
    const leads = await prisma.lead.findMany({
      where: {
        ...(filters?.status && { status: filters.status }),
        ...(filters?.assignedToUserId && { assignedToUserId: filters.assignedToUserId }),
        ...(filters?.minScore && { score: { gte: filters.minScore } }),
        ...(filters?.search && {
          OR: [
            { name: { contains: filters.search, mode: 'insensitive' } },
            { email: { contains: filters.search, mode: 'insensitive' } },
            { company: { contains: filters.search, mode: 'insensitive' } },
          ],
        }),
      },
      include: {
        assignedTo: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
        _count: {
          select: {
            interactions: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return leads;
  } catch (error) {
    console.error('Error getting leads:', error);
    ErrorTracker.trackError(error as Error, 'getLeads');
    throw error;
  }
}

/**
 * Record a new interaction for a lead
 * @param interactionData The interaction data to create
 * @returns The created interaction
 */
export async function recordInteraction(interactionData: InteractionData) {
  try {
    // Create the interaction
    const interaction = await prisma.interaction.create({
      data: {
        leadId: interactionData.leadId,
        type: interactionData.type,
        details: interactionData.details,
        createdBy: interactionData.createdBy,
      },
    });

    // Update the lead's lastContactedAt field
    await prisma.lead.update({
      where: { id: interactionData.leadId },
      data: {
        lastContactedAt: new Date(),
      },
    });

    return interaction;
  } catch (error) {
    console.error('Error recording interaction:', error);
    ErrorTracker.trackError(error as Error, 'recordInteraction');
    throw error;
  }
}

/**
 * Calculate a lead score based on various factors
 * @param leadId The ID of the lead to score
 * @returns The updated lead score
 */
export async function calculateLeadScore(leadId: string): Promise<number> {
  try {
    // Get the lead with interactions
    const lead = await prisma.lead.findUnique({
      where: { id: leadId },
      include: {
        interactions: true,
      },
    });

    if (!lead) {
      throw new Error(`Lead with ID ${leadId} not found`);
    }

    // Get event tracking data for this lead
    const events = await prisma.eventTracking.findMany({
      where: {
        leadId,
      },
    });

    // Calculate score factors
    const scoreFactors: LeadScoreFactors = {
      formCompleteness: 0,
      interactionFrequency: 0,
      pageVisits: 0,
      highValuePageVisits: 0,
      conversionEvents: 0,
    };

    // Form completeness (0-20 points)
    scoreFactors.formCompleteness = calculateFormCompletenessScore(lead);

    // Interaction frequency (0-20 points)
    scoreFactors.interactionFrequency = calculateInteractionFrequencyScore(lead.interactions);

    // Page visits (0-20 points)
    scoreFactors.pageVisits = calculatePageVisitsScore(events);

    // High-value page visits (0-20 points)
    scoreFactors.highValuePageVisits = calculateHighValuePageVisitsScore(events);

    // Conversion events (0-20 points)
    scoreFactors.conversionEvents = calculateConversionEventsScore(events);

    // Calculate total score (0-100)
    const totalScore = 
      scoreFactors.formCompleteness +
      scoreFactors.interactionFrequency +
      scoreFactors.pageVisits +
      scoreFactors.highValuePageVisits +
      scoreFactors.conversionEvents;

    // Update the lead's score
    await prisma.lead.update({
      where: { id: leadId },
      data: {
        score: totalScore,
      },
    });

    return totalScore;
  } catch (error) {
    console.error(`Error calculating lead score for ${leadId}:`, error);
    ErrorTracker.trackError(error as Error, 'calculateLeadScore');
    throw error;
  }
}

// Helper functions for lead scoring

function calculateFormCompletenessScore(lead: any): number {
  let score = 0;
  
  // Basic information (up to 10 points)
  if (lead.name) score += 2;
  if (lead.email) score += 2;
  if (lead.phone) score += 2;
  if (lead.company) score += 2;
  if (lead.notes && lead.notes.length > 10) score += 2;
  
  // Additional points for completeness (up to 10 more points)
  const fieldsCount = Object.keys(lead).filter(key => 
    lead[key] !== null && 
    lead[key] !== undefined && 
    !['id', 'createdAt', 'updatedAt', 'interactions', 'assignedTo'].includes(key)
  ).length;
  
  score += Math.min(10, fieldsCount);
  
  return Math.min(20, score);
}

function calculateInteractionFrequencyScore(interactions: any[]): number {
  // No interactions
  if (!interactions || interactions.length === 0) return 0;
  
  // Score based on number of interactions (up to 20 points)
  return Math.min(20, interactions.length * 5);
}

function calculatePageVisitsScore(events: any[]): number {
  // Filter for page view events
  const pageViewEvents = events.filter(event => 
    event.eventType === 'pageView'
  );
  
  // No page views
  if (pageViewEvents.length === 0) return 0;
  
  // Score based on number of page views (up to 20 points)
  return Math.min(20, pageViewEvents.length);
}

function calculateHighValuePageVisitsScore(events: any[]): number {
  // Define high-value pages
  const highValuePages = [
    '/pricing',
    '/contact',
    '/request',
    '/web-development',
    '/logo-design',
    '/social-media',
    '/services',
  ];
  
  // Filter for high-value page view events
  const highValuePageViews = events.filter(event => 
    event.eventType === 'pageView' && 
    highValuePages.some(page => event.url?.includes(page))
  );
  
  // No high-value page views
  if (highValuePageViews.length === 0) return 0;
  
  // Score based on number of high-value page views (up to 20 points)
  return Math.min(20, highValuePageViews.length * 4);
}

function calculateConversionEventsScore(events: any[]): number {
  // Define conversion events
  const conversionEventTypes = [
    'formSubmission',
    'buttonClick',
    'download',
    'Lead',
    'Contact',
  ];
  
  // Filter for conversion events
  const conversionEvents = events.filter(event => 
    conversionEventTypes.includes(event.eventType) || 
    conversionEventTypes.includes(event.eventName)
  );
  
  // No conversion events
  if (conversionEvents.length === 0) return 0;
  
  // Score based on number of conversion events (up to 20 points)
  return Math.min(20, conversionEvents.length * 10);
}
