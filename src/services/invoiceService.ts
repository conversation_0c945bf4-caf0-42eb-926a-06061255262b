/**
 * Service for managing invoices
 */
import prisma from '@/lib/prisma';
import { generateInvoicePDF, InvoiceData, InvoiceItemData } from '@/utils/pdfGenerator';
import { getServiceById } from './serviceItemService';
import path from 'path';
import { promises as fs } from 'fs';
import { addDays } from 'date-fns';

export interface Invoice {
  id: string;
  invoiceNumber: string;
  totalAmount: number;
  amountPaid: number;
  balance: number;
  customerName: string;
  phoneNumber: string;
  email: string | null;
  status: string;
  notes: string | null;
  createdAt: Date;
  updatedAt: Date;
  issuedAt: Date;
  dueDate: Date;
  paidAt: Date | null;
  pdfUrl?: string;
  items: InvoiceItem[];
  quoteId?: string | null;
}

export interface InvoiceItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  description: string | null;
  serviceId: string;
  serviceName?: string;
}

/**
 * Get all invoices
 * @returns All invoices
 */
export async function getAllInvoices(): Promise<Invoice[]> {
  try {
    const invoices = await prisma.invoice.findMany({
      include: {
        items: {
          include: {
            service: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return invoices.map(formatInvoice);
  } catch (error) {
    console.error('Error getting all invoices:', error);
    throw error;
  }
}

/**
 * Get an invoice by ID
 * @param id The invoice ID
 * @returns The invoice or null if not found
 */
export async function getInvoiceById(id: string): Promise<Invoice | null> {
  try {
    const invoice = await prisma.invoice.findUnique({
      where: { id },
      include: {
        items: {
          include: {
            service: true
          }
        }
      }
    });

    if (!invoice) {
      return null;
    }

    return formatInvoice(invoice);
  } catch (error) {
    console.error(`Error getting invoice ${id}:`, error);
    throw error;
  }
}

/**
 * Generate a unique invoice number
 * @returns A unique invoice number
 */
export async function generateInvoiceNumber(): Promise<string> {
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');

  let count = 0;
  try {
    // Get the count of invoices for today
    const todayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const todayEnd = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);

    // Try to count invoices, handle potential errors
    try {
      count = await prisma.invoice.count({
        where: {
          createdAt: {
            gte: todayStart,
            lt: todayEnd
          }
        }
      });
    } catch (error) {
      console.error('Error counting invoices:', error);
      // If there's an error, assume count is 0
      count = 0;
    }
  } catch (error) {
    console.error('Error in generateInvoiceNumber:', error);
    // If there's any error, use a fallback count
    count = 0;
  }

  // Format: INV-YY-MM-DD-XXX where XXX is a sequential number
  const sequentialNumber = (count + 1).toString().padStart(3, '0');
  return `INV-${year}${month}${day}-${sequentialNumber}`;
}

/**
 * Create a new invoice
 * @param data The invoice data
 * @returns The created invoice
 */
export async function createInvoice(data: {
  customerName: string;
  phoneNumber: string;
  email?: string;
  notes?: string;
  dueDate?: Date;
  items: {
    serviceId: string;
    quantity: number;
    unitPrice?: number;
    description?: string;
  }[];
  quoteId?: string;
}): Promise<Invoice | null> {
  try {
    // Process items
    const invoiceItems = [];
    let totalAmount = 0;

    for (const item of data.items) {
      // Get service details
      const service = await getServiceById(item.serviceId);
      if (!service) {
        throw new Error(`Service ${item.serviceId} not found`);
      }

      // Use provided unit price or service price
      const unitPrice = item.unitPrice !== undefined ? item.unitPrice : Number(service.price);
      const totalPrice = unitPrice * item.quantity;

      // Add to total amount
      totalAmount += totalPrice;

      // Add to invoice items
      invoiceItems.push({
        serviceId: service.id,
        quantity: item.quantity,
        unitPrice,
        totalPrice,
        description: item.description || service.description
      });
    }

    // Generate invoice number
    const invoiceNumber = await generateInvoiceNumber();

    // Set due date (default to 14 days from now if not provided)
    const dueDate = data.dueDate || addDays(new Date(), 14);

    // Create the invoice
    const invoice = await prisma.invoice.create({
      data: {
        invoiceNumber,
        totalAmount,
        amountPaid: 0,
        balance: totalAmount,
        customerName: data.customerName,
        phoneNumber: data.phoneNumber,
        email: data.email || null,
        status: 'pending',
        notes: data.notes || null,
        issuedAt: new Date(),
        dueDate,
        quoteId: data.quoteId || null,
        items: {
          create: invoiceItems
        }
      },
      include: {
        items: true
      }
    });

    // Format the invoice
    const formattedInvoice = formatInvoice(invoice);

    // Generate PDF file
    try {
      // Convert invoice to invoice data for PDF generation
      const invoiceData = mapToInvoiceData(formattedInvoice);

      // Generate the PDF file
      const pdfUrl = await generateInvoicePDF(invoiceData);

      // Update the invoice with the PDF URL
      await prisma.invoice.update({
        where: { id: invoice.id },
        data: { pdfUrl }
      });

      // Return the invoice with PDF URL
      return {
        ...formattedInvoice,
        pdfUrl
      };
    } catch (pdfError) {
      console.error('Error generating PDF:', pdfError);
      // Return the invoice without PDF URL
      return formattedInvoice;
    }
  } catch (error) {
    console.error('Error creating invoice:', error);
    throw error;
  }
}

/**
 * Update an invoice's payment status
 * @param id The invoice ID
 * @param amountPaid The amount paid
 * @returns The updated invoice
 */
export async function updateInvoicePayment(id: string, amountPaid: number): Promise<Invoice | null> {
  try {
    const invoice = await prisma.invoice.findUnique({
      where: { id },
      include: {
        items: true
      }
    });

    if (!invoice) {
      return null;
    }

    const totalAmount = Number(invoice.totalAmount);
    const newAmountPaid = Number(amountPaid);
    const balance = totalAmount - newAmountPaid;

    // Determine invoice status
    let status = 'pending';
    let paidAt = invoice.paidAt;

    if (newAmountPaid >= totalAmount) {
      status = 'paid';
      paidAt = new Date();
    } else if (newAmountPaid > 0) {
      status = 'partial';
    }

    // Update the invoice
    const updatedInvoice = await prisma.invoice.update({
      where: { id },
      data: {
        amountPaid: newAmountPaid,
        balance,
        status,
        paidAt
      },
      include: {
        items: {
          include: {
            service: true
          }
        }
      }
    });

    return formatInvoice(updatedInvoice);
  } catch (error) {
    console.error(`Error updating invoice payment ${id}:`, error);
    throw error;
  }
}

/**
 * Format an invoice from the database
 * @param invoice The invoice from the database
 * @returns The formatted invoice
 */
function formatInvoice(invoice: any): Invoice {
  return {
    id: invoice.id,
    invoiceNumber: invoice.invoiceNumber,
    totalAmount: Number(invoice.totalAmount),
    amountPaid: Number(invoice.amountPaid),
    balance: Number(invoice.balance),
    customerName: invoice.customerName,
    phoneNumber: invoice.phoneNumber,
    email: invoice.email,
    status: invoice.status,
    notes: invoice.notes,
    createdAt: new Date(invoice.createdAt),
    updatedAt: new Date(invoice.updatedAt),
    issuedAt: new Date(invoice.issuedAt),
    dueDate: new Date(invoice.dueDate),
    paidAt: invoice.paidAt ? new Date(invoice.paidAt) : null,
    pdfUrl: invoice.pdfUrl,
    quoteId: invoice.quoteId,
    items: invoice.items.map((item: any) => ({
      id: item.id,
      quantity: item.quantity,
      unitPrice: Number(item.unitPrice),
      totalPrice: Number(item.totalPrice),
      description: item.description,
      serviceId: item.serviceId,
      serviceName: item.service?.name
    }))
  };
}

/**
 * Map an invoice to invoice data for PDF generation
 * @param invoice The invoice
 * @returns The invoice data for PDF generation
 */
function mapToInvoiceData(invoice: Invoice): InvoiceData {
  return {
    invoiceNumber: invoice.invoiceNumber,
    customerName: invoice.customerName,
    phoneNumber: invoice.phoneNumber,
    email: invoice.email || '',
    totalAmount: invoice.totalAmount,
    amountPaid: invoice.amountPaid,
    balance: invoice.balance,
    issuedAt: invoice.issuedAt.toISOString(),
    dueDate: invoice.dueDate.toISOString(),
    status: invoice.status,
    notes: invoice.notes || '',
    items: invoice.items.map(item => ({
      description: item.description || '',
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      totalPrice: item.totalPrice,
      serviceName: item.serviceName || ''
    }))
  };
}
