/**
 * Service for managing quotes
 */
import prisma from '@/lib/prisma';
import { generateQuotePDF, QuoteData, QuoteItemData } from '@/utils/pdfGenerator';
import { getServiceById } from './serviceItemService';
import { createInvoice } from './invoiceService';
import path from 'path';
import { promises as fs } from 'fs';
import { addDays } from 'date-fns';

export interface Quote {
  id: string;
  quoteNumber: string;
  totalAmount: number;
  customerName: string;
  phoneNumber: string;
  email: string | null;
  status: string;
  notes: string | null;
  createdAt: Date;
  updatedAt: Date;
  issuedAt: Date;
  validUntil: Date;
  pdfUrl?: string;
  items: QuoteItem[];
}

export interface QuoteItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  description: string | null;
  serviceId: string;
  serviceName?: string;
}

/**
 * Get all quotes
 * @returns All quotes
 */
export async function getAllQuotes(): Promise<Quote[]> {
  try {
    const quotes = await prisma.quote.findMany({
      include: {
        items: {
          include: {
            service: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return quotes.map(formatQuote);
  } catch (error) {
    console.error('Error getting all quotes:', error);
    throw error;
  }
}

/**
 * Get a quote by ID
 * @param id The quote ID
 * @returns The quote or null if not found
 */
export async function getQuoteById(id: string): Promise<Quote | null> {
  try {
    const quote = await prisma.quote.findUnique({
      where: { id },
      include: {
        items: {
          include: {
            service: true
          }
        }
      }
    });

    if (!quote) {
      return null;
    }

    return formatQuote(quote);
  } catch (error) {
    console.error(`Error getting quote ${id}:`, error);
    throw error;
  }
}

/**
 * Generate a unique quote number
 * @returns A unique quote number
 */
export async function generateQuoteNumber(): Promise<string> {
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');

  let count = 0;
  try {
    // Get the count of quotes for today
    const todayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const todayEnd = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);

    // Try to count quotes, handle potential errors
    try {
      count = await prisma.quote.count({
        where: {
          createdAt: {
            gte: todayStart,
            lt: todayEnd
          }
        }
      });
    } catch (error) {
      console.error('Error counting quotes:', error);
      // If there's an error, assume count is 0
      count = 0;
    }
  } catch (error) {
    console.error('Error in generateQuoteNumber:', error);
    // If there's any error, use a fallback count
    count = 0;
  }

  // Format: QUO-YY-MM-DD-XXX where XXX is a sequential number
  const sequentialNumber = (count + 1).toString().padStart(3, '0');
  return `QUO-${year}${month}${day}-${sequentialNumber}`;
}

/**
 * Create a new quote
 * @param data The quote data
 * @returns The created quote
 */
export async function createQuote(data: {
  customerName: string;
  phoneNumber: string;
  email?: string;
  notes?: string;
  validUntil?: Date;
  items: {
    serviceId: string;
    quantity: number;
    unitPrice?: number;
    description?: string;
  }[];
}): Promise<Quote | null> {
  try {
    // Process items
    const quoteItems = [];
    let totalAmount = 0;

    for (const item of data.items) {
      // Get service details
      const service = await getServiceById(item.serviceId);
      if (!service) {
        throw new Error(`Service ${item.serviceId} not found`);
      }

      // Use provided unit price or service price
      const unitPrice = item.unitPrice !== undefined ? item.unitPrice : Number(service.price);
      const totalPrice = unitPrice * item.quantity;

      // Add to total amount
      totalAmount += totalPrice;

      // Add to quote items
      quoteItems.push({
        serviceId: service.id,
        quantity: item.quantity,
        unitPrice,
        totalPrice,
        description: item.description || service.description
      });
    }

    // Generate quote number
    const quoteNumber = await generateQuoteNumber();

    // Set valid until date (default to 30 days from now if not provided)
    const validUntil = data.validUntil || addDays(new Date(), 30);

    // Create the quote
    const quote = await prisma.quote.create({
      data: {
        quoteNumber,
        totalAmount,
        customerName: data.customerName,
        phoneNumber: data.phoneNumber,
        email: data.email || null,
        status: 'pending',
        notes: data.notes || null,
        issuedAt: new Date(),
        validUntil,
        items: {
          create: quoteItems
        }
      },
      include: {
        items: true
      }
    });

    // Format the quote
    const formattedQuote = formatQuote(quote);

    // Generate PDF file
    try {
      // Convert quote to quote data for PDF generation
      const quoteData = mapToQuoteData(formattedQuote);

      // Generate the PDF file
      const pdfUrl = await generateQuotePDF(quoteData);

      // Update the quote with the PDF URL
      await prisma.quote.update({
        where: { id: quote.id },
        data: { pdfUrl }
      });

      // Return the quote with PDF URL
      return {
        ...formattedQuote,
        pdfUrl
      };
    } catch (pdfError) {
      console.error('Error generating PDF:', pdfError);
      // Return the quote without PDF URL
      return formattedQuote;
    }
  } catch (error) {
    console.error('Error creating quote:', error);
    throw error;
  }
}

/**
 * Update a quote's status
 * @param id The quote ID
 * @param status The new status
 * @returns The updated quote
 */
export async function updateQuoteStatus(id: string, status: string): Promise<Quote | null> {
  try {
    const quote = await prisma.quote.findUnique({
      where: { id },
      include: {
        items: true
      }
    });

    if (!quote) {
      return null;
    }

    // Update the quote
    const updatedQuote = await prisma.quote.update({
      where: { id },
      data: {
        status
      },
      include: {
        items: {
          include: {
            service: true
          }
        }
      }
    });

    return formatQuote(updatedQuote);
  } catch (error) {
    console.error(`Error updating quote status ${id}:`, error);
    throw error;
  }
}

/**
 * Convert a quote to an invoice
 * @param quoteId The quote ID
 * @returns The created invoice
 */
export async function convertQuoteToInvoice(quoteId: string): Promise<any> {
  try {
    const quote = await getQuoteById(quoteId);

    if (!quote) {
      throw new Error(`Quote ${quoteId} not found`);
    }

    // Check if quote is already converted to an invoice
    const existingInvoice = await prisma.invoice.findUnique({
      where: {
        quoteId
      }
    });

    if (existingInvoice) {
      throw new Error(`Quote ${quoteId} is already converted to invoice ${existingInvoice.invoiceNumber}`);
    }

    // Convert quote to invoice
    const invoice = await createInvoice({
      customerName: quote.customerName,
      phoneNumber: quote.phoneNumber,
      email: quote.email || undefined,
      notes: quote.notes || undefined,
      items: quote.items.map(item => ({
        serviceId: item.serviceId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        description: item.description || undefined
      })),
      quoteId: quote.id
    });

    // Update quote status to 'accepted'
    await updateQuoteStatus(quoteId, 'accepted');

    return invoice;
  } catch (error) {
    console.error(`Error converting quote ${quoteId} to invoice:`, error);
    throw error;
  }
}

/**
 * Format a quote from the database
 * @param quote The quote from the database
 * @returns The formatted quote
 */
function formatQuote(quote: any): Quote {
  return {
    id: quote.id,
    quoteNumber: quote.quoteNumber,
    totalAmount: Number(quote.totalAmount),
    customerName: quote.customerName,
    phoneNumber: quote.phoneNumber,
    email: quote.email,
    status: quote.status,
    notes: quote.notes,
    createdAt: new Date(quote.createdAt),
    updatedAt: new Date(quote.updatedAt),
    issuedAt: new Date(quote.issuedAt),
    validUntil: new Date(quote.validUntil),
    pdfUrl: quote.pdfUrl,
    items: quote.items.map((item: any) => ({
      id: item.id,
      quantity: item.quantity,
      unitPrice: Number(item.unitPrice),
      totalPrice: Number(item.totalPrice),
      description: item.description,
      serviceId: item.serviceId,
      serviceName: item.service?.name
    }))
  };
}

/**
 * Map a quote to quote data for PDF generation
 * @param quote The quote
 * @returns The quote data for PDF generation
 */
function mapToQuoteData(quote: Quote): QuoteData {
  return {
    quoteNumber: quote.quoteNumber,
    customerName: quote.customerName,
    phoneNumber: quote.phoneNumber,
    email: quote.email || '',
    totalAmount: quote.totalAmount,
    issuedAt: quote.issuedAt.toISOString(),
    validUntil: quote.validUntil.toISOString(),
    status: quote.status,
    notes: quote.notes || '',
    items: quote.items.map(item => ({
      description: item.description || '',
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      totalPrice: item.totalPrice,
      serviceName: item.serviceName || ''
    }))
  };
}
