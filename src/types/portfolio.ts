export interface PortfolioItem {
  id: string;
  title: string;
  description?: string;
  category: string;
  imageSrc: string;
  alt?: string;
  featured?: boolean;
  createdAt: string;
  updatedAt: string;
  url?: string; // For website portfolio items
}

export type PortfolioCategory =
  | 'logos'
  | 'branding'
  | 'fliers'
  | 'cards'
  | 'letterheads'
  | 'profiles'
  | 'websites'
  | 'other';

export const PORTFOLIO_CATEGORIES: { value: PortfolioCategory; label: string }[] = [
  { value: 'logos', label: 'Logos' },
  { value: 'branding', label: 'Branding' },
  { value: 'fliers', label: 'Fliers' },
  { value: 'cards', label: 'Business Cards' },
  { value: 'letterheads', label: 'Letterheads' },
  { value: 'profiles', label: 'Company Profiles' },
  { value: 'websites', label: 'Websites' },
  { value: 'other', label: 'Other' }
];

export const WEBSITE_CATEGORIES: { value: string; label: string }[] = [
  { value: 'e-commerce', label: 'E-commerce' },
  { value: 'corporate', label: 'Corporate' },
  { value: 'portfolio', label: 'Portfolio' },
  { value: 'blog', label: 'Blog' },
  { value: 'educational', label: 'Educational' },
  { value: 'nonprofit', label: 'Non-profit' },
  { value: 'logistics', label: 'Logistics & Transportation' },
  { value: 'security', label: 'Security Services' },
  { value: 'automotive', label: 'Automotive' },
  { value: 'food', label: 'Food & Restaurant' },
  { value: 'other', label: 'Other' }
];

export interface PortfolioFormData {
  title: string;
  description: string;
  category: PortfolioCategory;
  image: File | null;
  alt: string;
  featured: boolean;
  url?: string; // For website portfolio items
}

export interface WebsitePortfolioItem {
  id: string;
  title: string;
  description: string;
  imageSrc: string;
  url: string;
  category: string; // Type of website (e-commerce, corporate, etc.)
  featured?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface WebsitePortfolioFormData {
  title: string;
  description: string;
  category: string;
  image: File | null;
  url: string;
  featured: boolean;
}
