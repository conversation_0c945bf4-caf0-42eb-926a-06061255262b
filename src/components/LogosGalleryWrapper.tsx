'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import type { ImageItem } from '@/utils/getImages';

interface LogosGalleryWrapperProps {
  logos?: ImageItem[];
}

export default function LogosGalleryWrapper({ logos = [] }: LogosGalleryWrapperProps) {
  const [isClient, setIsClient] = useState(false);
  const [selectedImage, setSelectedImage] = useState<ImageItem | null>(null);

  // Initialize on the client side only
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Don't render anything on the server side
  if (!isClient) {
    return (
      <div className="w-full text-center py-12">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
      </div>
    );
  }

  // Ensure logos is always an array with required properties
  const safeLogos = Array.isArray(logos) ? logos.filter(logo =>
    logo && typeof logo === 'object' && (
      (logo.url && typeof logo.url === 'string') ||
      (logo.src && typeof logo.src === 'string')
    )
  ) : [];

  if (!safeLogos.length) {
    return (
      <div className="text-center py-12 text-gray-500">
        <p>No logo examples available at the moment.</p>
        <p className="mt-2 text-sm">Please check back later or contact us to see our portfolio.</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 md:gap-6 lg:gap-8">
        {safeLogos.map((item) => (
          <div
            key={item.id}
            className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer transition-transform hover:scale-105"
            onClick={() => setSelectedImage(item)}
          >
            <Image
              src={item.url || item.src}
              alt={item.alt || `Logo design example`}
              fill
              className="object-contain hover:opacity-90 transition-opacity duration-300"
              sizes="(max-width: 640px) 50vw, (max-width: 768px) 50vw, 33vw"
              loading="lazy"
            />
          </div>
        ))}
      </div>

      {/* Modal for selected image */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-4xl w-full h-auto">
            <Image
              src={selectedImage.url || selectedImage.src}
              alt={selectedImage.alt || 'Selected logo design'}
              width={1200}
              height={800}
              className="w-full h-auto rounded-lg"
              priority
            />
            <button
              className="absolute top-4 right-4 text-white bg-black bg-opacity-60 rounded-full p-2 hover:bg-opacity-80 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedImage(null);
              }}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}