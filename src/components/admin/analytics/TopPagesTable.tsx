'use client';

import { useState, useEffect } from 'react';
import { ArrowPathIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import type { TopPage } from '@/services/googleAnalyticsService';

export default function TopPagesTable() {
  const [topPages, setTopPages] = useState<TopPage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchTopPages = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`${window.location.origin}/api/admin/analytics/top-pages`);

      if (!response.ok) {
        throw new Error('Failed to fetch top pages data');
      }

      const data = await response.json();
      setTopPages(data);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching top pages:', err);
      setError('Failed to load top pages data');
      toast.error('Failed to load top pages data');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchTopPages();
  }, []);

  // Handle refresh
  const handleRefresh = () => {
    fetchTopPages();
  };

  // Render loading state
  if (isLoading && topPages.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border border-slate-100 animate-pulse">
        <div className="flex justify-between items-center mb-6">
          <div className="h-6 w-40 bg-slate-200 rounded"></div>
          <div className="h-8 w-8 bg-slate-200 rounded-full"></div>
        </div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-12 bg-slate-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  // Render error state
  if (error && topPages.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6 text-center">
        <p className="text-red-500 mb-4">{error}</p>
        <button
          onClick={handleRefresh}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-slate-100">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-slate-800">Top Pages</h2>
        <div className="flex items-center space-x-2">
          {topPages.length > 0 && (
            <div className="flex items-center">
              <span
                className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full mr-2 ${
                  topPages[0].isRealData
                    ? 'bg-green-100 text-green-800'
                    : 'bg-amber-100 text-amber-800'
                }`}
              >
                {topPages[0].isRealData ? 'Real Data' : 'Mock Data'}
              </span>
            </div>
          )}
          {lastUpdated && (
            <span className="text-xs text-slate-500">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </span>
          )}
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100 transition-colors"
            aria-label="Refresh top pages"
          >
            <ArrowPathIcon className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-slate-200">
          <thead className="bg-slate-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                Page
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                Views
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                Unique Views
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                Avg. Time
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                Bounce Rate
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-slate-200">
            {topPages.length > 0 ? (
              topPages.map((page) => (
                <tr key={page.path} className="hover:bg-slate-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">
                    {page.path}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                    {page.pageViews.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                    {page.uniquePageViews.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                    {page.avgTimeOnPage}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                    {page.bounceRate}%
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="px-6 py-4 text-center text-sm text-slate-500">
                  No page data available
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
