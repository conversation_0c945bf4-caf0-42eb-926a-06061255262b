'use client';

import { useState, useEffect } from 'react';
import { ArrowPathIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import type { PageViewData } from '@/services/googleAnalyticsService';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface PageViewsChartProps {
  days?: number;
}

export default function PageViewsChart({ days = 30 }: PageViewsChartProps) {
  const [pageViews, setPageViews] = useState<PageViewData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [selectedDays, setSelectedDays] = useState(days);

  const fetchPageViews = async (numDays: number) => {
    try {
      setIsLoading(true);
      setError(null);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout for chart data

      const response = await fetch(`${window.location.origin}/api/admin/analytics/page-views?days=${numDays}`, {
        signal: controller.signal,
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch page views data`);
      }

      const data = await response.json();

      // Validate data structure
      if (!Array.isArray(data)) {
        throw new Error('Invalid data format received from page views API');
      }

      setPageViews(data);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching page views:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load page views data';
      setError(errorMessage);

      // Only show toast for non-abort errors
      if (!(err instanceof Error && err.name === 'AbortError')) {
        toast.error(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchPageViews(selectedDays);
  }, [selectedDays]);

  // Handle refresh
  const handleRefresh = () => {
    fetchPageViews(selectedDays);
  };

  // Handle time range change
  const handleTimeRangeChange = (newDays: number) => {
    setSelectedDays(newDays);
  };

  // Format dates for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  // Prepare chart data
  const chartData = {
    labels: pageViews.map(item => formatDate(item.date)),
    datasets: [
      {
        label: 'Page Views',
        data: pageViews.map(item => item.views),
        borderColor: 'rgba(54, 162, 235, 1)',
        backgroundColor: 'rgba(54, 162, 235, 0.1)',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointRadius: 3,
        pointBackgroundColor: 'rgba(54, 162, 235, 1)',
        pointBorderColor: '#fff',
        pointBorderWidth: 1,
        pointHoverRadius: 5,
        pointHoverBackgroundColor: 'rgba(54, 162, 235, 1)',
        pointHoverBorderColor: '#fff',
        pointHoverBorderWidth: 2,
      },
    ],
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
        callbacks: {
          label: function(context: any) {
            const label = context.dataset.label || '';
            const value = context.raw || 0;
            return `${label}: ${value.toLocaleString()}`;
          }
        }
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          maxRotation: 45,
          minRotation: 45,
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          callback: function(value: any) {
            return value.toLocaleString();
          }
        }
      },
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false,
    },
  };

  // Render loading state
  if (isLoading && pageViews.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border border-slate-100 animate-pulse">
        <div className="flex justify-between items-center mb-6">
          <div className="h-6 w-40 bg-slate-200 rounded"></div>
          <div className="h-8 w-32 bg-slate-200 rounded"></div>
        </div>
        <div className="h-64 bg-slate-200 rounded"></div>
      </div>
    );
  }

  // Render error state
  if (error && pageViews.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6 text-center">
        <p className="text-red-500 mb-4">{error}</p>
        <button
          onClick={handleRefresh}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-slate-100">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <h2 className="text-xl font-semibold text-slate-800">Page Views Trend</h2>
        <div className="flex items-center space-x-2">
          <div className="flex rounded-md shadow-sm" role="group">
            <button
              type="button"
              onClick={() => handleTimeRangeChange(7)}
              className={`px-3 py-1.5 text-xs font-medium rounded-l-lg ${
                selectedDays === 7
                  ? 'bg-blue-500 text-white'
                  : 'bg-white text-slate-700 hover:bg-slate-100'
              }`}
            >
              7 Days
            </button>
            <button
              type="button"
              onClick={() => handleTimeRangeChange(30)}
              className={`px-3 py-1.5 text-xs font-medium ${
                selectedDays === 30
                  ? 'bg-blue-500 text-white'
                  : 'bg-white text-slate-700 hover:bg-slate-100'
              }`}
            >
              30 Days
            </button>
            <button
              type="button"
              onClick={() => handleTimeRangeChange(90)}
              className={`px-3 py-1.5 text-xs font-medium rounded-r-lg ${
                selectedDays === 90
                  ? 'bg-blue-500 text-white'
                  : 'bg-white text-slate-700 hover:bg-slate-100'
              }`}
            >
              90 Days
            </button>
          </div>
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100 transition-colors"
            aria-label="Refresh page views"
          >
            <ArrowPathIcon className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      <div className="h-64 md:h-80">
        {pageViews.length > 0 ? (
          <Line data={chartData} options={chartOptions} />
        ) : (
          <div className="flex items-center justify-center h-full">
            <p className="text-slate-500">No page view data available</p>
          </div>
        )}
      </div>

      {lastUpdated && (
        <div className="mt-4 text-right">
          <span className="text-xs text-slate-500">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </span>
        </div>
      )}
    </div>
  );
}
