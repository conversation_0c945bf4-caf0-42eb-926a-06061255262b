'use client';

import { useState, useEffect } from 'react';
import { ArrowPathIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import { Doughnut } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import type { DeviceData } from '@/services/googleAnalyticsService';

// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

// Define colors for the chart
const chartColors = [
  'rgba(54, 162, 235, 0.8)',
  'rgba(255, 99, 132, 0.8)',
  'rgba(255, 206, 86, 0.8)',
];

export default function DeviceChart() {
  const [deviceData, setDeviceData] = useState<DeviceData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchDeviceData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`${window.location.origin}/api/admin/analytics/devices`);

      if (!response.ok) {
        throw new Error('Failed to fetch device data');
      }

      const data = await response.json();
      setDeviceData(data);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching device data:', err);
      setError('Failed to load device data');
      toast.error('Failed to load device data');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchDeviceData();
  }, []);

  // Handle refresh
  const handleRefresh = () => {
    fetchDeviceData();
  };

  // Prepare chart data
  const chartData = {
    labels: deviceData.map(item => item.device),
    datasets: [
      {
        data: deviceData.map(item => item.sessions),
        backgroundColor: chartColors.slice(0, deviceData.length),
        borderColor: chartColors.map(color => color.replace('0.8', '1')),
        borderWidth: 1,
      },
    ],
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          boxWidth: 15,
          padding: 15,
          font: {
            size: 12,
          },
        },
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.raw || 0;
            const dataset = context.dataset;
            const total = dataset.data.reduce((acc: number, data: number) => acc + data, 0);
            const percentage = Math.round((value / total) * 100);
            return `${label}: ${value.toLocaleString()} sessions (${percentage}%)`;
          }
        }
      }
    },
  };

  // Render loading state
  if (isLoading && deviceData.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border border-slate-100 animate-pulse">
        <div className="flex justify-between items-center mb-6">
          <div className="h-6 w-40 bg-slate-200 rounded"></div>
          <div className="h-8 w-8 bg-slate-200 rounded-full"></div>
        </div>
        <div className="h-64 bg-slate-200 rounded"></div>
      </div>
    );
  }

  // Render error state
  if (error && deviceData.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6 text-center">
        <p className="text-red-500 mb-4">{error}</p>
        <button
          onClick={handleRefresh}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-slate-100">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-slate-800">Device Usage</h2>
        <div className="flex items-center space-x-2">
          {lastUpdated && (
            <span className="text-xs text-slate-500">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </span>
          )}
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100 transition-colors"
            aria-label="Refresh device data"
          >
            <ArrowPathIcon className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      <div className="h-64">
        {deviceData.length > 0 ? (
          <Doughnut data={chartData} options={chartOptions} />
        ) : (
          <div className="flex items-center justify-center h-full">
            <p className="text-slate-500">No device data available</p>
          </div>
        )}
      </div>
    </div>
  );
}
