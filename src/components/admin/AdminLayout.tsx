'use client';

import React from 'react';
import AdminLayoutClient from './AdminLayoutClient';

/**
 * AdminLayout component
 * 
 * This is a wrapper component that uses the AdminLayoutClient component
 * to provide a consistent layout for admin pages.
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render within the layout
 */
export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <AdminLayoutClient>{children}</AdminLayoutClient>;
}
