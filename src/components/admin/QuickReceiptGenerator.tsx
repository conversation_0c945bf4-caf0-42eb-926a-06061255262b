'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useNotification } from '@/contexts/NotificationContext';
import { ArrowPathIcon, CheckCircleIcon, XCircleIcon, DocumentTextIcon } from '@heroicons/react/24/outline';

interface Service {
  id: string;
  name: string;
  description: string | null;
  price: number;
  category: string;
}

interface Transaction {
  id: string;
  transactionId: string;
  amount: number;
  customerName: string;
  phoneNumber: string;
  transactionDate: string;
  status: string;
}

interface QuickReceiptGeneratorProps {
  transactionId: string;
}

export default function QuickReceiptGenerator({ transactionId }: QuickReceiptGeneratorProps) {
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [selectedService, setSelectedService] = useState<string>('');
  const [quantity, setQuantity] = useState<number>(1);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDuplicateReceipt, setIsDuplicateReceipt] = useState<boolean>(false);
  const [existingReceiptId, setExistingReceiptId] = useState<string | null>(null);
  const router = useRouter();
  const { showNotification } = useNotification();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch services
        try {
          console.log('Fetching services...');
          const servicesResponse = await fetch('/api/admin/services', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            cache: 'no-store'
          });

          if (!servicesResponse.ok) {
            const errorText = await servicesResponse.text();
            console.error('Services response error:', servicesResponse.status, errorText);
            throw new Error(`Failed to fetch services: ${servicesResponse.status} ${errorText}`);
          }

          const servicesData = await servicesResponse.json();
          console.log(`Fetched ${servicesData?.length || 0} services`);
          setServices(servicesData || []);
        } catch (servicesError) {
          console.error('Error fetching services:', servicesError);
          throw new Error('Failed to fetch services. Please try again.');
        }

        // Fetch transaction
        try {
          console.log(`Fetching transaction with ID: ${transactionId}`);
          const transactionResponse = await fetch(`/api/admin/transactions/${transactionId}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            cache: 'no-store'
          });

          if (!transactionResponse.ok) {
            const errorText = await transactionResponse.text();
            console.error('Transaction response error:', transactionResponse.status, errorText);
            throw new Error(`Failed to fetch transaction: ${transactionResponse.status} ${errorText}`);
          }

          const transactionData = await transactionResponse.json();
          console.log('Transaction data fetched successfully:', transactionData?.id);
          setTransaction(transactionData);
        } catch (transactionError) {
          console.error('Error fetching transaction:', transactionError);
          throw new Error('Failed to fetch transaction. Please try again.');
        }
      } catch (err) {
        console.error('Error in fetchData:', err);
        setError(err instanceof Error ? err.message : 'Failed to load required data');
        showNotification('error', 'Failed to load required data');
      } finally {
        setLoading(false);
      }
    };

    if (transactionId) {
      fetchData();
    } else {
      setError('No transaction ID provided');
    }
  }, [transactionId, showNotification]);

  const handleServiceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedService(e.target.value);
  };

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    setQuantity(value > 0 ? value : 1);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!transaction) {
      setError('No transaction selected');
      return;
    }

    if (!selectedService) {
      setError('Please select a service');
      return;
    }

    try {
      setSubmitting(true);
      setError(null);

      const selectedServiceObj = services.find(s => s.id === selectedService);
      if (!selectedServiceObj) {
        throw new Error('Selected service not found');
      }

      // Prepare the request payload
      const payload = {
        transactionId: transaction.id,
        customerName: transaction.customerName,
        phoneNumber: transaction.phoneNumber,
        items: [{
          serviceId: selectedService,
          quantity: quantity,
          unitPrice: selectedServiceObj.price,
          description: selectedServiceObj.name
        }]
      };

      console.log('Sending receipt creation request with payload:', payload);

      try {
        const response = await fetch('/api/admin/receipts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify(payload),
          cache: 'no-store'
        });

        if (!response.ok) {
          const errorText = await response.text();
          let errorMessage = 'Failed to create receipt';
          let errorDetails = '';

          try {
            const errorData = JSON.parse(errorText);
            errorMessage = errorData.error || errorMessage;
            errorDetails = errorData.details || '';

            console.error('Receipt creation error:', errorData);

            // If this is a duplicate receipt error, provide a more helpful message
            if (response.status === 409 ||
                errorMessage.includes('already exists') ||
                (errorDetails && errorDetails.includes('Unique constraint failed'))) {
              setIsDuplicateReceipt(true);

              // Check if we have a receipt ID in the response
              if (errorData.receiptId) {
                setExistingReceiptId(errorData.receiptId);
                errorMessage = 'A receipt for this transaction already exists';
              } else {
                errorMessage = 'A receipt for this transaction already exists';
              }
            }
          } catch (parseError) {
            console.error('Error parsing error response:', parseError);
            errorMessage = errorText || errorMessage;

            // Check if the raw error text indicates a duplicate receipt
            if (errorText.includes('Unique constraint failed') ||
                errorText.includes('already exists')) {
              setIsDuplicateReceipt(true);
              errorMessage = 'A receipt for this transaction already exists';
            }
          }

          throw new Error(errorMessage);
        }

        const receipt = await response.json();
        showNotification('success', 'Receipt created successfully');

        // Navigate to the receipt page
        router.push(`/admin/receipts/${receipt.id}`);
      } catch (fetchError) {
        console.error('Fetch error:', fetchError);
        throw new Error(fetchError instanceof Error ? fetchError.message : 'Network error while creating receipt');
      }
    } catch (err) {
      console.error('Error creating receipt:', err);

      // If it's not a duplicate receipt error, reset the flag
      if (!isDuplicateReceipt) {
        setIsDuplicateReceipt(false);
      }

      // Set the error message
      const errorMessage = err instanceof Error ? err.message : 'Failed to create receipt';
      setError(errorMessage);

      // Show notification with appropriate message
      const notificationMessage = isDuplicateReceipt
        ? 'This transaction already has a receipt'
        : 'Failed to create receipt';

      showNotification(isDuplicateReceipt ? 'warning' : 'error', notificationMessage);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex justify-center items-center h-40">
          <ArrowPathIcon className="animate-spin h-8 w-8 text-blue-500" />
        </div>
      </div>
    );
  }

  if (!transaction) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="text-center py-8">
          <p className="text-gray-600">Transaction not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Receipt Generator</h2>

      {error && (
        <div className={`${isDuplicateReceipt ? 'bg-yellow-50 border-yellow-400' : 'bg-red-50 border-red-400'} border-l-4 p-4 mb-4`}>
          <div className="flex">
            <div className="flex-shrink-0">
              {isDuplicateReceipt ? (
                <DocumentTextIcon className="h-5 w-5 text-yellow-400" />
              ) : (
                <XCircleIcon className="h-5 w-5 text-red-400" />
              )}
            </div>
            <div className="ml-3">
              <p className={`text-sm ${isDuplicateReceipt ? 'text-yellow-700' : 'text-red-700'}`}>{error}</p>

              {isDuplicateReceipt && (
                <div className="mt-2">
                  <p className="text-sm text-yellow-700">
                    This transaction has already been processed. You can view the receipt directly or check the receipts list.
                  </p>
                  <div className="mt-2 flex flex-wrap gap-3">
                    {existingReceiptId && (
                      <Link
                        href={`/admin/receipts/${existingReceiptId}`}
                        className="inline-flex items-center text-sm font-medium text-yellow-700 hover:text-yellow-900"
                      >
                        <DocumentTextIcon className="-ml-0.5 mr-1 h-4 w-4" />
                        View This Receipt
                      </Link>
                    )}
                    <Link
                      href="/admin/receipts"
                      className="inline-flex items-center text-sm font-medium text-yellow-700 hover:text-yellow-900"
                    >
                      <DocumentTextIcon className="-ml-0.5 mr-1 h-4 w-4" />
                      View All Receipts
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h3 className="text-sm font-medium text-blue-800 mb-2">Transaction Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-600">Transaction ID:</p>
            <p className="font-medium">{transaction.transactionId}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Amount:</p>
            <p className="font-medium">KES {transaction.amount.toLocaleString()}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Customer:</p>
            <p className="font-medium">{transaction.customerName}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Phone:</p>
            <p className="font-medium">{transaction.phoneNumber}</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-1">
            Select Service
          </label>
          <select
            id="service"
            value={selectedService}
            onChange={handleServiceChange}
            className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            required
          >
            <option value="">Select a service</option>
            {services.map((service) => (
              <option key={service.id} value={service.id}>
                {service.name} - KES {service.price.toLocaleString()}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">
            Quantity
          </label>
          <input
            type="number"
            id="quantity"
            value={quantity}
            onChange={handleQuantityChange}
            min="1"
            className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            required
          />
        </div>

        <div className="pt-4">
          <button
            type="submit"
            disabled={submitting || !selectedService}
            className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {submitting ? (
              <>
                <ArrowPathIcon className="animate-spin -ml-1 mr-2 h-4 w-4" />
                Creating Receipt...
              </>
            ) : (
              <>
                <CheckCircleIcon className="-ml-1 mr-2 h-4 w-4" />
                Generate Receipt
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
