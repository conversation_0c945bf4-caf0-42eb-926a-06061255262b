'use client';

import { useState } from 'react';
import { toast } from 'react-hot-toast';
import imageCompression from 'browser-image-compression';

interface Props {
  onUploadSuccess: () => void;
}

export default function ImageUploadForm({ onUploadSuccess }: Props) {
  const [file, setFile] = useState<File | null>(null);
  const [category, setCategory] = useState('logos');
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState(0);

  const compressImage = async (file: File) => {
    // Adjust compression settings based on file size and type
    const isLargeFile = file.size > 5 * 1024 * 1024; // 5MB
    const isPNG = file.type === 'image/png';

    const options = {
      // Target size varies based on original file size
      maxSizeMB: isLargeFile ? 1 : 2,
      // Limit dimensions while preserving aspect ratio
      maxWidthOrHeight: isLargeFile ? 1600 : 1920,
      // PNG files need special handling to maintain transparency
      useWebWorker: true,
      // PNG compression is less aggressive to maintain quality
      initialQuality: isPNG ? 0.8 : 0.7,
      // Show compression progress
      onProgress: (p: number) => setProgress(Math.round(p * 100))
    };

    try {
      console.log(`Compressing image: ${file.name}, size: ${Math.round(file.size/1024)}KB, type: ${file.type}`);
      const compressedFile = await imageCompression(file, options);

      // Log compression results
      const compressionRatio = (file.size / compressedFile.size).toFixed(2);
      console.log(`Compression complete: ${Math.round(compressedFile.size/1024)}KB (${compressionRatio}x reduction)`);

      // Preserve original filename but use compressed data
      return new File([compressedFile], file.name, {
        type: compressedFile.type
      });
    } catch (error) {
      console.error('Compression error:', error);
      toast.error('Image compression failed. Please try a different image.');
      throw new Error('Image compression failed');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) return;

    setIsUploading(true);
    setProgress(0);

    try {
      // Validate file size before attempting upload
      if (file.size > 10 * 1024 * 1024) { // 10MB
        toast.error('File size exceeds 10MB limit. Please select a smaller image.');
        setIsUploading(false);
        return;
      }

      // Validate file type
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        toast.error('Invalid file type. Please upload a JPEG, PNG, GIF, or WebP image.');
        setIsUploading(false);
        return;
      }

      // Compress image if it's larger than 800KB
      let processedFile = file;
      if (file.size > 800 * 1024) {
        toast.loading('Compressing image to optimize upload...');
        processedFile = await compressImage(file);
        toast.dismiss();
      }

      const formData = new FormData();
      formData.append('file', processedFile);
      formData.append('category', category);

      const response = await fetch('/api/admin/upload', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        const errorMessage = errorData.error || errorData.message || 'Upload failed';
        throw new Error(errorMessage);
      }

      const result = await response.json();

      // Check for warnings in the response
      if (result.warnings && result.warnings.length > 0) {
        console.warn('Upload warnings:', result.warnings);
        toast.success('Image uploaded with some warnings');
      } else {
        toast.success('Image uploaded successfully');
      }

      setFile(null);
      setProgress(0);
      onUploadSuccess();
    } catch (error) {
      console.error('Upload error:', error);

      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('timeout')) {
          toast.error('Upload timed out. Please try a smaller image or check your connection.');
        } else if (error.message.includes('network')) {
          toast.error('Network error. Please check your connection and try again.');
        } else {
          toast.error(error.message);
        }
      } else {
        toast.error('Failed to upload image. Please try again.');
      }
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 max-w-md">
      <div>
        <label className="block mb-2 font-medium">Category:</label>
        <select
          value={category}
          onChange={(e) => setCategory(e.target.value)}
          className="w-full p-2 border rounded focus:ring-2 focus:ring-primary focus:border-transparent"
          disabled={isUploading}
        >
          <optgroup label="Portfolio">
            <option value="logos">Logos</option>
            <option value="cards">Business Cards</option>
            <option value="letterheads">Letterheads</option>
            <option value="fliers">Fliers</option>
            <option value="profiles">Company Profiles</option>
            <option value="websites">Websites</option>
            <option value="branding">Branding</option>
          </optgroup>
          <optgroup label="Content">
            <option value="about">About Page</option>
            <option value="services">Services</option>
            <option value="testimonials">Testimonials</option>
            <option value="hero">Hero Images</option>
            <option value="projects">Projects</option>
          </optgroup>
        </select>
      </div>

      <div>
        <label className="block mb-2 font-medium">Image:</label>
        <input
          type="file"
          accept="image/*"
          onChange={(e) => setFile(e.target.files?.[0] || null)}
          className="w-full p-2 border rounded focus:ring-2 focus:ring-primary focus:border-transparent"
          disabled={isUploading}
        />
      </div>

      {isUploading && progress > 0 && (
        <div className="w-full bg-gray-200 rounded-full h-2.5">
          <div
            className="bg-primary h-2.5 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      )}

      <button
        type="submit"
        disabled={!file || isUploading}
        className="w-full px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        {isUploading ? `Uploading... ${progress}%` : 'Upload Image'}
      </button>
    </form>
  );
}