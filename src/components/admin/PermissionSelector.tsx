'use client';

import { useState } from 'react';
import { CheckIcon } from '@heroicons/react/24/outline';

interface PermissionSelectorProps {
  selectedPermissions: string[];
  onChange: (permissions: string[]) => void;
  disabled?: boolean;
}

// Define permission groups
const permissionGroups = [
  {
    name: 'Dashboard',
    permissions: [
      { id: 'dashboard:read', name: 'View Dashboard' },
    ]
  },
  {
    name: 'Blog',
    permissions: [
      { id: 'blog:read', name: 'View Blog Posts' },
      { id: 'blog:write', name: 'Create/Edit Blog Posts' },
    ]
  },
  {
    name: 'Portfolio',
    permissions: [
      { id: 'portfolio:read', name: 'View Portfolio' },
      { id: 'portfolio:write', name: 'Create/Edit Portfolio' },
    ]
  },
  {
    name: 'Team',
    permissions: [
      { id: 'team:read', name: 'View Team Members' },
      { id: 'team:write', name: 'Create/Edit Team Members' },
    ]
  },
  {
    name: 'Receipting',
    permissions: [
      { id: 'transactions:read', name: 'View Transactions' },
      { id: 'receipts:read', name: 'View Receipts' },
      { id: 'receipts:write', name: 'Create/Edit Receipts' },
      { id: 'invoices:read', name: 'View Invoices' },
      { id: 'invoices:write', name: 'Create/Edit Invoices' },
      { id: 'quotes:read', name: 'View Quotes' },
      { id: 'quotes:write', name: 'Create/Edit Quotes' },
      { id: 'services:read', name: 'View Services' },
      { id: 'services:write', name: 'Create/Edit Services' },
    ]
  },
  {
    name: 'Website Configuration',
    permissions: [
      { id: 'pricing:read', name: 'View Pricing' },
      { id: 'pricing:write', name: 'Create/Edit Pricing' },
      { id: 'database:read', name: 'View Database' },
      { id: 'database:write', name: 'Manage Database' },
      { id: 'categories:read', name: 'View Categories' },
      { id: 'categories:write', name: 'Create/Edit Categories' },
      { id: 'tags:read', name: 'View Tags' },
      { id: 'tags:write', name: 'Create/Edit Tags' },
      { id: 'authors:read', name: 'View Authors' },
      { id: 'authors:write', name: 'Create/Edit Authors' },
    ]
  },
  {
    name: 'User Management',
    permissions: [
      { id: 'users:read', name: 'View Users' },
      { id: 'users:write', name: 'Create/Edit Users' },
      { id: 'roles:read', name: 'View Roles' },
      { id: 'roles:write', name: 'Create/Edit Roles' },
      { id: 'logs:read', name: 'View Activity Logs' },
    ]
  },
  {
    name: 'Settings',
    permissions: [
      { id: 'settings:read', name: 'View Settings' },
      { id: 'settings:write', name: 'Edit Settings' },
    ]
  },
  {
    name: 'Special',
    permissions: [
      { id: '*', name: 'All Permissions (Admin)' },
    ]
  }
];

export default function PermissionSelector({ selectedPermissions, onChange, disabled = false }: PermissionSelectorProps) {
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});

  const toggleGroup = (groupName: string) => {
    if (disabled) return;
    
    setExpandedGroups(prev => ({
      ...prev,
      [groupName]: !prev[groupName]
    }));
  };

  const togglePermission = (permissionId: string) => {
    if (disabled) return;
    
    // If selecting the wildcard permission, clear all others
    if (permissionId === '*') {
      onChange(['*']);
      return;
    }
    
    // If a specific permission is selected and wildcard was previously selected, remove wildcard
    let newPermissions = [...selectedPermissions];
    if (selectedPermissions.includes('*')) {
      newPermissions = [];
    }
    
    // Toggle the permission
    if (newPermissions.includes(permissionId)) {
      newPermissions = newPermissions.filter(id => id !== permissionId);
    } else {
      newPermissions.push(permissionId);
    }
    
    onChange(newPermissions);
  };

  const toggleGroupPermissions = (groupPermissions: { id: string, name: string }[]) => {
    if (disabled) return;
    
    // Check if all permissions in the group are already selected
    const permissionIds = groupPermissions.map(p => p.id);
    const allSelected = permissionIds.every(id => selectedPermissions.includes(id));
    
    let newPermissions = [...selectedPermissions];
    
    // Remove wildcard if it exists
    if (newPermissions.includes('*')) {
      newPermissions = newPermissions.filter(id => id !== '*');
    }
    
    if (allSelected) {
      // Remove all permissions in this group
      newPermissions = newPermissions.filter(id => !permissionIds.includes(id));
    } else {
      // Add all permissions in this group that aren't already selected
      permissionIds.forEach(id => {
        if (!newPermissions.includes(id)) {
          newPermissions.push(id);
        }
      });
    }
    
    onChange(newPermissions);
  };

  return (
    <div className={`border border-gray-300 rounded-md overflow-hidden ${disabled ? 'opacity-75' : ''}`}>
      {permissionGroups.map((group) => (
        <div key={group.name} className="border-b border-gray-300 last:border-b-0">
          <div 
            className="flex items-center justify-between p-3 bg-gray-50 cursor-pointer hover:bg-gray-100"
            onClick={() => toggleGroup(group.name)}
          >
            <div className="flex items-center">
              <div 
                className={`w-5 h-5 mr-3 border rounded flex items-center justify-center ${
                  group.permissions.every(p => selectedPermissions.includes(p.id) || selectedPermissions.includes('*'))
                    ? 'bg-orange-500 border-orange-500 text-white'
                    : group.permissions.some(p => selectedPermissions.includes(p.id))
                    ? 'bg-orange-200 border-orange-300'
                    : 'border-gray-300'
                }`}
                onClick={(e) => {
                  e.stopPropagation();
                  if (group.name !== 'Special') {
                    toggleGroupPermissions(group.permissions);
                  }
                }}
              >
                {group.permissions.every(p => selectedPermissions.includes(p.id) || selectedPermissions.includes('*')) && (
                  <CheckIcon className="h-3 w-3" />
                )}
              </div>
              <span className="font-medium">{group.name}</span>
            </div>
            <svg 
              className={`h-5 w-5 text-gray-500 transform transition-transform ${expandedGroups[group.name] ? 'rotate-180' : ''}`} 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
          
          {expandedGroups[group.name] && (
            <div className="p-3 bg-white">
              <div className="space-y-2">
                {group.permissions.map((permission) => (
                  <div key={permission.id} className="flex items-center">
                    <div 
                      className={`w-5 h-5 mr-3 border rounded flex items-center justify-center cursor-pointer ${
                        selectedPermissions.includes(permission.id) || selectedPermissions.includes('*')
                          ? 'bg-orange-500 border-orange-500 text-white'
                          : 'border-gray-300 hover:border-orange-300'
                      }`}
                      onClick={() => togglePermission(permission.id)}
                    >
                      {(selectedPermissions.includes(permission.id) || selectedPermissions.includes('*')) && (
                        <CheckIcon className="h-3 w-3" />
                      )}
                    </div>
                    <span className="text-sm">{permission.name}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
