'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  HeartIcon,
  HomeIcon,
  ShieldCheckIcon,
  DocumentTextIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline';

export default function AdminFooter() {
  const [currentYear, setCurrentYear] = useState<number>(new Date().getFullYear());

  useEffect(() => {
    setCurrentYear(new Date().getFullYear());
  }, []);

  return (
    <footer className="bg-white border-t border-[#0A1929]/10 py-4 px-4 w-full">
      <div className="max-w-[1600px] mx-auto">
        {/* Mobile view - stacked layout */}
        <div className="md:hidden">
          <div className="flex flex-col items-center space-y-4">
            {/* Links */}
            <div className="grid grid-cols-2 gap-3 w-full max-w-xs">
              <Link
                href="/"
                className="flex items-center justify-center p-2 text-[#0A1929] hover:text-[#FF5400] text-sm bg-gray-50 rounded-md transition-colors"
                target="_blank"
              >
                <HomeIcon className="h-4 w-4 mr-2" />
                <span>Main Site</span>
              </Link>
              <Link
                href="/privacy"
                className="flex items-center justify-center p-2 text-[#0A1929] hover:text-[#FF5400] text-sm bg-gray-50 rounded-md transition-colors"
                target="_blank"
              >
                <ShieldCheckIcon className="h-4 w-4 mr-2" />
                <span>Privacy</span>
              </Link>
              <Link
                href="/terms"
                className="flex items-center justify-center p-2 text-[#0A1929] hover:text-[#FF5400] text-sm bg-gray-50 rounded-md transition-colors"
                target="_blank"
              >
                <DocumentTextIcon className="h-4 w-4 mr-2" />
                <span>Terms</span>
              </Link>
              <Link
                href="mailto:<EMAIL>"
                className="flex items-center justify-center p-2 text-[#0A1929] hover:text-[#FF5400] text-sm bg-gray-50 rounded-md transition-colors"
              >
                <EnvelopeIcon className="h-4 w-4 mr-2" />
                <span>Contact</span>
              </Link>
            </div>

            {/* Copyright */}
            <div className="text-gray-500 text-xs text-center">
              © {currentYear} Mocky Digital. All rights reserved.
            </div>

            {/* Made with love */}
            <div className="text-gray-500 text-xs flex items-center">
              <span>Made with</span>
              <HeartIcon className="h-4 w-4 text-[#FF5400] mx-1" />
              <span>by Mocky Digital</span>
            </div>
          </div>
        </div>

        {/* Desktop view - horizontal layout */}
        <div className="hidden md:flex md:flex-row md:justify-between md:items-center">
          {/* Copyright */}
          <div className="text-gray-500 text-sm">
            © {currentYear} Mocky Digital. All rights reserved.
          </div>

          {/* Links */}
          <div className="flex items-center gap-4 text-sm">
            <Link
              href="/"
              className="text-[#0A1929] hover:text-[#FF5400] transition-colors flex items-center"
              target="_blank"
            >
              <HomeIcon className="h-4 w-4 mr-1" />
              <span>Main Site</span>
            </Link>
            <span className="text-gray-300">|</span>
            <Link
              href="/privacy"
              className="text-[#0A1929] hover:text-[#FF5400] transition-colors"
              target="_blank"
            >
              Privacy Policy
            </Link>
            <span className="text-gray-300">|</span>
            <Link
              href="/terms"
              className="text-[#0A1929] hover:text-[#FF5400] transition-colors"
              target="_blank"
            >
              Terms of Service
            </Link>
            <span className="text-gray-300">|</span>
            <Link
              href="mailto:<EMAIL>"
              className="text-[#0A1929] hover:text-[#FF5400] transition-colors"
            >
              Contact
            </Link>
          </div>

          {/* Made with love */}
          <div className="text-gray-500 text-xs flex items-center">
            <span>Made with</span>
            <HeartIcon className="h-4 w-4 text-[#FF5400] mx-1" />
            <span>by Mocky Digital</span>
          </div>
        </div>
      </div>
    </footer>
  );
}
