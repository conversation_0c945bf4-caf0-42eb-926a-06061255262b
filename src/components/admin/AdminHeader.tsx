'use client';

import { Fragment, useEffect, useState } from 'react';
import { Menu, Transition } from '@headlessui/react';
import {
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
  BellIcon,
  Bars3Icon,
  XMarkIcon,
  HomeIcon,
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { signOut } from 'next-auth/react';

interface AdminHeaderProps {
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
}

export default function AdminHeader({ sidebarOpen, setSidebarOpen }: AdminHeaderProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleLogout = async () => {
    await signOut({ redirect: true, callbackUrl: '/admin/login' });
  };

  if (!mounted) {
    return null;
  }

  return (
    <header className="fixed top-0 left-0 right-0 bg-white shadow-md z-20 border-b border-[#0A1929]/10">
      <div className="max-w-[1600px] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16 py-5">
          <div className="flex items-center">
            {/* Mobile menu button */}
            <button
              type="button"
              className="md:hidden inline-flex items-center justify-center p-2 rounded-md text-[#0A1929] hover:text-[#FF5400] hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[#FF5400] mr-2"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              aria-controls="mobile-menu"
              aria-expanded={sidebarOpen}
            >
              <span className="sr-only">Open sidebar menu</span>
              {sidebarOpen ? (
                <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
              ) : (
                <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
              )}
            </button>

            <Link href="/admin/dashboard" className="flex items-center">
              <img
                src="/images/logo.png"
                alt="Mocky Digital Logo"
                className="h-8 w-8 mr-2"
              />
              <span className="text-xl font-semibold text-[#0A1929]">Mocky</span>
              <span className="text-xl font-semibold text-[#FF5400]">Digital</span>
            </Link>
          </div>

          <div className="flex items-center space-x-4">
            <Link
              href="/"
              className="flex items-center text-[#0A1929] hover:text-[#FF5400] transition-colors"
              title="Go to main site"
              target="_blank"
              rel="noopener noreferrer"
            >
              <HomeIcon className="h-6 w-6" />
              <span className="ml-1 text-sm hidden sm:inline">Main Site</span>
            </Link>

            <button className="text-[#0A1929] hover:text-[#FF5400]">
              <BellIcon className="h-6 w-6" />
            </button>

            <Menu as="div" className="relative">
              <Menu.Button className="flex items-center text-[#0A1929] hover:text-[#FF5400]">
                <UserCircleIcon className="h-8 w-8" />
              </Menu.Button>

              <Transition
                as={Fragment}
                enter="transition ease-out duration-100"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
              >
                <Menu.Items className="absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg py-1 ring-1 ring-[#0A1929] ring-opacity-10">
                  <Menu.Item>
                    {({ active }) => (
                      <Link
                        href="/"
                        className={`flex items-center w-full px-4 py-2 text-sm ${
                          active ? 'bg-[#0A1929]/5 text-[#0A1929]' : 'text-[#0A1929]/80'
                        }`}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <HomeIcon className="h-5 w-5 mr-2" />
                        Visit Main Site
                      </Link>
                    )}
                  </Menu.Item>
                  <div className="border-t border-gray-100 my-1"></div>
                  <Menu.Item>
                    <button
                      onClick={handleLogout}
                      className="flex items-center w-full px-4 py-2 text-sm text-[#0A1929]/80 hover:bg-[#0A1929]/5 hover:text-[#0A1929]"
                    >
                      <ArrowRightOnRectangleIcon className="h-5 w-5 mr-2" />
                      Sign Out
                    </button>
                  </Menu.Item>
                </Menu.Items>
              </Transition>
            </Menu>
          </div>
        </div>
      </div>
    </header>
  );
}