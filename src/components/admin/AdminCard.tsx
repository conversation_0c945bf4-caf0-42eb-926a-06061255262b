import React, { ReactNode } from 'react';

interface AdminCardProps {
  title?: string;
  icon?: ReactNode;
  children: ReactNode;
  actions?: ReactNode;
  footer?: ReactNode;
  className?: string;
}

/**
 * AdminCard component for displaying content in a card format
 * Optimized for mobile display with responsive design
 */
export default function AdminCard({
  title,
  icon,
  children,
  actions,
  footer,
  className = '',
}: AdminCardProps) {
  return (
    <div className={`bg-white rounded-lg shadow-sm overflow-hidden ${className}`}>
      {/* Card Header */}
      {(title || actions) && (
        <div className="px-4 py-3 sm:px-6 border-b border-gray-200 flex flex-col sm:flex-row sm:items-center justify-between gap-2">
          {title && (
            <div className="flex items-center">
              {icon && <div className="mr-2 text-blue-500">{icon}</div>}
              <h3 className="text-lg font-medium text-gray-900">{title}</h3>
            </div>
          )}
          {actions && <div className="flex items-center space-x-2">{actions}</div>}
        </div>
      )}

      {/* Card Content */}
      <div className="px-4 py-4 sm:px-6">{children}</div>

      {/* Card Footer */}
      {footer && (
        <div className="px-4 py-3 sm:px-6 bg-gray-50 border-t border-gray-200">
          {footer}
        </div>
      )}
    </div>
  );
}

/**
 * AdminCardItem component for displaying items in a list within a card
 * Used for mobile-friendly display of table data
 */
export function AdminCardItem({
  label,
  value,
  valueClassName = '',
}: {
  label: string;
  value: ReactNode;
  valueClassName?: string;
}) {
  return (
    <div className="py-2 sm:grid sm:grid-cols-3 sm:gap-4">
      <dt className="text-sm font-medium text-gray-500">{label}</dt>
      <dd className={`mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 ${valueClassName}`}>
        {value}
      </dd>
    </div>
  );
}

/**
 * AdminCardList component for displaying a list of items in a card
 */
export function AdminCardList({
  items,
  className = '',
}: {
  items: { label: string; value: ReactNode; valueClassName?: string }[];
  className?: string;
}) {
  return (
    <dl className={`divide-y divide-gray-200 ${className}`}>
      {items.map((item, index) => (
        <AdminCardItem
          key={index}
          label={item.label}
          value={item.value}
          valueClassName={item.valueClassName}
        />
      ))}
    </dl>
  );
}
