import { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { ArrowUpTrayIcon, DocumentIcon } from '@heroicons/react/24/outline';

interface DatabaseFileDropzoneProps {
  onFileDrop: (file: File) => Promise<void>;
  isProcessing: boolean;
  error: string | null;
  success: string | null;
}

export default function DatabaseFileDropzone({
  onFileDrop,
  isProcessing,
  error,
  success,
}: DatabaseFileDropzoneProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const { getRootProps, getInputProps, isDragActive, fileRejections } = useDropzone({
    onDrop: async (acceptedFiles, rejectedFiles) => {
      // Clear previous file
      setSelectedFile(null);

      // Handle rejected files
      if (rejectedFiles.length > 0) {
        return;
      }

      const file = acceptedFiles[0];
      if (!file) return;

      // Set the selected file
      setSelectedFile(file);

      // Validate file type (basic check)
      if (!file.name.endsWith('.dump') && !file.name.endsWith('.sql')) {
        return;
      }

      await onFileDrop(file);
    },
    maxFiles: 1,
    multiple: false,
    accept: {
      'application/octet-stream': ['.dump'],
      'application/sql': ['.sql'],
      'application/x-sql': ['.sql'],
    },
    disabled: isProcessing,
    maxSize: 100 * 1024 * 1024, // 100MB
  });

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div
      {...getRootProps()}
      className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
        isDragActive
          ? 'border-green-500 bg-green-50'
          : 'border-gray-300 hover:border-green-400'
      } ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      <input {...getInputProps()} disabled={isProcessing} />

      {selectedFile && !isProcessing ? (
        <div className="flex flex-col items-center">
          <DocumentIcon className="h-10 w-10 text-green-500" />
          <p className="mt-2 text-sm font-medium text-gray-900">{selectedFile.name}</p>
          <p className="text-xs text-gray-500">{formatFileSize(selectedFile.size)}</p>
          <p className="mt-2 text-xs text-gray-500">File selected. Click or drag to replace.</p>
        </div>
      ) : (
        <ArrowUpTrayIcon className="mx-auto h-10 w-10 text-gray-400" />
      )}

      {isProcessing ? (
        <div className="mt-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500">Processing database file...</p>
        </div>
      ) : !selectedFile && (
        <div className="mt-4">
          <p className="text-sm text-gray-600">
            {isDragActive
              ? 'Drop the backup file here'
              : 'Drag & drop a backup file here, or click to select'}
          </p>
          <p className="mt-1 text-xs text-gray-500">
            Supported formats: .dump, .sql
          </p>
          <p className="mt-1 text-xs text-gray-500">
            Maximum size: 100MB
          </p>
        </div>
      )}

      {fileRejections.length > 0 && (
        <div className="mt-2 text-sm text-red-600">
          {fileRejections[0].errors.map(e => (
            <p key={e.code}>{e.message}</p>
          ))}
        </div>
      )}

      {error && (
        <p className="mt-2 text-sm text-red-600">{error}</p>
      )}

      {success && (
        <p className="mt-2 text-sm text-green-600">{success}</p>
      )}
    </div>
  );
}
