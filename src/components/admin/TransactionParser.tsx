'use client';

import { useState } from 'react';
import { parseTransactionMessage } from '@/utils/transactionParser';
import { ArrowPathIcon, CheckCircleIcon, XCircleIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import { useNotification } from '@/contexts/NotificationContext';
import Link from 'next/link';

interface TransactionParserProps {
  onTransactionCreated?: (transactionId: string) => void;
}

export default function TransactionParser({ onTransactionCreated }: TransactionParserProps) {
  const [message, setMessage] = useState('');
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [existingReceiptId, setExistingReceiptId] = useState<string | null>(null);
  const [isDuplicateReceipt, setIsDuplicateReceipt] = useState<boolean>(false);
  const router = useRouter();
  const { showNotification } = useNotification();

  const handleParse = async () => {
    if (!message.trim()) {
      setError('Please enter a transaction message');
      return;
    }

    setProcessing(true);
    setError(null);
    setIsDuplicateReceipt(false);
    setExistingReceiptId(null);

    try {
      // Parse the message
      const parsedData = parseTransactionMessage(message);

      if (!parsedData) {
        throw new Error('Failed to parse transaction message. Please check the format and try again.');
      }

      // First, check if a receipt already exists for this M-Pesa transaction ID
      console.log(`Checking if receipt exists for M-Pesa transaction ID: ${parsedData.transactionId}`);
      const checkResponse = await fetch('/api/admin/receipts/check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mpesaTransactionId: parsedData.transactionId
        }),
      });

      if (!checkResponse.ok) {
        const errorData = await checkResponse.json().catch(() => ({}));
        console.error('Error checking for existing receipt:', errorData);
        // Continue with transaction creation even if check fails
      } else {
        const checkResult = await checkResponse.json();

        if (checkResult.exists) {
          console.log(`Receipt already exists for transaction ID ${parsedData.transactionId}:`, checkResult);
          setIsDuplicateReceipt(true);
          setExistingReceiptId(checkResult.receiptId);
          setError(`A receipt already exists for this transaction (${parsedData.transactionId})`);
          showNotification('warning', 'This transaction already has a receipt');
          setProcessing(false);
          return;
        }
      }

      // Create a transaction in the database
      const response = await fetch('/api/admin/transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionId: parsedData.transactionId,
          amount: parsedData.amount,
          customerName: parsedData.customerName,
          phoneNumber: parsedData.phoneNumber,
          transactionDate: parsedData.transactionDate,
          rawMessage: parsedData.rawMessage,
          status: 'pending'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to create transaction');
      }

      const transaction = await response.json();

      showNotification('success', 'Transaction created successfully');

      // Clear the form
      setMessage('');

      // Call the callback if provided
      if (onTransactionCreated) {
        onTransactionCreated(transaction.id);
      } else {
        // Navigate to create receipt page
        router.push(`/admin/receipts/new?transactionId=${transaction.id}`);
      }
    } catch (err) {
      console.error('Error processing transaction:', err);
      setError(err instanceof Error ? err.message : 'Failed to process transaction');
      showNotification('error', 'Failed to process transaction');
    } finally {
      setProcessing(false);
    }
  };

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h2 className="text-lg font-medium text-gray-900 mb-4">Parse M-Pesa Transaction</h2>

      {error && (
        <div className={`${isDuplicateReceipt ? 'bg-yellow-50 border-yellow-400' : 'bg-red-50 border-red-400'} border-l-4 p-4 mb-4`}>
          <div className="flex">
            <div className="flex-shrink-0">
              {isDuplicateReceipt ? (
                <DocumentTextIcon className="h-5 w-5 text-yellow-400" />
              ) : (
                <XCircleIcon className="h-5 w-5 text-red-400" />
              )}
            </div>
            <div className="ml-3">
              <p className={`text-sm ${isDuplicateReceipt ? 'text-yellow-700' : 'text-red-700'}`}>{error}</p>

              {isDuplicateReceipt && existingReceiptId && (
                <div className="mt-2">
                  <p className="text-sm text-yellow-700">
                    This transaction has already been processed with a receipt.
                  </p>
                  <div className="mt-2 flex flex-wrap gap-3">
                    <Link
                      href={`/admin/receipts/${existingReceiptId}`}
                      className="inline-flex items-center text-sm font-medium text-yellow-700 hover:text-yellow-900"
                    >
                      <DocumentTextIcon className="-ml-0.5 mr-1 h-4 w-4" />
                      View This Receipt
                    </Link>
                    <Link
                      href="/admin/receipts"
                      className="inline-flex items-center text-sm font-medium text-yellow-700 hover:text-yellow-900"
                    >
                      <DocumentTextIcon className="-ml-0.5 mr-1 h-4 w-4" />
                      View All Receipts
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="space-y-4">
        <div>
          <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
            Paste M-Pesa Transaction Message
          </label>
          <textarea
            id="message"
            name="message"
            rows={5}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Paste the full M-Pesa transaction message here..."
            className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
          <div className="mt-2">
            <button
              type="button"
              onClick={() => setMessage('TEF5PIQ2H7 Confirmed. Ksh1,500.00 received from JOHN DOE 254722000000 on 15/05/2023 at 10:30 AM. New M-PESA balance is Ksh2,500.00')}
              className="text-xs text-blue-600 hover:text-blue-800"
            >
              Load sample message
            </button>
          </div>
        </div>

        <div className="bg-gray-50 p-3 rounded-md text-xs text-gray-600">
          <p className="font-medium mb-1">Example M-Pesa Message Format:</p>
          <p>TEF5PIQ2H7 Confirmed. Ksh1,500.00 received from JOHN DOE 254722000000 on 15/05/2023 at 10:30 AM. New M-PESA balance is Ksh2,500.00</p>
          <p className="mt-2 text-gray-500">The system will extract the transaction ID, amount, customer name, phone number, and date from the message.</p>
        </div>

        <div className="flex justify-end">
          <button
            type="button"
            onClick={handleParse}
            disabled={processing || !message.trim()}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {processing ? (
              <>
                <ArrowPathIcon className="animate-spin -ml-1 mr-2 h-4 w-4" />
                Processing...
              </>
            ) : (
              <>
                <CheckCircleIcon className="-ml-1 mr-2 h-4 w-4" />
                Parse & Create Receipt
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
