'use client';

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import AdminHeader from './AdminHeader';
import AdminSidebar from './AdminSidebar';
import AdminFooter from './AdminFooter';
import Cookies from 'js-cookie';
import { NotificationProvider } from '@/contexts/NotificationContext';

export default function AdminLayoutClient({
  children,
}: {
  children: React.ReactNode;
}) {
  const [mounted, setMounted] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const pathname = usePathname();
  const isLoginPage = pathname === '/admin/login';

  useEffect(() => {
    setMounted(true);
  }, []);

  // Close sidebar when route changes (for mobile)
  useEffect(() => {
    setSidebarOpen(false);
  }, [pathname]);

  // Don't render anything until mounted on client
  if (!mounted) {
    return null;
  }

  // Don't show any layout elements on login page
  if (isLoginPage) {
    return (
      <NotificationProvider>
        <div className="min-h-screen bg-[#0A1929]">{children}</div>
      </NotificationProvider>
    );
  }

  return (
    <NotificationProvider>
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <AdminHeader sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

        {/* Main content area with sidebar */}
        <div className="flex-1 flex pt-20">
          <AdminSidebar open={sidebarOpen} setOpen={setSidebarOpen} />
          <main className="flex-1 p-3 sm:p-4 md:p-6 pt-20 md:ml-[240px] overflow-auto max-w-full">
            {children}
          </main>
        </div>

        {/* Footer - positioned at the bottom and spans full width */}
        <div className="w-full md:pl-[240px] z-20 relative">
          <AdminFooter />
        </div>

        {/* Overlay for mobile when sidebar is open */}
        {sidebarOpen && (
          <div
            className="md:hidden fixed inset-0 bg-black/50 z-10"
            onClick={() => setSidebarOpen(false)}
            aria-hidden="true"
          />
        )}
      </div>
    </NotificationProvider>
  );
}