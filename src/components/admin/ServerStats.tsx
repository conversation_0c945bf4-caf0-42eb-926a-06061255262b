'use client';

import { useState, useEffect } from 'react';
import {
  ServerIcon,
  CpuChipIcon,
  ArrowPathIcon,
  CircleStackIcon,
  ClockIcon,
  ComputerDesktopIcon,
  ArrowTrendingUpIcon,
  BoltIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

interface ServerStatsProps {
  refreshInterval?: number; // in milliseconds
}

interface SystemStats {
  cpu: {
    usage: number;
    cores: number;
    model: string;
    loadAvg: number[];
  };
  memory: {
    total: string;
    free: string;
    used: string;
    usedPercent: number;
  };
  disk: {
    total: string;
    free: string;
    used: string;
    usedPercent: number;
  };
  uptime: {
    system: string;
    process: string;
  };
  os: {
    platform: string;
    release: string;
    hostname: string;
  };
  network: {
    interfaces: string[];
  };
}

const ServerStats = ({ refreshInterval = 30000 }: ServerStatsProps) => {
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchStats = async () => {
    try {
      setError(null);

      const response = await fetch('/api/admin/stats/server');

      if (!response.ok) {
        throw new Error('Failed to fetch server statistics');
      }

      const data = await response.json();
      setStats(data);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching server stats:', err);
      setError('Failed to load server statistics');
      toast.error('Failed to load server statistics');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchStats();
  }, []);

  // Set up refresh interval
  useEffect(() => {
    const intervalId = setInterval(fetchStats, refreshInterval);

    // Clean up on unmount
    return () => clearInterval(intervalId);
  }, [refreshInterval]);

  // Handle manual refresh
  const handleRefresh = () => {
    setIsLoading(true);
    fetchStats();
  };

  // Render loading state
  if (isLoading && !stats) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6 overflow-hidden relative animate-pulse">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <div className="p-2 bg-slate-200 rounded-lg w-10 h-10 mr-3"></div>
            <div className="h-7 bg-slate-200 rounded w-40"></div>
          </div>
          <div className="h-8 w-8 bg-slate-200 rounded-lg"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="bg-slate-100 p-5 rounded-lg border border-slate-200">
              <div className="flex items-center mb-4">
                <div className="p-2 bg-slate-200 rounded-lg w-9 h-9 mr-3"></div>
                <div className="h-6 bg-slate-200 rounded w-20"></div>
                <div className="ml-auto h-6 bg-slate-200 rounded-full w-12"></div>
              </div>
              <div className="space-y-4">
                <div className="h-3 bg-slate-200 rounded-full w-full"></div>
                <div className="grid grid-cols-2 gap-3">
                  <div className="h-16 bg-slate-200 rounded-lg"></div>
                  <div className="h-16 bg-slate-200 rounded-lg"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Render error state
  if (error && !stats) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6 overflow-hidden relative">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-50 text-blue-500 rounded-lg mr-3">
              <ServerIcon className="h-6 w-6" />
            </div>
            <h2 className="text-xl font-semibold text-slate-800">Server Statistics</h2>
          </div>
          <button
            onClick={handleRefresh}
            className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100 transition-colors"
            aria-label="Refresh statistics"
          >
            <ArrowPathIcon className="h-5 w-5" />
          </button>
        </div>

        <div className="p-4 bg-red-50 border border-red-100 text-red-700 rounded-lg flex items-center">
          <div className="p-2 bg-red-100 rounded-full mr-3">
            <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <div>
            <h3 className="text-sm font-medium text-red-800 mb-1">Error Loading Statistics</h3>
            <p className="text-sm text-red-700">{error}. Click refresh to try again.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 overflow-hidden relative">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-2">
        <div className="flex items-center">
          <div className="p-2 bg-blue-50 text-blue-500 rounded-lg mr-3">
            <ServerIcon className="h-6 w-6" />
          </div>
          <h2 className="text-xl font-semibold text-slate-800">Server Statistics</h2>
        </div>
        <div className="flex items-center space-x-4 ml-auto">
          {lastUpdated && (
            <div className="flex items-center text-slate-500 text-xs">
              <ClockIcon className="h-3.5 w-3.5 mr-1.5" />
              <span>
                {lastUpdated.toLocaleTimeString()}
              </span>
            </div>
          )}
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100 transition-colors"
            aria-label="Refresh statistics"
          >
            <ArrowPathIcon className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {stats && (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
          {/* CPU Stats */}
          <div className="bg-slate-50 p-5 rounded-lg border border-slate-100 hover:shadow-md transition-all">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-blue-50 text-blue-500 rounded-lg mr-3">
                <CpuChipIcon className="h-5 w-5" />
              </div>
              <h3 className="text-md font-medium text-slate-700">CPU</h3>
              <div className="ml-auto flex items-center">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  stats.cpu.usage > 80 ? 'bg-red-100 text-red-700' :
                  stats.cpu.usage > 60 ? 'bg-orange-100 text-orange-700' :
                  'bg-green-100 text-green-700'
                }`}>
                  {stats.cpu.usage}%
                </span>
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm text-slate-600">Usage</span>
                </div>
                <div className="w-full bg-slate-200 rounded-full h-2.5">
                  <div
                    className={`h-2.5 rounded-full ${
                      stats.cpu.usage > 80 ? 'bg-red-500' :
                      stats.cpu.usage > 60 ? 'bg-orange-500' :
                      'bg-green-500'
                    }`}
                    style={{ width: `${stats.cpu.usage}%` }}
                  ></div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-white p-3 rounded-lg border border-slate-100">
                  <div className="flex items-center mb-1">
                    <span className="text-xs text-slate-500">Cores</span>
                  </div>
                  <span className="text-lg font-medium text-slate-800">{stats.cpu.cores}</span>
                </div>
                <div className="bg-white p-3 rounded-lg border border-slate-100">
                  <div className="flex items-center mb-1">
                    <span className="text-xs text-slate-500">Load Avg</span>
                  </div>
                  <span className="text-sm font-medium text-slate-800">
                    {stats.cpu.loadAvg[0]}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Memory Stats */}
          <div className="bg-slate-50 p-5 rounded-lg border border-slate-100 hover:shadow-md transition-all">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-purple-50 text-purple-500 rounded-lg mr-3">
                <CircleStackIcon className="h-5 w-5" />
              </div>
              <h3 className="text-md font-medium text-slate-700">Memory</h3>
              <div className="ml-auto flex items-center">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  stats.memory.usedPercent > 80 ? 'bg-red-100 text-red-700' :
                  stats.memory.usedPercent > 60 ? 'bg-orange-100 text-orange-700' :
                  'bg-green-100 text-green-700'
                }`}>
                  {stats.memory.usedPercent}%
                </span>
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm text-slate-600">Usage</span>
                </div>
                <div className="w-full bg-slate-200 rounded-full h-2.5">
                  <div
                    className={`h-2.5 rounded-full ${
                      stats.memory.usedPercent > 80 ? 'bg-red-500' :
                      stats.memory.usedPercent > 60 ? 'bg-orange-500' :
                      'bg-green-500'
                    }`}
                    style={{ width: `${stats.memory.usedPercent}%` }}
                  ></div>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-2">
                <div className="bg-white p-2 rounded-lg border border-slate-100">
                  <div className="text-xs text-slate-500 mb-1">Total</div>
                  <div className="text-sm font-medium text-slate-800">{stats.memory.total}</div>
                </div>
                <div className="bg-white p-2 rounded-lg border border-slate-100">
                  <div className="text-xs text-slate-500 mb-1">Used</div>
                  <div className="text-sm font-medium text-slate-800">{stats.memory.used}</div>
                </div>
                <div className="bg-white p-2 rounded-lg border border-slate-100">
                  <div className="text-xs text-slate-500 mb-1">Free</div>
                  <div className="text-sm font-medium text-slate-800">{stats.memory.free}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Disk Stats */}
          <div className="bg-slate-50 p-5 rounded-lg border border-slate-100 hover:shadow-md transition-all">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-green-50 text-green-500 rounded-lg mr-3">
                <ServerIcon className="h-5 w-5" />
              </div>
              <h3 className="text-md font-medium text-slate-700">Disk</h3>
              <div className="ml-auto flex items-center">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  stats.disk.usedPercent > 80 ? 'bg-red-100 text-red-700' :
                  stats.disk.usedPercent > 60 ? 'bg-orange-100 text-orange-700' :
                  'bg-green-100 text-green-700'
                }`}>
                  {stats.disk.usedPercent}%
                </span>
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm text-slate-600">Usage</span>
                </div>
                <div className="w-full bg-slate-200 rounded-full h-2.5">
                  <div
                    className={`h-2.5 rounded-full ${
                      stats.disk.usedPercent > 80 ? 'bg-red-500' :
                      stats.disk.usedPercent > 60 ? 'bg-orange-500' :
                      'bg-green-500'
                    }`}
                    style={{ width: `${stats.disk.usedPercent}%` }}
                  ></div>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-2">
                <div className="bg-white p-2 rounded-lg border border-slate-100">
                  <div className="text-xs text-slate-500 mb-1">Total</div>
                  <div className="text-sm font-medium text-slate-800">{stats.disk.total}</div>
                </div>
                <div className="bg-white p-2 rounded-lg border border-slate-100">
                  <div className="text-xs text-slate-500 mb-1">Used</div>
                  <div className="text-sm font-medium text-slate-800">{stats.disk.used}</div>
                </div>
                <div className="bg-white p-2 rounded-lg border border-slate-100">
                  <div className="text-xs text-slate-500 mb-1">Free</div>
                  <div className="text-sm font-medium text-slate-800">{stats.disk.free}</div>
                </div>
              </div>
            </div>
          </div>

          {/* System Info */}
          <div className="bg-slate-50 p-5 rounded-lg border border-slate-100 hover:shadow-md transition-all">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-orange-50 text-orange-500 rounded-lg mr-3">
                <ComputerDesktopIcon className="h-5 w-5" />
              </div>
              <h3 className="text-md font-medium text-slate-700">System</h3>
            </div>
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-white p-3 rounded-lg border border-slate-100">
                  <div className="text-xs text-slate-500 mb-1">Hostname</div>
                  <div className="text-sm font-medium text-slate-800 truncate">{stats.os.hostname}</div>
                </div>
                <div className="bg-white p-3 rounded-lg border border-slate-100">
                  <div className="text-xs text-slate-500 mb-1">Platform</div>
                  <div className="text-sm font-medium text-slate-800 capitalize">{stats.os.platform}</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div className="bg-white p-3 rounded-lg border border-slate-100">
                  <div className="flex items-center mb-1">
                    <span className="text-xs text-slate-500">System Uptime</span>
                  </div>
                  <div className="text-sm font-medium text-slate-800">{stats.uptime.system}</div>
                </div>
                <div className="bg-white p-3 rounded-lg border border-slate-100">
                  <div className="flex items-center mb-1">
                    <span className="text-xs text-slate-500">Process Uptime</span>
                  </div>
                  <div className="text-sm font-medium text-slate-800">{stats.uptime.process}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ServerStats;
