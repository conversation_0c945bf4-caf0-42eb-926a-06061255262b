'use client';

import { useState, FormEvent } from 'react';
import { toast } from 'react-hot-toast';
import { SparklesIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

interface BlogPostGeneratorProps {
  onGenerated: (title: string, content: string, excerpt: string) => void;
}

export default function BlogPostGenerator({ onGenerated }: BlogPostGeneratorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [topic, setTopic] = useState('');
  const [tone, setTone] = useState('professional');
  const [length, setLength] = useState('medium');
  const [targetAudience, setTargetAudience] = useState('');
  const [includeHeadings, setIncludeHeadings] = useState(true);
  const [includeBulletPoints, setIncludeBulletPoints] = useState(true);
  const [includeConclusion, setIncludeConclusion] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    if (!topic.trim()) {
      setError('Please enter a topic');
      return;
    }
    
    setError(null);
    setIsGenerating(true);
    
    try {
      const response = await fetch('/api/admin/blog/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic,
          tone,
          length,
          targetAudience: targetAudience || undefined,
          includeHeadings,
          includeBulletPoints,
          includeConclusion,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate blog post');
      }
      
      const data = await response.json();
      
      // Call the callback with the generated content
      onGenerated(data.title, data.content, data.excerpt);
      
      // Close the generator panel
      setIsOpen(false);
      
      // Show success message
      toast.success('Blog post generated successfully!');
    } catch (error) {
      console.error('Error generating blog post:', error);
      setError(error instanceof Error ? error.message : 'Failed to generate blog post');
      toast.error(error instanceof Error ? error.message : 'Failed to generate blog post');
    } finally {
      setIsGenerating(false);
    }
  };
  
  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      >
        <SparklesIcon className="h-4 w-4 mr-2" />
        AI Writer
      </button>
    );
  }
  
  return (
    <div className="bg-white rounded-lg shadow-sm p-6 mb-6 border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-medium text-slate-800 flex items-center">
          <SparklesIcon className="h-5 w-5 text-indigo-500 mr-2" />
          AI Blog Post Generator
        </h2>
        <button
          onClick={() => setIsOpen(false)}
          className="text-gray-400 hover:text-gray-500"
        >
          <span className="sr-only">Close</span>
          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Topic */}
        <div>
          <label htmlFor="topic" className="block text-sm font-medium text-gray-700 mb-1">
            Topic <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="topic"
            value={topic}
            onChange={(e) => setTopic(e.target.value)}
            placeholder="e.g., The benefits of responsive web design"
            className="block w-full rounded-md border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          />
          {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Tone */}
          <div>
            <label htmlFor="tone" className="block text-sm font-medium text-gray-700 mb-1">
              Tone
            </label>
            <select
              id="tone"
              value={tone}
              onChange={(e) => setTone(e.target.value)}
              className="block w-full rounded-md border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value="professional">Professional</option>
              <option value="casual">Casual</option>
              <option value="humorous">Humorous</option>
              <option value="technical">Technical</option>
              <option value="conversational">Conversational</option>
            </select>
          </div>
          
          {/* Length */}
          <div>
            <label htmlFor="length" className="block text-sm font-medium text-gray-700 mb-1">
              Length
            </label>
            <select
              id="length"
              value={length}
              onChange={(e) => setLength(e.target.value)}
              className="block w-full rounded-md border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value="short">Short (300-500 words)</option>
              <option value="medium">Medium (600-800 words)</option>
              <option value="long">Long (1000-1500 words)</option>
            </select>
          </div>
        </div>
        
        {/* Target Audience */}
        <div>
          <label htmlFor="targetAudience" className="block text-sm font-medium text-gray-700 mb-1">
            Target Audience (optional)
          </label>
          <input
            type="text"
            id="targetAudience"
            value={targetAudience}
            onChange={(e) => setTargetAudience(e.target.value)}
            placeholder="e.g., small business owners, tech enthusiasts"
            className="block w-full rounded-md border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Include Headings */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="includeHeadings"
              checked={includeHeadings}
              onChange={(e) => setIncludeHeadings(e.target.checked)}
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label htmlFor="includeHeadings" className="ml-2 block text-sm text-gray-700">
              Include Headings
            </label>
          </div>
          
          {/* Include Bullet Points */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="includeBulletPoints"
              checked={includeBulletPoints}
              onChange={(e) => setIncludeBulletPoints(e.target.checked)}
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label htmlFor="includeBulletPoints" className="ml-2 block text-sm text-gray-700">
              Include Bullet Points
            </label>
          </div>
          
          {/* Include Conclusion */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="includeConclusion"
              checked={includeConclusion}
              onChange={(e) => setIncludeConclusion(e.target.checked)}
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label htmlFor="includeConclusion" className="ml-2 block text-sm text-gray-700">
              Include Conclusion
            </label>
          </div>
        </div>
        
        <div className="flex justify-end pt-4">
          <button
            type="button"
            onClick={() => setIsOpen(false)}
            className="mr-3 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isGenerating}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-indigo-400 disabled:cursor-not-allowed"
          >
            {isGenerating ? (
              <>
                <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <SparklesIcon className="h-4 w-4 mr-2" />
                Generate Blog Post
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
