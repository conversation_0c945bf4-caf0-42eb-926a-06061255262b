'use client';

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useNotification } from '@/contexts/NotificationContext';
import {
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  DocumentTextIcon,
  PlusIcon,
  MinusIcon,
  CurrencyDollarIcon,
  ReceiptRefundIcon,
  UserIcon,
  PhoneIcon,
  ClockIcon,
  TrashIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline';
import { parseTransactionMessage } from '@/utils/transactionParser';

interface Service {
  id: string;
  name: string;
  description: string | null;
  price: number;
  category: string;
}

interface Transaction {
  id: string;
  transactionId: string;
  amount: number;
  customerName: string;
  phoneNumber: string;
  transactionDate: string;
  status: string;
}

interface ReceiptItem {
  serviceId: string;
  quantity: number;
  unitPrice: number;
  description?: string;
  searchTerm?: string;
}

export default function QuickReceiptForm() {
  // Transaction state
  const [message, setMessage] = useState('');
  const [transaction, setTransaction] = useState<Transaction | null>(null);

  // Services state
  const [services, setServices] = useState<Service[]>([]);
  const [receiptItems, setReceiptItems] = useState<ReceiptItem[]>([]);
  const [activeDropdownIndex, setActiveDropdownIndex] = useState<number | null>(null);

  // UI state
  const [processing, setProcessing] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDuplicateReceipt, setIsDuplicateReceipt] = useState<boolean>(false);
  const [existingReceiptId, setExistingReceiptId] = useState<string | null>(null);

  // Refs for click outside detection - one ref per dropdown item
  const dropdownRefs = React.useRef<{[key: number]: HTMLDivElement | null}>({});

  // State to track if we're on the client side (for createPortal)
  const [isMounted, setIsMounted] = useState(false);

  const router = useRouter();
  const { showNotification } = useNotification();

  // Set isMounted to true when component mounts
  useEffect(() => {
    setIsMounted(true);

    // Add window resize handler to update dropdown position
    const handleResize = () => {
      if (activeDropdownIndex !== null) {
        // Force a re-render to update dropdown position
        setActiveDropdownIndex(prev => {
          if (prev !== null) {
            // Toggle to force re-render
            setActiveDropdownIndex(null);
            setTimeout(() => setActiveDropdownIndex(prev), 10);
          }
          return prev;
        });
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      setIsMounted(false);
      window.removeEventListener('resize', handleResize);
    };
  }, [activeDropdownIndex]);

  // Load services and initialize receipt items on component mount
  useEffect(() => {
    const fetchServices = async () => {
      try {
        const response = await fetch('/api/admin/services');
        if (!response.ok) {
          throw new Error('Failed to fetch services');
        }
        const data = await response.json();
        setServices(data);

        // Initialize with one empty receipt item if none exists
        if (receiptItems.length === 0) {
          setReceiptItems([{ serviceId: '', quantity: 1, unitPrice: 0, searchTerm: '' }]);
        }
      } catch (err) {
        console.error('Error fetching services:', err);
        showNotification('error', 'Failed to load services');
      }
    };

    fetchServices();
  }, [showNotification, receiptItems.length]);

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      // Only close if we have an active dropdown
      if (activeDropdownIndex !== null) {
        // Check if the click is on a dropdown item
        const isClickOnDropdownItem = (event.target as Element).closest('.service-dropdown-item');

        // Check if the click is on the input field
        const activeRef = dropdownRefs.current[activeDropdownIndex];
        const isClickOnInput = activeRef && activeRef.contains(event.target as Node);

        // If click is outside both the dropdown items and input, close the dropdown
        if (!isClickOnDropdownItem && !isClickOnInput) {
          setActiveDropdownIndex(null);
        }
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeDropdownIndex]);

  // Add a new receipt item
  const addReceiptItem = () => {
    setReceiptItems([...receiptItems, { serviceId: '', quantity: 1, unitPrice: 0, searchTerm: '' }]);
  };

  // Remove a receipt item
  const removeReceiptItem = (index: number) => {
    const updatedItems = [...receiptItems];
    updatedItems.splice(index, 1);
    setReceiptItems(updatedItems);
  };

  // Update a receipt item
  const updateReceiptItem = (index: number, field: keyof ReceiptItem, value: any) => {
    const updatedItems = [...receiptItems];
    updatedItems[index] = { ...updatedItems[index], [field]: value };

    // If service is changed, update the unit price
    if (field === 'serviceId') {
      const service = services.find(s => s.id === value);
      if (service) {
        updatedItems[index].unitPrice = service.price;

        // Also update the search term to match the selected service name
        updatedItems[index].searchTerm = service.name;
      }
    }

    setReceiptItems(updatedItems);
  };

  // Handle search term change
  const handleSearchTermChange = (index: number, value: string) => {
    const updatedItems = [...receiptItems];
    updatedItems[index] = { ...updatedItems[index], searchTerm: value };

    // If the search term exactly matches a service name, select that service
    const matchedService = services.find(s =>
      s.name.toLowerCase() === value.toLowerCase() ||
      `${s.name} - KES ${s.price.toLocaleString()}`.toLowerCase() === value.toLowerCase()
    );

    if (matchedService) {
      updatedItems[index].serviceId = matchedService.id;
      updatedItems[index].unitPrice = matchedService.price;
      setActiveDropdownIndex(null); // Close dropdown on exact match
    } else if (value.trim()) {
      setActiveDropdownIndex(index); // Open dropdown when typing
    } else {
      setActiveDropdownIndex(null); // Close dropdown when empty
    }

    setReceiptItems(updatedItems);
  };

  // Handle focus on search input - show dropdown regardless of search term
  const handleSearchFocus = (index: number) => {
    setActiveDropdownIndex(index);
  };

  // Load sample message
  const loadSampleMessage = async () => {
    const sampleMessage = 'TEF890217 Confirmed. Ksh1,500.00 received from JOHN DOE 254722000000 on 15/05/2023 at 10:30 AM. New M-PESA balance is Ksh2,500.00';
    setMessage(sampleMessage);
    await handleParse(sampleMessage);
  };

  // Handle paste event
  const handlePaste = async (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
    const pastedText = e.clipboardData.getData('text');
    if (pastedText.trim()) {
      // Let the default paste happen first
      setTimeout(() => {
        handleParse(pastedText);
      }, 100);
    }
  };

  // Parse transaction message
  const handleParse = async (textMessage?: string) => {
    const messageToProcess = textMessage || message;

    if (!messageToProcess.trim()) {
      setError('Please enter a transaction message');
      return;
    }

    setProcessing(true);
    setError(null);
    setIsDuplicateReceipt(false);
    setExistingReceiptId(null);

    try {
      // Parse the message
      const parsedData = parseTransactionMessage(messageToProcess);

      if (!parsedData) {
        throw new Error('Failed to parse transaction message. Please check the format and try again.');
      }

      // First, check if a receipt already exists for this M-Pesa transaction ID
      console.log(`Checking if receipt exists for M-Pesa transaction ID: ${parsedData.transactionId}`);
      const checkResponse = await fetch('/api/admin/receipts/check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mpesaTransactionId: parsedData.transactionId
        }),
      });

      const checkData = await checkResponse.json();

      if (checkData.exists) {
        setIsDuplicateReceipt(true);
        setExistingReceiptId(checkData.receiptId);
        throw new Error(`A receipt already exists for this transaction (Receipt #${checkData.receiptNumber})`);
      }

      // Create the transaction
      const response = await fetch('/api/admin/transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: messageToProcess
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create transaction');
      }

      const transaction = await response.json();
      setTransaction(transaction);

      showNotification('success', 'Transaction parsed successfully');
    } catch (err) {
      console.error('Error processing transaction:', err);
      setError(err instanceof Error ? err.message : 'Failed to process transaction');
      showNotification('error', 'Failed to process transaction');
    } finally {
      setProcessing(false);
    }
  };

  // Generate receipt
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // If no transaction exists yet, try to parse it first
    if (!transaction && message.trim()) {
      try {
        await handleParse();
        // Wait a bit for the transaction to be set
        await new Promise(resolve => setTimeout(resolve, 500));

        // If still no transaction, show error
        if (!transaction) {
          setError('Failed to parse transaction. Please check the message format.');
          return;
        }
      } catch (err) {
        setError('Failed to parse transaction. Please check the message format.');
        return;
      }
    } else if (!transaction) {
      setError('Please enter a transaction message');
      return;
    }

    if (receiptItems.length === 0 || receiptItems.some(item => !item.serviceId)) {
      setError('Please select at least one service');
      return;
    }

    try {
      setSubmitting(true);
      setError(null);

      // Prepare the request payload with multiple items
      const payload = {
        transactionId: transaction.id,
        customerName: transaction.customerName,
        phoneNumber: transaction.phoneNumber,
        items: receiptItems.map(item => ({
          serviceId: item.serviceId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          description: services.find(s => s.id === item.serviceId)?.name
        }))
      };

      console.log('Sending receipt creation request with payload:', payload);

      // Create the receipt
      try {
        const response = await fetch('/api/admin/receipts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        });

        if (!response.ok) {
          const errorText = await response.text();
          let errorMessage = 'Failed to create receipt';

          try {
            const errorData = JSON.parse(errorText);
            errorMessage = errorData.error || errorData.details || errorMessage;

            // Check if this is a duplicate receipt error
            if (errorData.receiptId) {
              setIsDuplicateReceipt(true);
              setExistingReceiptId(errorData.receiptId);
            }
          } catch (parseError) {
            console.error('Error parsing error response:', parseError);
            errorMessage = errorText || errorMessage;

            // Check if the raw error text indicates a duplicate receipt
            if (errorText.includes('Unique constraint failed') ||
                errorText.includes('already exists')) {
              setIsDuplicateReceipt(true);
              errorMessage = 'A receipt for this transaction already exists';
            }
          }

          throw new Error(errorMessage);
        }

        const receipt = await response.json();
        showNotification('success', 'Receipt created successfully');

        // Navigate to the receipt page
        router.push(`/admin/receipts/${receipt.id}`);
      } catch (fetchError) {
        console.error('Fetch error:', fetchError);
        throw new Error(fetchError instanceof Error ? fetchError.message : 'Network error while creating receipt');
      }
    } catch (err) {
      console.error('Error creating receipt:', err);

      // If it's not a duplicate receipt error, reset the flag
      if (!isDuplicateReceipt) {
        setIsDuplicateReceipt(false);
      }

      // Set the error message
      const errorMessage = err instanceof Error ? err.message : 'Failed to create receipt';
      setError(errorMessage);

      // Show notification with appropriate message
      const notificationMessage = isDuplicateReceipt
        ? 'This transaction already has a receipt'
        : 'Failed to create receipt';

      showNotification(isDuplicateReceipt ? 'warning' : 'error', notificationMessage);
    } finally {
      setSubmitting(false);
    }
  };

  // Calculate total amount
  const calculateTotal = () => {
    return receiptItems.reduce((total, item) => {
      const service = services.find(s => s.id === item.serviceId);
      const price = service ? service.price : item.unitPrice;
      return total + (price * item.quantity);
    }, 0);
  };

  // Filter services based on search term
  const getFilteredServices = (index: number) => {
    const searchTerm = receiptItems[index]?.searchTerm?.toLowerCase() || '';

    if (!searchTerm.trim()) {
      return services;
    }

    return services.filter(service =>
      service.name.toLowerCase().includes(searchTerm) ||
      (service.description && service.description.toLowerCase().includes(searchTerm)) ||
      service.price.toString().includes(searchTerm) ||
      `${service.name} - KES ${service.price.toLocaleString()}`.toLowerCase().includes(searchTerm)
    );
  };

  return (
    <div className="bg-white shadow-md rounded-lg overflow-hidden">
      <div className="bg-[#0F2557] px-6 py-4 flex items-center">
        <ReceiptRefundIcon className="h-6 w-6 text-[#FF5400] mr-2" />
        <h1 className="text-xl font-semibold text-white">Quick Receipt Generator</h1>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-8">
        {/* Transaction Section */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
          <div className="bg-[#0F2557]/10 px-4 py-3 border-b border-gray-200">
            <div className="flex items-center">
              <CurrencyDollarIcon className="h-5 w-5 text-[#0F2557] mr-2" />
              <h2 className="text-md font-medium text-[#0F2557]">M-Pesa Transaction</h2>
            </div>
          </div>

          <div className="p-4">
            {!transaction ? (
              <>
                <div className="mb-4">
                  <label htmlFor="message" className="block text-sm font-medium text-[#0F2557] mb-1">
                    Paste M-Pesa Transaction Message
                  </label>
                  <div className="relative">
                    <textarea
                      id="message"
                      rows={3}
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      onPaste={handlePaste}
                      placeholder="Paste the full M-Pesa transaction message here..."
                      className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-[#FF5400] focus:border-[#0F2557] sm:text-sm"
                      required
                    />
                    {message && !processing && (
                      <div className="absolute right-2 bottom-2">
                        <button
                          type="button"
                          onClick={() => handleParse()}
                          className="inline-flex items-center p-1.5 rounded-full bg-[#0F2557] text-white hover:bg-[#0F2557]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF5400]"
                          title="Parse Transaction"
                        >
                          <ArrowDownIcon className="h-4 w-4" />
                        </button>
                      </div>
                    )}
                  </div>
                  <div className="mt-2 flex flex-wrap gap-2">
                    <button
                      type="button"
                      onClick={loadSampleMessage}
                      className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF5400]"
                    >
                      Load sample message
                    </button>

                    {processing && (
                      <div className="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-[#0F2557]">
                        <ArrowPathIcon className="animate-spin -ml-0.5 mr-1 h-4 w-4 text-[#FF5400]" />
                        Processing transaction...
                      </div>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <div className="bg-white rounded-lg border border-[#0F2557]/20 overflow-hidden">
                <div className="grid grid-cols-1 md:grid-cols-3 divide-y md:divide-y-0 md:divide-x divide-gray-200">
                  <div className="p-3 flex items-start">
                    <div className="flex-shrink-0 mt-0.5">
                      <DocumentTextIcon className="h-5 w-5 text-[#FF5400]" />
                    </div>
                    <div className="ml-2">
                      <p className="text-xs text-gray-500">Transaction ID</p>
                      <p className="text-sm font-medium text-[#0F2557]">{transaction.transactionId}</p>
                    </div>
                  </div>
                  <div className="p-3 flex items-start">
                    <div className="flex-shrink-0 mt-0.5">
                      <CurrencyDollarIcon className="h-5 w-5 text-[#FF5400]" />
                    </div>
                    <div className="ml-2">
                      <p className="text-xs text-gray-500">Amount</p>
                      <p className="text-sm font-medium text-[#0F2557]">KES {transaction.amount.toLocaleString()}</p>
                    </div>
                  </div>
                  <div className="p-3 flex items-start">
                    <div className="flex-shrink-0 mt-0.5">
                      <UserIcon className="h-5 w-5 text-[#FF5400]" />
                    </div>
                    <div className="ml-2">
                      <p className="text-xs text-gray-500">Customer</p>
                      <p className="text-sm font-medium text-[#0F2557]">{transaction.customerName}</p>
                      <p className="text-xs text-gray-500 mt-0.5">{transaction.phoneNumber}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-[#0F2557]/5 px-3 py-2 border-t border-[#0F2557]/10 flex justify-between items-center">
                  <p className="text-xs text-[#0F2557]">
                    <ClockIcon className="inline-block h-3 w-3 mr-1" />
                    Transaction Date: {new Date(transaction.transactionDate).toLocaleString()}
                  </p>
                  <button
                    type="button"
                    onClick={() => {
                      setTransaction(null);
                      setMessage('');
                    }}
                    className="text-xs text-[#FF5400] hover:text-[#FF5400]/80"
                  >
                    Clear
                  </button>
                </div>
              </div>
            )}

            {error && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <div className="flex">
                  <XCircleIcon className="h-5 w-5 text-red-400 flex-shrink-0" aria-hidden="true" />
                  <div className="ml-2">
                    <p className="text-sm text-red-700">{error}</p>
                    {isDuplicateReceipt && existingReceiptId && (
                      <div className="mt-2">
                        <Link
                          href={`/admin/receipts/${existingReceiptId}`}
                          className="inline-flex items-center text-sm font-medium text-red-700 hover:text-red-900"
                        >
                          <DocumentTextIcon className="-ml-0.5 mr-1 h-4 w-4" />
                          View Existing Receipt
                        </Link>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Services Section */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
          <div className="bg-[#0F2557]/10 px-4 py-3 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <ReceiptRefundIcon className="h-5 w-5 text-[#0F2557] mr-2" />
                <h2 className="text-md font-medium text-[#0F2557]">Services</h2>
              </div>
              <button
                type="button"
                onClick={addReceiptItem}
                className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-white bg-[#FF5400] hover:bg-[#FF5400]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0F2557]"
              >
                <PlusIcon className="h-3 w-3 mr-1" />
                Add Service
              </button>
            </div>
          </div>

          <div className="p-4">
            <div className="space-y-4">
              {receiptItems.map((item, index) => {
                const selectedService = services.find(s => s.id === item.serviceId);
                return (
                  <div key={index} className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
                    <div className="bg-[#0F2557]/5 px-3 py-2 border-b border-gray-200 flex justify-between items-center">
                      <h3 className="text-sm font-medium text-[#0F2557] flex items-center">
                        <span className="inline-flex items-center justify-center h-5 w-5 rounded-full bg-[#0F2557] text-white text-xs mr-2">
                          {index + 1}
                        </span>
                        {selectedService ? selectedService.name : `Service Item ${index + 1}`}
                      </h3>
                      {receiptItems.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeReceiptItem(index)}
                          className="text-[#FF5400] hover:text-[#FF5400]/80 p-1 rounded-full hover:bg-gray-100"
                          title="Remove Item"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      )}
                    </div>

                    <div className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div className="md:col-span-3">
                          <label htmlFor={`service-${index}`} className="block text-sm font-medium text-[#0F2557] mb-1">
                            Service
                          </label>
                          <div
                            className="relative z-40 dropdown-container"
                            style={{ position: 'relative' }}
                            ref={(el) => dropdownRefs.current[index] = el}>
                            <div className="relative">
                              <input
                                type="text"
                                id={`service-search-${index}`}
                                value={item.searchTerm || ''}
                                onChange={(e) => handleSearchTermChange(index, e.target.value)}
                                onFocus={() => handleSearchFocus(index)}
                                onClick={() => handleSearchFocus(index)}
                                placeholder="Select or type to search for a service..."
                                className={`block w-full border border-gray-300 rounded-md shadow-sm py-2 pl-3 pr-10 focus:outline-none focus:ring-[#FF5400] focus:border-[#0F2557] sm:text-sm cursor-pointer appearance-none bg-white ${
                                  item.serviceId ? 'bg-[#0F2557]/5 border-[#0F2557]/20' : 'bg-gray-50'
                                }`}
                                autoComplete="off"
                              />
                              {item.serviceId && item.searchTerm ? (
                                <button
                                  type="button"
                                  onClick={() => {
                                    const updatedItems = [...receiptItems];
                                    updatedItems[index] = { ...updatedItems[index], serviceId: '', searchTerm: '', unitPrice: 0 };
                                    setReceiptItems(updatedItems);
                                  }}
                                  className="absolute inset-y-0 right-0 flex items-center pr-2 text-gray-400 hover:text-gray-600"
                                >
                                  <XCircleIcon className="h-5 w-5" />
                                </button>
                              ) : (
                                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                  <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                  </svg>
                                </div>
                              )}
                            </div>

                            {activeDropdownIndex === index && isMounted && createPortal(
                              <div
                                className="fixed z-[1000] bg-white shadow-xl max-h-60 rounded-md overflow-hidden focus:outline-none sm:text-sm border border-gray-200"
                                style={{
                                  width: dropdownRefs.current[index]?.getBoundingClientRect().width || 300,
                                  left: dropdownRefs.current[index]?.getBoundingClientRect().left || 0,
                                  top: (dropdownRefs.current[index]?.getBoundingClientRect().bottom || 0) + 4,
                                  position: 'fixed'
                                }}
                              >
                                <div className="sticky top-0 z-10 bg-[#0F2557] px-3 py-1.5 text-white text-xs font-medium">
                                  {item.searchTerm ? 'Search results' : 'Select a service'}
                                </div>

                                <div className="max-h-52 overflow-y-auto py-1">
                                  {getFilteredServices(index).length > 0 ? (
                                    getFilteredServices(index).map((service) => (
                                      <div
                                        key={service.id}
                                        onClick={() => {
                                          updateReceiptItem(index, 'serviceId', service.id);
                                          setActiveDropdownIndex(null);
                                        }}
                                        className={`service-dropdown-item cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-100 ${
                                          item.serviceId === service.id
                                            ? 'bg-[#0F2557]/10 text-[#0F2557] font-medium border-l-4 border-[#FF5400]'
                                            : 'text-gray-900'
                                        }`}
                                      >
                                        <div className="flex justify-between">
                                          <span className="block truncate">{service.name}</span>
                                          <span className="text-[#FF5400] font-medium">KES {service.price.toLocaleString()}</span>
                                        </div>
                                        {service.description && (
                                          <p className="text-xs text-gray-500 truncate">{service.description}</p>
                                        )}
                                      </div>
                                    ))
                                  ) : (
                                    <div className="py-8 text-center">
                                      <p className="text-gray-500 text-sm">No services found matching "{item.searchTerm}"</p>
                                      <p className="text-gray-400 text-xs mt-1">Try a different search term</p>
                                    </div>
                                  )}
                                </div>
                              </div>,
                              document.body
                            )}
                            {item.serviceId && (
                              <input
                                type="hidden"
                                id={`service-${index}`}
                                value={item.serviceId}
                                required
                              />
                            )}
                          </div>
                        </div>

                        <div>
                          <label htmlFor={`quantity-${index}`} className="block text-sm font-medium text-[#0F2557] mb-1">
                            Quantity
                          </label>
                          <div className="flex rounded-md shadow-sm">
                            <button
                              type="button"
                              onClick={() => updateReceiptItem(index, 'quantity', Math.max(1, item.quantity - 1))}
                              className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-sm font-medium text-gray-500 hover:bg-gray-100"
                            >
                              <span className="sr-only">Decrease</span>
                              <MinusIcon className="h-4 w-4" />
                            </button>
                            <input
                              type="number"
                              id={`quantity-${index}`}
                              min="1"
                              value={item.quantity}
                              onChange={(e) => updateReceiptItem(index, 'quantity', parseInt(e.target.value) || 1)}
                              className="block w-full border border-gray-300 py-2 px-3 focus:outline-none focus:ring-[#FF5400] focus:border-[#0F2557] sm:text-sm text-center"
                              required
                            />
                            <button
                              type="button"
                              onClick={() => updateReceiptItem(index, 'quantity', item.quantity + 1)}
                              className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-sm font-medium text-gray-500 hover:bg-gray-100"
                            >
                              <span className="sr-only">Increase</span>
                              <PlusIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>

                      {selectedService && (
                        <div className="mt-3 flex justify-between items-center bg-[#0F2557]/5 px-3 py-2 rounded-md">
                          <div className="text-sm text-[#0F2557]">
                            <span className="font-medium">Unit Price:</span> KES {selectedService.price.toLocaleString()}
                          </div>
                          <div className="text-sm font-medium text-[#0F2557]">
                            <span>Subtotal:</span> KES {(selectedService.price * item.quantity).toLocaleString()}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>

            {receiptItems.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500">No services added yet. Click "Add Service" to begin.</p>
              </div>
            )}
          </div>
        </div>

        {/* Summary Section */}
        {transaction && (
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
            <div className="bg-[#0F2557]/10 px-4 py-3 border-b border-gray-200">
              <div className="flex items-center">
                <CurrencyDollarIcon className="h-5 w-5 text-[#0F2557] mr-2" />
                <h2 className="text-md font-medium text-[#0F2557]">Payment Summary</h2>
              </div>
            </div>

            <div className="p-4">
              <div className="space-y-3">
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Total Services:</span>
                  <span className="text-sm font-medium text-[#0F2557]">KES {calculateTotal().toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Amount Paid via M-Pesa:</span>
                  <span className="text-sm font-medium text-[#0F2557]">KES {transaction.amount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-sm font-medium text-[#0F2557]">Balance:</span>
                  <span className={`text-sm font-bold ${calculateTotal() > transaction.amount ? 'text-red-600' : 'text-[#FF5400]'}`}>
                    KES {(calculateTotal() - transaction.amount).toLocaleString()}
                  </span>
                </div>

                {calculateTotal() > transaction.amount && (
                  <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p className="text-xs text-yellow-700">
                      <strong>Note:</strong> The customer will have a balance of KES {(calculateTotal() - transaction.amount).toLocaleString()} to be paid later.
                    </p>
                  </div>
                )}

                {calculateTotal() < transaction.amount && (
                  <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-md">
                    <p className="text-xs text-green-700">
                      <strong>Note:</strong> The customer has overpaid by KES {(transaction.amount - calculateTotal()).toLocaleString()}.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Submit Button */}
        <div className="pt-4">
          <button
            type="submit"
            disabled={submitting || (!transaction && !message) || receiptItems.some(item => !item.serviceId)}
            className={`w-full inline-flex justify-center items-center px-4 py-3 border border-transparent rounded-md shadow-md text-base font-medium text-white ${
              submitting ? 'bg-gray-400' : 'bg-[#FF5400] hover:bg-[#FF5400]/90'
            } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0F2557] disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200`}
          >
            {submitting ? (
              <>
                <ArrowPathIcon className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" />
                Creating Receipt...
              </>
            ) : (
              <>
                <DocumentTextIcon className="-ml-1 mr-2 h-5 w-5 text-white" />
                {!transaction && message ? 'Parse & Generate Receipt' : 'Generate Receipt'}
              </>
            )}
          </button>

          {!submitting && transaction && (
            <p className="mt-2 text-center text-xs text-gray-500">
              This will create a receipt for transaction {transaction.transactionId} and redirect you to the receipt page.
            </p>
          )}
        </div>
      </form>
    </div>
  );
}
