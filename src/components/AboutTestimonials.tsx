'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Testimonial interface
interface Testimonial {
  id: number;
  name: string;
  location: string;
  project: string;
  testimonial: string;
  rating: number;
  company?: string;
}

// Sample testimonials data related to digital services
const testimonialData: Testimonial[] = [
  {
    id: 1,
    name: "<PERSON>",
    location: "Nairobi, Kenya",
    project: "Website Development, 2 weeks",
    testimonial: "These guys always do awesome work - their developers know their stuff. Plus, the customer service is top-notch; they're responsive, helpful, and always ready to go the extra mile to make me happy.",
    rating: 5,
    company: "TechHub Kenya"
  },
  {
    id: 2,
    name: "<PERSON>",
    location: "Mombasa, Kenya",
    project: "Branding, 1 month",
    testimonial: "You are the best! Thanks for another great branding project; I submitted it to my board yesterday and expect great feedback, as always. The logo design and brand guidelines are perfect.",
    rating: 5,
    company: "Coastal Ventures"
  },
  {
    id: 3,
    name: "<PERSON>",
    location: "Kisumu, Kenya",
    project: "E-commerce Website, 3 weeks",
    testimonial: "For my retail business, I needed an online store that was both detailed and professionally built. Mocky Digital's service was excellent. The developer assigned to my project had a deep understanding of e-commerce and provided a comprehensive solution.",
    rating: 5,
    company: "Lake Region Retail"
  },
  {
    id: 4,
    name: "Sarah",
    location: "Nakuru, Kenya",
    project: "Digital Marketing, 2 months",
    testimonial: "The digital marketing campaign exceeded our expectations. Their team was responsive and delivered results that perfectly aligned with our goals. The SEO improvements and social media strategy have significantly increased our online visibility.",
    rating: 5,
    company: "Highland Tours"
  },
  {
    id: 5,
    name: "James",
    location: "Eldoret, Kenya",
    project: "Logo Design, 1 week",
    testimonial: "I needed a professional logo for my startup and Mocky Digital delivered beyond my expectations. The designer captured the essence of my brand perfectly. The process was smooth and the communication was excellent throughout.",
    rating: 5,
    company: "Upland Innovations"
  },
  {
    id: 6,
    name: "Catherine",
    location: "Thika, Kenya",
    project: "UI/UX Design, 3 weeks",
    testimonial: "The user interface design for our app was exactly what we needed. Mocky Digital's attention to detail and understanding of user experience principles resulted in a design that our customers love. Highly recommended for any UI/UX work.",
    rating: 5,
    company: "Central Tech Solutions"
  }
];

// Star rating component
const StarRating = ({ rating }: { rating: number }) => {
  return (
    <div className="flex">
      {[...Array(5)].map((_, i) => (
        <svg
          key={i}
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill={i < rating ? "#FF5400" : "#e2e8f0"}
          className="w-5 h-5"
        >
          <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd" />
        </svg>
      ))}
    </div>
  );
};

// User icon component
const UserIcon = () => (
  <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500">
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
      <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
    </svg>
  </div>
);

export default function AboutTestimonials() {
  const [currentPage, setCurrentPage] = useState(0);
  const testimonialsPerPage = 3;
  const totalPages = Math.ceil(testimonialData.length / testimonialsPerPage);

  // Get current testimonials
  const getCurrentTestimonials = () => {
    const start = currentPage * testimonialsPerPage;
    const end = start + testimonialsPerPage;
    return testimonialData.slice(start, end);
  };

  // Navigation functions
  const goToPage = (pageIndex: number) => {
    setCurrentPage(pageIndex);
  };

  return (
    <section className="py-24 relative overflow-hidden bg-[#f8f9fa]">
      <div className="container relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <span className="inline-block px-4 py-1.5 rounded-full bg-[#0A2647]/10 backdrop-blur-md border border-[#0A2647]/10 text-[#0A2647]/80 text-xs font-medium mb-4 shadow-sm">Client Success Stories</span>
          <h2 className="text-4xl lg:text-5xl font-bold text-[#0A2647] mb-8">
            What Our Clients Say
          </h2>
          <p className="text-[#0A2647]/70 text-lg max-w-3xl mx-auto">
            Don't just take our word for it. Here's what our clients have to say about working with us.
          </p>
        </motion.div>

        <div className="max-w-7xl mx-auto">
          {/* Testimonials Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <AnimatePresence mode="wait">
              {getCurrentTestimonials().map((testimonial, index) => (
                <motion.div
                  key={testimonial.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="bg-white rounded-lg shadow-sm overflow-hidden h-full"
                >
                  <div className="p-6 flex flex-col h-full">
                    {/* Header with name, location and rating */}
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex items-center">
                        <UserIcon />
                        <div className="ml-3">
                          <h3 className="text-lg font-bold text-[#0A2647]">{testimonial.name}</h3>
                          <p className="text-sm text-gray-500">{testimonial.location}</p>
                        </div>
                      </div>
                      <StarRating rating={testimonial.rating} />
                    </div>

                    {/* Project details */}
                    <div className="mb-4 text-sm text-gray-600 font-medium">
                      {testimonial.project}
                    </div>

                    {/* Testimonial text */}
                    <p className="text-gray-700 flex-grow">
                      {testimonial.testimonial}
                    </p>

                    {/* Company name if available */}
                    {testimonial.company && (
                      <div className="mt-4 text-sm font-medium text-[#FF5400]">
                        {testimonial.company}
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>

          {/* Navigation dots */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-10 space-x-2">
              {[...Array(totalPages)].map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToPage(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentPage
                      ? 'bg-[#FF5400] scale-125'
                      : 'bg-[#0A2647]/20 hover:bg-[#0A2647]/30'
                  }`}
                  aria-label={`Go to page ${index + 1}`}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  );
}
