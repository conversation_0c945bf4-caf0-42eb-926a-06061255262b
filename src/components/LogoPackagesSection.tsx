'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import LogoDesignBriefModal from './LogoDesignBriefModal';

interface PricingPlan {
  name: string;
  price: string;
  description: string;
  features: string[];
  highlight?: boolean;
  whatsappMessage: string;
}

const pricingPlans = [
  {
    name: 'Simple Logo',
    price: '1,500',
    description: 'Basic logo design with quick delivery',
    features: [
      '3 Initial Logo Concepts',
      '3 Rounds of Revisions',
      'Final Files in PNG, JPG, and PDF',
      '24 Hours Delivery'
    ],
    highlight: false,
    whatsappMessage: "Hello Mocky Digital! 👋 I'm interested in the Simple Logo Package (KSH 1,500) that includes:\n• 3 Initial Logo Concepts\n• 3 Rounds of Revisions\n• Final Files in PNG, JPG, and PDF\n• 24 Hours Delivery\n\nCan you help me with my quick logo design?"
  },
  {
    name: 'Basic Logo',
    price: '5,000',
    description: 'Perfect for small businesses and startups',
    features: [
      '5 Initial Concepts',
      '3 Revision Rounds',
      'Professional files (AI, EPS, SVG)',
      'High Resolution Files',
      'Basic Brand Guidelines',
      '48 Hours Delivery'
    ],
    highlight: false,
    whatsappMessage: "Hello Mocky Digital! 👋 I'm interested in the Basic Logo Package (KSH 5,000) that includes:\n• 5 Initial Concepts\n• 3 Revision Rounds\n• Professional files (AI, EPS, SVG)\n• High Resolution Files\n• Basic Brand Guidelines\n• 48 Hours Delivery\n\nCan you help me with my logo design?"
  },
  {
    name: 'Professional Logo',
    price: '15,000',
    description: 'Professional logo with more options',
    features: [
      '7 unique concepts',
      '4 revision rounds',
      'Professional files (AI, EPS, SVG)',
      'Web formats (PNG, JPG, PDF)',
      'Color variations & palettes',
      'Basic brand guidelines',
      'Social media formats',
      'Logo usage guide',
      '3 Days Delivery'
    ],
    highlight: false,
    whatsappMessage: "Hello Mocky Digital! 👋 I'm interested in the Professional Logo Package (KSH 15,000) that includes:\n• 7 unique concepts\n• 4 revision rounds\n• Professional files (AI, EPS, SVG)\n• Web formats (PNG, JPG, PDF)\n• Color variations & palettes\n• Basic brand guidelines\n• Social media formats\n• Logo usage guide\n• 3 Days Delivery\n\nCan you help me with my professional logo design?"
  },
  {
    name: 'Premium Logo',
    price: '30,000',
    description: 'Premium logo design & branding elements',
    features: [
      '9 unique concepts',
      'Unlimited revisions',
      'Professional files (AI, EPS, SVG)',
      'All web formats & sizes (PNG, JPG, PDF)',
      'Extended color variations',
      'Comprehensive guidelines',
      'Icon/favicon design',
      'Social media kit',
      'Business card mockup',
      'Logo animation',
      'Comprehensive delivery (5 Days)'
    ],
    highlight: false,
    whatsappMessage: "Hello Mocky Digital! 👋 I'm interested in the Premium Logo Package (KSH 30,000) that includes:\n• 9 unique concepts\n• Unlimited revisions\n• Professional files (AI, EPS, SVG)\n• All web formats & sizes (PNG, JPG, PDF)\n• Extended color variations\n• Comprehensive guidelines\n• Icon/favicon design\n• Social media kit\n• Business card mockup\n• Logo animation\n• Comprehensive delivery (5 Days)\n\nCan you help me with my premium logo design?"
  }
];

export default function LogoPackagesSection() {
  const [selectedPlan, setSelectedPlan] = useState<PricingPlan | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleOrderClick = (plan: PricingPlan) => {
    // Set the selected plan and open the modal
    setSelectedPlan(plan);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedPlan(null);
  };

  if (!mounted) {
    return (
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Logo Design Packages
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Choose the perfect logo design package for your business needs
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {pricingPlans.map((_, index) => (
              <div
                key={index}
                className="relative bg-gray-100 rounded-xl overflow-hidden shadow-md h-[500px] animate-pulse"
              ></div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-24 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-64 h-64 bg-[#FF5400] opacity-5 rounded-full -translate-x-1/2 -translate-y-1/2"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-[#FF5400] opacity-5 rounded-full translate-x-1/3 translate-y-1/3"></div>
      <div className="absolute top-1/4 right-10 w-20 h-20 bg-[#FF5400] opacity-5 rounded-full"></div>
      <div className="absolute bottom-1/4 left-10 w-32 h-32 bg-[#FF5400] opacity-5 rounded-full"></div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
            Logo Design Packages
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto text-lg">
            Choose the perfect logo design package for your business needs
          </p>
          <div className="w-24 h-1 bg-[#FF5400] mx-auto mt-8 rounded-full"></div>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {pricingPlans.map((plan, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className={`relative bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-500 border-0 ${
                plan.highlight ? 'ring-2 ring-[#FF5400]' : ''
              } transform hover:-translate-y-2 flex flex-col h-full`}
            >
              {plan.highlight && (
                <div className="absolute top-0 right-0 left-0 bg-gradient-to-r from-[#FF5400] to-[#FF7E00] text-white text-center py-3 px-4 text-sm font-medium">
                  <span className="flex items-center justify-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    Most Popular
                  </span>
                </div>
              )}

              <div className="p-8 pt-12 flex flex-col flex-grow w-full">
                <div className="flex-grow">
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">{plan.name}</h3>
                  <p className="text-gray-600 mb-6">{plan.description}</p>

                  <div className="mt-6 mb-8">
                    <div className="flex items-end gap-1">
                      <span className="text-sm font-medium text-gray-500">KSH</span>
                      <span className="text-4xl font-bold text-[#FF5400]">{plan.price}</span>
                    </div>
                  </div>

                  <div className="h-px w-full bg-gray-100 my-6"></div>

                  <h4 className="text-sm font-medium text-gray-700 mb-4">What's included:</h4>
                  <div className="max-h-[220px] overflow-y-auto pr-2">
                    <ul className="space-y-3">
                      {plan.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start">
                          <span className="h-5 w-5 rounded-full bg-green-100 flex items-center justify-center mr-3 flex-shrink-0 mt-0.5">
                            <svg className="h-3 w-3 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </span>
                          <span className="text-gray-700">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                <div className="mt-8">
                  <motion.button
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => handleOrderClick(plan)}
                    className="w-full block text-center px-6 py-4 rounded-xl font-medium text-white transition-all duration-300 shadow-md hover:shadow-lg bg-[#FF5400] hover:bg-[#E54D00]"
                    aria-label={`Order ${plan.name || plan.description} package now`}
                  >
                    <span className="flex items-center justify-center gap-2">
                      Order Now
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </span>
                  </motion.button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Logo Design Brief Modal */}
      {selectedPlan && (
        <LogoDesignBriefModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          packageName={selectedPlan.name}
          packagePrice={selectedPlan.price}
          packageFeatures={selectedPlan.features}
          whatsappMessage={selectedPlan.whatsappMessage}
        />
      )}
    </section>
  );
}
