'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';

interface ModernPageHeroProps {
  title: string;
  subtitle?: string;
  bgImage?: string;
  description?: string;
  showShapes?: boolean;
}

export default function ModernPageHero({
  title,
  subtitle,
  bgImage,
  description,
  showShapes = true
}: ModernPageHeroProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Simple SSR-compatible version
    return (
      <section className="relative bg-gradient-to-b from-[#1a2942] to-[#121f35] text-white py-16 md:py-20 mt-20 overflow-hidden">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-1 bg-[#FF5400]"></div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">{title}</h1>
            {subtitle && <p className="text-lg md:text-xl text-gray-300 mb-4">{subtitle}</p>}
            {description && <p className="text-lg text-gray-300">{description}</p>}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="relative bg-gradient-to-b from-[#1a2942] to-[#121f35] text-white py-16 md:py-20 mt-20 overflow-hidden">
      <div className="container relative z-10 mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
          className="max-w-3xl mx-auto text-center"
        >
          {/* Accent Line */}
          <div className="flex justify-center mb-6">
            <div className="w-16 h-1 bg-[#FF5400]"></div>
          </div>

          <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white leading-tight">
            {title}
          </h1>

          {subtitle && (
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, ease: "easeOut", delay: 0.1 }}
              className="text-lg md:text-xl text-gray-300 mb-4"
            >
              {subtitle}
            </motion.p>
          )}

          {description && (
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, ease: "easeOut", delay: 0.2 }}
              className="text-lg text-gray-300"
            >
              {description}
            </motion.p>
          )}
        </motion.div>
      </div>
    </section>
  );
}
