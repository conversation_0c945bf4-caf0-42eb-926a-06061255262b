import Link from 'next/link';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  baseUrl: string;
}

export default function Pagination({ currentPage, totalPages, baseUrl }: PaginationProps) {
  const pages = Array.from({ length: totalPages }, (_, i) => i + 1);
  const separator = '...';

  // Create array of page numbers to display
  const getPageNumbers = () => {
    if (totalPages <= 7) {
      return pages;
    }

    if (currentPage <= 3) {
      return [...pages.slice(0, 5), separator, totalPages];
    }

    if (currentPage >= totalPages - 2) {
      return [1, separator, ...pages.slice(totalPages - 5)];
    }

    return [
      1,
      separator,
      ...pages.slice(currentPage - 2, currentPage + 1),
      separator,
      totalPages,
    ];
  };

  const pageNumbers = getPageNumbers();

  // Helper function to generate page URL
  const getPageUrl = (page: number) => {
    const url = new URL(baseUrl, 'http://placeholder');
    url.searchParams.set('page', page.toString());
    return url.pathname + url.search;
  };

  if (totalPages <= 1) return null;

  return (
    <nav className="flex justify-center mt-8" aria-label="Pagination">
      <ul className="flex items-center -space-x-px">
        {/* Previous button */}
        <li>
          <Link
            href={getPageUrl(currentPage - 1)}
            className={`block px-3 py-2 ml-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-100 hover:text-gray-700 ${
              currentPage === 1 ? 'pointer-events-none opacity-50' : ''
            }`}
            aria-disabled={currentPage === 1}
          >
            <span className="sr-only">Previous</span>
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
          </Link>
        </li>

        {/* Page numbers */}
        {pageNumbers.map((pageNumber, index) => (
          <li key={index}>
            {pageNumber === separator ? (
              <span className="px-3 py-2 text-gray-500 bg-white border border-gray-300">
                {separator}
              </span>
            ) : (
              <Link
                href={getPageUrl(pageNumber as number)}
                className={`px-3 py-2 leading-tight border border-gray-300 hover:bg-gray-100 hover:text-gray-700 ${
                  currentPage === pageNumber
                    ? 'text-blue-600 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 z-10'
                    : 'text-gray-500 bg-white'
                }`}
              >
                {pageNumber}
              </Link>
            )}
          </li>
        ))}

        {/* Next button */}
        <li>
          <Link
            href={getPageUrl(currentPage + 1)}
            className={`block px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 hover:text-gray-700 ${
              currentPage === totalPages ? 'pointer-events-none opacity-50' : ''
            }`}
            aria-disabled={currentPage === totalPages}
          >
            <span className="sr-only">Next</span>
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                clipRule="evenodd"
              />
            </svg>
          </Link>
        </li>
      </ul>
    </nav>
  );
}