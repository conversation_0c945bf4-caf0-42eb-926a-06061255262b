'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Fa<PERSON><PERSON>ed<PERSON>, <PERSON>aT<PERSON><PERSON>, FaGithub, FaEnvelope } from 'react-icons/fa';

interface TeamMember {
  id: string;
  name: string;
  role: string;
  bio: string;
  imageSrc: string;
  linkedinUrl?: string;
  twitterUrl?: string;
  githubUrl?: string;
  emailAddress?: string;
}

const TeamSection: React.FC = () => {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/team');

        if (!response.ok) {
          throw new Error('Failed to fetch team members');
        }

        const data = await response.json();
        setTeamMembers(data);
      } catch (err) {
        console.error('Error fetching team members:', err);
        setError('Failed to load team members. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTeamMembers();
  }, []);

  // Fallback data in case API fails
  const fallbackTeamMembers: TeamMember[] = [
    {
      id: '1',
      name: 'Don Omondi Onyango',
      role: 'Graphics Designer | Web Developer',
      bio: 'Creative designer with expertise in web development and graphic design.',
      imageSrc: '/images/team/don-omondi.jpg',
      linkedinUrl: 'https://linkedin.com/in/donomondi',
      twitterUrl: 'https://twitter.com/don_omondi',
      githubUrl: 'https://github.com/donomondi',
    },
    {
      id: '2',
      name: 'Jack Sequeira Onyango',
      role: 'Graphics Designer',
      bio: 'Passionate graphic designer with an eye for detail and creative solutions.',
      imageSrc: '/images/team/jack-sequeira.jpg',
      linkedinUrl: 'https://linkedin.com/in/jacksequeira',
    },
    {
      id: '3',
      name: 'Sarah Wanjiku',
      role: 'UI/UX Designer',
      bio: 'Experienced UI/UX designer focused on creating intuitive and engaging user experiences.',
      imageSrc: '/images/team/sarah-wanjiku.jpg',
      linkedinUrl: 'https://linkedin.com/in/sarahwanjiku',
      twitterUrl: 'https://twitter.com/sarahwanjiku',
    },
    {
      id: '4',
      name: 'James Mwangi',
      role: 'Frontend Developer',
      bio: 'Frontend developer specializing in React and modern JavaScript frameworks.',
      imageSrc: '/images/team/james-mwangi.jpg',
      githubUrl: 'https://github.com/jamesmwangi',
      linkedinUrl: 'https://linkedin.com/in/jamesmwangi',
    }
  ];

  // Use fallback data if API fails or is loading
  const displayTeamMembers = teamMembers.length > 0 ? teamMembers : fallbackTeamMembers;

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Meet the Experts</h2>
          <div className="w-24 h-1 bg-orange-500 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-2xl mx-auto">
            Our talented team of professionals is dedicated to bringing your digital vision to life.
          </p>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
          </div>
        ) : error ? (
          <div className="text-center text-red-500 py-8">{error}</div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {displayTeamMembers.map((member) => (
              <div
                key={member.id}
                className="bg-white rounded-lg overflow-hidden shadow-lg transition-transform duration-300 hover:transform hover:scale-105"
              >
                <div className="relative h-80 w-full">
                  <Image
                    src={member.imageSrc}
                    alt={member.name}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                    className="object-cover"
                    priority
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900">{member.name}</h3>
                  <p className="text-orange-600 mb-4">{member.role}</p>
                  <p className="text-gray-600 mb-4">{member.bio}</p>

                  <div className="flex space-x-4">
                    {member.linkedinUrl && (
                      <Link href={member.linkedinUrl} target="_blank" rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800">
                        <FaLinkedin size={20} />
                      </Link>
                    )}
                    {member.twitterUrl && (
                      <Link href={member.twitterUrl} target="_blank" rel="noopener noreferrer"
                        className="text-blue-400 hover:text-blue-600">
                        <FaTwitter size={20} />
                      </Link>
                    )}
                    {member.githubUrl && (
                      <Link href={member.githubUrl} target="_blank" rel="noopener noreferrer"
                        className="text-gray-800 hover:text-gray-600">
                        <FaGithub size={20} />
                      </Link>
                    )}
                    {member.emailAddress && (
                      <Link href={`mailto:${member.emailAddress}`}
                        className="text-red-500 hover:text-red-700">
                        <FaEnvelope size={20} />
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default TeamSection;
