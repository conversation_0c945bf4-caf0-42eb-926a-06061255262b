'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { TeamMember } from '@/types/team';

export default function TeamMembers() {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    // Function to fetch team members from the API
    const fetchTeamMembers = async () => {
      try {
        setLoading(true);

        // Fetch team members from the API
        const response = await fetch('/api/team');
        const data = await response.json();

        // Check if the response is successful
        if (!response.ok) {
          console.error('API error:', data);
          throw new Error(data.error || `Error: ${response.status}`);
        }

        // Check if data is an array
        if (Array.isArray(data)) {
          console.log('Team members loaded:', data.length);
          setTeamMembers(data);
        } else if (data.error) {
          throw new Error(data.error);
        } else {
          // If no team members are found, use mock data
          console.log('No team members found, using mock data');
          setTeamMembers(getFallbackTeamMembers());
        }
      } catch (err) {
        console.error('Failed to load team members:', err);
        setError('Failed to load team members');
        // Use mock data as fallback
        setTeamMembers(getFallbackTeamMembers());
      } finally {
        setLoading(false);
      }
    };

    fetchTeamMembers();
  }, []);

  // Fallback team members data that matches the screenshot
  const getFallbackTeamMembers = (): TeamMember[] => {
    return [
      {
        id: '1',
        name: 'Don Omondi Onyango',
        role: 'Graphics Designer | Web Developer',
        bio: 'Creative designer with expertise in web development and graphic design.',
        imageSrc: '/images/team/don-omondi.jpg',
        order: 0,
        linkedinUrl: 'https://linkedin.com/in/donomondi',
        githubUrl: 'https://github.com/onyangodonomondi',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '2',
        name: 'Jack Sequeira Onyango',
        role: 'Graphics Designer',
        bio: 'Passionate graphic designer with an eye for detail and creative solutions.',
        imageSrc: '/images/team/jack-sequeira.jpg',
        order: 1,
        linkedinUrl: 'https://linkedin.com/in/jacksequeira',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '3',
        name: 'Sarah Wanjiku',
        role: 'UI/UX Designer',
        bio: 'Experienced UI/UX designer focused on creating intuitive and engaging user experiences.',
        imageSrc: '/images/team/sarah-wanjiku.jpg',
        order: 2,
        linkedinUrl: 'https://linkedin.com/in/sarahwanjiku',
        twitterUrl: 'https://twitter.com/sarahwanjiku',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '4',
        name: 'James Mwangi',
        role: 'Frontend Developer',
        bio: 'Frontend developer specializing in React and modern JavaScript frameworks.',
        imageSrc: '/images/team/james-mwangi.jpg',
        order: 3,
        githubUrl: 'https://github.com/jamesmwangi',
        linkedinUrl: 'https://linkedin.com/in/jamesmwangi',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
  };

  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF5400]"></div>
      </div>
    );
  }

  if (error) {
    return null; // Don't show error on the frontend
  }

  if (teamMembers.length === 0) {
    return null; // Don't show empty section
  }

  return (
    <section className="py-16 relative overflow-hidden bg-white">
      <div className="container mx-auto px-4 relative z-10">
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            Meet the Experts
          </h2>
          <div className="h-1 w-24 bg-[#FF5400] mx-auto mb-6"></div>
          <p className="text-gray-700 text-lg max-w-2xl mx-auto">
            Our talented team of professionals is dedicated to bringing your digital vision to life.
          </p>
        </motion.div>

        {/* Team members grid - 3 experts per row with bio */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {teamMembers.slice(0, 6).map((member, index) => (
            <motion.div
              key={member.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 hover:transform hover:scale-105 group"
            >
              {/* Image container - Square aspect ratio */}
              <div className="relative w-full aspect-square overflow-hidden">
                <Image
                  src={member.imageSrc}
                  alt={member.name}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 25vw"
                  className="object-cover transition-transform duration-300 group-hover:scale-110"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>

              {/* Content section */}
              <div className="p-6">
                {/* Name and role */}
                <div className="text-center mb-4">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h3>
                  <p className="text-[#FF5400] font-semibold text-sm">{member.role}</p>
                </div>

                {/* Bio */}
                <div className="mb-4">
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {member.bio}
                  </p>
                </div>

                {/* Social links */}
                {(member.linkedinUrl || member.twitterUrl || member.githubUrl || member.emailAddress) && (
                  <div className="flex justify-center space-x-3 pt-4 border-t border-gray-100">
                    {member.linkedinUrl && (
                      <a
                        href={member.linkedinUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 transition-colors duration-200 p-2 rounded-full hover:bg-blue-50"
                      >
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clipRule="evenodd" />
                        </svg>
                        <span className="sr-only">LinkedIn</span>
                      </a>
                    )}
                    {member.twitterUrl && (
                      <a
                        href={member.twitterUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-400 hover:text-blue-600 transition-colors duration-200 p-2 rounded-full hover:bg-blue-50"
                      >
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
                        </svg>
                        <span className="sr-only">Twitter</span>
                      </a>
                    )}
                    {member.githubUrl && (
                      <a
                        href={member.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-800 hover:text-gray-600 transition-colors duration-200 p-2 rounded-full hover:bg-gray-50"
                      >
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                        </svg>
                        <span className="sr-only">GitHub</span>
                      </a>
                    )}
                    {member.emailAddress && (
                      <a
                        href={`mailto:${member.emailAddress}`}
                        className="text-red-500 hover:text-red-700 transition-colors duration-200 p-2 rounded-full hover:bg-red-50"
                      >
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                          <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                        </svg>
                        <span className="sr-only">Email</span>
                      </a>
                    )}
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
