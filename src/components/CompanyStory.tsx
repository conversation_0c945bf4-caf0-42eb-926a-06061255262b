'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';

// Hardcoded company story data
const companyStory = {
  title: 'Our Journey',
  subtitle: 'Delivering exceptional design, branding, and digital marketing solutions for Kenyan businesses',
  imageSrc: '/images/about/ceo.jpg',
  quote1: 'We started Mocky Digital with a simple mission: to help Kenyan businesses succeed in the digital world through exceptional design and marketing. Our team combines creative expertise with strategic thinking to deliver custom solutions that elevate brands and drive growth in today\'s competitive marketplace.',
  quote2: 'Every day, we strive to deliver work that not only looks great but also drives real business results for our clients. Whether it\'s logo design, web development, or digital marketing campaigns, we approach each project with the same dedication to quality and measurable outcomes.',
  founder<PERSON><PERSON>: '<PERSON>',
  founder<PERSON><PERSON>: 'Founder & Creative Director',
  linkedinUrl: 'https://www.linkedin.com/in/don-omondi-*********/',
  twitterUrl: 'https://x.com/onyango__omondi',
  instagramUrl: 'https://www.instagram.com/tk_omondi/',
  tiktokUrl: 'https://www.tiktok.com/@mocky_digital'
};

export default function CompanyStory() {
  // Use the hardcoded data directly instead of fetching from API

  return (
    <section className="py-20 relative overflow-hidden bg-white">
      {/* Dynamic background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full bg-[url('/images/grid.svg')] opacity-[0.03]"></div>

        {/* Subtle background elements */}
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-[#0A2647] rounded-full mix-blend-multiply filter blur-[120px] opacity-[0.03]"></div>
        <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-[#FF5400] rounded-full mix-blend-multiply filter blur-[120px] opacity-[0.03]"></div>
      </div>

      <div className="container relative z-10">
        <div className="max-w-7xl mx-auto">
          {/* Header with animated reveal */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
            className="text-center mb-16 md:mb-20"
          >
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <span className="inline-block px-5 py-2 rounded-full bg-[#0A2647]/5 text-[#0A2647] text-xs font-semibold mb-5 shadow-sm">Our Story</span>
            </motion.div>

            <motion.h2
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-4xl lg:text-5xl font-bold text-[#0A2647] mb-6 tracking-tight"
            >
              {companyStory.title}
            </motion.h2>

            <motion.div
              initial={{ y: 40, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.7, delay: 0.3 }}
              viewport={{ once: true }}
              className="flex justify-center"
            >
              <div className="h-1 w-20 bg-[#FF5400] mb-6"></div>
            </motion.div>

            <motion.p
              initial={{ y: 50, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="text-[#0A2647]/70 text-lg max-w-3xl mx-auto"
            >
              {companyStory.subtitle}
            </motion.p>
          </motion.div>

          {/* Content layout */}
          <div className="grid lg:grid-cols-12 gap-8 md:gap-12 items-center">
            {/* Founder image - takes 5 columns */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7 }}
              viewport={{ once: true }}
              className="lg:col-span-5 relative"
            >
              <div className="relative group">
                {/* Image container with clean styling */}
                <div className="relative rounded-xl overflow-hidden shadow-lg">
                  <Image
                    src={companyStory.imageSrc}
                    alt={`${companyStory.founderName} of Mocky Digital`}
                    width={600}
                    height={750}
                    className="object-cover w-full aspect-[4/5]"
                    style={{ objectPosition: "center 10%" }}
                    priority
                  />

                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-[#0A2647] to-transparent opacity-70"></div>

                  {/* Founder info with improved styling */}
                  <div className="absolute bottom-0 left-0 p-6 md:p-8 text-white w-full">
                    <h3 className="text-xl md:text-2xl font-bold mb-1 group-hover:text-[#FF5400] transition-colors duration-300">
                      {companyStory.founderName}
                    </h3>
                    <p className="text-white/90 text-sm font-medium tracking-wide mb-4">
                      {companyStory.founderRole}
                    </p>

                    {/* Social links with improved styling */}
                    <div className="flex flex-wrap gap-3 mt-2">
                      {/* LinkedIn */}
                      <a
                        href={companyStory.linkedinUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-8 h-8 md:w-9 md:h-9 rounded-full bg-white/10 hover:bg-[#0A66C2] flex items-center justify-center text-white transition-all duration-300 transform hover:scale-110"
                        aria-label="LinkedIn Profile"
                      >
                        <i className="fab fa-linkedin-in text-sm md:text-base"></i>
                      </a>

                      {/* Twitter/X */}
                      <a
                        href={companyStory.twitterUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-8 h-8 md:w-9 md:h-9 rounded-full bg-white/10 hover:bg-[#1DA1F2] flex items-center justify-center text-white transition-all duration-300 transform hover:scale-110"
                        aria-label="Twitter Profile"
                      >
                        <i className="fab fa-twitter text-sm md:text-base"></i>
                      </a>

                      {/* Instagram */}
                      <a
                        href={companyStory.instagramUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-8 h-8 md:w-9 md:h-9 rounded-full bg-white/10 hover:bg-gradient-to-br hover:from-[#833AB4] hover:via-[#FD1D1D] hover:to-[#FCAF45] flex items-center justify-center text-white transition-all duration-300 transform hover:scale-110"
                        aria-label="Instagram Profile"
                      >
                        <i className="fab fa-instagram text-sm md:text-base"></i>
                      </a>

                      {/* TikTok */}
                      <a
                        href={companyStory.tiktokUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-8 h-8 md:w-9 md:h-9 rounded-full bg-white/10 hover:bg-[#000000] flex items-center justify-center text-white transition-all duration-300 transform hover:scale-110"
                        aria-label="TikTok Profile"
                      >
                        <i className="fab fa-tiktok text-sm md:text-base"></i>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Vertical timeline connector - takes 2 columns */}
            <div className="hidden lg:block lg:col-span-2 h-full">
              <div className="flex flex-col items-center justify-center h-full">
                <div className="w-0.5 h-24 bg-gradient-to-b from-transparent via-[#FF5400]/50 to-[#0A2647]/50"></div>
                <div className="w-6 h-6 rounded-full bg-[#0A2647] flex items-center justify-center border-2 border-[#FF5400]">
                  <div className="w-2 h-2 rounded-full bg-white"></div>
                </div>
                <div className="w-0.5 h-24 bg-gradient-to-b from-[#0A2647]/50 via-[#FF5400]/50 to-transparent"></div>
              </div>
            </div>

            {/* Quote section - takes 5 columns */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7, delay: 0.2 }}
              viewport={{ once: true }}
              className="lg:col-span-5"
            >
              <div className="relative">
                {/* Quote container with clean styling */}
                <div className="relative p-6 md:p-8 rounded-xl bg-white shadow-md border border-gray-100">
                  <div className="relative z-10">
                    {/* Enhanced quote styling */}
                    <div className="text-5xl md:text-6xl text-[#0A2647] font-serif leading-none opacity-30">"</div>

                    <div className="space-y-4 my-4 md:my-6">
                      <p className="text-lg md:text-xl text-[#0A2647] leading-relaxed">
                        {companyStory.quote1}
                      </p>
                      {companyStory.quote2 && (
                        <p className="text-lg md:text-xl text-[#0A2647]/80 leading-relaxed">
                          {companyStory.quote2}
                        </p>
                      )}
                    </div>

                    <div className="text-5xl md:text-6xl text-[#0A2647] font-serif text-right leading-none opacity-30">"</div>

                    {/* Year marker */}
                    <div className="absolute -right-3 -bottom-3 w-14 h-14 rounded-full bg-[#0A2647] flex items-center justify-center text-white font-bold shadow-md">
                      {new Date().getFullYear()}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
