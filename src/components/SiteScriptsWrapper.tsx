'use client';

import { useEffect, useState } from 'react';
import { SiteScript } from '@prisma/client';

interface SiteScriptsWrapperProps {
  scriptType: 'head' | 'body' | 'footer';
}

export default function SiteScriptsWrapper({ scriptType }: SiteScriptsWrapperProps) {
  const [scripts, setScripts] = useState<SiteScript[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchScripts = async () => {
      try {
        const response = await fetch(`/api/scripts?type=${scriptType}`);
        if (response.ok) {
          const data = await response.json();
          setScripts(data);
        }
      } catch (error) {
        console.error(`Error fetching ${scriptType} scripts:`, error);
      } finally {
        setLoading(false);
      }
    };

    fetchScripts();
  }, [scriptType]);

  if (loading || scripts.length === 0) {
    return null;
  }

  // For head scripts, we need to render them differently to avoid div in head
  if (scriptType === 'head') {
    return (
      <>
        {scripts.map((script) => (
          <script
            key={script.id}
            dangerouslySetInnerHTML={{ __html: script.content }}
          />
        ))}
      </>
    );
  }

  // For body and footer scripts, div is fine
  return (
    <>
      {scripts.map((script) => (
        <div key={script.id} dangerouslySetInnerHTML={{ __html: script.content }} />
      ))}
    </>
  );
}
