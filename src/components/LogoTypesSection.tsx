'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';

interface LogoType {
  type: string;
  description: string;
  image: string;
  examples: string[];
}

interface LogoTypesSectionProps {
  logoTypes: LogoType[];
}

export default function LogoTypesSection({ logoTypes }: LogoTypesSectionProps) {
  const [mounted, setMounted] = useState(false);
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [autoSwitch, setAutoSwitch] = useState(true);

  // Auto-switch between logo types
  useEffect(() => {
    if (!autoSwitch || !mounted || logoTypes.length === 0) return;

    const interval = setInterval(() => {
      setSelectedType(prevType => {
        const currentIndex = logoTypes.findIndex(logo => logo.type === prevType);
        const nextIndex = (currentIndex + 1) % logoTypes.length;
        return logoTypes[nextIndex].type;
      });
    }, 5000); // Switch every 5 seconds

    return () => clearInterval(interval);
  }, [logoTypes, autoSwitch, mounted]);

  useEffect(() => {
    setMounted(true);
    // Set the first logo type as selected by default
    if (logoTypes.length > 0) {
      setSelectedType(logoTypes[0].type);
    }
  }, [logoTypes]);

  if (!mounted) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-[#0A1929] mb-3">
              Types of Logo Designs
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Understanding different logo types can help you choose the perfect style for your brand
            </p>
          </div>

          <div className="flex justify-center gap-3 mb-8">
            {logoTypes.map((logo) => (
              <div key={logo.type} className="h-10 w-24 bg-gray-200 rounded-full animate-pulse"></div>
            ))}
          </div>

          <div className="max-w-5xl mx-auto bg-white rounded-xl shadow-sm p-6 animate-pulse">
            <div className="h-64 bg-gray-200 rounded-lg mb-6"></div>
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6 mb-6"></div>
            <div className="h-10 bg-gray-200 rounded-full w-1/3"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-10"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-[#0A1929] mb-3">
            Types of Logo Designs
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Understanding different logo types can help you choose the perfect style for your brand
          </p>
        </motion.div>

        {/* Logo Type Tabs */}
        <div className="mb-10">
          <div className="flex justify-center gap-1 md:gap-3">
            {logoTypes.map((logo, index) => (
              <motion.button
                key={logo.type}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={`px-2 md:px-5 py-2 rounded-full text-xs md:text-base font-medium transition-all duration-200 ${
                  selectedType === logo.type
                    ? 'bg-[#FF5400] text-white shadow-md'
                    : 'bg-white text-[#0A1929] hover:bg-[#0A1929] hover:text-white border border-[#0A1929]/10'
                } ${autoSwitch && selectedType === logo.type ? 'relative' : ''}`}
                onClick={() => {
                  setSelectedType(logo.type);
                  setAutoSwitch(false); // Pause auto-switching when user selects a type
                }}
              >
                {logo.type}
                {autoSwitch && selectedType === logo.type && (
                  <span className="absolute -top-1 -right-1 w-2 h-2 bg-[#FF5400] rounded-full animate-pulse"></span>
                )}
              </motion.button>
            ))}
          </div>

          {/* Auto-switch toggle */}
          <div className="flex justify-center mt-4">
            <button
              onClick={() => setAutoSwitch(!autoSwitch)}
              className={`text-xs flex items-center gap-1 px-3 py-1 rounded-full transition-colors ${
                autoSwitch
                  ? 'bg-[#0A1929]/10 text-[#0A1929]'
                  : 'bg-white text-gray-500 border border-gray-200'
              }`}
            >
              {autoSwitch ? (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clipRule="evenodd" />
                  </svg>
                  Auto-switching
                </>
              ) : (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                  </svg>
                  Start auto-switching
                </>
              )}
            </button>
          </div>
        </div>

        {/* Selected Logo Type Display */}
        {selectedType && (
          <motion.div
            key={selectedType}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.4 }}
            className="max-w-5xl mx-auto"
          >
            {logoTypes.map((logo) => {
              if (logo.type !== selectedType) return null;

              return (
                <div key={logo.type} className="bg-white rounded-xl shadow-sm overflow-hidden">
                  <div className="grid md:grid-cols-2 gap-0">
                    <div className="bg-[#0A1929]/5 p-6 md:p-8 flex items-center justify-center">
                      <div className="relative w-full h-56 md:h-72">
                        <Image
                          src={logo.image}
                          alt={`${logo.type} Logo Example`}
                          fill
                          className="object-contain"
                          sizes="(max-width: 768px) 100vw, 50vw"
                        />
                      </div>
                    </div>

                    <div className="p-6 md:p-8 flex flex-col justify-center">
                      <div className="mb-1">
                        <span className="inline-block px-3 py-1 text-xs font-medium bg-[#0A1929] text-white rounded-full mb-3">
                          Logo Type
                        </span>
                      </div>
                      <h3 className="text-2xl md:text-3xl font-bold text-[#0A1929] mb-3">{logo.type} Logos</h3>
                      <p className="text-gray-700 mb-5">{logo.description}</p>

                      <div className="bg-[#0A1929]/5 p-4 rounded-lg mb-6">
                        <h4 className="font-medium text-[#0A1929] mb-1 text-sm">Popular Examples:</h4>
                        <p className="text-gray-700 font-medium">
                          {logo.examples.map((example, i) => (
                            <span key={example}>
                              {example}
                              {i < logo.examples.length - 1 && <span className="mx-1.5 text-[#FF5400]">•</span>}
                            </span>
                          ))}
                        </p>
                      </div>

                      <button
                        className="px-6 py-3 bg-[#FF5400] text-white rounded-lg font-medium hover:bg-[#E54D00] transition-colors shadow-sm hover:shadow-md flex items-center justify-center gap-2 w-full md:w-auto"
                        onClick={() => window.scrollTo({
                          top: document.getElementById('pricing-section')?.offsetTop || 0,
                          behavior: 'smooth'
                        })}
                      >
                        <span>Get Your {logo.type} Logo</span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M5 12h14"></path>
                          <path d="m12 5 7 7-7 7"></path>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </motion.div>
        )}
      </div>
    </section>
  );
}
