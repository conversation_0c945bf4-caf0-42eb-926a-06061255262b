'use client';

import type { ImageItem } from '@/utils/getImages';
import ClientGallerySection from '@/components/ClientGallerySection';

// Client component wrapper for the gallery
export default function LogoGalleryWrapper({ logos }: { logos: ImageItem[] }) {
  return (
    <div className="w-full">
      <ClientGallerySection
        items={logos}
        gridCols="grid-cols-2 md:grid-cols-3"
        aspectRatio="aspect-square"
        objectFit="object-contain"
        category="logos"
      />
    </div>
  );
}