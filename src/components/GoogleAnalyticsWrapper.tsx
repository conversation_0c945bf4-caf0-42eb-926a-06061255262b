'use client';

import { useEffect, useState } from 'react';
import Cookies from 'js-cookie';

export default function GoogleAnalyticsWrapper() {
  const [hasConsent, setHasConsent] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // Get the Google Analytics ID from environment variables or use a fallback
  const gaId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || 'G-XXXXXXXXXX';

  useEffect(() => {
    setIsClient(true);

    // Check if user has given analytics consent
    const consentStatus = Cookies.get('cookie-consent-status');
    const analyticsConsent = Cookies.get('cookie-analytics-consent');

    // Only initialize Google Analytics if user has explicitly given consent
    if (consentStatus === 'set' && analyticsConsent === 'true') {
      setHasConsent(true);
    }
  }, []);

  // Don't render anything on the server side
  if (!isClient) return null;

  // Don't render if no consent
  if (!hasConsent) return null;

  return (
    <>
      <script
        async
        src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`}
      />
      <script
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${gaId}');
          `,
        }}
      />
    </>
  );
}