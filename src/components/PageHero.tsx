interface PageHeroProps {
  title: string;
  subtitle?: string;
  bgImage?: string;
  description?: string;
}

export default function PageHero({ title, subtitle, bgImage, description }: PageHeroProps) {
  return (
    <section className="bg-gradient-to-b from-[#1a2942] to-[#121f35] text-white py-16 md:py-20 mt-24">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <div className="flex justify-center mb-6">
            <div className="w-16 h-1 bg-[#FF5400]"></div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">{title}</h1>
          {subtitle && <p className="text-lg md:text-xl text-gray-300 mb-4">{subtitle}</p>}
          {description && <p className="text-lg text-gray-300">{description}</p>}
        </div>
      </div>
    </section>
  );
}