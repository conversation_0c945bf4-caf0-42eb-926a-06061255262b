'use client';

import { useState, useEffect, useRef, FormEvent } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { trackLogoFormSubmission } from '@/utils/analytics';

interface LogoDesignBriefModalProps {
  isOpen: boolean;
  onClose: () => void;
  packageName: string;
  packagePrice: string;
  packageFeatures: string[];
  whatsappMessage: string;
}

export default function LogoDesignBriefModal({
  isOpen,
  onClose,
  packageName,
  packagePrice,
  packageFeatures,
  whatsappMessage
}: LogoDesignBriefModalProps) {
  const [formData, setFormData] = useState({
    businessName: '',
    industry: '',
    logoType: 'wordmark',
    slogan: '',
    additionalInfo: ''
  });

  const modalRef = useRef<HTMLDivElement>(null);

  // Handle escape key press
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    // <PERSON>le clicks outside the modal
    const handleClickOutside = (e: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleClickOutside);

    // Prevent body scrolling
    document.body.style.overflow = 'hidden';

    // Clean up
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  // If modal is not open, don't render anything
  if (!isOpen) return null;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();

    // Create enhanced WhatsApp message with form data
    const enhancedMessage = `${whatsappMessage}

*Logo Design Brief:*
Business Name: ${formData.businessName}
Industry/Niche: ${formData.industry}
Logo Type: ${formData.logoType}
${formData.slogan ? `Slogan: ${formData.slogan}` : ''}
${formData.additionalInfo ? `Additional Information: ${formData.additionalInfo}` : ''}`;

    // Track form submission with Facebook Pixel
    trackLogoFormSubmission(packageName, packagePrice, formData);

    // Create WhatsApp URL with the enhanced message
    const whatsappUrl = `https://wa.me/254741590670?text=${encodeURIComponent(enhancedMessage)}`;

    // Open WhatsApp in a new tab
    window.open(whatsappUrl, '_blank', 'noopener,noreferrer');

    // Close the modal
    onClose();
  };

  const logoTypes = [
    { value: 'wordmark', label: 'Wordmark (Text-based logo like Coca-Cola, Google)' },
    { value: 'lettermark', label: 'Lettermark (Initials/acronyms like IBM, HBO)' },
    { value: 'symbol', label: 'Symbol/Icon (Graphic symbol like Apple, Twitter)' },
    { value: 'combination', label: 'Combination (Text and symbol together)' },
    { value: 'emblem', label: 'Emblem (Text inside a symbol like Starbucks)' },
    { value: 'mascot', label: 'Mascot (Character-based logo)' },
    { value: 'abstract', label: 'Abstract (Geometric forms like Pepsi)' },
    { value: 'not-sure', label: 'Not sure (Designer will recommend)' }
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 z-50 bg-black/75 flex items-center justify-center p-4 backdrop-blur-sm"
        >
          <motion.div
            ref={modalRef}
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.4, type: "spring" }}
            className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-6 md:p-8">
              <div className="flex justify-between items-center mb-6 pb-4 border-b">
                <h3 className="text-2xl font-bold text-gray-900">Logo Design Brief</h3>
                <motion.button
                  whileHover={{ scale: 1.1, rotate: 90 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={onClose}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  aria-label="Close"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </motion.button>
              </div>

              <div className="mb-6">
                <div className="bg-gray-50 p-6 rounded-xl mb-6 border border-gray-100">
                  <h4 className="font-semibold text-lg mb-3 text-[#FF5400]">{packageName} - KSH {packagePrice}</h4>
                  <ul className="text-sm text-gray-600 space-y-2">
                    {packageFeatures.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-green-500 mr-2 flex-shrink-0">✓</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <p className="text-gray-600 mb-4">
                  Please fill out this brief form to help us understand your logo requirements better.
                  This information will help our designers create a logo that perfectly represents your brand.
                </p>
              </div>

              <form onSubmit={handleSubmit}>
                <div className="space-y-5">
                  <div>
                    <label htmlFor="businessName" className="block text-sm font-medium text-gray-700 mb-1">
                      Business Name *
                    </label>
                    <input
                      type="text"
                      id="businessName"
                      name="businessName"
                      value={formData.businessName}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#FF5400] focus:border-[#FF5400] transition-colors"
                      placeholder="Your business or brand name"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-1">
                      Industry/Niche *
                    </label>
                    <input
                      type="text"
                      id="industry"
                      name="industry"
                      value={formData.industry}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#FF5400] focus:border-[#FF5400] transition-colors"
                      placeholder="e.g. Restaurant, Technology, Fashion"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="logoType" className="block text-sm font-medium text-gray-700 mb-1">
                      Logo Type *
                    </label>
                    <select
                      id="logoType"
                      name="logoType"
                      value={formData.logoType}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#FF5400] focus:border-[#FF5400] transition-colors"
                      required
                    >
                      {logoTypes.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="slogan" className="block text-sm font-medium text-gray-700 mb-1">
                      Slogan/Tagline (Optional)
                    </label>
                    <input
                      type="text"
                      id="slogan"
                      name="slogan"
                      value={formData.slogan}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#FF5400] focus:border-[#FF5400] transition-colors"
                      placeholder="Your business slogan or tagline if you have one"
                    />
                  </div>

                  <div>
                    <label htmlFor="additionalInfo" className="block text-sm font-medium text-gray-700 mb-1">
                      Additional Information (Optional)
                    </label>
                    <textarea
                      id="additionalInfo"
                      name="additionalInfo"
                      value={formData.additionalInfo}
                      onChange={handleChange}
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#FF5400] focus:border-[#FF5400] transition-colors"
                      placeholder="Any other details about your brand, color preferences, style preferences, etc."
                    ></textarea>
                  </div>
                </div>

                <div className="mt-8 flex flex-col sm:flex-row justify-end gap-3">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    type="button"
                    onClick={onClose}
                    className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    type="submit"
                    className="px-6 py-3 bg-[#FF5400] text-white rounded-lg hover:bg-[#E54D00] focus:outline-none focus:ring-2 focus:ring-[#FF5400] shadow-md hover:shadow-lg transition-all"
                  >
                    Submit & Continue to WhatsApp
                  </motion.button>
                </div>
              </form>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
