'use client';

import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import type { ImageItem } from '@/utils/getImages';

// Fallback image for errors
const FALLBACK_IMAGE = '/images/placeholder.jpg';

interface ModernLogoGalleryProps {
  logos: ImageItem[];
}

export default function ModernLogoGallery({ logos }: ModernLogoGalleryProps) {
  const [mounted, setMounted] = useState(false);
  const [visibleLogos, setVisibleLogos] = useState<number>(6); // Show 6 logos initially
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedLogo, setSelectedLogo] = useState<ImageItem | null>(null);
  const [safeLogos, setSafeLogos] = useState<ImageItem[]>([]);

  // Set mounted state and prepare safe logos on client-side only
  useEffect(() => {
    setMounted(true);

    // Process logos on client side only
    if (Array.isArray(logos)) {
      const filtered = logos.filter(item =>
        item && typeof item === 'object' && (item.url || item.src)
      );
      setSafeLogos(filtered);
    }
  }, [logos]);

  const handleLoadMore = useCallback(() => {
    // Only run on client side
    if (!mounted) return;

    setIsLoading(true);
    // Add a small delay for better UX
    setTimeout(() => {
      setVisibleLogos(prev => Math.min(prev + 6, safeLogos.length));
      setIsLoading(false);
    }, 500);
  }, [mounted, safeLogos.length]);

  const handleLogoClick = useCallback((logo: ImageItem) => {
    setSelectedLogo(logo);
  }, []);

  const closeModal = useCallback(() => {
    setSelectedLogo(null);
  }, []);

  // Show loading skeletons before mounting
  if (!mounted) {
    return (
      <div className="min-h-[300px] py-8">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 lg:gap-8">
          {Array.from({ length: 8 }).map((_, index) => (
            <div
              key={`skeleton-${index}`}
              className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden animate-pulse"
            />
          ))}
        </div>
      </div>
    );
  }

  // Handle case where no logos are provided
  if (!safeLogos.length) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">No logo examples are available at the moment.</p>
      </div>
    );
  }

  // Get the visible subset of logos
  const displayedLogos = safeLogos.slice(0, visibleLogos);

  return (
    <>
      <div className="min-h-[300px]">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 lg:gap-8"
        >
          {displayedLogos.map((logo, index) => (
            <motion.div
              key={logo.id || index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.05 }}
              className="group relative aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer transition-transform hover:scale-105"
              onClick={() => handleLogoClick(logo)}
            >
              <Image
                src={logo.url || logo.src || FALLBACK_IMAGE}
                alt={logo.alt || 'Logo design example'}
                fill
                sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 25vw"
                className="object-cover transition-all group-hover:scale-105 duration-500"
                loading={index < 4 ? "eager" : "lazy"}
                onError={(e) => {
                  // Fallback for images that fail to load
                  (e.target as HTMLImageElement).src = FALLBACK_IMAGE;
                }}
              />

              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4">
                <h3 className="text-white text-lg font-medium">
                  {logo.alt || logo.title || 'Logo Design'}
                </h3>
                <p className="text-white/80 text-sm capitalize">
                  Logo Design
                </p>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Load More Button */}
        {visibleLogos < safeLogos.length && (
          <div className="mt-12 text-center">
            <motion.button
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              onClick={handleLoadMore}
              disabled={isLoading}
              className="px-8 py-3 bg-[#0A1929] text-white rounded-full font-medium hover:bg-[#152A3B] transition-colors disabled:opacity-75 shadow-md hover:shadow-lg"
            >
              {isLoading ? (
                <>
                  <span className="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
                  Loading...
                </>
              ) : (
                'Load More Logos'
              )}
            </motion.button>
          </div>
        )}
      </div>

      {/* Logo Modal */}
      <AnimatePresence>
        {selectedLogo && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4"
            onClick={closeModal}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="relative bg-white rounded-xl overflow-hidden max-w-4xl w-full max-h-[90vh]"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Loading background */}
              <div className="absolute inset-0 bg-gray-100 animate-pulse-subtle"></div>

              <div className="relative h-[70vh]">
                <Image
                  src={selectedLogo.url || selectedLogo.src || FALLBACK_IMAGE}
                  alt={selectedLogo.alt || 'Logo design'}
                  fill
                  className="object-contain"
                  sizes="90vw"
                  quality={90}
                  priority
                />
              </div>

              <div className="p-6 bg-white border-t border-gray-100">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {selectedLogo.alt || selectedLogo.title || 'Logo Design'}
                </h3>
                <p className="text-gray-600 text-sm">Logo Design</p>

                <button
                  onClick={closeModal}
                  className="absolute top-4 right-4 bg-white/80 backdrop-blur-sm text-gray-800 p-2 rounded-full hover:bg-gray-200 transition-colors"
                  aria-label="Close"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
