'use client';

import { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import Cookies from 'js-cookie';

const COOKIE_CONSENT_KEY = 'cookie-consent-status';
const COOKIE_ANALYTICS_KEY = 'cookie-analytics-consent';
const COOKIE_MARKETING_KEY = 'cookie-marketing-consent';

export default function CookieConsent() {
  const [isVisible, setIsVisible] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [analyticsConsent, setAnalyticsConsent] = useState(true);
  const [marketingConsent, setMarketingConsent] = useState(true);

  useEffect(() => {
    // Check if user has already made a choice
    const consentStatus = Cookies.get(COOKIE_CONSENT_KEY);

    if (!consentStatus) {
      // If no consent status is found, show the banner after a short delay
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 1000);

      return () => clearTimeout(timer);
    } else {
      // Load saved preferences
      const savedAnalyticsConsent = Cookies.get(COOKIE_ANALYTICS_KEY) === 'true';
      const savedMarketingConsent = Cookies.get(COOKIE_MARKETING_KEY) === 'true';

      setAnalyticsConsent(savedAnalyticsConsent);
      setMarketingConsent(savedMarketingConsent);

      // Apply the saved preferences
      if (savedMarketingConsent) {
        enableMetaPixel();
      }

      if (savedAnalyticsConsent) {
        enableGoogleAnalytics();
      }
    }
  }, []);

  const acceptAll = () => {
    setAnalyticsConsent(true);
    setMarketingConsent(true);

    savePreferences(true, true);
    setIsVisible(false);

    // Enable tracking scripts
    enableMetaPixel();
    enableGoogleAnalytics();
  };

  const acceptSelected = () => {
    savePreferences(analyticsConsent, marketingConsent);
    setIsVisible(false);

    // Enable tracking scripts based on consent
    if (marketingConsent) {
      enableMetaPixel();
    }

    if (analyticsConsent) {
      enableGoogleAnalytics();
    }
  };

  const rejectAll = () => {
    setAnalyticsConsent(false);
    setMarketingConsent(false);

    savePreferences(false, false);
    setIsVisible(false);
  };

  const savePreferences = (analytics: boolean, marketing: boolean) => {
    // Set cookies with a 6-month expiry
    const expiryDays = 180;

    Cookies.set(COOKIE_CONSENT_KEY, 'set', { expires: expiryDays, sameSite: 'strict' });
    Cookies.set(COOKIE_ANALYTICS_KEY, analytics.toString(), { expires: expiryDays, sameSite: 'strict' });
    Cookies.set(COOKIE_MARKETING_KEY, marketing.toString(), { expires: expiryDays, sameSite: 'strict' });
  };

  const enableMetaPixel = () => {
    // Initialize Meta Pixel if it's not already initialized
    if (typeof window !== 'undefined' && !window.fbq) {
      const pixelId = process.env.NEXT_PUBLIC_META_PIXEL_ID;

      if (pixelId) {
        // Add Meta Pixel base code
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');

        // Initialize pixel
        window.fbq('init', pixelId);
        window.fbq('track', 'PageView');
      }
    }
  };

  const enableGoogleAnalytics = () => {
    // Initialize Google Analytics if it's not already initialized
    if (typeof window !== 'undefined' && !window.gtag) {
      const gaId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;

      if (gaId) {
        // Create and add the Google Analytics script
        const gaScript = document.createElement('script');
        gaScript.async = true;
        gaScript.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;
        document.head.appendChild(gaScript);

        // Initialize Google Analytics
        window.dataLayer = window.dataLayer || [];
        function gtag() {
          window.dataLayer.push(arguments);
        }
        gtag('js', new Date());
        gtag('config', gaId);

        // Assign gtag to window
        window.gtag = gtag;
      }
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-white border-t border-gray-200 shadow-lg">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between">
          <div className="mb-4 md:mb-0 pr-4">
            <h3 className="text-lg font-medium text-gray-900">Cookie Preferences</h3>
            <p className="mt-1 text-sm text-gray-600">
              We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking "Accept All", you consent to our use of cookies.
            </p>

            {showDetails && (
              <div className="mt-4 space-y-3">
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="analytics"
                      type="checkbox"
                      checked={analyticsConsent}
                      onChange={(e) => setAnalyticsConsent(e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="analytics" className="font-medium text-gray-700">Analytics</label>
                    <p className="text-gray-500">Help us improve by allowing us to collect anonymous usage data.</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="marketing"
                      type="checkbox"
                      checked={marketingConsent}
                      onChange={(e) => setMarketingConsent(e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="marketing" className="font-medium text-gray-700">Marketing</label>
                    <p className="text-gray-500">Allow us to provide personalized ads via Meta Pixel and other services.</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
            <button
              type="button"
              onClick={() => setShowDetails(!showDetails)}
              className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              {showDetails ? 'Hide Details' : 'Customize'}
            </button>

            <button
              type="button"
              onClick={rejectAll}
              className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              Reject All
            </button>

            {showDetails ? (
              <button
                type="button"
                onClick={acceptSelected}
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              >
                Save Preferences
              </button>
            ) : (
              <button
                type="button"
                onClick={acceptAll}
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              >
                Accept All
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
