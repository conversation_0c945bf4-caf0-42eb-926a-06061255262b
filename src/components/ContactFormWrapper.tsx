'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import the ContactForm component with no SSR
const ContactForm = dynamic(() => import('./ContactForm'), {
  ssr: false,
  loading: () => (
    <div className="space-y-6">
      <div className="h-12 bg-gray-100 rounded animate-pulse"></div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
        <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
        <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
      </div>
      <div className="h-32 bg-gray-100 rounded animate-pulse"></div>
      <div className="h-14 bg-gray-100 rounded animate-pulse"></div>
    </div>
  )
});

export default function ContactFormWrapper() {
  const [mounted, setMounted] = useState(false);

  // Set mounted state when component mounts
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Only render ContactForm on the client-side
  if (!mounted) {
    return (
      <div className="space-y-6">
        <div className="h-12 bg-gray-100 rounded animate-pulse"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
          <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
          <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
        </div>
        <div className="h-32 bg-gray-100 rounded animate-pulse"></div>
        <div className="h-14 bg-gray-100 rounded animate-pulse"></div>
      </div>
    );
  }

  // Error boundary for the contact form
  try {
    return <ContactForm />;
  } catch (error) {
    console.error('Error rendering ContactForm:', error);
    return (
      <div className="p-4 bg-red-50 text-red-700 rounded-lg">
        There was an error loading the contact form. Please try again later or contact us <NAME_EMAIL>.
      </div>
    );
  }
} 