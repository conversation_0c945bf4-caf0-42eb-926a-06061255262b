'use client';

import React, { useState, useEffect } from 'react';
import type { ImageItem } from '@/utils/getImages';

// Fallback image for errors
const FALLBACK_IMAGE = '/images/placeholder.jpg';

interface LogoGalleryProps {
  logos: ImageItem[];
}

export default function LogoGallery({ logos }: LogoGalleryProps) {
  const [mounted, setMounted] = useState(false);
  const [visibleLogos, setVisibleLogos] = useState<number>(8); // Show 8 logos initially
  const [isLoading, setIsLoading] = useState<boolean>(false);
  // For client-side processing
  const [safeLogos, setSafeLogos] = useState<ImageItem[]>([]);
  
  // Set mounted state and prepare safe logos on client-side only
  useEffect(() => {
    setMounted(true);
    
    // Process logos on client side only
    if (Array.isArray(logos)) {
      const filtered = logos.filter(item => 
        item && typeof item === 'object' && (item.url || item.src)
      );
      setSafeLogos(filtered);
    }
  }, [logos]);
  
  const handleLoadMore = () => {
    // Only run on client side
    if (!mounted) return;
    
    setIsLoading(true);
    // Add a small delay for better UX
    setTimeout(() => {
      setVisibleLogos(prev => Math.min(prev + 8, safeLogos.length));
      setIsLoading(false);
    }, 500);
  };
  
  // Show loading skeletons before mounting
  if (!mounted) {
    return (
      <div className="min-h-[300px] py-8">
        <div className="w-full">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 lg:gap-8">
            {Array.from({ length: Math.min(8, Array.isArray(logos) ? logos.length : 0) }).map((_, index) => (
              <div 
                key={`skeleton-${index}`}
                className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden animate-pulse"
              />
            ))}
          </div>
        </div>
      </div>
    );
  }
  
  // Handle case where no logos are provided (client-side only)
  if (!safeLogos.length) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">No logo examples are available at the moment.</p>
      </div>
    );
  }
  
  // Get the visible subset of logos
  const displayedLogos = safeLogos.slice(0, visibleLogos);
  
  return (
    <div className="min-h-[300px]">
      <div className="w-full">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 lg:gap-8">
          {displayedLogos.map((item) => (
            <div 
              key={item.id}
              className="relative aspect-square bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300"
            >
              <img
                src={item.url || item.src || ''}
                alt={item.alt || `Logo design example`}
                className="w-full h-full object-contain p-4 hover:scale-105 transition-transform duration-300"
                loading="lazy"
                onError={(e) => {
                  // Fallback for images that fail to load
                  (e.target as HTMLImageElement).src = FALLBACK_IMAGE;
                }}
              />
            </div>
          ))}
        </div>
        
        {/* Load More Button */}
        {visibleLogos < safeLogos.length && (
          <div className="mt-10 text-center">
            <button
              onClick={handleLoadMore}
              disabled={isLoading}
              className="px-6 py-3 bg-[#FF5400] text-white rounded-full font-medium hover:bg-[#E54D00] transition-colors disabled:opacity-75"
            >
              {isLoading ? (
                <>
                  <span className="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
                  Loading...
                </>
              ) : (
                'Load More Logos'
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  );
} 