'use client';

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import Image from 'next/image';
import { ImageItem } from '@/utils/getImages';
import { useSocket } from '@/hooks/useSocket';

interface Props {
  initialLogos?: ImageItem[];
  initialGraphics?: ImageItem[];
  initialFliers?: ImageItem[];
  initialWebsites?: ImageItem[];
}

export default function PortfolioGallery({
  initialLogos = [],
  initialGraphics = [],
  initialFliers = [],
  initialWebsites = []
}: Props) {
  // Process initial data once with memoization
  const processedInitialData = useMemo(() => {
    return {
      logos: initialLogos.map(logo => ({ ...logo, category: 'logo' })),
      graphics: initialGraphics.map(graphic => ({ ...graphic, category: 'graphic' })),
      fliers: initialFliers.map(flier => ({ ...flier, category: 'flier' })),
      websites: initialWebsites.map(website => ({ ...website, category: 'website' }))
    };
  }, [initialLogos, initialGraphics, initialFliers, initialWebsites]);

  // State
  const [activeFilter, setActiveFilter] = useState('all');
  const [selectedImage, setSelectedImage] = useState<ImageItem | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [allImages, setAllImages] = useState(processedInitialData);

  // Visible counts for each category
  const [visibleCounts, setVisibleCounts] = useState({
    logos: 8,
    graphics: 8,
    fliers: 8,
    websites: 8
  });

  // Refs for intersection observer and scroll restoration
  const galleryRef = useRef<HTMLDivElement>(null);

  // Initialize socket
  const socket = useSocket();

  // Function to fetch images for a specific category if needed
  const fetchImagesIfNeeded = useCallback(async (category: 'logos' | 'graphics' | 'fliers' | 'websites', path: string) => {
    // Check if we already have images for this category
    if (
      (category === 'logos' && allImages.logos.length > 0) ||
      (category === 'graphics' && allImages.graphics.length > 0) ||
      (category === 'fliers' && allImages.fliers.length > 0) ||
      (category === 'websites' && allImages.websites.length > 0)
    ) {
      console.log(`Using cached ${category} images`);
      return null;
    }

    try {
      console.log(`Fetching ${category} images from ${path}`);
      setIsLoading(true);

      // Use the images API with S3 as the source
      const response = await fetch(`/api/images?path=${path}&source=s3`, {
        headers: { 'Cache-Control': 'no-cache' }
      });
      const data = await response.json();

      console.log(`Fetched ${data.length} ${category} images`);

      if (!Array.isArray(data)) {
        console.error(`Invalid data format for ${category}`);
        setIsLoading(false);
        return [];
      }

      // Map the data to our format and ensure type safety
      const mappedData = data.map((item: any) => {
        // Create a safe date string
        let dateStr = new Date().toISOString();
        try {
          if (item.createdAt) {
            dateStr = new Date(String(item.createdAt)).toISOString();
          }
        } catch (e) {
          console.error('Error parsing date:', e);
        }

        return {
          id: item.id || Math.random(),
          title: item.title || '',
          src: item.src || '',
          alt: item.alt || '',
          category: category.slice(0, -1),
          date: dateStr,
          createdAt: item.createdAt || dateStr
        };
      });

      // Sort by date if possible
      const sortedData = mappedData.sort((a, b) => {
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      });

      setAllImages(prev => ({
        ...prev,
        [category]: sortedData
      }));

      setIsLoading(false);
      return sortedData;
    } catch (error) {
      console.error(`Error fetching ${category} images:`, error);
      setIsLoading(false);
      return [];
    }
  }, [allImages]);

  // Prefetch all categories on initial load
  useEffect(() => {
    // Check which categories need to be fetched
    const fetchMissingData = async () => {
      setIsLoading(true);
      const fetchPromises = [];

      if (initialLogos.length === 0) {
        fetchPromises.push(fetchImagesIfNeeded('logos', '/images/portfolio/logos'));
      }

      if (initialGraphics.length === 0) {
        fetchPromises.push(fetchImagesIfNeeded('graphics', '/images/portfolio/branding'));
      }

      if (initialFliers.length === 0) {
        fetchPromises.push(fetchImagesIfNeeded('fliers', '/images/portfolio/fliers'));
      }

      if (initialWebsites.length === 0) {
        fetchPromises.push(fetchImagesIfNeeded('websites', '/images/portfolio/websites'));
      }

      await Promise.all(fetchPromises);
      setIsLoading(false);
    };

    fetchMissingData();
  }, [fetchImagesIfNeeded, initialLogos.length, initialGraphics.length, initialFliers.length, initialWebsites.length]);

  // Handle real-time updates
  useEffect(() => {
    if (!socket) return;

    const handleImageUploaded = (data: { category: string; image: ImageItem }) => {
      const { category, image } = data;

      setAllImages(prev => {
        // Map the category name to our state key
        const categoryKey = category.endsWith('s') ? category : `${category}s`;

        if (categoryKey in prev) {
          return {
            ...prev,
            [categoryKey]: [image, ...prev[categoryKey as keyof typeof prev]]
          };
        }
        return prev;
      });
    };

    socket.on('imageUploaded', handleImageUploaded);

    return () => {
      socket.off('imageUploaded', handleImageUploaded);
    };
  }, [socket]);

  // Scroll to section when clicking on a filter
  const scrollToSection = useCallback((sectionId: string) => {
    const section = document.getElementById(sectionId);
    if (section) {
      section.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, []);

  // Handle "Load More" for a specific category
  const handleLoadMore = useCallback((category: 'logos' | 'graphics' | 'fliers' | 'websites') => {
    setVisibleCounts(prev => ({
      ...prev,
      [category]: prev[category] + 8 // Load 8 more images
    }));
  }, []);

  // Sort images by date
  const getSortedImages = useCallback((images: ImageItem[]) => {
    return [...images].sort((a, b) => {
      const dateA = a.date || a.createdAt || '';
      const dateB = b.date || b.createdAt || '';
      return new Date(dateB).getTime() - new Date(dateA).getTime();
    });
  }, []);

  // Get visible images for a category
  const getVisibleImages = useCallback((category: 'logos' | 'graphics' | 'fliers' | 'websites') => {
    const images = allImages[category];
    const sortedImages = getSortedImages(images);
    return sortedImages.slice(0, visibleCounts[category]);
  }, [allImages, visibleCounts, getSortedImages]);

  return (
    <div className="portfolio-gallery" ref={galleryRef}>
      {/* Category navigation - sticky on mobile, responsive tabs */}
      <div className="sticky top-16 z-10 bg-white/95 backdrop-blur-sm shadow-sm py-4 px-2 overflow-x-auto mb-8">
        <div className="filter-nav flex justify-start md:justify-center gap-2 min-w-max">
          <FilterButton
            active={activeFilter === 'all'}
            onClick={() => {
              setActiveFilter('all');
              galleryRef.current?.scrollIntoView({ behavior: 'smooth' });
            }}
          >
            All Projects
          </FilterButton>
          <FilterButton
            active={activeFilter === 'logos'}
            onClick={() => {
              setActiveFilter('logos');
              scrollToSection('logos-section');
            }}
          >
            Logos
          </FilterButton>
          <FilterButton
            active={activeFilter === 'graphics'}
            onClick={() => {
              setActiveFilter('graphics');
              scrollToSection('graphics-section');
            }}
          >
            Graphics
          </FilterButton>
          <FilterButton
            active={activeFilter === 'fliers'}
            onClick={() => {
              setActiveFilter('fliers');
              scrollToSection('fliers-section');
            }}
          >
            Fliers
          </FilterButton>
          <FilterButton
            active={activeFilter === 'websites'}
            onClick={() => {
              setActiveFilter('websites');
              scrollToSection('websites-section');
            }}
          >
            Websites
          </FilterButton>
        </div>
      </div>

      {/* Show all categories with their own sections */}
      <div className="space-y-20">
        {/* Logos Section */}
        <section id="logos-section" className="category-section">
          <div className="category-header mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 text-center">Logo Design</h2>
            <p className="text-gray-600 mt-2 max-w-2xl mx-auto text-center">
              Professional logos that establish brand identity and make lasting impressions
            </p>
          </div>

          {/* Logos Gallery */}
          <div className="min-h-[200px]">
            {isLoading && allImages.logos.length === 0 ? (
              <LoadingSkeleton count={4} />
            ) : (
              <>
                <div className="gallery-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {getVisibleImages('logos').map((image, index) => (
                    <GalleryItem
                      key={`logo-${image.id}`}
                      image={image}
                      onClick={() => setSelectedImage(image)}
                      priority={index < 4}
                      index={index}
                    />
                  ))}
                </div>

                {/* Load More Logos Button */}
                {allImages.logos.length > visibleCounts.logos && (
                  <div className="text-center mt-8">
                    <button
                      onClick={() => handleLoadMore('logos')}
                      className="px-6 py-3 bg-primary text-white rounded-full hover:bg-primary-dark transition-colors inline-flex items-center gap-2"
                    >
                      Load More Logos
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        </section>

        {/* Graphics Section */}
        <section id="graphics-section" className="category-section">
          <div className="category-header mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 text-center">Graphics & Branding</h2>
            <p className="text-gray-600 mt-2 max-w-2xl mx-auto text-center">
              Comprehensive branding solutions that communicate your unique identity and values
            </p>
          </div>

          {/* Graphics Gallery */}
          <div className="min-h-[200px]">
            {isLoading && allImages.graphics.length === 0 ? (
              <LoadingSkeleton count={4} />
            ) : (
              <>
                <div className="gallery-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {getVisibleImages('graphics').map((image, index) => (
                    <GalleryItem
                      key={`graphic-${image.id}`}
                      image={image}
                      onClick={() => setSelectedImage(image)}
                      priority={index < 4}
                      index={index}
                    />
                  ))}
                </div>

                {/* Load More Graphics Button */}
                {allImages.graphics.length > visibleCounts.graphics && (
                  <div className="text-center mt-8">
                    <button
                      onClick={() => handleLoadMore('graphics')}
                      className="px-6 py-3 bg-primary text-white rounded-full hover:bg-primary-dark transition-colors inline-flex items-center gap-2"
                    >
                      Load More Graphics
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        </section>

        {/* Fliers Section */}
        <section id="fliers-section" className="category-section">
          <div className="category-header mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 text-center">Fliers & Marketing Materials</h2>
            <p className="text-gray-600 mt-2 max-w-2xl mx-auto text-center">
              Eye-catching marketing materials that drive engagement and promote your business
            </p>
          </div>

          {/* Fliers Gallery */}
          <div className="min-h-[200px]">
            {isLoading && allImages.fliers.length === 0 ? (
              <LoadingSkeleton count={4} />
            ) : (
              <>
                <div className="gallery-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {getVisibleImages('fliers').map((image, index) => (
                    <GalleryItem
                      key={`flier-${image.id}`}
                      image={image}
                      onClick={() => setSelectedImage(image)}
                      priority={index < 4}
                      index={index}
                    />
                  ))}
                </div>

                {/* Load More Fliers Button */}
                {allImages.fliers.length > visibleCounts.fliers && (
                  <div className="text-center mt-8">
                    <button
                      onClick={() => handleLoadMore('fliers')}
                      className="px-6 py-3 bg-primary text-white rounded-full hover:bg-primary-dark transition-colors inline-flex items-center gap-2"
                    >
                      Load More Fliers
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        </section>

        {/* Websites Section */}
        <section id="websites-section" className="category-section">
          <div className="category-header mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 text-center">Website Projects</h2>
            <p className="text-gray-600 mt-2 max-w-2xl mx-auto text-center">
              Custom websites built for performance and exceptional user experience
            </p>
          </div>

          {/* Websites Gallery */}
          <div className="min-h-[200px]">
            {isLoading && allImages.websites.length === 0 ? (
              <LoadingSkeleton count={4} />
            ) : (
              <>
                <div className="gallery-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {getVisibleImages('websites').map((image, index) => (
                    <GalleryItem
                      key={`website-${image.id}`}
                      image={image}
                      onClick={() => setSelectedImage(image)}
                      priority={index < 4}
                      index={index}
                    />
                  ))}
                </div>

                {/* Load More Websites Button */}
                {allImages.websites.length > visibleCounts.websites && (
                  <div className="text-center mt-8">
                    <button
                      onClick={() => handleLoadMore('websites')}
                      className="px-6 py-3 bg-primary text-white rounded-full hover:bg-primary-dark transition-colors inline-flex items-center gap-2"
                    >
                      Load More Websites
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        </section>
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <ImageModal
          image={selectedImage}
          onClose={() => setSelectedImage(null)}
        />
      )}

      {/* Custom animation definitions */}
      <style jsx global>{`
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes pulse-subtle {
          0% { opacity: 0.5; }
          50% { opacity: 0.7; }
          100% { opacity: 0.5; }
        }

        .animate-pulse-subtle {
          animation: pulse-subtle 2s ease-in-out infinite;
        }

        .gallery-item {
          will-change: transform, opacity;
          transform: translateZ(0);
        }

        .category-section {
          scroll-margin-top: 100px;
        }
      `}</style>
    </div>
  );
}

// Loading skeleton component
function LoadingSkeleton({ count = 4 }: { count?: number }) {
  return (
    <div className="loading-skeleton grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {[...Array(count)].map((_, index) => (
        <div key={index} className="aspect-square bg-gray-100 rounded-xl">
          <div className="w-full h-full bg-gradient-to-r from-gray-100 to-gray-200 animate-pulse-subtle rounded-xl"></div>
        </div>
      ))}
    </div>
  );
}

// FilterButton component with improved accessibility
function FilterButton({
  active,
  onClick,
  children
}: {
  active: boolean;
  onClick: () => void;
  children: React.ReactNode;
}) {
  return (
    <button
      onClick={onClick}
      className={`px-4 py-2 rounded-full text-sm md:text-base transition-colors duration-300 whitespace-nowrap ${
        active
          ? 'bg-primary text-white'
          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
      }`}
      aria-current={active ? 'page' : undefined}
    >
      {children}
    </button>
  );
}

// GalleryItem component with improved loading and animation
function GalleryItem({
  image,
  onClick,
  priority = false,
  index = 0
}: {
  image: ImageItem;
  onClick: () => void;
  priority?: boolean;
  index?: number;
}) {
  const [isLoaded, setIsLoaded] = useState(false);

  return (
    <div
      className="gallery-item relative aspect-square overflow-hidden rounded-xl cursor-pointer shadow-sm hover:shadow-md transition-all duration-300 transform-gpu"
      onClick={onClick}
      style={{
        animationDelay: `${Math.min(index * 50, 500)}ms`,
        animationDuration: '400ms',
        animationFillMode: 'both',
        animationName: 'fadeIn',
        backfaceVisibility: 'hidden'
      }}
    >
      {/* Background gradient for placeholder */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl"></div>

      {/* Loading indicator */}
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-transparent">
          <div className="w-7 h-7 border-2 border-primary/30 border-t-primary rounded-full animate-spin opacity-80"></div>
        </div>
      )}

      {/* Optimized image */}
      <div className="absolute inset-0 overflow-hidden rounded-xl">
        <Image
          src={image.src}
          alt={image.alt || 'Portfolio image'}
          fill
          sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
          className={`object-cover transition-opacity duration-500 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          onLoad={() => {
            setTimeout(() => setIsLoaded(true), 50);
          }}
          loading={priority ? "eager" : "lazy"}
          quality={60}
          placeholder="blur"
          blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEhAI6dtiLOgAAAABJRU5ErkJggg=="
        />
      </div>

      {/* Caption overlay */}
      <div
        className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent p-4 flex flex-col justify-end transition-opacity duration-300 opacity-70 hover:opacity-100"
      >
        <h3 className="text-white text-lg font-medium line-clamp-1">{image.alt || image.title}</h3>
        <p className="text-white/80 text-sm">{
          image.category === 'logo' ? 'Logo Design' :
          image.category === 'graphic' ? 'Graphic Design' :
          image.category === 'flier' ? 'Marketing Material' : 'Web Project'
        }</p>
      </div>

      {/* Hover overlay effect */}
      <div className="absolute inset-0 bg-black/10 opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
    </div>
  );
}

// ImageModal component
function ImageModal({
  image,
  onClose
}: {
  image: ImageItem;
  onClose: () => void
}) {
  const [isLoaded, setIsLoaded] = useState(false);
  const modalContentRef = useRef<HTMLDivElement>(null);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.body.style.overflow = 'hidden'; // Prevent scrolling

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = ''; // Restore scrolling
    };
  }, [onClose]);

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4 backdrop-blur-sm transition-opacity duration-300"
      onClick={onClose}
      style={{ animation: 'fadeIn 300ms ease-out' }}
    >
      <div
        ref={modalContentRef}
        className="relative max-w-5xl w-full max-h-[90vh] bg-white rounded-xl overflow-hidden shadow-2xl transform transition-all duration-300 opacity-0"
        onClick={e => e.stopPropagation()}
        style={{
          animation: 'fadeIn 400ms ease-out forwards',
          animationDelay: '50ms'
        }}
      >
        {/* Loading background */}
        <div className="absolute inset-0 bg-gray-100 animate-pulse-subtle"></div>

        {/* Image container */}
        <div className="relative h-[70vh]">
          <Image
            src={image.src}
            alt={image.alt || 'Portfolio image'}
            fill
            className={`object-contain transition-opacity duration-500 ${
              isLoaded ? 'opacity-100' : 'opacity-0'
            }`}
            sizes="90vw"
            quality={90}
            priority
            onLoad={() => setIsLoaded(true)}
          />

          {/* Loading indicator */}
          {!isLoaded && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-10 h-10 border-3 border-primary/30 border-t-primary rounded-full animate-spin"></div>
            </div>
          )}
        </div>

        {/* Image info panel */}
        <div className="p-4 bg-white">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-medium text-gray-900">{image.alt || image.title}</h3>
              <p className="text-gray-500">{
                image.category === 'logo' ? 'Logo Design' :
                image.category === 'graphic' ? 'Graphic Design' :
                image.category === 'flier' ? 'Marketing Material' : 'Web Project'
              }</p>
            </div>
            <button
              onClick={onClose}
              className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-300"
              aria-label="Close modal"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}