'use client';

import { usePathname } from 'next/navigation';
import Head from 'next/head';

export default function HreflangTags() {
  const pathname = usePathname();
  const baseUrl = 'https://mocky.co.ke';
  
  // Handle language prefix in path
  const supportedLanguages = ['en-US', 'sw-KE'];
  const pathSegments = pathname.split('/').filter(Boolean);
  const hasLangPrefix = pathSegments.length > 0 && supportedLanguages.includes(pathSegments[0]);
  
  // Get the path without language prefix
  const pathWithoutLang = hasLangPrefix
    ? '/' + pathSegments.slice(1).join('/')
    : pathname;
  
  // Get the current language from URL or default
  const currentLang = hasLangPrefix ? pathSegments[0] : 'x-default';
  
  return (
    <Head>
      {/* Self-referencing tag for current language */}
      <link 
        rel="alternate" 
        hrefLang={currentLang} 
        href={hasLangPrefix 
          ? `${baseUrl}/${currentLang}${pathWithoutLang}` 
          : `${baseUrl}${pathname}`
        } 
      />
      
      {/* Default hreflang tag */}
      <link 
        rel="alternate" 
        hrefLang="x-default" 
        href={`${baseUrl}${pathWithoutLang}`} 
      />
      
      {/* All supported languages */}
      {supportedLanguages.map(lang => (
        <link 
          key={lang}
          rel="alternate" 
          hrefLang={lang} 
          href={`${baseUrl}/${lang}${pathWithoutLang}`} 
        />
      ))}
    </Head>
  );
} 