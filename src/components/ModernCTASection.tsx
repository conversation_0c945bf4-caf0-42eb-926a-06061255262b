'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';

interface ModernCTASectionProps {
  title: string;
  description: string;
  buttonText: string;
  buttonLink: string;
}

export default function ModernCTASection({
  title,
  description,
  buttonText,
  buttonLink
}: ModernCTASectionProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <section className="py-16 bg-orange-50">
        <div className="container mx-auto px-4">
          <div className="h-[120px] bg-orange-100 animate-pulse rounded-lg"></div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-orange-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="bg-gradient-to-br from-[#FF5400] to-[#FF7A00] rounded-xl shadow-lg overflow-hidden">
            <div className="px-6 py-12 md:py-16 relative">
              {/* Decorative circles */}
              <div className="absolute inset-0 overflow-hidden">
                <div className="absolute right-0 top-0 w-96 h-96 bg-white/10 rounded-full translate-x-1/3 -translate-y-1/2"></div>
                <div className="absolute left-0 bottom-0 w-80 h-80 bg-white/10 rounded-full -translate-x-1/3 translate-y-1/2"></div>
              </div>

              <div className="relative z-10">
                <div className="flex flex-col items-center text-center">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="max-w-3xl"
                  >
                    <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight">
                      {title}
                    </h2>
                    <p className="text-white/90 text-lg md:text-xl mb-10 max-w-2xl mx-auto">
                      {description}
                    </p>

                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                      className="flex flex-col sm:flex-row items-center justify-center gap-4"
                    >
                      <Link
                        href={buttonLink}
                        className="inline-flex items-center px-8 py-4 bg-white text-[#FF5400] font-medium rounded-full hover:bg-white/90 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                      >
                        <span>{buttonText}</span>
                        <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                        </svg>
                      </Link>

                      <Link
                        href="/portfolio"
                        className="inline-flex items-center px-8 py-4 bg-transparent border border-white text-white font-medium rounded-full hover:bg-white/10 transition-all duration-200"
                      >
                        <span>View Our Work</span>
                      </Link>
                    </motion.div>
                  </motion.div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
