'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

interface SimplePricingCardProps {
  id: string;
  service: string;
  price: number;
  description?: string;
  features?: string[];
  icon?: string;
  popular?: boolean;
  imageUrl?: string;
  imageUrl2?: string;
  imageUrl3?: string;
  category?: string;
}

export default function SimplePricingCard({
  id,
  service,
  price,
  description,
  features,
  icon,
  popular = false,
  imageUrl,
  imageUrl2,
  imageUrl3,
  category
}: SimplePricingCardProps) {
  // State for image carousel
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Collect all available images and ensure they have proper URLs
  const images = [imageUrl, imageUrl2, imageUrl3]
    .filter(Boolean)
    .map(url => {
      // If URL is missing protocol but contains linodeobjects.com, add https://
      if (url && url.includes('linodeobjects.com') && !url.startsWith('http')) {
        return `https://${url}`;
      }
      return url;
    }) as string[];

  const hasMultipleImages = images.length > 1;

  // Format price to include commas for thousands
  const formattedPrice = price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  // Handle WhatsApp click
  const handleWhatsAppClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const productUrl = `https://mocky.co.ke/product/${id}`;
    const whatsappMessage = `Hello! I'm interested in the ${service} service (KSh ${formattedPrice}). Can you provide more information?\n\nProduct Link: ${productUrl}`;
    const whatsappLink = `https://wa.me/254741590670?text=${encodeURIComponent(whatsappMessage)}`;
    window.open(whatsappLink, '_blank', 'noopener,noreferrer');
  };

  // Image navigation handlers
  const goToNextImage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex((prevIndex) =>
      prevIndex === images.length - 1 ? 0 : prevIndex + 1
    );
  };

  const goToPrevImage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex((prevIndex) =>
      prevIndex === 0 ? images.length - 1 : prevIndex - 1
    );
  };

  return (
    <div className="group relative">
      <Link href={`/product/${id}`} className="block">
        <div className={`flex flex-col h-full bg-white rounded-sm overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 border ${
          popular ? 'border-orange-500' : 'border-gray-200'
        }`}>
          {popular && (
            <div className="bg-orange-500 text-white text-xs font-medium py-1 px-3 absolute right-0 top-0 rounded-bl-sm z-10">
              Popular
            </div>
          )}

          {/* Image Section */}
          {images.length > 0 && (
            <div className="relative w-full pt-[100%] bg-gray-50 overflow-hidden">
              <img
                src={images[currentImageIndex]}
                alt={`${service} - image ${currentImageIndex + 1}`}
                className="absolute inset-0 w-full h-full object-contain p-4 transition-transform duration-300 group-hover:scale-105"
              />

              {/* Category tag */}
              {category && (
                <span className="absolute top-3 left-3 bg-orange-500 text-white text-xs px-2.5 py-1 rounded-sm font-medium z-10">
                  {category}
                </span>
              )}

              {/* Image counter indicator */}
              {hasMultipleImages && (
                <div className="absolute top-3 right-3 bg-white text-gray-700 text-xs px-2 py-0.5 rounded-sm font-medium shadow-sm z-10">
                  {currentImageIndex + 1}/{images.length}
                </div>
              )}

              {/* Navigation arrows for multiple images */}
              {hasMultipleImages && (
                <>
                  <button
                    onClick={goToPrevImage}
                    className="absolute left-2 top-1/2 -translate-y-1/2 bg-white hover:bg-gray-100 text-gray-800 rounded-full p-2 transition-all duration-200 shadow-md opacity-80 hover:opacity-100"
                    aria-label="Previous image"
                  >
                    <ChevronLeftIcon className="h-5 w-5" />
                  </button>

                  <button
                    onClick={goToNextImage}
                    className="absolute right-2 top-1/2 -translate-y-1/2 bg-white hover:bg-gray-100 text-gray-800 rounded-full p-2 transition-all duration-200 shadow-md opacity-80 hover:opacity-100"
                    aria-label="Next image"
                  >
                    <ChevronRightIcon className="h-5 w-5" />
                  </button>
                </>
              )}

              {/* Image navigation dots */}
              {hasMultipleImages && (
                <div className="absolute bottom-3 left-0 right-0 flex justify-center space-x-1.5">
                  {images.map((_, index) => (
                    <button
                      key={index}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setCurrentImageIndex(index);
                      }}
                      className={`w-2.5 h-2.5 rounded-full transition-all duration-200 ${
                        currentImageIndex === index
                          ? 'bg-orange-500 border border-white shadow-sm'
                          : 'bg-gray-300 hover:bg-gray-400 border border-white/80'
                      }`}
                      aria-label={`Go to image ${index + 1}`}
                    />
                  ))}
                </div>
              )}
            </div>
          )}

          <div className="p-5 flex flex-col h-full">
            {/* Icon (if no images) */}
            {images.length === 0 && icon && (
              <div className="text-orange-500 mb-3">
                <i className={`fas fa-${icon} text-xl`}></i>
              </div>
            )}

            <h3 className="text-base font-medium text-gray-800 mb-2 hover:text-orange-600 transition-colors duration-200 line-clamp-2 min-h-[2.75rem]">{service}</h3>

            <div className="mb-4">
              <div className="flex items-baseline">
                <span className="text-sm font-medium text-gray-500 mr-1">KSh</span>
                <span className="text-xl font-bold text-orange-600">{formattedPrice}</span>
              </div>
            </div>

            {description && (
              <p className="text-gray-600 mb-4 text-sm leading-relaxed flex-grow line-clamp-2">{description}</p>
            )}

            {!description && (
              <div className="flex-grow mb-4">
                <p className="text-gray-500 text-xs italic">Contact us for more details</p>
              </div>
            )}

            {/* Features (optional) */}
            {features && features.length > 0 && (
              <ul className="mb-4 space-y-1.5">
                {features.slice(0, 3).map((feature, index) => (
                  <li key={index} className="flex items-center text-xs text-gray-600">
                    <svg className="h-3.5 w-3.5 text-green-500 mr-1.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </Link>

      {/* WhatsApp button outside the Link to prevent navigation */}
      <div className="px-5 pb-5 pt-0 bg-white">
        <button
          onClick={handleWhatsAppClick}
          className="block w-full py-2 px-4 bg-orange-500 hover:bg-orange-600 text-white text-center rounded-sm transition-all duration-300 text-sm font-medium"
          suppressHydrationWarning
        >
          Get Started
        </button>
      </div>
    </div>
  );
}
