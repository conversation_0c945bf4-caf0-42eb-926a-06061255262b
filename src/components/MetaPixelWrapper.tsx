'use client';

import { useEffect, useState } from 'react';
import Cookies from 'js-cookie';

export default function MetaPixelWrapper() {
  const [hasConsent, setHasConsent] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // Get the Meta Pixel ID from environment variables or use a fallback
  const pixelId = process.env.NEXT_PUBLIC_META_PIXEL_ID || '761297382630674';

  useEffect(() => {
    setIsClient(true);

    // Check if user has given marketing consent
    const consentStatus = Cookies.get('cookie-consent-status');
    const marketingConsent = Cookies.get('cookie-marketing-consent');

    // Only initialize Meta Pixel if user has explicitly given consent
    if (consentStatus === 'set' && marketingConsent === 'true') {
      setHasConsent(true);
    }
  }, []);

  // Don't render anything on the server side
  if (!isClient) return null;

  // Don't render if no consent
  if (!hasConsent) return null;

  return (
    <>
      <script
        dangerouslySetInnerHTML={{
          __html: `
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '${pixelId}');
            fbq('track', 'PageView');
          `,
        }}
      />
      <noscript>
        <img
          height="1"
          width="1"
          style={{ display: 'none' }}
          src={`https://www.facebook.com/tr?id=${pixelId}&ev=PageView&noscript=1`}
          alt=""
        />
      </noscript>
    </>
  );
}
