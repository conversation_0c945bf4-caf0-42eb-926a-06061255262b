'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import type { ImageItem } from '@/utils/getImages';
import { normalizeImageUrl } from '@/utils/imageUtils';

// Fallback image to use when original image fails to load
const FALLBACK_IMAGE = '/images/placeholder.jpg';
// Number of images to load initially and when "Load More" is clicked
const ITEMS_PER_PAGE = 8;

interface ClientGallerySectionProps {
  items?: ImageItem[];
  gridCols?: string;
  aspectRatio?: string;
  objectFit?: string;
  category?: string;
}

export default function ClientGallerySection({
  items = [],
  gridCols = "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3",
  aspectRatio = "aspect-video",
  objectFit = "object-cover",
  category = "gallery"
}: ClientGallerySectionProps) {
  // Client rendering flag
  const [mounted, setMounted] = useState(false);
  // Manage client-side state
  const [selectedImage, setSelectedImage] = useState<ImageItem | null>(null);
  const [loadingImages, setLoadingImages] = useState<Set<number>>(new Set());
  const [failedImages, setFailedImages] = useState<Set<number>>(new Set());
  const [visibleItems, setVisibleItems] = useState<number>(ITEMS_PER_PAGE);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);

  // Items filtering moved to useEffect to avoid hydration mismatch
  const [safeItems, setSafeItems] = useState<ImageItem[]>([]);

  // Set mounted state and prepare safe items
  useEffect(() => {
    setMounted(true);

    // Filter items only on client side
    if (Array.isArray(items)) {
      const filtered = items.filter(item =>
        item && typeof item === 'object' && (item.url || item.src)
      );
      setSafeItems(filtered);
    }
  }, [items]);

  // Initialize loadingImages after component mounts
  useEffect(() => {
    if (safeItems.length > 0 && mounted) {
      const initialLoadingSet = new Set<number>();
      safeItems.slice(0, visibleItems).forEach(item => {
        if (item && item.id) {
          initialLoadingSet.add(item.id);
        }
      });
      setLoadingImages(initialLoadingSet);
    }
  }, [safeItems, visibleItems, mounted]);

  const handleImageLoad = (id: number) => {
    if (!mounted) return;

    setLoadingImages(prev => {
      const next = new Set(prev);
      next.delete(id);
      return next;
    });
  };

  const handleImageError = (id: number) => {
    if (!mounted) return;

    // Log error but don't use console.error as it triggers browser console errors
    console.log(`Using fallback for image with id: ${id}`);

    // Remove from loading state
    setLoadingImages(prev => {
      const next = new Set(prev);
      next.delete(id);
      return next;
    });

    // Add to failed images set to use fallback
    setFailedImages(prev => {
      const next = new Set(prev);
      next.add(id);
      return next;
    });
  };

  const handleLoadMore = () => {
    // Only run on client side
    if (!mounted) return;

    setIsLoadingMore(true);

    // Simulate loading delay for better UX
    setTimeout(() => {
      setVisibleItems(prev => Math.min(prev + ITEMS_PER_PAGE, safeItems.length));
      setIsLoadingMore(false);
    }, 500);
  };

  // Pre-mounting state - very simple JSX to avoid hydration issues
  if (!mounted) {
    return (
      <div className="w-full min-h-[300px] py-8">
        <div className={`grid ${gridCols} gap-4 md:gap-6 lg:gap-8`}>
          {Array.from({ length: Math.min(ITEMS_PER_PAGE, Array.isArray(items) ? items.length : 0) }).map((_, index) => (
            <div
              key={`skeleton-${index}`}
              className={`relative ${aspectRatio} bg-gray-100 rounded-lg overflow-hidden animate-pulse`}
            />
          ))}
        </div>
      </div>
    );
  }

  // Post-mounting state with user interaction
  if (!safeItems.length) {
    return (
      <div className="w-full py-20 text-center text-gray-500">
        No images available
      </div>
    );
  }

  // Only show the number of items set in visibleItems state
  const displayedItems = safeItems.slice(0, visibleItems);

  return (
    <div className="w-full">
      <div className={`grid ${gridCols} gap-4 md:gap-6 lg:gap-8`}>
        {displayedItems.map((item) => (
          <div
            key={item.id}
            className={`relative ${aspectRatio} bg-gray-100 rounded-lg overflow-hidden cursor-pointer transition-transform hover:scale-105`}
            onClick={() => setSelectedImage(item)}
          >
            {loadingImages.has(item.id) && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin" />
              </div>
            )}
            <Image
              src={failedImages.has(item.id) ? FALLBACK_IMAGE : normalizeImageUrl(item.url || item.src)}
              alt={item.alt || `${category} image`}
              fill
              className={`${objectFit} hover:opacity-90 transition-opacity duration-300`}
              sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
              onLoadingComplete={() => handleImageLoad(item.id)}
              onError={(e) => {
                console.error(`Image load error for ${item.id}:`, item.url || item.src);
                handleImageError(item.id);
              }}
              loading="lazy"
              unoptimized={process.env.NODE_ENV === 'development'}
            />
          </div>
        ))}
      </div>

      {/* Load More Button */}
      {visibleItems < safeItems.length && (
        <div className="mt-10 text-center">
          <button
            onClick={handleLoadMore}
            disabled={isLoadingMore}
            className="px-6 py-3 bg-primary text-white rounded-full font-medium hover:bg-primary-dark transition-colors disabled:opacity-75"
          >
            {isLoadingMore ? (
              <>
                <span className="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
                Loading...
              </>
            ) : (
              `Load More ${category}`
            )}
          </button>
        </div>
      )}

      {/* Modal for selected image */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-4xl w-full h-auto">
            <Image
              src={failedImages.has(selectedImage.id) ? FALLBACK_IMAGE : normalizeImageUrl(selectedImage.url || selectedImage.src)}
              alt={selectedImage.alt || 'Selected image'}
              width={1200}
              height={800}
              className="w-full h-auto rounded-lg"
              priority
              unoptimized={process.env.NODE_ENV === 'development'}
            />
            <button
              className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedImage(null);
              }}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}