'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import type { ImageItem } from '@/utils/getImages';
import ClientGallerySection from './ClientGallerySection';

interface ClientLogosGalleryProps {
  logos?: ImageItem[];
}

export default function ClientLogosGallery({ logos = [] }: ClientLogosGalleryProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Don't render anything on the server side
  if (!isClient) {
    return null;
  }

  // Ensure logos is always an array and has required properties
  const safeLogos = Array.isArray(logos) ? logos.filter(logo =>
    logo && typeof logo === 'object' && (logo.url || logo.src)
  ) : [];

  if (!safeLogos.length) {
    return (
      <div className="text-center py-20 text-gray-500">
        No logos available at the moment
      </div>
    );
  }

  return (
    <ClientGallerySection
      items={safeLogos}
      gridCols="grid-cols-1 sm:grid-cols-2 md:grid-cols-3"
      aspectRatio="aspect-square"
      objectFit="object-contain"
      category="logos"
    />
  );
}