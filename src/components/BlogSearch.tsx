'use client';

import { useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';

interface BlogSearchProps {
  initialValue?: string;
}

export default function BlogSearch({ initialValue = '' }: BlogSearchProps) {
  const [searchTerm, setSearchTerm] = useState(initialValue);
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    if (searchTerm.trim()) {
      startTransition(() => {
        router.push(`/blog?search=${encodeURIComponent(searchTerm.trim())}`);
      });
    } else {
      // If search is empty, go back to main blog page
      startTransition(() => {
        router.push('/blog');
      });
    }
  };

  return (
    <form onSubmit={handleSearch} className="w-full relative">
      <div className="relative">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <MagnifyingGlassIcon className="w-5 h-5 text-gray-400" />
        </div>
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="bg-white border border-gray-200 text-gray-900 text-sm block w-full pl-10 p-3 focus:outline-none focus:border-gray-400 transition-colors"
          placeholder="Search blog posts..."
          aria-label="Search blog posts"
        />
        {isPending && (
          <div className="absolute right-2.5 inset-y-0 flex items-center">
            <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
      </div>
      <button type="submit" className="sr-only">
        Search
      </button>
    </form>
  );
}