'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence, useAnimation } from 'framer-motion';

interface ProcessStep {
  step: string;
  title: string;
  description: string;
  icon?: string;
}

interface ModernProcessSectionProps {
  steps: ProcessStep[];
}

export default function ModernProcessSection({ steps }: ModernProcessSectionProps) {
  const [mounted, setMounted] = useState(false);
  const [activeStep, setActiveStep] = useState<number | null>(0);
  const [autoPlay, setAutoPlay] = useState(true);
  const mobileStepsRef = useRef<HTMLDivElement>(null);
  const stepRefs = useRef<(HTMLButtonElement | null)[]>([]);
  const autoPlayTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize refs array
  useEffect(() => {
    stepRefs.current = stepRefs.current.slice(0, steps.length);
  }, [steps]);

  // Auto-cycle through steps
  useEffect(() => {
    if (autoPlay && mounted) {
      autoPlayTimerRef.current = setInterval(() => {
        setActiveStep(prev => {
          if (prev === null) return 0;
          return (prev + 1) % steps.length;
        });
      }, 5000); // Change step every 5 seconds
    }

    return () => {
      if (autoPlayTimerRef.current) {
        clearInterval(autoPlayTimerRef.current);
      }
    };
  }, [autoPlay, steps.length, mounted]);

  // Pause auto-play when user interacts
  const handleStepClick = (index: number) => {
    setActiveStep(index);
    setAutoPlay(false); // Stop auto-cycling when user clicks

    // Resume auto-play after 15 seconds of inactivity
    if (autoPlayTimerRef.current) {
      clearTimeout(autoPlayTimerRef.current);
    }

    autoPlayTimerRef.current = setTimeout(() => {
      setAutoPlay(true);
    }, 15000);
  };

  // Scroll to active step in mobile view
  useEffect(() => {
    if (activeStep !== null && mobileStepsRef.current && stepRefs.current[activeStep]) {
      const container = mobileStepsRef.current;
      const activeElement = stepRefs.current[activeStep];

      if (activeElement) {
        const scrollLeft = activeElement.offsetLeft - container.offsetWidth / 2 + activeElement.offsetWidth / 2;

        container.scrollTo({
          left: scrollLeft,
          behavior: 'smooth'
        });
      }
    }
  }, [activeStep]);

  useEffect(() => {
    setMounted(true);

    // Clean up any timers when component unmounts
    return () => {
      if (autoPlayTimerRef.current) {
        clearInterval(autoPlayTimerRef.current);
      }
    };
  }, []);

  if (!mounted) {
    return (
      <section className="py-10 sm:py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-6 sm:mb-8">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#0A1929] mb-2 sm:mb-3">
              Our Logo Design Process
            </h2>
            <p className="text-sm sm:text-base text-gray-600 max-w-2xl mx-auto">
              We follow a systematic approach to create the perfect logo for your brand
            </p>
          </div>

          <div className="max-w-5xl mx-auto">
            <div className="flex justify-between mb-6 sm:mb-8 animate-pulse px-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="w-12 h-12 sm:w-16 sm:h-16 bg-gray-200 rounded-full"></div>
              ))}
            </div>
            <div className="h-[150px] sm:h-[200px] bg-gray-200 rounded-xl animate-pulse"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-10 sm:py-16 bg-white">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-6 sm:mb-10"
        >
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#0A1929] mb-2 sm:mb-3">
            Our Logo Design Process
          </h2>
          <p className="text-sm sm:text-base text-gray-600 max-w-2xl mx-auto">
            We follow a systematic approach to create the perfect logo for your brand
          </p>
        </motion.div>

        <div className="max-w-5xl mx-auto">
          {/* Process Timeline */}
          <div className="mb-8 sm:mb-12">
            {/* Desktop Timeline */}
            <div className="hidden md:block relative">
              {/* Timeline Line */}
              <div className="absolute left-0 right-0 h-[2px] top-[40px] bg-gray-200"></div>

              {/* Progress Line */}
              <div
                className="absolute left-0 h-[2px] top-[40px] bg-[#FF5400] transition-all duration-500"
                style={{
                  width: activeStep !== null ? `${(activeStep / (steps.length - 1)) * 100}%` : '0%',
                }}
              ></div>

              {/* Steps */}
              <div className="flex justify-between relative">
                {steps.map((step, index) => (
                  <div key={step.step} className="flex flex-col items-center relative">
                    <motion.button
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: index * 0.1 }}
                      className="relative z-10 mb-3"
                      onClick={() => setActiveStep(index)}
                      onMouseEnter={() => {
                        setActiveStep(index);
                        setAutoPlay(false);
                      }}
                    >
                      <div
                        className={`w-16 h-16 lg:w-20 lg:h-20 rounded-full flex items-center justify-center text-lg lg:text-xl font-bold transition-all duration-300 ${
                          activeStep === index
                            ? 'bg-[#FF5400] text-white scale-110 shadow-lg'
                            : 'bg-white text-[#0A1929] border-2 border-[#0A1929]/10 hover:border-[#FF5400]/50'
                        }`}
                      >
                        {step.step}
                      </div>
                    </motion.button>
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.4, delay: index * 0.1 + 0.2 }}
                      className="text-center"
                    >
                      <h3 className={`text-sm lg:text-base font-bold transition-colors duration-300 ${
                        activeStep === index ? 'text-[#FF5400]' : 'text-[#0A1929]'
                      }`}>
                        {step.title}
                      </h3>
                    </motion.div>
                  </div>
                ))}
              </div>
            </div>

            {/* Mobile Timeline */}
            <div className="md:hidden">
              {/* Mobile Progress Indicator */}
              <div className="relative h-1 bg-gray-200 rounded-full mb-4 mx-4">
                <motion.div
                  className="absolute left-0 h-1 bg-[#FF5400] rounded-full"
                  initial={{ width: '0%' }}
                  animate={{
                    width: activeStep !== null ? `${(activeStep / (steps.length - 1)) * 100}%` : '0%'
                  }}
                  transition={{ duration: 0.5 }}
                ></motion.div>
              </div>

              {/* Mobile Steps */}
              <div
                ref={mobileStepsRef}
                className="flex overflow-x-auto py-2 px-4 space-x-6 hide-scrollbar snap-x snap-mandatory"
              >
                {steps.map((step, index) => (
                  <motion.button
                    key={step.step}
                    ref={el => stepRefs.current[index] = el}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    whileTap={{ scale: 0.95 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    className={`flex flex-col items-center min-w-[80px] p-3 rounded-lg transition-all snap-start ${
                      activeStep === index
                        ? 'bg-[#FF5400]/10'
                        : 'bg-transparent hover:bg-gray-100'
                    }`}
                    onClick={() => handleStepClick(index)}
                  >
                    <div
                      className={`w-14 h-14 rounded-full flex items-center justify-center text-lg font-bold mb-2 transition-all duration-300 ${
                        activeStep === index
                          ? 'bg-[#FF5400] text-white scale-110 shadow-sm'
                          : 'bg-white text-[#0A1929] border border-[#0A1929]/20'
                      }`}
                    >
                      {step.step}
                    </div>
                    <div className={`text-sm font-medium ${
                      activeStep === index ? 'text-[#FF5400]' : 'text-[#0A1929]'
                    }`}>
                      {step.title}
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>
          </div>

          {/* Step Details */}
          {/* Mobile: Only show active card with swipe gesture */}
          <div className="md:hidden">
            <AnimatePresence mode="wait">
              {steps.map((step, index) => (
                activeStep === index && (
                  <motion.div
                    key={step.step}
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0, scale: 1 }}
                    exit={{ opacity: 0, x: -50 }}
                    transition={{ duration: 0.3 }}
                    className="bg-white rounded-xl p-5 shadow-md border-2 border-[#FF5400] mb-4"
                    drag="x"
                    dragConstraints={{ left: 0, right: 0 }}
                    dragElastic={0.2}
                    onDragEnd={(e, { offset, velocity }) => {
                      const swipe = Math.abs(offset.x) > 50;

                      if (swipe) {
                        const direction = offset.x < 0 ? 1 : -1;
                        const newIndex = (activeStep + direction + steps.length) % steps.length;
                        handleStepClick(newIndex);
                      }
                    }}
                  >
                    <div className="flex items-start mb-4">
                      <div className="w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold mr-4 flex-shrink-0 bg-[#FF5400] text-white">
                        {step.step}
                      </div>
                      <h3 className="text-xl font-bold text-[#0A1929] pt-2">
                        {step.title}
                      </h3>
                    </div>
                    <p className="text-gray-700">
                      {step.description}
                    </p>
                    <div className="mt-4 text-xs text-gray-500 italic text-center">
                      Swipe to navigate
                    </div>
                  </motion.div>
                )
              ))}
            </AnimatePresence>
          </div>

          {/* Desktop: Show all cards in grid */}
          <div className="hidden sm:grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {steps.map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, y: 20 }}
                animate={{
                  opacity: activeStep === index || activeStep === null ? 1 : 0.6,
                  y: 0,
                  scale: activeStep === index ? 1.03 : 1
                }}
                transition={{ duration: 0.4 }}
                className={`bg-white rounded-xl p-6 shadow-sm transition-all duration-300 ${
                  activeStep === index
                    ? 'border-2 border-[#FF5400] shadow-md'
                    : 'border border-gray-200 hover:border-[#0A1929]/20'
                }`}
                onClick={() => handleStepClick(index)}
                onMouseEnter={() => {
                  setActiveStep(index);
                  setAutoPlay(false);
                }}
              >
                <div className="flex items-start mb-4">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold mr-4 flex-shrink-0 ${
                    activeStep === index
                      ? 'bg-[#FF5400] text-white'
                      : 'bg-[#0A1929] text-white'
                  }`}>
                    {step.step}
                  </div>
                  <h3 className="text-xl font-bold text-[#0A1929] pt-2">
                    {step.title}
                  </h3>
                </div>
                <p className="text-gray-700">
                  {step.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
