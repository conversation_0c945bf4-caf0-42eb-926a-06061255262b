'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaGithub, FaEnvelope } from 'react-icons/fa';

interface TeamMember {
  id: string;
  name: string;
  role: string;
  bio: string;
  imageSrc: string;
  order: number;
  linkedinUrl?: string;
  twitterUrl?: string;
  githubUrl?: string;
  emailAddress?: string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
}

const MeetTheExpertsAlternative: React.FC = () => {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/team');

        if (!response.ok) {
          throw new Error('Failed to fetch team members');
        }

        const data = await response.json();
        setTeamMembers(data);
      } catch (err) {
        console.error('Error fetching team members:', err);
        setError('Failed to load team members');
        // Use fallback data in case of error
        setTeamMembers(fallbackTeamMembers);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTeamMembers();
  }, []);

  // Fallback data with bio information
  const fallbackTeamMembers: TeamMember[] = [
    {
      id: '1',
      name: 'Jack Sequeira Onyango',
      role: 'Graphics Designer',
      bio: 'With a sharp eye for detail and a deep understanding of design trends and tools like Adobe Creative Suite (Photoshop, Illustrator, InDesign), I help brands tell their stories visually and build lasting connections with their audiences.',
      imageSrc: '/images/team/jack-sequeira.jpg',
      order: 0,
      linkedinUrl: 'https://linkedin.com/in/jacksequeira',
    },
    {
      id: '2',
      name: 'Collins Kimokoti',
      role: 'Video Editor',
      bio: 'Collins Kimokoti is a Senior Graphics Designer and Video Editor with a passion for creating striking visuals that bring ideas to life. With extensive experience in branding, digital marketing design, and video production.',
      imageSrc: '/images/team/collins-kimokoti.jpg',
      order: 1,
      linkedinUrl: 'https://linkedin.com/in/collinskimokoti',
    },
    {
      id: '3',
      name: 'Anita Kay',
      role: 'Executive Assistant | Social Media Expert',
      bio: 'Anita Kay is a dynamic marketing strategist with a keen eye for consumer behavior and digital trends. She specializes in building brand awareness, driving engagement, and creating compelling marketing campaigns.',
      imageSrc: '/images/team/anita-kay.jpg',
      order: 2,
      linkedinUrl: 'https://linkedin.com/in/anitakay',
    }
  ];

  // Use API data if available, otherwise use fallback
  const displayTeamMembers = teamMembers.length > 0 ? teamMembers : fallbackTeamMembers;

  return (
    <section className="py-16 bg-gradient-to-b from-white to-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Meet the Experts</h2>
          <div className="w-24 h-1 bg-orange-500 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-2xl mx-auto">
            Our talented team of professionals is dedicated to bringing your digital vision to life.
          </p>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
            {displayTeamMembers.map((member) => (
              <div
                key={member.id}
                className="group relative"
              >
                <div className="relative h-[500px] w-full overflow-hidden rounded-2xl shadow-2xl">
                  <Image
                    src={member.imageSrc}
                    alt={member.name}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                    priority
                  />

                  {/* Default overlay with basic info */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent opacity-90 transition-opacity duration-300 group-hover:opacity-0"></div>
                  <div className="absolute bottom-0 left-0 right-0 p-6 text-white transition-opacity duration-300 group-hover:opacity-0">
                    <h3 className="text-2xl font-bold mb-2">{member.name}</h3>
                    <p className="text-orange-400 font-semibold text-lg">{member.role}</p>
                  </div>

                  {/* Hover overlay with bio and social links */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/95 via-black/70 to-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-between p-6 text-white">
                    <div className="flex-1 flex flex-col justify-center">
                      <h3 className="text-2xl font-bold mb-2">{member.name}</h3>
                      <p className="text-orange-400 font-semibold text-lg mb-4">{member.role}</p>
                      <p className="text-white/90 text-sm leading-relaxed">
                        {member.bio}
                      </p>
                    </div>

                    <div className="flex justify-center space-x-4 mt-6">
                      {member.linkedinUrl && (
                        <Link
                          href={member.linkedinUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="bg-white/20 backdrop-blur-sm p-3 rounded-full hover:bg-white/40 hover:scale-110 transition-all duration-200"
                        >
                          <FaLinkedin size={20} />
                          <span className="sr-only">LinkedIn</span>
                        </Link>
                      )}
                      {member.twitterUrl && (
                        <Link
                          href={member.twitterUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="bg-white/20 backdrop-blur-sm p-3 rounded-full hover:bg-white/40 hover:scale-110 transition-all duration-200"
                        >
                          <FaTwitter size={20} />
                          <span className="sr-only">Twitter</span>
                        </Link>
                      )}
                      {member.githubUrl && (
                        <Link
                          href={member.githubUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="bg-white/20 backdrop-blur-sm p-3 rounded-full hover:bg-white/40 hover:scale-110 transition-all duration-200"
                        >
                          <FaGithub size={20} />
                          <span className="sr-only">GitHub</span>
                        </Link>
                      )}
                      {member.emailAddress && (
                        <Link
                          href={`mailto:${member.emailAddress}`}
                          className="bg-white/20 backdrop-blur-sm p-3 rounded-full hover:bg-white/40 hover:scale-110 transition-all duration-200"
                        >
                          <FaEnvelope size={20} />
                          <span className="sr-only">Email</span>
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default MeetTheExpertsAlternative;
