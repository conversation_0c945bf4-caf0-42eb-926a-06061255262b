// Script to check blog posts in the database
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkBlogPosts() {
  try {
    // Get all blog posts
    console.log('Fetching all blog posts:');
    const posts = await prisma.blogPost.findMany();
    
    console.log(`Found ${posts.length} blog posts`);
    posts.forEach(post => {
      console.log(`\nID: ${post.id}, Title: ${post.title}`);
      console.log(`Status: ${post.status}`);
      console.log(`Slug: ${post.slug}`);
      console.log(`Published At: ${post.publishedAt ? new Date(post.publishedAt).toISOString() : 'null'}`);
      console.log(`Created At: ${new Date(post.createdAt).toISOString()}`);
      console.log(`Updated At: ${new Date(post.updatedAt).toISOString()}`);
    });
    
    // Also check published posts specifically
    console.log('\n\nFetching only published posts:');
    const publishedPosts = await prisma.blogPost.findMany({
      where: {
        status: 'published'
      }
    });
    
    console.log(`Found ${publishedPosts.length} published blog posts`);
    publishedPosts.forEach(post => {
      console.log(`\nID: ${post.id}, Title: ${post.title}`);
      console.log(`Published At: ${post.publishedAt ? new Date(post.publishedAt).toISOString() : 'null'}`);
    });
    
  } catch (error) {
    console.error('Error checking blog posts:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkBlogPosts()
  .then(() => console.log('\nDone!'))
  .catch(e => console.error(e)); 