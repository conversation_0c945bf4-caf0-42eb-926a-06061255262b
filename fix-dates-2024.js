// Script to update blog post dates to use 2024 as the year
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function updateBlogPosts() {
  try {
    // Get the current date and force the year to be 2024
    const currentDate = new Date();
    currentDate.setFullYear(2024);
    
    console.log(`Updating blog posts to use 2024 date: ${currentDate.toISOString()}`);

    // Update all blog posts
    const updateResult = await prisma.blogPost.updateMany({
      data: {
        publishedAt: currentDate,
        createdAt: currentDate,
        updatedAt: currentDate,
      },
    });

    console.log(`Updated ${updateResult.count} blog posts to use 2024 date: ${currentDate.toISOString()}`);
  } catch (error) {
    console.error('Error updating blog posts:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateBlogPosts()
  .then(() => console.log('Done!'))
  .catch((e) => console.error(e)); 