# Centralized Storage Configuration

This document describes the centralized storage configuration system that has been implemented in the Mocky application.

## Overview

The storage configuration system allows you to manage file storage providers (S3, Cloudinary, etc.) from a central location in the admin panel. This means you can change where files are stored without updating code or environment variables.

## Features

- Multiple storage providers support: S3-compatible, Linode Object Storage, Cloudinary, local storage
- Set a default storage provider that all file operations will use
- Change storage providers at any time without code changes
- Securely store credentials in the database
- Fallback to environment variables if no configurations are found

## How It Works

The system consists of:

1. A `StorageConfig` model in the database that stores provider configuration
2. A centralized API to access storage settings (`src/lib/storageConfig.ts`)
3. Updated utility functions that use the centralized config (`src/utils/s3.ts`)
4. Admin UI for managing storage providers (`src/app/admin/settings/storage/page.tsx`)

## Usage

### Admin Panel

1. Go to the admin panel and navigate to Settings > Storage
2. Add a new storage configuration by clicking the "Add New" button
3. Fill in the required details:
   - Provider: Select the storage provider type
   - Region: The region where your bucket is located
   - Endpoint URL: The endpoint for your storage provider
   - Bucket Name: The name of your storage bucket
   - Access Key ID and Secret Access Key: Your provider credentials
4. Check "Set as default" if you want this to be the active configuration
5. Click Save to store the configuration

### For Developers

When accessing storage in your code:

```typescript
// Import the modified S3 utilities
import { uploadToS3, getS3Url } from '@/utils/s3';

// Upload a file - automatically uses the default provider
const fileUrl = await uploadToS3(fileBuffer, 'path/to/file.jpg', 'image/jpeg');

// Get a URL for a file
const url = await getS3Url('path/to/file.jpg');
```

## Migrating Existing Files

If you're changing storage providers and need to migrate files:

1. Set up both old and new providers in the admin panel
2. Use the following script to copy files between providers:

```bash
npm run migrate-storage --from=<old-provider-id> --to=<new-provider-id>
```

## Fallback Mechanism

If no storage configuration is found in the database, the system will fall back to using the following environment variables:

- `NEXT_PUBLIC_S3_REGION`
- `NEXT_PUBLIC_S3_ENDPOINT`
- `NEXT_PUBLIC_S3_BUCKET`
- `NEXT_PUBLIC_S3_ACCESS_KEY`
- `NEXT_PUBLIC_S3_SECRET_KEY`

## Security Considerations

- Access to the storage settings is restricted to authenticated admin users
- Credentials are stored securely in the database
- The storage API is not exposed to the client side

## Troubleshooting

If you encounter issues with the storage configuration:

1. Check that the default configuration is set correctly
2. Verify that your credentials and endpoint URLs are correct
3. Ensure that your bucket permissions allow the operations you're attempting
4. Check the application logs for detailed error messages 