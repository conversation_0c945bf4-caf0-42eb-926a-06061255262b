# Manual Nginx Setup Instructions

Follow these steps to set up Nginx for your Mocky website:

1. Make sure Nginx is installed:
```
sudo apt update && sudo apt install -y nginx
```

2. Copy the Nginx configuration from your project:
```
sudo cp /var/www/mocky/nginx/prod.mocky.co.ke.conf /etc/nginx/sites-available/mocky.co.ke.conf
```

3. Create a symbolic link to enable the site:
```
sudo ln -sf /etc/nginx/sites-available/mocky.co.ke.conf /etc/nginx/sites-enabled/
```

4. Remove the default configuration (if it exists):
```
sudo rm -f /etc/nginx/sites-enabled/default
```

5. Create necessary directories and symbolic links:
```
sudo mkdir -p /var/www/mocky.co.ke
sudo ln -sf /var/www/mocky/public /var/www/mocky.co.ke/public
sudo ln -sf /var/www/mocky/.next /var/www/mocky.co.ke/.next
```

6. Check that the Nginx configuration is valid:
```
sudo nginx -t
```

7. Restart Nginx:
```
sudo systemctl restart nginx
```

8. Check Nginx status:
```
sudo systemctl status nginx
```

Your Mocky website should now be accessible at your domain (https://mocky.co.ke) once DNS is properly configured to point to your server's IP address.

## Troubleshooting

If you encounter issues:

1. Check Nginx error logs:
```
sudo tail -f /var/log/nginx/error.log
```

2. Check your application logs:
```
tail -f app.log
```

3. Make sure your application is running (on port 3000):
```
curl http://localhost:3000
```

4. Verify Nginx is listening on port 80:
```
sudo netstat -tulpn | grep nginx
``` 