// Script to seed pricing data
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('Seeding pricing data...');

  // Pricing items from requirements
  const pricingItems = [
    {
      service: 'Business Card Design',
      price: 500,
      description: 'Professional business card design for your brand'
    },
    {
      service: 'Poster Design',
      price: 1000,
      description: 'Eye-catching poster designs for your marketing campaigns'
    },
    {
      service: 'Company Profile',
      price: 5000,
      description: 'Comprehensive and professional company profile design'
    },
    {
      service: 'Catalogue',
      price: 3000,
      description: 'Professional product catalogue design'
    },
    {
      service: 'Invoice Design',
      price: 500,
      description: 'Professional invoice design for your business'
    },
    {
      service: 'Letterhead',
      price: 500,
      description: 'Professional letterhead design for your brand'
    }
  ];

  // Get existing services
  const existingServices = await prisma.catalogue.findMany();
  const existingServiceNames = existingServices.map(item => item.service.toLowerCase());

  let added = 0;

  // Insert catalogue items that don't already exist
  for (const item of pricingItems) {
    // Check if this service already exists (case-insensitive)
    if (!existingServiceNames.includes(item.service.toLowerCase())) {
      await prisma.catalogue.create({
        data: item
      });
      added++;
      console.log(`Added: ${item.service}`);
    } else {
      console.log(`Skipped (already exists): ${item.service}`);
    }
  }

  console.log(`Added ${added} new catalogue items.`);
}

main()
  .catch(e => {
    console.error('Error seeding pricing data:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });