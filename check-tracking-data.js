const { PrismaClient } = require('@prisma/client');

async function checkTrackingData() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Checking tracking data in database...\n');
    
    // Check total events
    const totalEvents = await prisma.eventTracking.count();
    console.log(`📊 Total events in database: ${totalEvents}`);
    
    if (totalEvents === 0) {
      console.log('❌ No tracking data found in database');
      return;
    }
    
    // Check event types
    const eventTypes = await prisma.$queryRaw`
      SELECT "eventType", COUNT(*) as count
      FROM "event_tracking"
      GROUP BY "eventType"
      ORDER BY count DESC
    `;
    
    console.log('\n📈 Event types:');
    eventTypes.forEach(type => {
      console.log(`  ${type.eventType}: ${Number(type.count)}`);
    });
    
    // Check recent events (last 10)
    const recentEvents = await prisma.eventTracking.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      select: {
        eventName: true,
        eventType: true,
        createdAt: true,
        url: true
      }
    });
    
    console.log('\n🕒 Recent events:');
    recentEvents.forEach(event => {
      console.log(`  ${event.createdAt.toISOString()} - ${event.eventType}: ${event.eventName}`);
    });
    
    // Check date range
    const dateRange = await prisma.$queryRaw`
      SELECT 
        MIN("created_at") as earliest,
        MAX("created_at") as latest
      FROM "event_tracking"
    `;
    
    console.log('\n📅 Date range:');
    console.log(`  Earliest: ${dateRange[0]?.earliest}`);
    console.log(`  Latest: ${dateRange[0]?.latest}`);
    
  } catch (error) {
    console.error('❌ Error checking tracking data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkTrackingData();
