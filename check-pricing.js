// Script to check pricing data
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    const catalogue = await prisma.catalogue.findMany();
    console.log('Current catalogue items:');
    console.log(JSON.stringify(catalogue, null, 2));
    console.log(`Total catalogue items: ${catalogue.length}`);
  } catch (error) {
    console.error('Error fetching catalogue data:', error);
  }
}

main()
  .catch(e => {
    console.error('Error:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });