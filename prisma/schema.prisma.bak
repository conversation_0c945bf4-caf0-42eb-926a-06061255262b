generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model BlogPost {
  id            Int       @id @default(autoincrement())
  title         String    @db.VarChar(255)
  slug          String    @unique @db.VarChar(255)
  content       String
  excerpt       String?
  author        String?   @db.VarChar(100)
  category      String?   @db.VarChar(100)
  tags          String[]  @default([])
  status        String    @default("draft") @db.VarChar(20)
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")
  publishedAt   DateTime? @map("published_at")

  @@map("blog_posts")
}

model Category {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(100)
  slug        String   @unique @db.VarChar(100)
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("categories")
}

model WebsitePortfolio {
  id          String   @id @default(uuid())
  title       String   @db.VarChar(255)
  description String?
  category    String   @db.VarChar(100)
  imageSrc    String
  url         String
  featured    Boolean  @default(false)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("website_portfolio")
}

model Pricing {
  id          Int      @id @default(autoincrement())
  service     String   @db.VarChar(255)
  price       Int
  description String?
  features    String[] @default([])
  icon        String?  @db.VarChar(100)
  popular     Boolean  @default(false)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("pricing")
}

model StorageConfig {
  id             String   @id @default(uuid())
  provider       String   @db.VarChar(50)
  region         String   @db.VarChar(100)
  endpoint       String   @db.VarChar(255)
  bucketName     String   @db.VarChar(100)
  accessKeyId    String   @db.VarChar(255)
  secretAccessKey String  @db.VarChar(255)
  isDefault      Boolean  @default(false)
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("storage_config")
}

model SiteSettings {
  id             String   @id @default(uuid())
  siteName       String   @db.VarChar(100)
  siteDescription String?  @db.VarChar(255)
  contactEmail   String?  @db.VarChar(100)
  phoneNumber    String?  @db.VarChar(50)
  address        String?  @db.VarChar(255)
  facebookUrl    String?  @db.VarChar(255)
  twitterUrl     String?  @db.VarChar(255)
  instagramUrl   String?  @db.VarChar(255)
  tiktokUrl      String?  @db.VarChar(255)
  linkedinUrl    String?  @db.VarChar(255)
  metaTitle      String?  @db.VarChar(255)
  metaDescription String?  @db.Text
  googleAnalyticsId String? @db.VarChar(50)
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("site_settings")
}
