const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // Check if a storage config already exists
    const existingConfig = await prisma.storageConfig.findFirst({
      where: { isDefault: true }
    });

    if (existingConfig) {
      console.log('Default storage configuration already exists, skipping seed');
      return;
    }

    // Create default storage configuration from environment variables
    const storageConfig = await prisma.storageConfig.create({
      data: {
        provider: 'S3',
        region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
        endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
        bucketName: process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky',
        accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
        secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
        isDefault: true
      }
    });

    console.log('Created default storage configuration:', storageConfig.id);
  } catch (error) {
    console.error('Error seeding storage configuration:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main(); 