-- CreateTable
CREATE TABLE "storage_config" (
  "id" TEXT NOT NULL,
  "provider" VARCHAR(50) NOT NULL,
  "region" VARCHAR(100) NOT NULL,
  "endpoint" VARCHAR(255) NOT NULL,
  "bucketName" VARCHAR(100) NOT NULL,
  "accessKeyId" VARCHAR(255) NOT NULL,
  "secretAccessKey" VARCHAR(255) NOT NULL,
  "isDefault" BOOLEAN NOT NULL DEFAULT false,
  "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

  CONSTRAINT "storage_config_pkey" PRIMARY KEY ("id")
); 