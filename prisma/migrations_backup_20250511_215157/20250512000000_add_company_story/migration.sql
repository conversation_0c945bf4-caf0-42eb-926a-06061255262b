-- CreateTable
CREATE TABLE "company_story" (
    "id" TEXT NOT NULL,
    "title" VARCHAR(200) NOT NULL,
    "subtitle" VARCHAR(255) NOT NULL,
    "imageSrc" TEXT NOT NULL,
    "quote1" TEXT NOT NULL,
    "quote2" TEXT,
    "founderName" VARCHAR(100) NOT NULL,
    "founderRole" VARCHAR(100) NOT NULL,
    "linkedinUrl" VARCHAR(255),
    "twitterUrl" VARCHAR(255),
    "instagramUrl" VARCHAR(255),
    "tiktokUrl" VARCHAR(255),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "company_story_pkey" PRIMARY KEY ("id")
);
