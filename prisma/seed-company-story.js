const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('Seeding company story...');
  
  // Check if a company story already exists
  const existingStory = await prisma.companyStory.findFirst({
    where: { isActive: true }
  });
  
  if (existingStory) {
    console.log('Active company story already exists. Skipping seed.');
    return;
  }
  
  // Create a new company story
  const story = await prisma.companyStory.create({
    data: {
      title: 'Our Journey',
      subtitle: 'From humble beginnings to digital excellence',
      imageSrc: '/images/about/ceo.jpg',
      quote1: 'We started Mocky Digital with a simple mission: to help Kenyan businesses succeed in the digital world through exceptional design and marketing.',
      quote2: 'Every day, we strive to deliver work that not only looks great but also drives real business results for our clients.',
      founder<PERSON><PERSON>: '<PERSON>',
      founder<PERSON><PERSON>: 'Founder & Creative Director',
      linkedinUrl: 'https://linkedin.com/',
      twitterUrl: 'https://twitter.com/',
      isActive: true,
    }
  });
  
  console.log('Company story created:', story);
}

main()
  .catch((e) => {
    console.error('Error seeding company story:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
