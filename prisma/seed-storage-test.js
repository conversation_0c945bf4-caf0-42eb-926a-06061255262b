const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // Check if a storage config already exists
    const existingConfig = await prisma.storageConfig.findFirst({
      where: { isDefault: true }
    });

    if (existingConfig) {
      console.log('Default storage configuration already exists, updating it');

      const updatedConfig = await prisma.storageConfig.update({
        where: { id: existingConfig.id },
        data: {
          provider: 'S3',
          region: 'fr-par-1',
          endpoint: 'https://fr-par-1.linodeobjects.com',
          bucketName: 'mocky',
          accessKeyId: '73OQS52ORLRPBO3KG6YN',
          secretAccessKey: 'p5UW5FZ7Gog5PMP749MpdHGhPRXUKYnStFJtAaMx',
          isDefault: true
        }
      });

      console.log('Updated storage configuration:', updatedConfig.id);
      return;
    }

    // Create default storage configuration from provided credentials
    const storageConfig = await prisma.storageConfig.create({
      data: {
        provider: 'S3',
        region: 'fr-par-1',
        endpoint: 'https://fr-par-1.linodeobjects.com',
        bucketName: 'mocky',
        accessKeyId: '73OQS52ORLRPBO3KG6YN',
        secretAccessKey: 'p5UW5FZ7Gog5PMP749MpdHGhPRXUKYnStFJtAaMx',
        isDefault: true
      }
    });

    console.log('Created default storage configuration:', storageConfig.id);
  } catch (error) {
    console.error('Error seeding storage configuration:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();