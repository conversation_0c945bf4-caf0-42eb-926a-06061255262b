/*
  Warnings:

  - The primary key for the `blog_posts` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `category_id` on the `blog_posts` table. All the data in the column will be lost.
  - You are about to drop the column `published` on the `blog_posts` table. All the data in the column will be lost.
  - The `id` column on the `blog_posts` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to alter the column `title` on the `blog_posts` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - You are about to alter the column `slug` on the `blog_posts` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - The primary key for the `categories` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The `id` column on the `categories` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to alter the column `name` on the `categories` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to alter the column `slug` on the `categories` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to drop the column `content` on the `company_story` table. All the data in the column will be lost.
  - You are about to alter the column `title` on the `company_story` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(200)`.
  - The primary key for the `pricing` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `isPopular` on the `pricing` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `pricing` table. All the data in the column will be lost.
  - The `id` column on the `pricing` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to alter the column `price` on the `pricing` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Integer`.
  - You are about to drop the column `contactPhone` on the `site_settings` table. All the data in the column will be lost.
  - You are about to drop the column `youtubeUrl` on the `site_settings` table. All the data in the column will be lost.
  - You are about to alter the column `siteName` on the `site_settings` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to alter the column `siteDescription` on the `site_settings` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - You are about to alter the column `contactEmail` on the `site_settings` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to alter the column `address` on the `site_settings` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - You are about to alter the column `facebookUrl` on the `site_settings` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - You are about to alter the column `twitterUrl` on the `site_settings` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - You are about to alter the column `instagramUrl` on the `site_settings` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - You are about to alter the column `linkedinUrl` on the `site_settings` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - You are about to drop the column `name` on the `storage_config` table. All the data in the column will be lost.
  - You are about to alter the column `provider` on the `storage_config` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(50)`.
  - You are about to alter the column `region` on the `storage_config` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to alter the column `endpoint` on the `storage_config` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - You are about to alter the column `bucketName` on the `storage_config` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to alter the column `accessKeyId` on the `storage_config` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - You are about to alter the column `secretAccessKey` on the `storage_config` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - You are about to alter the column `name` on the `team_members` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to alter the column `role` on the `team_members` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to alter the column `linkedinUrl` on the `team_members` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - You are about to alter the column `twitterUrl` on the `team_members` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - You are about to alter the column `githubUrl` on the `team_members` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - You are about to alter the column `emailAddress` on the `team_members` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to drop the column `order` on the `website_portfolio` table. All the data in the column will be lost.
  - You are about to alter the column `title` on the `website_portfolio` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - Added the required column `founderName` to the `company_story` table without a default value. This is not possible if the table is not empty.
  - Added the required column `founderRole` to the `company_story` table without a default value. This is not possible if the table is not empty.
  - Added the required column `quote1` to the `company_story` table without a default value. This is not possible if the table is not empty.
  - Added the required column `subtitle` to the `company_story` table without a default value. This is not possible if the table is not empty.
  - Made the column `imageSrc` on table `company_story` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `service` to the `pricing` table without a default value. This is not possible if the table is not empty.
  - Added the required column `category` to the `website_portfolio` table without a default value. This is not possible if the table is not empty.
  - Made the column `url` on table `website_portfolio` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "blog_posts" DROP CONSTRAINT "blog_posts_category_id_fkey";

-- DropIndex
DROP INDEX "company_story_id_key";

-- AlterTable
ALTER TABLE "blog_posts" DROP CONSTRAINT "blog_posts_pkey",
DROP COLUMN "category_id",
DROP COLUMN "published",
ADD COLUMN     "author" VARCHAR(100),
ADD COLUMN     "category" VARCHAR(100),
ADD COLUMN     "status" VARCHAR(20) NOT NULL DEFAULT 'draft',
ADD COLUMN     "tags" TEXT[] DEFAULT ARRAY[]::TEXT[],
DROP COLUMN "id",
ADD COLUMN     "id" SERIAL NOT NULL,
ALTER COLUMN "title" SET DATA TYPE VARCHAR(255),
ALTER COLUMN "slug" SET DATA TYPE VARCHAR(255),
ALTER COLUMN "updated_at" SET DEFAULT CURRENT_TIMESTAMP,
ADD CONSTRAINT "blog_posts_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "categories" DROP CONSTRAINT "categories_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" SERIAL NOT NULL,
ALTER COLUMN "name" SET DATA TYPE VARCHAR(100),
ALTER COLUMN "slug" SET DATA TYPE VARCHAR(100),
ALTER COLUMN "updated_at" SET DEFAULT CURRENT_TIMESTAMP,
ADD CONSTRAINT "categories_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "company_story" DROP COLUMN "content",
ADD COLUMN     "founderName" VARCHAR(100) NOT NULL,
ADD COLUMN     "founderRole" VARCHAR(100) NOT NULL,
ADD COLUMN     "instagramUrl" VARCHAR(255),
ADD COLUMN     "isActive" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "linkedinUrl" VARCHAR(255),
ADD COLUMN     "quote1" TEXT NOT NULL,
ADD COLUMN     "quote2" TEXT,
ADD COLUMN     "subtitle" VARCHAR(255) NOT NULL,
ADD COLUMN     "tiktokUrl" VARCHAR(255),
ADD COLUMN     "twitterUrl" VARCHAR(255),
ALTER COLUMN "title" SET DATA TYPE VARCHAR(200),
ALTER COLUMN "imageSrc" SET NOT NULL,
ALTER COLUMN "updated_at" SET DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "pricing" DROP CONSTRAINT "pricing_pkey",
DROP COLUMN "isPopular",
DROP COLUMN "name",
ADD COLUMN     "icon" VARCHAR(100),
ADD COLUMN     "popular" BOOLEAN DEFAULT false,
ADD COLUMN     "service" VARCHAR(255) NOT NULL,
DROP COLUMN "id",
ADD COLUMN     "id" SERIAL NOT NULL,
ALTER COLUMN "description" DROP NOT NULL,
ALTER COLUMN "price" SET DATA TYPE INTEGER,
ALTER COLUMN "features" SET DEFAULT ARRAY[]::TEXT[],
ALTER COLUMN "updated_at" SET DEFAULT CURRENT_TIMESTAMP,
ADD CONSTRAINT "pricing_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "site_settings" DROP COLUMN "contactPhone",
DROP COLUMN "youtubeUrl",
ADD COLUMN     "googleAnalyticsId" VARCHAR(50),
ADD COLUMN     "metaDescription" TEXT,
ADD COLUMN     "metaTitle" VARCHAR(255),
ADD COLUMN     "phoneNumber" VARCHAR(50),
ADD COLUMN     "tiktokUrl" VARCHAR(255),
ALTER COLUMN "siteName" DROP DEFAULT,
ALTER COLUMN "siteName" SET DATA TYPE VARCHAR(100),
ALTER COLUMN "siteDescription" SET DATA TYPE VARCHAR(255),
ALTER COLUMN "contactEmail" SET DATA TYPE VARCHAR(100),
ALTER COLUMN "address" SET DATA TYPE VARCHAR(255),
ALTER COLUMN "facebookUrl" SET DATA TYPE VARCHAR(255),
ALTER COLUMN "twitterUrl" SET DATA TYPE VARCHAR(255),
ALTER COLUMN "instagramUrl" SET DATA TYPE VARCHAR(255),
ALTER COLUMN "linkedinUrl" SET DATA TYPE VARCHAR(255),
ALTER COLUMN "updated_at" SET DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "storage_config" DROP COLUMN "name",
ALTER COLUMN "provider" DROP DEFAULT,
ALTER COLUMN "provider" SET DATA TYPE VARCHAR(50),
ALTER COLUMN "region" SET DATA TYPE VARCHAR(100),
ALTER COLUMN "endpoint" SET DATA TYPE VARCHAR(255),
ALTER COLUMN "bucketName" SET DATA TYPE VARCHAR(100),
ALTER COLUMN "accessKeyId" SET DATA TYPE VARCHAR(255),
ALTER COLUMN "secretAccessKey" SET DATA TYPE VARCHAR(255),
ALTER COLUMN "updated_at" SET DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "team_members" ALTER COLUMN "name" SET DATA TYPE VARCHAR(100),
ALTER COLUMN "role" SET DATA TYPE VARCHAR(100),
ALTER COLUMN "linkedinUrl" SET DATA TYPE VARCHAR(255),
ALTER COLUMN "twitterUrl" SET DATA TYPE VARCHAR(255),
ALTER COLUMN "githubUrl" SET DATA TYPE VARCHAR(255),
ALTER COLUMN "emailAddress" SET DATA TYPE VARCHAR(100),
ALTER COLUMN "updated_at" SET DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "website_portfolio" DROP COLUMN "order",
ADD COLUMN     "category" VARCHAR(100) NOT NULL,
ADD COLUMN     "featured" BOOLEAN NOT NULL DEFAULT false,
ALTER COLUMN "title" SET DATA TYPE VARCHAR(255),
ALTER COLUMN "description" DROP NOT NULL,
ALTER COLUMN "url" SET NOT NULL,
ALTER COLUMN "updated_at" SET DEFAULT CURRENT_TIMESTAMP;

-- CreateTable
CREATE TABLE "technology_items" (
    "id" SERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "icon" VARCHAR(50) NOT NULL,
    "level" VARCHAR(20) NOT NULL,
    "experience" VARCHAR(50) NOT NULL,
    "stackId" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "technology_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "technology_stacks" (
    "id" SERIAL NOT NULL,
    "category" VARCHAR(100) NOT NULL,
    "icon" VARCHAR(50) NOT NULL,
    "description" TEXT NOT NULL,
    "bgColor" VARCHAR(100) NOT NULL,
    "textColor" VARCHAR(100) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "technology_stacks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "database_backups" (
    "id" TEXT NOT NULL,
    "filename" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "size" INTEGER NOT NULL DEFAULT 0,
    "path" VARCHAR(255) NOT NULL,
    "type" VARCHAR(50) NOT NULL,
    "status" VARCHAR(50) NOT NULL DEFAULT 'completed',
    "createdBy" VARCHAR(100),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "database_backups_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "scheduled_blog_posts" (
    "id" TEXT NOT NULL,
    "category" VARCHAR(100),
    "tone" VARCHAR(50),
    "length" VARCHAR(20),
    "targetAudience" VARCHAR(255),
    "scheduledDate" TIMESTAMP(3) NOT NULL,
    "status" VARCHAR(20) NOT NULL DEFAULT 'pending',
    "blogPostId" VARCHAR(255),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "scheduled_blog_posts_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "technology_items" ADD CONSTRAINT "technology_items_stackId_fkey" FOREIGN KEY ("stackId") REFERENCES "technology_stacks"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
