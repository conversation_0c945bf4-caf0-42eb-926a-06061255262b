-- Improve catalogue schema with better constraints and indexes

-- Add unique constraint on service name (case insensitive)
CREATE UNIQUE INDEX IF NOT EXISTS "catalogue_service_unique_idx" ON "catalogue" (LOWER("service"));

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS "catalogue_category_idx" ON "catalogue" ("category");
CREATE INDEX IF NOT EXISTS "catalogue_price_idx" ON "catalogue" ("price");
CREATE INDEX IF NOT EXISTS "catalogue_popular_idx" ON "catalogue" ("popular");
CREATE INDEX IF NOT EXISTS "catalogue_created_at_idx" ON "catalogue" ("created_at");
CREATE INDEX IF NOT EXISTS "catalogue_updated_at_idx" ON "catalogue" ("updated_at");

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS "catalogue_category_price_idx" ON "catalogue" ("category", "price");
CREATE INDEX IF NOT EXISTS "catalogue_popular_price_idx" ON "catalogue" ("popular", "price");
CREATE INDEX IF NOT EXISTS "catalogue_category_popular_idx" ON "catalogue" ("category", "popular");

-- Full-text search index for service and description
CREATE INDEX IF NOT EXISTS "catalogue_service_search_idx" ON "catalogue" USING gin(to_tsvector('english', "service"));
CREATE INDEX IF NOT EXISTS "catalogue_description_search_idx" ON "catalogue" USING gin(to_tsvector('english', COALESCE("description", '')));

-- Add check constraints for data integrity
ALTER TABLE "catalogue" ADD CONSTRAINT IF NOT EXISTS "catalogue_price_positive" CHECK ("price" > 0);
ALTER TABLE "catalogue" ADD CONSTRAINT IF NOT EXISTS "catalogue_price_reasonable" CHECK ("price" <= 10000000);
ALTER TABLE "catalogue" ADD CONSTRAINT IF NOT EXISTS "catalogue_service_not_empty" CHECK (LENGTH(TRIM("service")) > 0);
ALTER TABLE "catalogue" ADD CONSTRAINT IF NOT EXISTS "catalogue_service_length" CHECK (LENGTH("service") <= 255);
ALTER TABLE "catalogue" ADD CONSTRAINT IF NOT EXISTS "catalogue_description_length" CHECK (LENGTH(COALESCE("description", '')) <= 1000);
ALTER TABLE "catalogue" ADD CONSTRAINT IF NOT EXISTS "catalogue_category_length" CHECK (LENGTH(COALESCE("category", '')) <= 100);

-- Add constraint for features array size
ALTER TABLE "catalogue" ADD CONSTRAINT IF NOT EXISTS "catalogue_features_size" CHECK (array_length("features", 1) IS NULL OR array_length("features", 1) <= 10);

-- Add constraint for valid image URLs (basic check)
ALTER TABLE "catalogue" ADD CONSTRAINT IF NOT EXISTS "catalogue_image_url_format" 
  CHECK ("imageUrl" IS NULL OR "imageUrl" ~ '^https?://.*');
ALTER TABLE "catalogue" ADD CONSTRAINT IF NOT EXISTS "catalogue_image_url2_format" 
  CHECK ("imageUrl2" IS NULL OR "imageUrl2" ~ '^https?://.*');
ALTER TABLE "catalogue" ADD CONSTRAINT IF NOT EXISTS "catalogue_image_url3_format" 
  CHECK ("imageUrl3" IS NULL OR "imageUrl3" ~ '^https?://.*');

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_catalogue_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_catalogue_updated_at_trigger ON "catalogue";
CREATE TRIGGER update_catalogue_updated_at_trigger
  BEFORE UPDATE ON "catalogue"
  FOR EACH ROW
  EXECUTE FUNCTION update_catalogue_updated_at();

-- Create a function for case-insensitive service name uniqueness
CREATE OR REPLACE FUNCTION check_service_name_unique()
RETURNS TRIGGER AS $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM "catalogue" 
    WHERE LOWER("service") = LOWER(NEW."service") 
    AND "id" != COALESCE(NEW."id", -1)
  ) THEN
    RAISE EXCEPTION 'Service name already exists (case insensitive): %', NEW."service";
  END IF;
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for service name uniqueness
DROP TRIGGER IF EXISTS check_service_name_unique_trigger ON "catalogue";
CREATE TRIGGER check_service_name_unique_trigger
  BEFORE INSERT OR UPDATE ON "catalogue"
  FOR EACH ROW
  EXECUTE FUNCTION check_service_name_unique();

-- Create audit table for tracking changes
CREATE TABLE IF NOT EXISTS "catalogue_audit" (
  "id" SERIAL PRIMARY KEY,
  "catalogue_id" INTEGER NOT NULL,
  "action" VARCHAR(10) NOT NULL CHECK ("action" IN ('INSERT', 'UPDATE', 'DELETE')),
  "old_data" JSONB,
  "new_data" JSONB,
  "changed_by" VARCHAR(255),
  "changed_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "ip_address" INET,
  "user_agent" TEXT
);

-- Create indexes for audit table
CREATE INDEX IF NOT EXISTS "catalogue_audit_catalogue_id_idx" ON "catalogue_audit" ("catalogue_id");
CREATE INDEX IF NOT EXISTS "catalogue_audit_action_idx" ON "catalogue_audit" ("action");
CREATE INDEX IF NOT EXISTS "catalogue_audit_changed_at_idx" ON "catalogue_audit" ("changed_at");

-- Create audit trigger function
CREATE OR REPLACE FUNCTION catalogue_audit_trigger()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'DELETE' THEN
    INSERT INTO "catalogue_audit" ("catalogue_id", "action", "old_data")
    VALUES (OLD."id", 'DELETE', row_to_json(OLD));
    RETURN OLD;
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO "catalogue_audit" ("catalogue_id", "action", "old_data", "new_data")
    VALUES (NEW."id", 'UPDATE', row_to_json(OLD), row_to_json(NEW));
    RETURN NEW;
  ELSIF TG_OP = 'INSERT' THEN
    INSERT INTO "catalogue_audit" ("catalogue_id", "action", "new_data")
    VALUES (NEW."id", 'INSERT', row_to_json(NEW));
    RETURN NEW;
  END IF;
  RETURN NULL;
END;
$$ language 'plpgsql';

-- Create audit trigger
DROP TRIGGER IF EXISTS catalogue_audit_trigger ON "catalogue";
CREATE TRIGGER catalogue_audit_trigger
  AFTER INSERT OR UPDATE OR DELETE ON "catalogue"
  FOR EACH ROW
  EXECUTE FUNCTION catalogue_audit_trigger();

-- Create view for catalogue with computed fields
CREATE OR REPLACE VIEW "catalogue_enhanced" AS
SELECT 
  c.*,
  CASE 
    WHEN c."price" < 1000 THEN 'Budget'
    WHEN c."price" < 5000 THEN 'Standard'
    WHEN c."price" < 20000 THEN 'Premium'
    ELSE 'Enterprise'
  END as "price_tier",
  array_length(c."features", 1) as "feature_count",
  CASE 
    WHEN c."imageUrl" IS NOT NULL OR c."imageUrl2" IS NOT NULL OR c."imageUrl3" IS NOT NULL 
    THEN true 
    ELSE false 
  END as "has_images",
  to_tsvector('english', c."service" || ' ' || COALESCE(c."description", '') || ' ' || COALESCE(c."category", '')) as "search_vector"
FROM "catalogue" c;

-- Create materialized view for statistics (refresh periodically)
CREATE MATERIALIZED VIEW IF NOT EXISTS "catalogue_stats" AS
SELECT 
  COUNT(*) as "total_items",
  COUNT(CASE WHEN "popular" = true THEN 1 END) as "popular_items",
  AVG("price")::INTEGER as "avg_price",
  MIN("price") as "min_price",
  MAX("price") as "max_price",
  COUNT(DISTINCT "category") as "category_count",
  COUNT(CASE WHEN "imageUrl" IS NOT NULL THEN 1 END) as "items_with_images",
  CURRENT_TIMESTAMP as "last_updated"
FROM "catalogue";

-- Create index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS "catalogue_stats_unique_idx" ON "catalogue_stats" ("last_updated");

-- Create function to refresh stats
CREATE OR REPLACE FUNCTION refresh_catalogue_stats()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY "catalogue_stats";
END;
$$ language 'plpgsql';

-- Add comments for documentation
COMMENT ON TABLE "catalogue" IS 'Main catalogue table storing product/service information';
COMMENT ON COLUMN "catalogue"."service" IS 'Name of the service/product (unique, case-insensitive)';
COMMENT ON COLUMN "catalogue"."price" IS 'Price in smallest currency unit (e.g., cents)';
COMMENT ON COLUMN "catalogue"."features" IS 'Array of feature descriptions (max 10 items)';
COMMENT ON COLUMN "catalogue"."popular" IS 'Flag indicating if this is a popular/featured item';
COMMENT ON TABLE "catalogue_audit" IS 'Audit trail for all catalogue changes';
COMMENT ON VIEW "catalogue_enhanced" IS 'Enhanced view with computed fields for better querying';
COMMENT ON MATERIALIZED VIEW "catalogue_stats" IS 'Cached statistics for dashboard display';
