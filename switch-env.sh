#!/bin/bash

# <PERSON><PERSON>t to switch between development and production environments

if [ "$1" == "dev" ]; then
  echo "Switching to development environment..."
  
  # Stop the production PM2 process
  pm2 stop mocky-digital || echo "No production process to stop"
  
  # Create a symlink to the development configuration
  echo "Using development Next.js configuration"
  
  # Start the development server
  echo "Starting development server..."
  NODE_ENV=development NEXT_CONFIG_PATH=next.config.dev.js npm run dev
  
elif [ "$1" == "prod" ]; then
  echo "Switching to production environment..."
  
  # Kill any running development servers
  pkill -f "next dev" || echo "No development servers to kill"
  
  # Build for production
  echo "Building for production..."
  npm run build
  
  # Start with PM2
  echo "Starting production server with PM2..."
  pm2 start ecosystem.config.js
  
else
  echo "Usage: $0 [dev|prod]"
  echo "  dev  - Switch to development environment"
  echo "  prod - Switch to production environment"
  exit 1
fi 