# Google Analytics 4 Setup Guide

This guide will help you set up Google Analytics 4 (GA4) for your Mocky Digital website to get real analytics data instead of mock data.

## Prerequisites

- Google account
- Access to Google Analytics
- Access to Google Cloud Console
- Admin access to your website

## Step 1: Create Google Analytics 4 Property

1. **Go to Google Analytics**: Visit [analytics.google.com](https://analytics.google.com)

2. **Create Account** (if you don't have one):
   - Click "Start measuring"
   - Enter account name: "Mocky Digital"
   - Configure data sharing settings as needed

3. **Create Property**:
   - Property name: "Mocky Digital Website"
   - Reporting time zone: Select your timezone (e.g., "Africa/Nairobi")
   - Currency: Select your currency (e.g., "Kenyan Shilling - KES")

4. **Business Information**:
   - Industry category: "Technology" or "Professional Services"
   - Business size: Select appropriate size
   - How you plan to use Analytics: Select relevant options

5. **Create Data Stream**:
   - Choose "Web"
   - Website URL: `https://mocky.co.ke` (or your domain)
   - Stream name: "Mocky Digital Main Site"

6. **Get Measurement ID**:
   - After creating the stream, you'll see a Measurement ID like `G-XXXXXXXXXX`
   - **Save this ID** - you'll need it for `NEXT_PUBLIC_GA_MEASUREMENT_ID`

## Step 2: Create Service Account for API Access

1. **Go to Google Cloud Console**: Visit [console.cloud.google.com](https://console.cloud.google.com)

2. **Create or Select Project**:
   - Create new project: "Mocky Digital Analytics"
   - Or select existing project

3. **Enable Google Analytics Reporting API**:
   - Go to "APIs & Services" > "Library"
   - Search for "Google Analytics Reporting API"
   - Click "Enable"

4. **Enable Google Analytics Data API**:
   - Search for "Google Analytics Data API"
   - Click "Enable"

5. **Create Service Account**:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "Service Account"
   - Service account name: "mocky-analytics-service"
   - Service account ID: "mocky-analytics-service"
   - Description: "Service account for Mocky Digital analytics access"

6. **Create and Download Key**:
   - Click on the created service account
   - Go to "Keys" tab
   - Click "Add Key" > "Create new key"
   - Choose "JSON" format
   - Download the JSON file
   - **Keep this file secure!**

## Step 3: Grant Analytics Access to Service Account

1. **Get Service Account Email**:
   - From the downloaded JSON file, find the `client_email` field
   - It looks like: `<EMAIL>`

2. **Add User to Analytics Property**:
   - Go back to Google Analytics
   - Click "Admin" (gear icon)
   - In the "Property" column, click "Property access management"
   - Click "+" (Add users)
   - Enter the service account email
   - Select "Viewer" role (minimum required)
   - Click "Add"

## Step 4: Get Property ID

1. **In Google Analytics**:
   - Go to "Admin" > "Property Settings"
   - Find "Property ID" - it looks like `*********`
   - The full property ID for the API is `properties/*********`

## Step 5: Update Environment Variables

Update your `.env` file with the following values:

```env
# Google Analytics Configuration
NEXT_PUBLIC_GA_MEASUREMENT_ID="G-XXXXXXXXXX"  # From Step 1
GOOGLE_ANALYTICS_CLIENT_EMAIL="<EMAIL>"  # From JSON file
GOOGLE_ANALYTICS_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour actual private key here\n-----END PRIVATE KEY-----"  # From JSON file
GOOGLE_ANALYTICS_PROPERTY_ID="properties/*********"  # From Step 4
```

### Important Notes for Private Key:

- Copy the entire `private_key` value from the JSON file
- Keep the `\n` characters as they are
- Ensure the key starts with `-----BEGIN PRIVATE KEY-----` and ends with `-----END PRIVATE KEY-----`

## Step 6: Restart Your Application

After updating the environment variables:

```bash
# Stop the current server
# Restart with:
npm run dev
# or
yarn dev
```

## Step 7: Verify Setup

1. **Check Analytics Dashboard**:
   - Go to `/admin/analytics` in your application
   - You should see "Real Data" badges instead of "Mock Data"
   - Data might be limited initially as GA4 needs time to collect data

2. **Check Browser Console**:
   - Open browser developer tools
   - Look for any error messages related to analytics
   - Should see successful API calls

## Troubleshooting

### Common Issues:

1. **"Mock Data" still showing**:
   - Verify all environment variables are set correctly
   - Restart the application
   - Check that property ID format is correct (`properties/*********`)

2. **API Errors**:
   - Verify service account has access to the Analytics property
   - Check that both APIs are enabled in Google Cloud Console
   - Verify private key format (common issue)

3. **No Data Available**:
   - GA4 needs time to collect data (24-48 hours for meaningful data)
   - Verify website tracking is working (check Real-time reports in GA4)

### Testing Real-time Data:

1. **In Google Analytics**:
   - Go to "Reports" > "Realtime"
   - Visit your website in another tab
   - You should see the visit appear in real-time

2. **Check Tracking**:
   - Use browser developer tools
   - Go to Network tab
   - Visit your website
   - Look for requests to `google-analytics.com` or `googletagmanager.com`

## Security Best Practices

1. **Protect Service Account Key**:
   - Never commit the JSON file to version control
   - Store private key securely in environment variables
   - Rotate keys periodically

2. **Limit Permissions**:
   - Use "Viewer" role for service account (minimum required)
   - Don't grant unnecessary permissions

3. **Monitor Access**:
   - Regularly review who has access to your Analytics property
   - Remove unused service accounts

## Next Steps

Once setup is complete:

1. **Set up Goals/Conversions** in GA4 for better tracking
2. **Configure Enhanced Ecommerce** if you have an online store
3. **Set up Custom Dimensions** for additional tracking
4. **Create Custom Reports** for specific business needs

## Support

If you encounter issues:

1. Check the browser console for error messages
2. Verify all environment variables are correctly set
3. Ensure the service account has proper permissions
4. Contact support with specific error messages if needed
