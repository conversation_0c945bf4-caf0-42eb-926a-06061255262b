#!/bin/bash

# Comprehensive setup script for Mocky website with Nginx and PM2
echo "Starting full setup for Mocky website..."

# Step 1: Install PM2 globally if not already installed
echo "Installing PM2..."
npm install -g pm2

# Step 2: Build the application
echo "Building the application..."
cd /var/www/mocky
npm run build

# Step 3: Set up PM2 to manage the application
echo "Setting up PM2 for the application..."
# Stop any existing instance
pm2 stop mocky-digital 2>/dev/null || true
pm2 delete mocky-digital 2>/dev/null || true

# Start with PM2
cd /var/www/mocky
pm2 start server.js --name mocky-digital --env production

# Save PM2 configuration to resurrect on reboot
pm2 save

# Set up PM2 to start on system boot
pm2 startup | tail -1 > pm2-startup-command.txt
echo "If needed, run the command in pm2-startup-command.txt to enable PM2 on startup"

# Step 4: Set up Nginx
echo "Setting up Nginx..."

# Install Nginx if not already installed
apt update && apt install -y nginx

# Copy Nginx configuration
cp /var/www/mocky/nginx/prod.mocky.co.ke.conf /etc/nginx/sites-available/mocky.co.ke.conf

# Create symlink to enable the site
ln -sf /etc/nginx/sites-available/mocky.co.ke.conf /etc/nginx/sites-enabled/

# Remove default config if it exists
if [ -f /etc/nginx/sites-enabled/default ]; then
    echo "Removing default Nginx config..."
    rm /etc/nginx/sites-enabled/default
fi

# Update document root paths
mkdir -p /var/www/mocky.co.ke
ln -sf /var/www/mocky/public /var/www/mocky.co.ke/public
ln -sf /var/www/mocky/.next /var/www/mocky.co.ke/.next

# Check Nginx configuration
echo "Checking Nginx configuration..."
nginx -t

# Restart Nginx
echo "Restarting Nginx..."
systemctl restart nginx

# Step 5: Final checks
echo "Checking if application is running..."
if curl -s http://localhost:3000 > /dev/null; then
  echo "✅ Application is running successfully on port 3000!"
else
  echo "❌ Application is not running on port 3000. Check PM2 logs."
fi

echo "Checking Nginx status..."
if systemctl is-active --quiet nginx; then
  echo "✅ Nginx is running successfully!"
else
  echo "❌ Nginx is not running. Check system logs."
fi

echo "Setup complete! Your website should now be accessible at https://mocky.co.ke"
echo "You can check the PM2 status with: pm2 status"
echo "You can check the PM2 logs with: pm2 logs mocky-digital" 