#!/bin/bash

# Script to set up Nginx for Mocky website
echo "Setting up Nginx..."

# Install Nginx if not already installed
apt update && apt install -y nginx

# Copy Nginx configuration
cp /var/www/mocky/nginx/prod.mocky.co.ke.conf /etc/nginx/sites-available/mocky.co.ke.conf

# Create symlink to enable the site
ln -sf /etc/nginx/sites-available/mocky.co.ke.conf /etc/nginx/sites-enabled/

# Remove default config if it exists
if [ -f /etc/nginx/sites-enabled/default ]; then
    echo "Removing default Nginx config..."
    rm /etc/nginx/sites-enabled/default
fi

# Update document root paths
mkdir -p /var/www/mocky.co.ke
ln -sf /var/www/mocky/public /var/www/mocky.co.ke/public
ln -sf /var/www/mocky/.next /var/www/mocky.co.ke/.next

# Check Nginx configuration
echo "Checking Nginx configuration..."
nginx -t

# Restart Nginx
echo "Restarting Nginx..."
systemctl restart nginx

echo "Nginx setup complete!" 