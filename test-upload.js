// Test S3 upload functionality
require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');

// Hardcoded credentials for testing
const region = 'fr-par-1';
const endpoint = 'https://fr-par-1.linodeobjects.com';
const bucketName = 'mocky';
const accessKeyId = '73OQS52ORLRPBO3KG6YN';
const secretAccessKey = 'p5UW5FZ7Gog5PMP749MpdHGhPRXUKYnStFJtAaMx';

// Create S3 client
const s3Client = new S3Client({
  region,
  endpoint,
  credentials: {
    accessKeyId,
    secretAccessKey,
  },
  forcePathStyle: true,
});

// Test file path (using an existing image in the project)
const testFilePath = path.join(__dirname, 'test-image.jpg');

// Check if test file exists
if (!fs.existsSync(testFilePath)) {
  console.error(`Test file not found: ${testFilePath}`);
  process.exit(1);
}

// Read test file
const fileContent = fs.readFileSync(testFilePath);
const fileSize = fs.statSync(testFilePath).size;

console.log(`Test file: ${testFilePath}`);
console.log(`File size: ${fileSize} bytes`);

// Generate a unique key for the test upload
const timestamp = Date.now();
const testKey = `test/test-upload-${timestamp}.jpg`;

// Upload function
async function uploadFile() {
  try {
    console.log(`Uploading file to S3 bucket: ${bucketName}`);
    console.log(`Key: ${testKey}`);

    // Create upload command
    const command = new PutObjectCommand({
      Bucket: bucketName,
      Key: testKey,
      Body: fileContent,
      ContentType: 'image/jpeg',
      ACL: 'public-read',
      Metadata: {
        originalname: 'test-upload.jpg',
        uploadDate: new Date().toISOString(),
      },
    });

    // Upload file
    const response = await s3Client.send(command);

    console.log(`✅ Upload successful!`);
    console.log(`ETag: ${response.ETag}`);

    // Generate public URL
    const url = `${endpoint}/${bucketName}/${testKey}`;
    console.log(`Public URL: ${url}`);

    return true;
  } catch (error) {
    console.error(`❌ Upload failed: ${error.message}`);
    console.error(error);
    return false;
  }
}

// Run the upload test
uploadFile()
  .then(success => {
    if (!success) {
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
