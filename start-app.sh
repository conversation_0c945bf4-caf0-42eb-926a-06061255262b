#!/bin/bash

# <PERSON>ript to start the Mocky application in production mode
echo "Starting Mocky application in production mode..."

# Kill any existing node processes
echo "Killing any existing node processes..."
pkill -f "node server.js" || true

# Set environment variables
export NODE_ENV=production
export PORT=3000

# Start the application in the background
echo "Starting the application..."
nohup node server.js > app.log 2>&1 &

# Wait for app to start
echo "Waiting for application to start..."
sleep 5

# Check if application is running
if curl -s http://localhost:3000 > /dev/null; then
  echo "Application is running successfully!"
  echo "Check app.log for details"
else
  echo "Application failed to start. Check app.log for errors."
fi 