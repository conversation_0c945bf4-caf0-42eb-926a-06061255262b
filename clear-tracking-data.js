const { PrismaClient } = require('@prisma/client');

async function clearTrackingData() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Checking current tracking data...');
    const count = await prisma.eventTracking.count();
    console.log(`Found ${count} tracking events in database`);
    
    if (count === 0) {
      console.log('✅ No tracking data to clear');
      return;
    }
    
    console.log('🗑️ Clearing all tracking data...');
    const result = await prisma.eventTracking.deleteMany({});
    console.log(`✅ Successfully deleted ${result.count} tracking events`);
    
    // Verify deletion
    const remainingCount = await prisma.eventTracking.count();
    console.log(`📊 Remaining events: ${remainingCount}`);
    
    if (remainingCount === 0) {
      console.log('🎉 All sample tracking data has been cleared!');
      console.log('💡 The tracking system will now collect real user data as visitors use your website.');
    }
    
  } catch (error) {
    console.error('❌ Error clearing tracking data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

clearTrackingData();
