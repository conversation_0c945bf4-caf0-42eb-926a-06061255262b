/**
 * Critical CSS for Mocky Digital
 * These styles are loaded first for better performance
 */

/* Body defaults */
body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Basic layout */
.container {
  width: 100%;
  padding-right: 1rem;
  padding-left: 1rem;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

/* Navigation */
.nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Loading animation */
@keyframes loadingProgress {
  0% {
    width: 0%;
    opacity: 1;
  }
  50% {
    width: 70%;
    opacity: 1;
  }
  90% {
    width: 90%;
    opacity: 1;
  }
  100% {
    width: 100%;
    opacity: 0;
  }
}

/* Images and galleries - critical styles */
.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.gap-4 {
  gap: 1rem;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.overflow-hidden {
  overflow: hidden;
}

.relative {
  position: relative;
}

.object-cover {
  object-fit: cover;
}

/* Optimize for image loading */
.img-placeholder {
  background: linear-gradient(110deg, #ececec 8%, #f5f5f5 18%, #ececec 33%);
  background-size: 200% 100%;
  animation: 1.5s shine linear infinite;
}

@keyframes shine {
  to {
    background-position-x: -200%;
  }
}

/* Critical CSS styles */
/* This file contains critical styles needed for initial page rendering */

:root {
  --primary: #ff6b00;
  --primary-dark: #e05a00;
  --primary-light: #ff8a3d;
  --navy-900: #0A1929;
  --navy-400: #1E3A5F;
  --accent: #00A3FF;
  --header-bg: white; /* Explicitly set header background color */
  --header-padding: 1.5rem 0; /* Explicitly set header padding */
  --transition-speed: 300ms; /* Standardize transition speed */
}

/* Base styles that prevent layout shifts */
html {
  scroll-behavior: smooth;
}

body {
  line-height: 1.5;
  color: #333;
  /* Prevent content jumps during page transitions */
  overflow-y: scroll;
}

/* Force hardware acceleration for smoother animations */
.transform-gpu {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: transform;
}

/* Ensure image rendering is optimized */
img {
  image-rendering: auto;
  transform: translateZ(0); /* Hardware acceleration for images */
}

/* Critical utility classes */
.text-primary { color: var(--primary); }
.bg-primary { background-color: var(--primary); }
.bg-primary-light { background-color: var(--primary-light); }
.bg-primary-dark { background-color: var(--primary-dark); }

/* Container and Layout classes */
.container {
  contain: layout;
}

/* Header styles exactly matching the Navbar component initial render */
header.fixed,
header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background-color: var(--header-bg);
  padding: var(--header-padding);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  /* Remove transition property - now using inline styles */
  transform: translateZ(0); /* Force hardware acceleration */
  contain: layout; /* Optimize rendering performance */
}

/* CRITICAL: This ensures the color is always white in the static HTML (before JS) */
header, header.fixed {
  background-color: white !important;
}

/* Navbar state classes */
.navbar-scrolled {
  padding: 0.5rem 0 !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

/* Subtle loading animations that don't cause flickering */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes pulse-subtle {
  0% { opacity: 0.5; }
  50% { opacity: 0.7; }
  100% { opacity: 0.5; }
}

.animate-fade-in {
  animation: fadeIn var(--transition-speed) ease-out;
}

.animate-pulse-subtle {
  animation: pulse-subtle 2s ease-in-out infinite;
}

/* Hero section initial styles - prevents layout shift */
.min-h-screen {
  min-height: 100vh;
}

/* Hero image placeholder - prevents layout shift */
.relative {
  position: relative;
}

.aspect-square {
  /* Prevent layout shifts when loading images in grid */
  contain: strict;
  content-visibility: auto;
}

/* Spinner animation for loading states */
@keyframes spin {
  to { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Smoother transitions for all interactive elements */
a, button, .btn {
  transition: all var(--transition-speed) ease;
}

/* Reduce flicker for images */
.img-wrapper {
  background-color: #f5f5f5;
  overflow: hidden;
}

/* Optimize rendering performance for the gallery */
.gallery-grid {
  contain: content;
  content-visibility: auto;
  contain-intrinsic-size: auto 400px;
} 