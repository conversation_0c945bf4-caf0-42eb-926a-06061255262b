@layer utilities {
  .animate-float {
    animation: float 6s ease-in-out infinite;
    transform-origin: center;
  }
  
  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  .animate-pulse-slower {
    animation: pulse 5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  .stroke-1 {
    -webkit-text-stroke-width: 1px;
    text-stroke-width: 1px;
  }
  
  .stroke-2 {
    -webkit-text-stroke-width: 2px;
    text-stroke-width: 2px;
  }
  
  .text-shadow-sm {
    text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.1);
  }
  
  .text-outline {
    -webkit-text-stroke: 2px currentColor;
    text-stroke: 2px currentColor;
  }
}

/* Optional: Add hover effect */
.animate-float:hover {
  animation-play-state: paused;
  transform: scale(1.05);
  transition: transform 0.3s ease;
} 