// Script to update blog post dates to the current server date
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function updateBlogPosts() {
  try {
    // Get the current date from the server
    const currentDate = new Date();
    
    console.log(`Updating blog posts to use current date: ${currentDate.toISOString()}`);

    // Update all blog posts
    const updateResult = await prisma.blogPost.updateMany({
      data: {
        publishedAt: currentDate,
        createdAt: currentDate,
        updatedAt: currentDate,
      },
    });

    console.log(`Updated ${updateResult.count} blog posts to use current date: ${currentDate.toISOString()}`);
  } catch (error) {
    console.error('Error updating blog posts:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateBlogPosts()
  .then(() => console.log('Done!'))
  .catch((e) => console.error(e)); 