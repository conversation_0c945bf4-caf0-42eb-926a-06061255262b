// Script to update pricing data with new fields
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Map services to icons
const serviceIcons = {
  'Business Card Design': 'id-card',
  'Poster Design': 'file',
  'Company Profile': 'book-open',
  'Catalogue': 'book',
  'Invoice Design': 'file-invoice',
  'Letterhead': 'file-alt',
  'Flyer Design': 'file-image',
  'Brochure Design': 'newspaper',
  'Social Media Posts': 'hashtag',
  'Banner Design': 'image',
  'Label Design': 'tag',
  // Fallback icon
  'default': 'paint-brush'
};

// Function to get service-specific features
function getServiceFeatures(service) {
  switch(service) {
    case 'Business Card Design':
      return [
        '3 unique concepts',
        '2 revision rounds',
        'Print-ready files',
        'Digital formats for online use',
        'Double-sided design',
        'Delivery in 2 days'
      ];
    case 'Flyer Design':
      return [
        '2 design concepts',
        '3 revision rounds',
        'Print-ready files (CMYK)',
        'Web formats (RGB)',
        'Custom illustrations',
        'Delivery in 3 days'
      ];
    case 'Brochure Design':
      return [
        'Up to 8-page design',
        '3 revision rounds',
        'Print-ready files',
        'Digital formats',
        'Custom graphics',
        'Layout & typography',
        'Delivery in 5 days'
      ];
    case 'Company Profile':
      return [
        'Professional layout design',
        'Up to 15 pages',
        '3 revision rounds',
        'Print & digital formats',
        'Custom graphics',
        'Delivery in 7 days'
      ];
    case 'Catalogue':
      return [
        'Professional product layout',
        'Up to 20 pages',
        '3 revision rounds',
        'Print-ready files',
        'Digital version',
        'Delivery in 7 days'
      ];
    case 'Invoice Design':
      return [
        'Professional layout',
        '2 revision rounds',
        'Print & digital formats',
        'Editable template',
        'Delivery in 2 days'
      ];
    case 'Letterhead':
      return [
        'Professional design',
        '2 revision rounds',
        'Print & digital formats',
        'Editable template',
        'Delivery in 2 days'
      ];
    case 'Poster Design':
      return [
        '2 design concepts',
        '3 revision rounds',
        'High-resolution files',
        'Print & digital formats',
        'Delivery in 3 days'
      ];
    default:
      return [
        'Professional design',
        '2 revision rounds',
        'High-quality files',
        'Fast turnaround time',
        'Commercial usage rights'
      ];
  }
}

async function main() {
  console.log('Updating pricing data with new fields...');

  try {
    // Get all catalogue items
    const catalogueItems = await prisma.catalogue.findMany();
    console.log(`Found ${catalogueItems.length} catalogue items to update`);

    // Update each catalogue item
    for (const item of catalogueItems) {
      // Skip items that already have features
      if (item.features && item.features.length > 0) {
        console.log(`Skipping ${item.service} - already has features`);
        continue;
      }

      // Determine if this is a popular service - use a consistent rule
      // Services with price >= 3000 are marked as popular
      const isPopular = item.price >= 3000;

      // Get icon for this service
      const icon = serviceIcons[item.service] || serviceIcons.default;

      // Get features for this service
      const features = getServiceFeatures(item.service);

      // Check if the database schema has the fields we need
      try {
        // Update the catalogue item using Prisma's update method
        await prisma.catalogue.update({
          where: { id: item.id },
          data: {
            features: features,
            icon: icon,
            popular: isPopular
          }
        });
      } catch (updateError) {
        console.error(`Error updating ${item.service}:`, updateError);
      }

      console.log(`Updated ${item.service} with new fields`);
    }

    console.log('Pricing data update completed successfully');
  } catch (error) {
    console.error('Error updating pricing data:', error);
  }
}

main()
  .catch(e => {
    console.error('Error:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
