{"compilerOptions": {"target": "es2015", "lib": ["dom", "dom.iterable", "esnext"], "types": ["node"], "typeRoots": ["./node_modules/@types"], "allowJs": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}