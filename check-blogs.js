// Script to check blog posts in the database
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkBlogPosts() {
  try {
    // Get all blog posts
    const posts = await prisma.blogPost.findMany();
    console.log('All blog posts in database:');
    posts.forEach(post => {
      console.log(`ID: ${post.id}, Title: ${post.title}`);
      console.log(`  Status: ${post.status}, PublishedAt: ${post.publishedAt}`);
      console.log(`  CreatedAt: ${post.createdAt}, UpdatedAt: ${post.updatedAt}`);
      console.log('---');
    });
    
    // Get published blog posts
    const publishedPosts = await prisma.blogPost.findMany({
      where: {
        status: 'published'
      }
    });
    console.log('\nPublished blog posts:');
    publishedPosts.forEach(post => {
      console.log(`ID: ${post.id}, Title: ${post.title}`);
      console.log(`  PublishedAt: ${post.publishedAt}`);
      console.log('---');
    });
  } catch (error) {
    console.error('Error checking blog posts:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkBlogPosts()
  .then(() => console.log('Done!'))
  .catch((e) => console.error(e)); 