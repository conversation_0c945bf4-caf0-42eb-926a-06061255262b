#!/bin/bash

# Create upload script
cat > /var/www/mocky.co.ke/scripts/upload-image.sh << 'EOL'
#!/bin/bash

# Usage: ./upload-image.sh source_file category

SOURCE=$1
CATEGORY=$2

if [ -z "$SOURCE" ] || [ -z "$CATEGORY" ]; then
  echo "Usage: $0 source_file category"
  echo "Categories: logos, branding, fliers, websites, cards, letterheads, profiles"
  exit 1
fi

if [ ! -f "$SOURCE" ]; then
  echo "Source file not found: $SOURCE"
  exit 1
fi

# Map categories to directories
case $CATEGORY in
  logos)
    DEST_DIR="/var/www/uploads/images/logos"
    ;;
  branding)
    DEST_DIR="/var/www/uploads/images/branding"
    ;;
  fliers)
    DEST_DIR="/var/www/uploads/images/portfolio/fliers"
    ;;
  websites)
    DEST_DIR="/var/www/uploads/images/portfolio/websites"
    ;;
  cards)
    DEST_DIR="/var/www/uploads/images/portfolio/cards"
    ;;
  letterheads)
    DEST_DIR="/var/www/uploads/images/portfolio/letterheads"
    ;;
  profiles)
    DEST_DIR="/var/www/uploads/images/portfolio/profiles"
    ;;
  *)
    echo "Invalid category: $CATEGORY"
    exit 1
    ;;
esac

# Create timestamp-based filename
TIMESTAMP=$(date +%Y%m%d%H%M%S)
FILENAME=$(basename "$SOURCE")
EXT="${FILENAME##*.}"
NEW_FILENAME="${TIMESTAMP}-${FILENAME}"

# Copy file to destination
cp "$SOURCE" "$DEST_DIR/$NEW_FILENAME"

# Set permissions
chown deployer:www-data "$DEST_DIR/$NEW_FILENAME"
chmod 644 "$DEST_DIR/$NEW_FILENAME"

echo "Image uploaded successfully to $DEST_DIR/$NEW_FILENAME"
echo "Web path: /uploads/images/${CATEGORY}/$NEW_FILENAME"
EOL

# Make the script executable
chmod +x /var/www/mocky.co.ke/scripts/upload-image.sh 