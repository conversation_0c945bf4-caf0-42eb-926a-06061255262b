// Script to seed the storage configuration in the database
require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Debug environment variables
console.log('Environment variables:');
console.log('NEXT_PUBLIC_S3_REGION:', process.env.NEXT_PUBLIC_S3_REGION);
console.log('NEXT_PUBLIC_S3_ENDPOINT:', process.env.NEXT_PUBLIC_S3_ENDPOINT);
console.log('NEXT_PUBLIC_S3_BUCKET:', process.env.NEXT_PUBLIC_S3_BUCKET);
console.log('NEXT_PUBLIC_S3_ACCESS_KEY:', process.env.NEXT_PUBLIC_S3_ACCESS_KEY ? '***' : 'undefined');
console.log('NEXT_PUBLIC_S3_SECRET_KEY:', process.env.NEXT_PUBLIC_S3_SECRET_KEY ? '***' : 'undefined');

async function main() {
  try {
    console.log('Starting to seed storage configuration...');

    // Check if storage config already exists
    const existingConfig = await prisma.storageConfig.findFirst({
      where: {
        provider: 'S3',
        bucketName: process.env.NEXT_PUBLIC_S3_BUCKET,
      },
    });

    if (existingConfig) {
      console.log('Storage configuration already exists. Updating...');

      // Update existing config
      const updatedConfig = await prisma.storageConfig.update({
        where: {
          id: existingConfig.id,
        },
        data: {
          provider: 'S3',
          region: process.env.NEXT_PUBLIC_S3_REGION,
          endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT,
          bucketName: process.env.NEXT_PUBLIC_S3_BUCKET,
          accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY,
          secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY,
          isDefault: true,
        },
      });

      console.log('Storage configuration updated successfully:', updatedConfig);
    } else {
      console.log('Creating new storage configuration...');

      // Create new storage config
      const newConfig = await prisma.storageConfig.create({
        data: {
          provider: 'S3',
          region: process.env.NEXT_PUBLIC_S3_REGION,
          endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT,
          bucketName: process.env.NEXT_PUBLIC_S3_BUCKET,
          accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY,
          secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY,
          isDefault: true,
        },
      });

      console.log('Storage configuration created successfully:', newConfig);
    }

    console.log('Storage configuration seeding completed.');
  } catch (error) {
    console.error('Error seeding storage configuration:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
