console.log('Lead management test script started');

const { PrismaClient } = require('@prisma/client');
console.log('PrismaClient imported successfully');

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});
console.log('PrismaClient instance created');

async function main() {
  try {
    console.log('Testing lead management functionality...');

    // Create a test lead
    console.log('Creating test lead...');
    const lead = await prisma.lead.create({
      data: {
        name: 'Test Lead',
        email: '<EMAIL>',
        phone: '1234567890',
        company: 'Test Company',
        source: 'test_script',
        status: 'new',
        score: 50,
        notes: 'This is a test lead created by the test script',
      },
    });

    console.log(`Created test lead with ID: ${lead.id}`);

    // Create a test interaction
    console.log('Creating test interaction...');
    const interaction = await prisma.interaction.create({
      data: {
        leadId: lead.id,
        type: 'note',
        details: 'This is a test interaction created by the test script',
      },
    });

    console.log(`Created test interaction with ID: ${interaction.id}`);

    // Create a test event
    console.log('Creating test event...');
    const event = await prisma.eventTracking.create({
      data: {
        eventName: 'test_event',
        eventType: 'pageView',
        url: 'http://example.com/test',
        sessionId: 'test_session',
        leadId: lead.id,
        metadata: {
          test: true,
          value: 123,
        },
      },
    });

    console.log(`Created test event with ID: ${event.id}`);

    // Fetch the lead with interactions
    console.log('Fetching lead with interactions...');
    const leadWithInteractions = await prisma.lead.findUnique({
      where: {
        id: lead.id,
      },
      include: {
        interactions: true,
      },
    });

    console.log('Lead with interactions:');
    console.log(JSON.stringify(leadWithInteractions, null, 2));

    // Fetch events for the lead
    console.log('Fetching events for the lead...');
    const events = await prisma.eventTracking.findMany({
      where: {
        leadId: lead.id,
      },
    });

    console.log('Events for the lead:');
    console.log(JSON.stringify(events, null, 2));

    // Clean up
    console.log('Cleaning up...');
    await prisma.eventTracking.deleteMany({
      where: {
        leadId: lead.id,
      },
    });
    await prisma.interaction.deleteMany({
      where: {
        leadId: lead.id,
      },
    });
    await prisma.lead.delete({
      where: {
        id: lead.id,
      },
    });

    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Error testing lead management:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
