/**
 * Test script for tracking API endpoints
 * This script will directly query the database to check if tracking data exists
 * and then try to access the API endpoints
 */
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

async function main() {
  try {
    console.log('Testing tracking API endpoints...');

    // Check if tracking data exists in the database
    const eventCount = await prisma.eventTracking.count();
    console.log(`Found ${eventCount} events in the database`);

    // Get event counts by type
    const eventTypes = await prisma.eventTracking.groupBy({
      by: ['eventType'],
      _count: {
        eventType: true
      }
    });

    console.log('Event counts by type:');
    console.log(JSON.stringify(eventTypes, null, 2));

    // Get page view counts
    const pageViews = await prisma.$queryRaw`
      SELECT "url", COUNT(*) as "views"
      FROM "event_tracking"
      WHERE "eventType" = 'pageView'
        AND "url" IS NOT NULL
      GROUP BY "url"
      ORDER BY "views" DESC
      LIMIT 5
    `;

    console.log('Top 5 page views:');
    // Convert BigInt to Number for JSON serialization
    const serializedPageViews = pageViews.map(item => ({
      url: item.url,
      views: Number(item.views)
    }));
    console.log(JSON.stringify(serializedPageViews, null, 2));

    // Test the raw SQL query that's failing
    console.log('Testing raw SQL query with created_at...');
    const testQuery = await prisma.$queryRaw`
      SELECT "eventType", COUNT(*) as count
      FROM "event_tracking"
      WHERE "created_at" >= NOW() - INTERVAL '30 days'
      GROUP BY "eventType"
      ORDER BY count DESC
    `;

    console.log('Query result:');
    // Convert BigInt to Number for JSON serialization
    const serializedTestQuery = testQuery.map(item => ({
      eventType: item.eventType,
      count: Number(item.count)
    }));
    console.log(JSON.stringify(serializedTestQuery, null, 2));

    console.log('All tests completed successfully!');
  } catch (error) {
    console.error('Error testing tracking API:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
