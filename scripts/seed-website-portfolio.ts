import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { WebsitePortfolioItem } from '@/types/portfolio';

// Define the website portfolio data from the image
const websitePortfolioData = [
  {
    title: 'Homestore.co.ke',
    description: 'Modern E-commerce Platform for Household Items',
    category: 'e-commerce',
    url: 'https://homestore.co.ke',
    imagePath: '/images/portfolio/websites/homestore.jpg', // This will be replaced with actual S3 URL
  },
  {
    title: 'Galaidh Ltd',
    description: 'IT Solutions & Security Systems Provider',
    category: 'corporate',
    url: 'https://galaidh.com',
    imagePath: '/images/portfolio/websites/galaidh.jpg',
  },
  {
    title: 'Top23 Security Ltd',
    description: 'Professional Security Services Website',
    category: 'security',
    url: 'https://top23security.com',
    imagePath: '/images/portfolio/websites/top23-security.jpg',
  },
  {
    title: 'Top23 USA Outlet',
    description: 'E-commerce Store for American Products',
    category: 'e-commerce',
    url: 'https://top23usa.com',
    imagePath: '/images/portfolio/websites/top23-usa.jpg',
  },
  {
    title: 'Reucher Africa Kenya Ltd',
    description: 'Industrial Chemicals & Water Treatment Solutions',
    category: 'corporate',
    url: 'https://reucherafrica.co.ke',
    imagePath: '/images/portfolio/websites/reucher.jpg',
  },
  {
    title: 'I4Food Organization',
    description: 'Institute For Food Systems & Climate Research Website',
    category: 'nonprofit',
    url: 'https://i4food.org',
    imagePath: '/images/portfolio/websites/i4food.jpg',
  },
  {
    title: 'WeShop254',
    description: 'E-commerce Platform for Home & Living Products',
    category: 'e-commerce',
    url: 'https://weshop254.co.ke',
    imagePath: '/images/portfolio/websites/weshop254.jpg',
  },
  {
    title: 'Knight Swift Logistics',
    description: 'Logistics & Transportation Company Website',
    category: 'logistics',
    url: 'https://knightswiftlogistics.com',
    imagePath: '/images/portfolio/websites/knight-swift.jpg',
  },
  {
    title: 'MRL Motors',
    description: 'Automotive Company Website with Modern Design',
    category: 'automotive',
    url: 'https://mrlmotors.com',
    imagePath: '/images/portfolio/websites/mrl-motors.jpg',
  },
];

// Path to the website portfolio data file
const dataFilePath = path.join(process.cwd(), 'src', 'data', 'website-portfolio.json');

// Function to read existing website portfolio data
async function readWebsitePortfolioData(): Promise<WebsitePortfolioItem[]> {
  try {
    const fileData = await fs.promises.readFile(dataFilePath, 'utf8');
    return JSON.parse(fileData);
  } catch (error) {
    if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
      await fs.promises.mkdir(path.dirname(dataFilePath), { recursive: true });
      await fs.promises.writeFile(dataFilePath, JSON.stringify([], null, 2));
      return [];
    }
    console.error('Error reading website portfolio data:', error);
    return [];
  }
}

// Function to write website portfolio data
async function writeWebsitePortfolioData(data: WebsitePortfolioItem[]): Promise<void> {
  try {
    await fs.promises.mkdir(path.dirname(dataFilePath), { recursive: true });
    await fs.promises.writeFile(dataFilePath, JSON.stringify(data, null, 2));
    console.log('Website portfolio data written successfully');
  } catch (error) {
    console.error('Error writing website portfolio data:', error);
    throw new Error('Failed to write website portfolio data');
  }
}

// Function to check if a website with the same URL already exists
function websiteExists(websites: WebsitePortfolioItem[], url: string): boolean {
  return websites.some(website => website.url === url);
}

// Main function to seed the database
async function seedWebsitePortfolio() {
  try {
    console.log('Starting to seed website portfolio data...');
    
    // Read existing data
    const existingWebsites = await readWebsitePortfolioData();
    console.log(`Found ${existingWebsites.length} existing website portfolio items`);
    
    // Create new items for websites that don't already exist
    const newWebsites: WebsitePortfolioItem[] = [];
    
    for (const website of websitePortfolioData) {
      if (!websiteExists(existingWebsites, website.url)) {
        // In a real scenario, we would upload the image to S3 here
        // For now, we'll use the local path as the imageSrc
        const s3ImageUrl = process.env.NEXT_PUBLIC_S3_ENDPOINT 
          ? `${process.env.NEXT_PUBLIC_S3_ENDPOINT}/${process.env.NEXT_PUBLIC_S3_BUCKET}/images/portfolio/websites/${path.basename(website.imagePath)}`
          : website.imagePath;
        
        const newWebsite: WebsitePortfolioItem = {
          id: uuidv4(),
          title: website.title,
          description: website.description,
          category: website.category,
          imageSrc: s3ImageUrl,
          url: website.url,
          featured: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        newWebsites.push(newWebsite);
        console.log(`Added new website: ${website.title}`);
      } else {
        console.log(`Website already exists: ${website.title}`);
      }
    }
    
    // Combine existing and new websites
    const updatedWebsites = [...existingWebsites, ...newWebsites];
    
    // Write the updated data
    await writeWebsitePortfolioData(updatedWebsites);
    
    console.log(`Successfully added ${newWebsites.length} new website portfolio items`);
    console.log(`Total website portfolio items: ${updatedWebsites.length}`);
  } catch (error) {
    console.error('Error seeding website portfolio data:', error);
  }
}

// Run the seed function
seedWebsitePortfolio();
