/**
 * Seed script for the tracking system
 * This script will populate the database with sample tracking data for:
 * - Page views
 * - <PERSON><PERSON> clicks
 * - Form submissions
 * - Form abandonments
 * - Custom events
 *
 * Run with: node scripts/seed-tracking-data.js
 */
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient();

// Sample user agents
const userAgents = [
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
  'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
  'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
  'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
  'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36'
];

// Sample IP addresses (fictional)
const ipAddresses = [
  '************',  // Kenya
  '************',  // Kenya
  '*************', // Kenya
  '*************', // Kenya
  '*************', // Kenya
  '*************', // Kenya
  '***********',   // Kenya
  '*************', // Kenya
  '*******',       // US
  '************',  // Australia
  '**********',    // UK
  '**************' // Europe
];

// Sample URLs
const urls = [
  '/',
  '/about',
  '/services',
  '/portfolio',
  '/blog',
  '/contact',
  '/pricing',
  '/services/web-design',
  '/services/graphic-design',
  '/services/printing',
  '/services/branding',
  '/blog/importance-of-responsive-web-design',
  '/blog/logo-design-trends-2023',
  '/portfolio/website-design'
];

// Sample session IDs
const sessionIds = [];
for (let i = 0; i < 50; i++) {
  sessionIds.push(`session_${Math.random().toString(36).substring(2, 15)}`);
}

// Sample event types
const eventTypes = [
  'pageView',
  'buttonClick',
  'formSubmission',
  'formAbandonment',
  'download',
  'videoPlay',
  'scroll'
];

// Generate a random date within the last 30 days
function getRandomDate(daysBack = 30) {
  const date = new Date();
  date.setDate(date.getDate() - Math.floor(Math.random() * daysBack));
  date.setHours(Math.floor(Math.random() * 24));
  date.setMinutes(Math.floor(Math.random() * 60));
  date.setSeconds(Math.floor(Math.random() * 60));
  return date;
}

// Get a random item from an array
function getRandomItem(array) {
  return array[Math.floor(Math.random() * array.length)];
}

// Generate a random tracking event
function generateEvent(sessionId, index) {
  const eventType = getRandomItem(eventTypes);
  const url = getRandomItem(urls);
  const baseEvent = {
    eventType,
    url,
    userAgent: getRandomItem(userAgents),
    ipAddress: getRandomItem(ipAddresses),
    sessionId,
    createdAt: getRandomDate(),
    metadata: {}
  };

  // Customize event based on type
  switch (eventType) {
    case 'pageView':
      return {
        ...baseEvent,
        eventName: 'pageView',
      };
    case 'buttonClick':
      const buttons = ['Contact Us', 'Get Quote', 'Learn More', 'Download', 'Submit', 'Sign Up', 'Subscribe'];
      return {
        ...baseEvent,
        eventName: getRandomItem(buttons),
        metadata: { buttonName: getRandomItem(buttons) }
      };
    case 'formSubmission':
      const forms = ['Contact Form', 'Quote Request', 'Newsletter Signup', 'Feedback Form'];
      const formName = getRandomItem(forms);
      return {
        ...baseEvent,
        eventName: formName,
        metadata: { formName, formData: { email: `test${index}@example.com` } }
      };
    case 'formAbandonment':
      const abandonedForms = ['Contact Form', 'Quote Request', 'Newsletter Signup', 'Feedback Form'];
      const abandonedFormName = getRandomItem(abandonedForms);
      return {
        ...baseEvent,
        eventName: abandonedFormName,
        metadata: { 
          formName: abandonedFormName, 
          completionPercentage: Math.floor(Math.random() * 90) + 10 
        }
      };
    case 'download':
      const downloads = ['Brochure', 'Price List', 'Case Study', 'White Paper'];
      return {
        ...baseEvent,
        eventName: `${getRandomItem(downloads)} Download`,
        metadata: { fileName: `${getRandomItem(downloads).toLowerCase().replace(' ', '_')}.pdf` }
      };
    case 'videoPlay':
      const videos = ['Company Overview', 'Product Demo', 'Client Testimonial', 'Tutorial'];
      return {
        ...baseEvent,
        eventName: `${getRandomItem(videos)} Video`,
        metadata: { 
          videoName: getRandomItem(videos),
          duration: Math.floor(Math.random() * 300) + 30,
          percentWatched: Math.floor(Math.random() * 100)
        }
      };
    case 'scroll':
      return {
        ...baseEvent,
        eventName: 'scroll',
        metadata: { 
          scrollDepth: Math.floor(Math.random() * 100),
          pageHeight: Math.floor(Math.random() * 5000) + 1000
        }
      };
    default:
      return baseEvent;
  }
}

// Main function to seed the database
async function main() {
  console.log('Starting to seed tracking data...');

  // Check if we already have tracking data
  const existingEventsCount = await prisma.eventTracking.count();
  
  if (existingEventsCount > 0) {
    console.log(`Database already has ${existingEventsCount} tracking events.`);
    const shouldContinue = process.argv.includes('--force');
    
    if (!shouldContinue) {
      console.log('Use --force flag to add more tracking data anyway.');
      await prisma.$disconnect();
      return;
    }
    
    console.log('Continuing with seeding due to --force flag...');
  }

  // Create events for each session
  let totalEvents = 0;
  const batchSize = 100;
  const totalEventsToCreate = 1000;
  
  for (let i = 0; i < totalEventsToCreate; i += batchSize) {
    const eventsToCreate = [];
    
    for (let j = 0; j < batchSize && i + j < totalEventsToCreate; j++) {
      const sessionId = getRandomItem(sessionIds);
      eventsToCreate.push(generateEvent(sessionId, i + j));
    }
    
    // Create events in batch
    await prisma.eventTracking.createMany({
      data: eventsToCreate
    });
    
    totalEvents += eventsToCreate.length;
    console.log(`Created ${totalEvents}/${totalEventsToCreate} events...`);
  }

  console.log(`Successfully created ${totalEvents} tracking events!`);
}

// Run the main function
main()
  .catch(e => {
    console.error('Error seeding tracking data:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
