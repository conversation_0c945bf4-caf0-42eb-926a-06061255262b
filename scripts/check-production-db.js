/**
 * Production Database Check Script
 * This script checks if the required tables exist on production
 * and provides instructions for fixing any issues
 */
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

async function checkDatabase() {
  try {
    console.log('🔍 Checking production database...');
    console.log('Database URL:', process.env.DATABASE_URL ? 'Set' : 'Not set');

    // Test basic connection
    try {
      await prisma.$connect();
      console.log('✅ Database connection successful');
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      return;
    }

    // Check if event_tracking table exists
    try {
      const tableExists = await prisma.$queryRaw`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'event_tracking'
        );
      `;
      
      const exists = tableExists[0].exists;
      console.log('event_tracking table exists:', exists);
      
      if (!exists) {
        console.log('❌ event_tracking table does not exist!');
        console.log('');
        console.log('🔧 To fix this, run the following commands on your production server:');
        console.log('1. npx prisma generate');
        console.log('2. npx prisma migrate deploy');
        console.log('');
        return;
      } else {
        console.log('✅ event_tracking table exists');
      }
    } catch (error) {
      console.error('❌ Error checking table existence:', error.message);
      return;
    }

    // Check if we can query the table
    try {
      const eventCount = await prisma.eventTracking.count();
      console.log(`✅ Found ${eventCount} events in the database`);
    } catch (error) {
      console.error('❌ Error querying event_tracking table:', error.message);
      return;
    }

    // Test the specific queries used by the tracking API
    try {
      console.log('🧪 Testing tracking API queries...');
      
      // Test event counts query
      const eventCounts = await prisma.$queryRaw`
        SELECT "eventType", COUNT(*) as count
        FROM "event_tracking"
        WHERE "created_at" >= NOW() - INTERVAL '30 days'
        GROUP BY "eventType"
        ORDER BY count DESC
      `;
      console.log('✅ Event counts query successful');
      
      // Test page views query
      const pageViews = await prisma.$queryRaw`
        SELECT "url", COUNT(*) as "views"
        FROM "event_tracking"
        WHERE "eventType" = 'pageView'
          AND "url" IS NOT NULL
        GROUP BY "url"
        ORDER BY "views" DESC
        LIMIT 5
      `;
      console.log('✅ Page views query successful');
      
      // Test form stats query
      const formNames = await prisma.$queryRaw`
        SELECT DISTINCT "eventName" as "formName"
        FROM "event_tracking"
        WHERE ("eventType" = 'formSubmission' OR "eventType" = 'formAbandonment')
      `;
      console.log('✅ Form stats query successful');
      
    } catch (error) {
      console.error('❌ Error testing tracking queries:', error.message);
      return;
    }

    console.log('');
    console.log('🎉 All database checks passed! Your tracking system should work.');
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase();
