// Script to check blog posts in the database
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkBlogPosts() {
  try {
    console.log('Fetching blog posts...');
    
    // Get all blog posts
    const blogPosts = await prisma.blogPost.findMany({
      orderBy: {
        createdAt: 'desc'
      },
      take: 5
    });
    
    console.log(`Found ${blogPosts.length} blog posts`);
    
    // Display blog post details
    blogPosts.forEach((post, index) => {
      console.log(`\n--- Blog Post ${index + 1} ---`);
      console.log(`ID: ${post.id}`);
      console.log(`Title: ${post.title}`);
      console.log(`Status: ${post.status}`);
      console.log(`Created: ${post.createdAt}`);
      console.log(`Published: ${post.publishedAt || 'Not published'}`);
      console.log(`Category: ${post.category}`);
      console.log(`Author: ${post.author}`);
      console.log(`Excerpt: ${post.excerpt.substring(0, 100)}...`);
      
      // Count words in content
      const wordCount = post.content.split(/\s+/).length;
      console.log(`Word Count: ${wordCount}`);
      
      // Check for website links
      const websiteUrl = process.env.WEBSITE_URL || 'https://mocky.co.ke';
      const websiteDomain = websiteUrl.replace(/^https?:\/\//, '').replace(/\/.*$/, '');
      const linkRegex = new RegExp(`href=["']https?://(www\\.)?${websiteDomain}`, 'gi');
      const linkMatches = post.content.match(linkRegex) || [];
      console.log(`Website Links: ${linkMatches.length}`);
    });
  } catch (error) {
    console.error('Error checking blog posts:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
checkBlogPosts();
