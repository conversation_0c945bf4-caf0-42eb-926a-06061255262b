// Example script using OpenAI API with the provided code
require('dotenv').config();
const { OpenAI } = require('openai');

// Create an OpenAI client instance with the API key from .env
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

async function generateHaiku() {
  try {
    console.log('Generating a haiku about AI using OpenAI...');
    
    // Create a chat completion using the provided code
    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini", // Using the model specified in your code
      store: true, // Store the conversation as specified in your code
      messages: [
        {"role": "user", "content": "write a haiku about ai"},
      ],
    });
    
    // Log the response
    console.log('\nGenerated Haiku:');
    console.log('----------------');
    console.log(completion.choices[0].message.content);
    console.log('----------------');
    
    // Log additional information about the response
    console.log('\nResponse Details:');
    console.log(`Model: ${completion.model}`);
    console.log(`Completion ID: ${completion.id}`);
    console.log(`Created: ${new Date(completion.created * 1000).toLocaleString()}`);
    console.log(`Prompt Tokens: ${completion.usage?.prompt_tokens}`);
    console.log(`Completion Tokens: ${completion.usage?.completion_tokens}`);
    console.log(`Total Tokens: ${completion.usage?.total_tokens}`);
    
  } catch (error) {
    console.error('Error generating haiku:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Error: ${JSON.stringify(error.response.data, null, 2)}`);
    } else {
      console.error(error);
    }
  }
}

// Run the function
generateHaiku();
