// Script to test team member creation
const { PrismaClient } = require('@prisma/client');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');
const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');

const prisma = new PrismaClient();

// Test the entire team member creation process
async function testTeamMemberCreation() {
  console.log('Starting team member creation test...');

  try {
    // Step 1: Test database connection
    console.log('\n--- Step 1: Testing database connection ---');
    await testDatabaseConnection();

    // Step 2: Test S3 connection
    console.log('\n--- Step 2: Testing S3 connection ---');
    const s3Config = await testS3Connection();

    // Step 3: Test image upload
    console.log('\n--- Step 3: Testing image upload ---');
    const imageUrl = await testImageUpload(s3Config);

    // Step 4: Test team member creation in database
    console.log('\n--- Step 4: Testing team member creation in database ---');
    await testTeamMemberDatabaseCreation(imageUrl);

    console.log('\n✅ All tests completed successfully!');
    console.log('Team member creation should work now. Try again in the admin interface.');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Error details:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Test database connection
async function testDatabaseConnection() {
  try {
    // Try to query the database
    const count = await prisma.teamMember.count();
    console.log(`✅ Database connection successful. Found ${count} existing team members.`);
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    throw new Error(`Database connection failed: ${error.message}`);
  }
}

// Test S3 connection and get configuration
async function testS3Connection() {
  try {
    // Get the default storage configuration
    const config = await prisma.storageConfig.findFirst({
      where: { isDefault: true },
    });

    if (!config) {
      throw new Error('No default storage configuration found');
    }

    console.log(`✅ Found storage configuration: ${config.provider} in region ${config.region}`);

    // Create S3 client
    const s3Client = new S3Client({
      region: config.region,
      endpoint: config.endpoint,
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
      },
      forcePathStyle: true
    });

    console.log('✅ S3 client created successfully');

    return {
      client: s3Client,
      config
    };
  } catch (error) {
    console.error('❌ S3 connection test failed:', error.message);
    throw new Error(`S3 connection failed: ${error.message}`);
  }
}

// Test image upload to S3
async function testImageUpload(s3Config) {
  try {
    const { client: s3Client, config } = s3Config;

    // Create a test image path
    let testImagePath = path.join(__dirname, '..', 'public', 'images', 'placeholder.jpg');

    // Check if the test image exists
    if (!fs.existsSync(testImagePath)) {
      console.log('Test image not found, using a different path...');
      // Try to find any image in the public directory
      const publicDir = path.join(__dirname, '..', 'public');

      // Function to find image files recursively
      const findImageFiles = (dir) => {
        const results = [];
        const list = fs.readdirSync(dir);

        list.forEach(file => {
          const filePath = path.join(dir, file);
          const stat = fs.statSync(filePath);

          if (stat.isDirectory()) {
            // Recursively search directories
            results.push(...findImageFiles(filePath));
          } else if (/\.(jpg|jpeg|png|gif)$/i.test(file)) {
            // Found an image file
            results.push(filePath);
          }
        });

        return results;
      };

      const imageFiles = findImageFiles(publicDir);

      if (imageFiles.length === 0) {
        // If no images found, create a simple test image
        console.log('No images found in public directory, creating a test image...');
        const testDir = path.join(__dirname, '..', 'public', 'test');

        // Create test directory if it doesn't exist
        if (!fs.existsSync(testDir)) {
          fs.mkdirSync(testDir, { recursive: true });
        }

        // Create a simple 1x1 pixel JPEG
        const onePixelJpeg = Buffer.from([
          0xff, 0xd8, 0xff, 0xe0, 0x00, 0x10, 0x4a, 0x46, 0x49, 0x46, 0x00, 0x01, 0x01, 0x01, 0x00, 0x48,
          0x00, 0x48, 0x00, 0x00, 0xff, 0xdb, 0x00, 0x43, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
          0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc2, 0x00, 0x0b, 0x08, 0x00, 0x01, 0x00,
          0x01, 0x01, 0x01, 0x11, 0x00, 0xff, 0xc4, 0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
          0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xda, 0x00, 0x08, 0x01,
          0x01, 0x00, 0x01, 0x3f, 0x10
        ]);

        testImagePath = path.join(testDir, 'test-pixel.jpg');
        fs.writeFileSync(testImagePath, onePixelJpeg);
        console.log(`Created test image at: ${testImagePath}`);
      } else {
        // Use the first image found
        testImagePath = imageFiles[0];
        console.log(`Using alternative image: ${testImagePath}`);
      }
    }

    // Read the test image
    const fileBuffer = fs.readFileSync(testImagePath);
    console.log(`✅ Test image read successfully: ${testImagePath} (${fileBuffer.length} bytes)`);

    // Generate a unique key for the test image
    const id = uuidv4();
    const key = `team/${id}/test-image.jpg`;

    // Upload to S3
    const command = new PutObjectCommand({
      Bucket: config.bucketName,
      Key: key,
      Body: fileBuffer,
      ContentType: 'image/jpeg',
      ACL: 'public-read',
    });

    await s3Client.send(command);
    console.log(`✅ Test image uploaded successfully to S3 path: ${key}`);

    // Generate the URL
    const url = `${config.endpoint}/${config.bucketName}/${key}`;
    console.log(`✅ Image URL generated: ${url}`);

    return url;
  } catch (error) {
    console.error('❌ Image upload test failed:', error.message);
    throw new Error(`Image upload failed: ${error.message}`);
  }
}

// Test team member creation in database
async function testTeamMemberDatabaseCreation(imageUrl) {
  try {
    // Create a test team member
    const testMember = await prisma.teamMember.create({
      data: {
        id: uuidv4(),
        name: 'Test Member',
        role: 'Test Role',
        bio: 'This is a test team member created by the diagnostic script.',
        imageSrc: imageUrl,
        order: 999, // High number to keep it at the end
        linkedinUrl: 'https://linkedin.com/test',
        twitterUrl: 'https://twitter.com/test',
        githubUrl: 'https://github.com/test',
        emailAddress: '<EMAIL>',
      },
    });

    console.log(`✅ Test team member created successfully with ID: ${testMember.id}`);

    // Clean up the test data
    await prisma.teamMember.delete({
      where: { id: testMember.id },
    });

    console.log('✅ Test team member deleted successfully');

    return true;
  } catch (error) {
    console.error('❌ Team member database creation test failed:', error.message);
    throw new Error(`Team member creation failed: ${error.message}`);
  }
}

// Run the test
testTeamMemberCreation();
