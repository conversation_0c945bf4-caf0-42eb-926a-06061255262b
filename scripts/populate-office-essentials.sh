#!/bin/bash

# Script to populate office essentials pricing data
# Usage: ./scripts/populate-office-essentials.sh

# Exit on error
set -e

echo "🔄 Starting office essentials pricing data population..."

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Run the seed script
echo "🌱 Seeding office essentials pricing data..."
node scripts/seed-office-essentials.js

echo "✅ Office essentials pricing data population completed successfully!"
