const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Define categories for the website
const categories = [
  {
    name: 'Web Development',
    slug: 'web-development',
    description: 'Custom website development services including responsive design, e-commerce solutions, and content management systems.'
  },
  {
    name: 'Mobile Development',
    slug: 'mobile-development',
    description: 'Native and cross-platform mobile app development for Android and iOS platforms.'
  },
  {
    name: 'UI/UX Design',
    slug: 'ui-ux-design',
    description: 'User interface and experience design services focused on creating intuitive, engaging digital experiences.'
  },
  {
    name: 'Graphic Design',
    slug: 'graphic-design',
    description: 'Professional graphic design services including logos, branding materials, and marketing collateral.'
  },
  {
    name: 'Digital Marketing',
    slug: 'digital-marketing',
    description: 'Comprehensive digital marketing strategies including SEO, social media, and content marketing.'
  },
  {
    name: 'E-commerce',
    slug: 'e-commerce',
    description: 'E-commerce solutions for businesses looking to sell products and services online.'
  },
  {
    name: 'Branding',
    slug: 'branding',
    description: 'Brand development and identity services to help businesses establish a strong market presence.'
  },
  {
    name: 'SEO',
    slug: 'seo',
    description: 'Search engine optimization services to improve website visibility and organic traffic.'
  },
  {
    name: 'Content Creation',
    slug: 'content-creation',
    description: 'Professional content creation services including blog posts, articles, and marketing copy.'
  },
  {
    name: 'Uncategorized',
    slug: 'uncategorized',
    description: 'Default category for content that does not fit into other categories.'
  }
];

// Define tags for the website
// Since there's no dedicated Tag model, we'll use these to tag blog posts
const tags = [
  // Web Development Tags
  { name: 'WordPress', slug: 'wordpress' },
  { name: 'React', slug: 'react' },
  { name: 'Next.js', slug: 'nextjs' },
  { name: 'JavaScript', slug: 'javascript' },
  { name: 'TypeScript', slug: 'typescript' },
  { name: 'PHP', slug: 'php' },
  { name: 'HTML5', slug: 'html5' },
  { name: 'CSS3', slug: 'css3' },
  { name: 'Responsive Design', slug: 'responsive-design' },
  
  // Mobile Development Tags
  { name: 'React Native', slug: 'react-native' },
  { name: 'Flutter', slug: 'flutter' },
  { name: 'iOS', slug: 'ios' },
  { name: 'Android', slug: 'android' },
  { name: 'Mobile UI', slug: 'mobile-ui' },
  
  // UI/UX Design Tags
  { name: 'User Experience', slug: 'user-experience' },
  { name: 'User Interface', slug: 'user-interface' },
  { name: 'Wireframing', slug: 'wireframing' },
  { name: 'Prototyping', slug: 'prototyping' },
  { name: 'Figma', slug: 'figma' },
  { name: 'Adobe XD', slug: 'adobe-xd' },
  
  // Graphic Design Tags
  { name: 'Logo Design', slug: 'logo-design' },
  { name: 'Brand Identity', slug: 'brand-identity' },
  { name: 'Print Design', slug: 'print-design' },
  { name: 'Illustration', slug: 'illustration' },
  { name: 'Typography', slug: 'typography' },
  
  // Digital Marketing Tags
  { name: 'Social Media', slug: 'social-media' },
  { name: 'Content Marketing', slug: 'content-marketing' },
  { name: 'Email Marketing', slug: 'email-marketing' },
  { name: 'PPC', slug: 'ppc' },
  { name: 'Analytics', slug: 'analytics' },
  
  // E-commerce Tags
  { name: 'Shopify', slug: 'shopify' },
  { name: 'WooCommerce', slug: 'woocommerce' },
  { name: 'Payment Gateways', slug: 'payment-gateways' },
  { name: 'E-commerce Strategy', slug: 'e-commerce-strategy' },
  
  // Branding Tags
  { name: 'Brand Strategy', slug: 'brand-strategy' },
  { name: 'Visual Identity', slug: 'visual-identity' },
  { name: 'Rebranding', slug: 'rebranding' },
  { name: 'Brand Guidelines', slug: 'brand-guidelines' },
  
  // SEO Tags
  { name: 'On-page SEO', slug: 'on-page-seo' },
  { name: 'Off-page SEO', slug: 'off-page-seo' },
  { name: 'Local SEO', slug: 'local-seo' },
  { name: 'Keyword Research', slug: 'keyword-research' },
  { name: 'SEO Audit', slug: 'seo-audit' },
  
  // Content Creation Tags
  { name: 'Blog Writing', slug: 'blog-writing' },
  { name: 'Copywriting', slug: 'copywriting' },
  { name: 'Content Strategy', slug: 'content-strategy' },
  { name: 'Technical Writing', slug: 'technical-writing' }
];

// Function to seed categories
async function seedCategories() {
  console.log('Seeding categories...');
  
  for (const category of categories) {
    // Check if category already exists
    const existingCategory = await prisma.category.findUnique({
      where: { slug: category.slug }
    });
    
    if (existingCategory) {
      console.log(`Category "${category.name}" already exists, skipping...`);
      continue;
    }
    
    // Create category
    await prisma.category.create({
      data: {
        name: category.name,
        slug: category.slug,
        description: category.description
      }
    });
    
    console.log(`Created category: ${category.name}`);
  }
  
  console.log('Categories seeding completed!');
}

// Function to add tags to existing blog posts
// Since there's no dedicated Tag model, we'll update blog posts with tags
async function addTagsToBlogPosts() {
  console.log('Adding tags to blog posts...');
  
  // Get all blog posts
  const blogPosts = await prisma.blogPost.findMany();
  
  if (blogPosts.length === 0) {
    console.log('No blog posts found to tag.');
    return;
  }
  
  // For each blog post, assign relevant tags based on its category
  for (const post of blogPosts) {
    // Skip posts that already have tags
    if (post.tags && post.tags.length > 0) {
      console.log(`Post "${post.title}" already has tags, skipping...`);
      continue;
    }
    
    // Determine which tags to add based on the post's category
    let tagsToAdd = [];
    
    // Add category as a tag
    if (post.category) {
      tagsToAdd.push(post.category);
    }
    
    // Add 2-4 relevant tags based on the category
    const categoryTags = getTagsForCategory(post.category);
    if (categoryTags.length > 0) {
      // Randomly select 2-4 tags
      const numTags = Math.floor(Math.random() * 3) + 2; // 2-4 tags
      const selectedTags = categoryTags
        .sort(() => 0.5 - Math.random()) // Shuffle array
        .slice(0, numTags)
        .map(tag => tag.slug);
      
      tagsToAdd = [...tagsToAdd, ...selectedTags];
    }
    
    // Update the blog post with the new tags
    await prisma.blogPost.update({
      where: { id: post.id },
      data: { tags: tagsToAdd }
    });
    
    console.log(`Added tags to post "${post.title}": ${tagsToAdd.join(', ')}`);
  }
  
  console.log('Finished adding tags to blog posts!');
}

// Helper function to get relevant tags for a category
function getTagsForCategory(category) {
  if (!category) return [];
  
  // Map category slug to relevant tag slugs
  const categoryTagMap = {
    'web-development': tags.filter(tag => 
      ['wordpress', 'react', 'nextjs', 'javascript', 'typescript', 'php', 'html5', 'css3', 'responsive-design'].includes(tag.slug)
    ),
    'mobile-development': tags.filter(tag => 
      ['react-native', 'flutter', 'ios', 'android', 'mobile-ui'].includes(tag.slug)
    ),
    'ui-ux-design': tags.filter(tag => 
      ['user-experience', 'user-interface', 'wireframing', 'prototyping', 'figma', 'adobe-xd'].includes(tag.slug)
    ),
    'graphic-design': tags.filter(tag => 
      ['logo-design', 'brand-identity', 'print-design', 'illustration', 'typography'].includes(tag.slug)
    ),
    'digital-marketing': tags.filter(tag => 
      ['social-media', 'content-marketing', 'email-marketing', 'ppc', 'analytics'].includes(tag.slug)
    ),
    'e-commerce': tags.filter(tag => 
      ['shopify', 'woocommerce', 'payment-gateways', 'e-commerce-strategy'].includes(tag.slug)
    ),
    'branding': tags.filter(tag => 
      ['brand-strategy', 'visual-identity', 'rebranding', 'brand-guidelines'].includes(tag.slug)
    ),
    'seo': tags.filter(tag => 
      ['on-page-seo', 'off-page-seo', 'local-seo', 'keyword-research', 'seo-audit'].includes(tag.slug)
    ),
    'content-creation': tags.filter(tag => 
      ['blog-writing', 'copywriting', 'content-strategy', 'technical-writing'].includes(tag.slug)
    )
  };
  
  return categoryTagMap[category] || [];
}

// Main function to run the seeding
async function main() {
  try {
    console.log('Starting database seeding for categories and tags...');
    
    // Seed categories
    await seedCategories();
    
    // Add tags to blog posts
    await addTagsToBlogPosts();
    
    console.log('Database seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the main function
main();
