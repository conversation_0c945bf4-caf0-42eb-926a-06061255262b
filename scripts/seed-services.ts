/**
 * Seed script to add initial services to the database
 * Run with: npx ts-node scripts/seed-services.ts
 */
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('Starting to seed services...');

  // Define services
  const services = [
    // Logo Design Services
    {
      name: 'Basic Logo Package',
      description: 'Professional logo design with 5 initial concepts and 2 revision rounds',
      price: 5000,
      category: 'Design'
    },
    {
      name: 'Professional Logo Package',
      description: 'Premium logo design with 7 initial concepts and 3 revision rounds',
      price: 10000,
      category: 'Design'
    },
    {
      name: 'Premium Logo Package',
      description: 'Comprehensive logo design with 9 initial concepts and unlimited revisions',
      price: 15000,
      category: 'Design'
    },

    // Graphic Design Services
    {
      name: 'Business Card Design',
      description: 'Professional business card design with print-ready files',
      price: 2500,
      category: 'Design'
    },
    {
      name: 'Flyer Design',
      description: 'Eye-catching flyer design for your marketing needs',
      price: 3000,
      category: 'Design'
    },
    {
      name: 'Brochure Design',
      description: 'Professional brochure design with up to 4 pages',
      price: 5000,
      category: 'Design'
    },
    {
      name: 'Social Media Graphics',
      description: 'Set of 5 social media graphics for your brand',
      price: 3500,
      category: 'Design'
    },

    // Web Development Services
    {
      name: 'Basic Website',
      description: 'Simple 5-page website with responsive design',
      price: 15000,
      category: 'Development'
    },
    {
      name: 'E-commerce Website',
      description: 'Full e-commerce website with product management and payment integration',
      price: 50000,
      category: 'Development'
    },
    {
      name: 'Website Maintenance',
      description: 'Monthly website maintenance and updates',
      price: 5000,
      category: 'Development'
    },

    // Digital Marketing Services
    {
      name: 'Social Media Management',
      description: 'Monthly social media management for one platform',
      price: 10000,
      category: 'Marketing'
    },
    {
      name: 'SEO Package',
      description: 'Basic SEO optimization for your website',
      price: 15000,
      category: 'Marketing'
    },
    {
      name: 'Content Creation',
      description: '5 blog posts or articles for your website',
      price: 7500,
      category: 'Marketing'
    }
  ];

  // Create services
  console.log(`Attempting to create ${services.length} services...`);

  for (const service of services) {
    try {
      // Check if service already exists
      const existingService = await prisma.service.findFirst({
        where: {
          name: service.name
        }
      });

      if (!existingService) {
        const created = await prisma.service.create({
          data: service
        });
        console.log(`Created service: ${service.name} with ID: ${created.id}`);
      } else {
        console.log(`Service already exists: ${service.name}`);
      }
    } catch (error) {
      console.error(`Error creating service ${service.name}:`, error);
    }
  }

  console.log('Seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('Error seeding services:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
