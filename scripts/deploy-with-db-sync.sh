#!/bin/bash

# Deployment script with database synchronization for <PERSON><PERSON>
# This script deploys the application and ensures database and code are in sync
# Usage: ./scripts/deploy-with-db-sync.sh

# Exit on error
set -e

echo "🚀 Starting deployment with database synchronization..."

# Get current timestamp for backup naming
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="./backups"
mkdir -p $BACKUP_DIR

# 1. Backup the database
echo "📦 Creating database backup..."
# Load environment variables from .env file
if [ -f .env ]; then
  export $(grep -v '^#' .env | grep DATABASE_URL | tail -n 1 | xargs)
fi

if [ -z "$DATABASE_URL" ]; then
  echo "⚠️  DATABASE_URL environment variable not set. Using default connection string."
  export DATABASE_URL="postgresql://don:password@localhost:5432/mocky?schema=public"
fi

# Extract connection details from the DATABASE_URL
DB_USER=$(echo $DATABASE_URL | sed -n 's/^postgresql:\/\/\([^:]*\):.*/\1/p')
DB_PASSWORD=$(echo $DATABASE_URL | sed -n 's/^postgresql:\/\/[^:]*:\([^@]*\)@.*/\1/p')
DB_HOST=$(echo $DATABASE_URL | sed -n 's/^postgresql:\/\/[^@]*@\([^:]*\):.*/\1/p')
DB_PORT=$(echo $DATABASE_URL | sed -n 's/^postgresql:\/\/[^:]*:[^@]*@[^:]*:\([0-9]*\)\/.*/\1/p')
DB_NAME=$(echo $DATABASE_URL | sed -n 's/^postgresql:\/\/[^:]*:[^@]*@[^:]*:[0-9]*\/\([^?]*\).*/\1/p')

echo "Using database connection:"
echo "  Host: $DB_HOST"
echo "  Port: $DB_PORT"
echo "  Database: $DB_NAME"
echo "  User: $DB_USER"

# Create backup using pg_dump
PGPASSWORD=$DB_PASSWORD pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -F c -f "$BACKUP_DIR/backup_$TIMESTAMP.dump"
echo "✅ Database backup created at $BACKUP_DIR/backup_$TIMESTAMP.dump"

# 2. Pull the latest changes from GitHub
echo "📥 Pulling latest changes from GitHub..."
git pull

# 3. Install dependencies
echo "📦 Installing dependencies..."
npm ci

# 4. Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# 5. Apply database migrations
echo "🔄 Applying database migrations..."
npx prisma migrate deploy

# 6. Build the application
echo "🔨 Building the application..."
npm run build

# 7. Restart the application using PM2
echo "🔄 Restarting the application..."
pm2 restart mocky-digital || pm2 start ecosystem.config.js

# 8. Reload nginx (if configuration has changed)
echo "🔄 Reloading Nginx configuration..."
if command -v nginx > /dev/null; then
  sudo systemctl reload nginx
else
  echo "⚠️  Nginx not found. Skipping Nginx reload."
fi

# 9. Verify deployment
echo "✅ Verifying deployment..."
if curl -s http://localhost:3000 > /dev/null; then
  echo "✅ Application is responding on port 3000"
else
  echo "⚠️  Application is not responding on port 3000. Check logs with: pm2 logs mocky-digital"
fi

# 10. Print summary
echo ""
echo "✅ Deployment with database synchronization completed!"
echo "📋 Summary:"
echo "  - Database backup: $BACKUP_DIR/backup_$TIMESTAMP.dump"
echo "  - Application restarted with PM2"
echo "  - Nginx reloaded"
echo ""
echo "🔍 To check application logs: pm2 logs mocky-digital"
echo "🔄 To rollback database (if needed): pg_restore -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME $BACKUP_DIR/backup_$TIMESTAMP.dump"
