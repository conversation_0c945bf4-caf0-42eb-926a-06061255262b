const { Pool } = require('pg');
require('dotenv').config();

// Create a connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function main() {
  const client = await pool.connect();
  
  try {
    console.log('Querying database...');
    
    // Check categories
    console.log('\nCategories:');
    const categoriesResult = await client.query('SELECT * FROM categories');
    console.log(`Found ${categoriesResult.rows.length} categories`);
    
    // Check blog posts
    console.log('\nBlog Posts:');
    const blogPostsResult = await client.query('SELECT id, title, category, tags FROM blog_posts');
    console.log(`Found ${blogPostsResult.rows.length} blog posts`);
    
    if (blogPostsResult.rows.length > 0) {
      console.log('\nBlog posts with tags:');
      blogPostsResult.rows.forEach(post => {
        console.log(`- ${post.title} (Category: ${post.category || 'None'})`);
        console.log(`  Tags: ${post.tags && post.tags.length > 0 ? post.tags.join(', ') : 'No tags'}`);
      });
    }
    
  } catch (error) {
    console.error('Error querying database:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

main();
