// Script to seed gift sets pricing data
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

// Initialize Prisma client
const prisma = new PrismaClient();

// Define gift sets products with pricing
const giftSets = [
  {
    service: 'Executive Wooden Gift Set',
    price: 6300,
    description: 'Premium wooden gift set for executives and corporate gifting',
    features: [
      'Luxury wooden presentation box',
      'Multiple premium items included',
      'Custom branding options',
      'Perfect for executive gifts',
      'High-quality craftsmanship',
      'Elegant professional appearance'
    ],
    icon: 'gift',
    popular: true
  },
  {
    service: 'Executive Gift Set (Premium)',
    price: 4900,
    description: 'Premium executive gift set with multiple high-quality items',
    features: [
      'Luxury presentation box',
      'Multiple premium items included',
      'Custom branding options',
      'Perfect for corporate gifting',
      'High-quality materials',
      'Elegant professional appearance'
    ],
    icon: 'gift',
    popular: true
  },
  {
    service: 'Executive Gift Set (Standard)',
    price: 3500,
    description: 'Standard executive gift set for professional corporate gifting',
    features: [
      'Professional presentation box',
      'Multiple quality items included',
      'Custom branding options',
      'Ideal for corporate gifting',
      'Quality materials',
      'Professional appearance'
    ],
    icon: 'gift',
    popular: true
  },
  {
    service: 'Executive Gift Set (Basic)',
    price: 2100,
    description: 'Basic executive gift set for affordable corporate gifting',
    features: [
      'Professional presentation box',
      'Multiple items included',
      'Custom branding options',
      'Budget-friendly option',
      'Quality materials',
      'Professional appearance'
    ],
    icon: 'gift',
    popular: false
  },
  {
    service: 'Deluxe Chess & Flask Set',
    price: 3500,
    description: 'Luxury chess set with flask for executive gifting',
    features: [
      'Premium chess board and pieces',
      'Matching flask included',
      'Elegant presentation box',
      'Custom branding options',
      'High-quality materials',
      'Perfect executive gift'
    ],
    icon: 'chess-knight',
    popular: false
  },
  {
    service: 'Executive Double Gift Set',
    price: 4200,
    description: 'Premium gift set with dual items for executive gifting',
    features: [
      'Two premium items included',
      'Luxury presentation box',
      'Custom branding options',
      'Perfect for executive gifts',
      'High-quality materials',
      'Elegant professional appearance'
    ],
    icon: 'gift',
    popular: false
  },
  {
    service: 'Executive Pen & Notebook Set',
    price: 2800,
    description: 'Professional pen and notebook set for corporate gifting',
    features: [
      'Premium pen included',
      'Matching notebook',
      'Elegant presentation box',
      'Custom branding options',
      'High-quality materials',
      'Professional appearance'
    ],
    icon: 'pen-fancy',
    popular: true
  },
  {
    service: 'Executive Desk Accessory Set',
    price: 5320,
    description: 'Complete desk accessory set for executive gifting',
    features: [
      'Multiple desk accessories included',
      'Luxury presentation box',
      'Custom branding options',
      'Perfect for executive gifts',
      'High-quality materials',
      'Elegant professional appearance'
    ],
    icon: 'briefcase',
    popular: false
  },
  {
    service: 'Executive Leather Gift Set',
    price: 7700,
    description: 'Premium leather gift set for luxury corporate gifting',
    features: [
      'Genuine leather items',
      'Luxury presentation box',
      'Custom branding options',
      'Perfect for VIP gifts',
      'Premium craftsmanship',
      'Elegant luxury appearance'
    ],
    icon: 'gift',
    popular: true
  },
  {
    service: 'Executive Travel Gift Set',
    price: 3920,
    description: 'Premium travel accessories gift set for corporate gifting',
    features: [
      'Multiple travel accessories included',
      'Elegant presentation box',
      'Custom branding options',
      'Perfect for business travelers',
      'High-quality materials',
      'Professional appearance'
    ],
    icon: 'plane',
    popular: false
  },
  {
    service: 'Executive Drinkware Gift Set',
    price: 4620,
    description: 'Premium drinkware gift set for corporate gifting',
    features: [
      'Multiple premium drinkware items',
      'Elegant presentation box',
      'Custom branding options',
      'Perfect for executive gifts',
      'High-quality materials',
      'Professional appearance'
    ],
    icon: 'glass-whiskey',
    popular: false
  },
  {
    service: 'Executive Wooden Desk Set',
    price: 8120,
    description: 'Premium wooden desk accessory set for luxury corporate gifting',
    features: [
      'Multiple wooden desk accessories',
      'Luxury wooden presentation box',
      'Custom branding options',
      'Perfect for executive gifts',
      'Premium craftsmanship',
      'Elegant luxury appearance'
    ],
    icon: 'briefcase',
    popular: true
  }
];

// Main function to seed the database
async function seedGiftSets() {
  try {
    console.log('Starting to seed gift sets pricing data...');

    // Get existing pricing items
    const existingPricing = await prisma.pricing.findMany();
    console.log(`Found ${existingPricing.length} existing pricing items`);

    // Create a map of existing services (case-insensitive)
    const existingServicesMap = new Map();
    existingPricing.forEach(item => {
      existingServicesMap.set(item.service.toLowerCase(), item);
    });

    let created = 0;
    let updated = 0;

    // Process each service
    for (const service of giftSets) {
      // Check if service already exists (case-insensitive)
      const existingService = existingServicesMap.get(service.service.toLowerCase());

      if (existingService) {
        // Update existing service
        await prisma.pricing.update({
          where: { id: existingService.id },
          data: {
            price: service.price,
            description: service.description,
            features: service.features,
            icon: service.icon,
            popular: service.popular
          }
        });
        updated++;
        console.log(`Updated existing service: ${service.service}`);
      } else {
        // Create new service
        await prisma.pricing.create({
          data: {
            service: service.service,
            price: service.price,
            description: service.description,
            features: service.features,
            icon: service.icon,
            popular: service.popular
          }
        });
        created++;
        console.log(`Created new service: ${service.service}`);
      }
    }

    console.log(`Successfully processed gift sets pricing data:`);
    console.log(`- Created: ${created} new services`);
    console.log(`- Updated: ${updated} existing services`);
    console.log(`- Total: ${giftSets.length} services processed`);

  } catch (error) {
    console.error('Error seeding gift sets pricing data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedGiftSets()
  .catch((error) => {
    console.error('Error running seed script:', error);
    process.exit(1);
  });
