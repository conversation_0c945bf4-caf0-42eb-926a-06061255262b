// Test script for OpenAI integration
require('dotenv').config();
const { OpenAI } = require('openai');

// Create an OpenAI client instance
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

async function testOpenAI() {
  try {
    console.log('Testing OpenAI integration...');
    
    // Check if API key is configured
    if (!process.env.OPENAI_API_KEY) {
      console.error('Error: OPENAI_API_KEY is not configured in .env file');
      process.exit(1);
    }
    
    console.log('API Key is configured. Sending test request to OpenAI...');
    
    // Create a simple chat completion
    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        { role: "user", content: "Write a haiku about AI" },
      ],
    });
    
    // Log the response
    console.log('\nOpenAI Response:');
    console.log('----------------');
    console.log(completion.choices[0].message.content);
    console.log('----------------');
    console.log('\nTest completed successfully!');
    
  } catch (error) {
    console.error('Error testing OpenAI integration:');
    console.error(error);
    process.exit(1);
  }
}

// Run the test
testOpenAI();
