const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Define relevant categories for a Kenyan design and web development website
const categories = [
  {
    name: 'Web Development',
    slug: 'web-development',
    description: 'Custom website development services including responsive design, e-commerce solutions, and content management systems.'
  },
  {
    name: 'Mobile Development',
    slug: 'mobile-development',
    description: 'Native and cross-platform mobile app development for Android and iOS platforms.'
  },
  {
    name: 'UI/UX Design',
    slug: 'ui-ux-design',
    description: 'User interface and experience design services focused on creating intuitive, engaging digital experiences.'
  },
  {
    name: 'Branding',
    slug: 'branding',
    description: 'Complete branding solutions including logo design, brand guidelines, and corporate identity development.'
  },
  {
    name: 'Graphic Design',
    slug: 'graphic-design',
    description: 'Professional graphic design services for print and digital media.'
  },
  {
    name: 'SEO',
    slug: 'seo',
    description: 'Search engine optimization strategies to improve your website visibility and ranking in Kenya and beyond.'
  },
  {
    name: 'Social Media',
    slug: 'social-media',
    description: 'Social media marketing, management and content creation services for Kenyan businesses.'
  },
  {
    name: 'E-commerce',
    slug: 'e-commerce',
    description: 'Online store development and e-commerce solutions for businesses in Kenya.'
  },
  {
    name: 'Print Design',
    slug: 'print-design',
    description: 'Professional design services for business cards, flyers, brochures, and other printed materials.'
  },
  {
    name: 'Logo Design',
    slug: 'logo-design',
    description: 'Custom logo design services for businesses and organizations in Kenya.'
  },
  {
    name: 'WordPress',
    slug: 'wordpress',
    description: 'WordPress website development, customization, and maintenance services.'
  },
  {
    name: 'Digital Marketing',
    slug: 'digital-marketing',
    description: 'Comprehensive digital marketing strategies for Kenyan businesses.'
  }
];

async function main() {
  try {
    console.log('Starting category creation...');
    let created = 0;
    let skipped = 0;
    
    // Process each category
    for (const category of categories) {
      // Check if category already exists
      const existing = await prisma.category.findUnique({
        where: {
          slug: category.slug
        }
      });
      
      if (existing) {
        console.log(`Category "${category.name}" already exists, skipping...`);
        skipped++;
        continue;
      }
      
      // Create the category
      const newCategory = await prisma.category.create({
        data: category
      });
      
      console.log(`Created category: ${newCategory.name}`);
      created++;
    }
    
    console.log('\nSummary:');
    console.log(`Total categories processed: ${categories.length}`);
    console.log(`Categories created: ${created}`);
    console.log(`Categories skipped: ${skipped}`);
    
    // Fetch all categories after creation
    const allCategories = await prisma.category.findMany({
      orderBy: {
        name: 'asc'
      }
    });
    
    console.log('\nAll categories in database:');
    allCategories.forEach(cat => {
      console.log(`- ${cat.name} (${cat.slug})`);
    });
    
  } catch (error) {
    console.error('Error creating categories:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main(); 