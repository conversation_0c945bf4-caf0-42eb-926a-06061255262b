const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Creating test lead...');
    
    // Create a test lead
    const lead = await prisma.lead.create({
      data: {
        name: 'Test Lead',
        email: '<EMAIL>',
        phone: '+1234567890',
        company: 'Test Company',
        source: 'manual',
        status: 'new',
        score: 50,
        notes: 'This is a test lead created for testing the lead management system.',
      },
    });
    
    console.log('Test lead created successfully with ID:', lead.id);
    console.log('Lead details:', lead);
    
    // Create a test interaction
    const interaction = await prisma.interaction.create({
      data: {
        leadId: lead.id,
        type: 'note',
        details: 'Initial contact made with the lead.',
      },
    });
    
    console.log('Test interaction created successfully with ID:', interaction.id);
    
  } catch (error) {
    console.error('Error creating test lead:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
