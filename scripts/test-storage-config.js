const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testStorageConfig() {
  try {
    // Get the default storage configuration
    const config = await prisma.storageConfig.findFirst({
      where: { isDefault: true },
    });
    
    console.log('Retrieved storage configuration:');
    console.log({
      id: config?.id,
      provider: config?.provider,
      region: config?.region,
      endpoint: config?.endpoint,
      bucketName: config?.bucketName,
      accessKeyId: config?.accessKeyId,
      secretAccessKey: '***************', // Masked for security
      isDefault: config?.isDefault,
    });
    
    // Test a simulated S3 client initialization
    console.log('\nTesting S3 client initialization with this config:');
    console.log(`- Region: ${config?.region}`);
    console.log(`- Endpoint: ${config?.endpoint}`);
    console.log(`- Bucket: ${config?.bucketName}`);
    
    // Create a URL for an example file
    const exampleKey = 'images/logo.png';
    const url = `${config?.endpoint}/${config?.bucketName}/${exampleKey}`;
    
    console.log(`\nGenerated URL for 'images/logo.png': ${url}`);
    
  } catch (error) {
    console.error('Error testing storage configuration:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testStorageConfig(); 