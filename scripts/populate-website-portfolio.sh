#!/bin/bash

# Script to populate the website portfolio
# Usage: ./scripts/populate-website-portfolio.sh

# Exit on error
set -e

echo "🔄 Starting website portfolio population..."

# Create a backup of the current database
echo "📦 Creating database backup..."
BACKUP_FILE="backups/backup_before_portfolio_seed_$(date +%Y%m%d_%H%M%S).sql"
sudo -u postgres pg_dump mocky > "$BACKUP_FILE"
echo "✅ Database backup created at $BACKUP_FILE"

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Run the seed script
echo "🌱 Seeding website portfolio data..."
node scripts/seed-website-portfolio.js

echo "✅ Website portfolio population completed successfully!"
