{"compilerOptions": {"target": "es2018", "module": "commonjs", "lib": ["es2018", "dom"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "baseUrl": "..", "paths": {"@/*": ["src/*"]}}, "include": ["**/*.ts"], "exclude": ["node_modules"]}