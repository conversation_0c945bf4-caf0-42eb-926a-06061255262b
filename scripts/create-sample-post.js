const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Creating a sample blog post with tags...');
    
    // First, check if we have categories
    const categories = await prisma.category.findMany();
    
    if (categories.length === 0) {
      console.log('No categories found. Please run the seed-categories-tags.js script first.');
      return;
    }
    
    // Select a random category
    const randomCategory = categories[Math.floor(Math.random() * categories.length)];
    
    // Create a sample blog post
    const blogPost = await prisma.blogPost.create({
      data: {
        title: 'Sample Blog Post with Tags',
        slug: 'sample-blog-post-with-tags',
        content: '<p>This is a sample blog post created to test tags functionality.</p>',
        excerpt: 'This is a sample blog post created to test tags functionality.',
        author: 'Test Author',
        category: randomCategory.slug,
        tags: [randomCategory.slug, 'test-tag', 'sample'],
        status: 'published',
        publishedAt: new Date()
      }
    });
    
    console.log('Sample blog post created successfully:');
    console.log(JSON.stringify(blogPost, null, 2));
    
  } catch (error) {
    console.error('Error creating sample blog post:', error);
    console.error(error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

main();
