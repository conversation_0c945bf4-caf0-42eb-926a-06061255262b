const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Testing database models...');
    
    // Test User model
    console.log('\n--- Testing User model ---');
    const users = await prisma.user.findMany({
      include: {
        role: true
      }
    });
    console.log(`Found ${users.length} users`);
    users.forEach(user => {
      console.log(`- ${user.username} (${user.email}), Role: ${user.role.name}`);
    });
    
    // Test Role model
    console.log('\n--- Testing Role model ---');
    const roles = await prisma.role.findMany();
    console.log(`Found ${roles.length} roles`);
    roles.forEach(role => {
      console.log(`- ${role.name}: ${role.description || 'No description'}`);
      console.log(`  Permissions: ${role.permissions.join(', ') || 'None'}`);
    });
    
    // Test ActivityLog model
    console.log('\n--- Testing ActivityLog model ---');
    const logs = await prisma.activityLog.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        user: true
      }
    });
    console.log(`Found ${logs.length} recent activity logs`);
    logs.forEach(log => {
      console.log(`- ${new Date(log.createdAt).toLocaleString()}: ${log.user.username} performed ${log.action}`);
      if (log.details) console.log(`  Details: ${log.details}`);
    });
    
    console.log('\nAll models tested successfully!');
  } catch (error) {
    console.error('Error testing models:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
