// <PERSON>ript to check the latest blog post in detail
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkLatestBlog() {
  try {
    console.log('Fetching latest blog post...');
    
    // Get the latest blog post
    const latestPost = await prisma.blogPost.findFirst({
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    if (!latestPost) {
      console.log('No blog posts found');
      return;
    }
    
    console.log(`\n--- Latest Blog Post ---`);
    console.log(`ID: ${latestPost.id}`);
    console.log(`Title: ${latestPost.title}`);
    console.log(`Status: ${latestPost.status}`);
    console.log(`Created: ${latestPost.createdAt}`);
    console.log(`Published: ${latestPost.publishedAt || 'Not published'}`);
    console.log(`Category: ${latestPost.category}`);
    console.log(`Author: ${latestPost.author}`);
    console.log(`Excerpt: ${latestPost.excerpt}`);
    
    // Count words in content
    const wordCount = latestPost.content.split(/\s+/).length;
    console.log(`Word Count: ${wordCount}`);
    
    // Check for website links
    const websiteUrl = process.env.WEBSITE_URL || 'https://mocky.co.ke';
    const websiteDomain = websiteUrl.replace(/^https?:\/\//, '').replace(/\/.*$/, '');
    const linkRegex = new RegExp(`href=["']https?://(www\\.)?${websiteDomain}`, 'gi');
    const linkMatches = latestPost.content.match(linkRegex) || [];
    console.log(`Website Links: ${linkMatches.length}`);
    
    // Print the first 500 characters of content
    console.log(`\nContent Preview (first 500 chars):`);
    console.log(latestPost.content.substring(0, 500) + '...');
    
    // Print the last 500 characters of content to see if our enhancements were added
    console.log(`\nContent End (last 500 chars):`);
    console.log('...' + latestPost.content.substring(latestPost.content.length - 500));
  } catch (error) {
    console.error('Error checking latest blog post:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
checkLatestBlog();
