// Script to verify and initialize storage configuration
const { PrismaClient } = require('@prisma/client');
const { S3Client, HeadBucketCommand } = require('@aws-sdk/client-s3');
const prisma = new PrismaClient();

async function verifyStorageConfig() {
  try {
    console.log('Verifying storage configuration...');
    
    // Check if there's a default storage configuration
    const config = await prisma.storageConfig.findFirst({
      where: { isDefault: true },
    });
    
    if (!config) {
      console.log('No default storage configuration found. Creating one from environment variables...');
      
      // Create a default configuration from environment variables
      const newConfig = await prisma.storageConfig.create({
        data: {
          provider: 'S3',
          region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
          endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
          bucketName: process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky',
          accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
          secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
          isDefault: true
        }
      });
      
      console.log('Created default storage configuration:', newConfig.id);
      
      // Use the newly created config
      return verifyS3Connection(newConfig);
    }
    
    console.log('Found default storage configuration:', config.id);
    
    // Verify S3 connection
    return verifyS3Connection(config);
  } catch (error) {
    console.error('Error verifying storage configuration:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function verifyS3Connection(config) {
  try {
    console.log(`Verifying S3 connection to ${config.endpoint}...`);
    
    // Create S3 client
    const s3Client = new S3Client({
      region: config.region,
      endpoint: config.endpoint,
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
      },
      forcePathStyle: true
    });
    
    // Check if bucket exists
    const command = new HeadBucketCommand({
      Bucket: config.bucketName
    });
    
    try {
      await s3Client.send(command);
      console.log(`✅ Successfully connected to S3 bucket: ${config.bucketName}`);
      return true;
    } catch (bucketError) {
      console.error(`❌ Failed to connect to S3 bucket: ${config.bucketName}`);
      console.error('Error details:', bucketError.message);
      
      // Log helpful information for troubleshooting
      console.log('\nTroubleshooting information:');
      console.log('- Check that your S3 bucket exists and is accessible');
      console.log('- Verify that your access key and secret key are correct');
      console.log('- Ensure that the region and endpoint are correct');
      console.log('- Check network connectivity to the S3 endpoint');
      
      return false;
    }
  } catch (error) {
    console.error('Error verifying S3 connection:', error);
    return false;
  }
}

// Run the verification
verifyStorageConfig()
  .then(success => {
    if (success) {
      console.log('Storage configuration verification completed successfully.');
      process.exit(0);
    } else {
      console.error('Storage configuration verification failed.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error during verification:', error);
    process.exit(1);
  });
