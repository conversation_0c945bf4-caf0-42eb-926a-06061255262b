// <PERSON>ript to check the storage configuration in the database
require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Checking storage configuration...');

    // Get all storage configs
    const storageConfigs = await prisma.storageConfig.findMany();
    
    console.log(`Found ${storageConfigs.length} storage configurations:`);
    
    // Print each config (hiding sensitive info)
    storageConfigs.forEach((config, index) => {
      console.log(`\nStorage Config #${index + 1}:`);
      console.log(`ID: ${config.id}`);
      console.log(`Provider: ${config.provider}`);
      console.log(`Region: ${config.region}`);
      console.log(`Endpoint: ${config.endpoint}`);
      console.log(`Bucket Name: ${config.bucketName}`);
      console.log(`Access Key ID: ${config.accessKeyId.substring(0, 4)}...`);
      console.log(`Secret Access Key: ${config.secretAccessKey.substring(0, 4)}...`);
      console.log(`Is Default: ${config.isDefault}`);
      console.log(`Created At: ${config.createdAt}`);
      console.log(`Updated At: ${config.updatedAt}`);
    });

    console.log('\nStorage configuration check completed.');
  } catch (error) {
    console.error('Error checking storage configuration:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
