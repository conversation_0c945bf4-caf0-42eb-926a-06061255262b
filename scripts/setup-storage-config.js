#!/usr/bin/env node

/**
 * Setup Storage Configuration Script
 * 
 * This script ensures that a default storage configuration exists in the database
 * using the environment variables from .env file.
 */

const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient();

async function setupStorageConfig() {
  try {
    console.log('🔧 Setting up storage configuration...');

    // Check if environment variables are available
    const requiredEnvVars = [
      'NEXT_PUBLIC_S3_BUCKET',
      'NEXT_PUBLIC_S3_ACCESS_KEY',
      'NEXT_PUBLIC_S3_SECRET_KEY',
      'NEXT_PUBLIC_S3_ENDPOINT'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      console.error('❌ Missing required environment variables:', missingVars.join(', '));
      console.log('Please check your .env file and ensure all S3 configuration variables are set.');
      process.exit(1);
    }

    console.log('✅ All required environment variables found');
    console.log('📋 S3 Configuration:');
    console.log(`   Bucket: ${process.env.NEXT_PUBLIC_S3_BUCKET}`);
    console.log(`   Region: ${process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1'}`);
    console.log(`   Endpoint: ${process.env.NEXT_PUBLIC_S3_ENDPOINT}`);
    console.log(`   Access Key: ${process.env.NEXT_PUBLIC_S3_ACCESS_KEY?.substring(0, 8)}...`);

    // Check if a default storage configuration already exists
    const existingConfig = await prisma.storageConfig.findFirst({
      where: { isDefault: true }
    });

    if (existingConfig) {
      console.log('✅ Default storage configuration already exists:', existingConfig.id);
      console.log('   Provider:', existingConfig.provider);
      console.log('   Bucket:', existingConfig.bucketName);
      console.log('   Endpoint:', existingConfig.endpoint);
      
      // Update the existing configuration with current env vars
      const updatedConfig = await prisma.storageConfig.update({
        where: { id: existingConfig.id },
        data: {
          region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
          endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT,
          bucketName: process.env.NEXT_PUBLIC_S3_BUCKET,
          accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY,
          secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY,
          updatedAt: new Date()
        }
      });
      
      console.log('✅ Updated existing storage configuration with current environment variables');
      return updatedConfig;
    }

    // Create a new default storage configuration
    console.log('📝 Creating new default storage configuration...');
    
    const newConfig = await prisma.storageConfig.create({
      data: {
        provider: 'S3',
        region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
        endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT,
        bucketName: process.env.NEXT_PUBLIC_S3_BUCKET,
        accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY,
        secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY,
        isDefault: true
      }
    });

    console.log('✅ Created new default storage configuration:', newConfig.id);
    return newConfig;

  } catch (error) {
    console.error('❌ Error setting up storage configuration:', error);
    
    if (error.code === 'P2002') {
      console.log('💡 This might be a unique constraint violation. Trying to find existing config...');
      
      try {
        const configs = await prisma.storageConfig.findMany();
        console.log(`Found ${configs.length} existing storage configurations`);
        
        if (configs.length > 0) {
          // Set the first one as default
          const defaultConfig = await prisma.storageConfig.update({
            where: { id: configs[0].id },
            data: { isDefault: true }
          });
          console.log('✅ Set existing configuration as default:', defaultConfig.id);
          return defaultConfig;
        }
      } catch (findError) {
        console.error('❌ Error finding existing configurations:', findError);
      }
    }
    
    throw error;
  }
}

async function testStorageConfig() {
  try {
    console.log('\n🧪 Testing storage configuration...');
    
    const config = await prisma.storageConfig.findFirst({
      where: { isDefault: true }
    });
    
    if (!config) {
      console.error('❌ No default storage configuration found');
      return false;
    }
    
    console.log('✅ Storage configuration test passed');
    console.log('   ID:', config.id);
    console.log('   Provider:', config.provider);
    console.log('   Bucket:', config.bucketName);
    console.log('   Region:', config.region);
    console.log('   Endpoint:', config.endpoint);
    console.log('   Is Default:', config.isDefault);
    
    return true;
  } catch (error) {
    console.error('❌ Storage configuration test failed:', error);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 Starting storage configuration setup...\n');
    
    await setupStorageConfig();
    await testStorageConfig();
    
    console.log('\n✅ Storage configuration setup completed successfully!');
    console.log('🎉 Your application should now be able to upload images to S3.');
    
  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    console.log('\n💡 Troubleshooting tips:');
    console.log('1. Check that your .env file contains all required S3 variables');
    console.log('2. Ensure your database is running and accessible');
    console.log('3. Run "npx prisma db push" to ensure the database schema is up to date');
    console.log('4. Verify your S3 credentials are correct');
    
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { setupStorageConfig, testStorageConfig };
