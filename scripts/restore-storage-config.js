const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function restoreStorageConfig() {
  try {
    // Find the default storage config
    const existingConfig = await prisma.storageConfig.findFirst({
      where: { isDefault: true }
    });

    if (!existingConfig) {
      console.log('No default storage configuration found. Cannot update.');
      return;
    }
    
    console.log('Found existing configuration:', existingConfig.id);
    
    // Restore the original bucket name
    const updatedConfig = await prisma.storageConfig.update({
      where: { id: existingConfig.id },
      data: {
        bucketName: 'mocky', // Restore to original value
      }
    });
    
    console.log('Restored storage configuration:');
    console.log({
      id: updatedConfig.id,
      provider: updatedConfig.provider,
      region: updatedConfig.region,
      endpoint: updatedConfig.endpoint,
      bucketName: updatedConfig.bucketName,
      accessKeyId: updatedConfig.accessKeyId.substring(0, 4) + '...',
      isDefault: updatedConfig.isDefault
    });
    
  } catch (error) {
    console.error('Error restoring storage configuration:', error);
  } finally {
    await prisma.$disconnect();
  }
}

restoreStorageConfig(); 