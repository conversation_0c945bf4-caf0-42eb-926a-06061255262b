import { PrismaClient } from '@prisma/client';
import { hashPassword } from '../src/utils/passwordUtils';

const prisma = new PrismaClient();

async function main() {
  try {
    // Check if admin role exists
    let adminRole = await prisma.role.findFirst({
      where: { name: 'admin' }
    });

    // Create admin role if it doesn't exist
    if (!adminRole) {
      console.log('Creating admin role...');
      adminRole = await prisma.role.create({
        data: {
          name: 'admin',
          description: 'Administrator with full access',
          permissions: ['*']
        }
      });
      console.log('Admin role created successfully.');
    } else {
      console.log('Admin role already exists.');
    }

    // Check if admin user exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: process.env.ADMIN_USERNAME },
          { email: process.env.ADMIN_EMAIL }
        ]
      }
    });

    if (existingUser) {
      console.log('Admin user already exists. Updating password...');
      
      // Update the existing user's password
      const passwordHash = await hashPassword(process.env.ADMIN_PASSWORD || 'Jack75522r');
      
      await prisma.user.update({
        where: { id: existingUser.id },
        data: {
          passwordHash,
          roleId: adminRole.id,
          active: true
        }
      });
      
      console.log('Admin user password updated successfully.');
    } else {
      console.log('Creating admin user...');
      
      // Create admin user
      const passwordHash = await hashPassword(process.env.ADMIN_PASSWORD || 'Jack75522r');
      
      await prisma.user.create({
        data: {
          username: process.env.ADMIN_USERNAME || '<EMAIL>',
          email: process.env.ADMIN_EMAIL || '<EMAIL>',
          name: process.env.ADMIN_NAME || 'Admin',
          passwordHash,
          roleId: adminRole.id,
          active: true
        }
      });
      
      console.log('Admin user created successfully.');
    }
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
