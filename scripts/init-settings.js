// Initialize site settings in the database
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function initializeSettings() {
  try {
    console.log('Checking if site settings exist...');
    
    // Check if settings already exist
    const existingSettings = await prisma.siteSettings.findFirst();
    
    if (existingSettings) {
      console.log('Site settings already exist. Skipping initialization.');
      return;
    }
    
    console.log('Creating default site settings...');
    
    // Create default settings
    const defaultSettings = await prisma.siteSettings.create({
      data: {
        siteName: 'Mocky Digital',
        siteDescription: 'Professional web design and digital marketing services',
        contactEmail: '<EMAIL>',
        phoneNumber: '+*********** 670',
        address: 'Nairobi, Kenya',
        facebookUrl: 'https://facebook.com/mockydigital',
        twitterUrl: 'https://x.com/mockydigital',
        instagramUrl: 'https://instagram.com/mockydigital',
        tiktokUrl: 'https://tiktok.com/@mocky_digital',
        linkedinUrl: 'https://linkedin.com/company/mockydigital',
        metaTitle: 'Mocky Digital - Web Design & Digital Marketing',
        metaDescription: 'Professional web design, development, and digital marketing services in Nairobi, Kenya.',
        googleAnalyticsId: ''
      }
    });
    
    console.log('Default site settings created successfully:', defaultSettings);
  } catch (error) {
    console.error('Error initializing site settings:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the initialization
initializeSettings();
