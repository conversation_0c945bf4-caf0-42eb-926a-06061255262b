#!/bin/bash

# Script to switch between development and production nginx configurations
# Usage: ./scripts/switch-nginx.sh [dev|prod]

NGINX_SITES_AVAILABLE="/etc/nginx/sites-available"
NGINX_SITES_ENABLED="/etc/nginx/sites-enabled"
DEV_CONF="$NGINX_SITES_AVAILABLE/dev.mocky.co.ke"
PROD_CONF="$NGINX_SITES_AVAILABLE/mocky.co.ke"
ENABLED_CONF="$NGINX_SITES_ENABLED/mocky.co.ke"

# Check if running as sudo/root
if [ "$EUID" -ne 0 ]; then
  echo "Please run as root or with sudo"
  exit 1
fi

# Function to install nginx configuration
install_nginx_conf() {
  local src_file="$1"
  local dest_file="$2"
  
  echo "Installing $src_file to $dest_file..."
  cp "$src_file" "$dest_file"
  
  # Enable the site if not already enabled
  if [ ! -L "$ENABLED_CONF" ]; then
    ln -s "$dest_file" "$ENABLED_CONF"
  fi
  
  # Test and reload nginx
  echo "Testing nginx configuration..."
  nginx -t && systemctl reload nginx
  
  if [ $? -eq 0 ]; then
    echo "Nginx configuration switched successfully"
  else
    echo "Error: Nginx configuration test failed"
    exit 1
  fi
}

if [ "$1" == "dev" ]; then
  echo "Switching to development nginx configuration..."
  
  # Copy our development config to sites-available
  install_nginx_conf "$(pwd)/nginx/dev.mocky.co.ke.conf" "$DEV_CONF"
  
  echo "Development nginx configuration is now active"
  echo "Don't forget to run './switch-env.sh dev' to switch the Node.js environment"
  
elif [ "$1" == "prod" ]; then
  echo "Switching to production nginx configuration..."
  
  # Copy our production config to sites-available
  install_nginx_conf "$(pwd)/nginx/prod.mocky.co.ke.conf" "$PROD_CONF"
  
  echo "Production nginx configuration is now active"
  echo "Don't forget to run './switch-env.sh prod' to switch the Node.js environment"
  
else
  echo "Usage: $0 [dev|prod]"
  echo "  dev  - Switch to development nginx configuration"
  echo "  prod - Switch to production nginx configuration"
  exit 1
fi 