/**
 * Setup S3 Migration Configuration
 * 
 * This script helps set up the destination S3 configuration in the .env file.
 * It reads the current S3 configuration and creates a second set of variables
 * with "_2" suffix that you can modify for the destination.
 * 
 * Usage: node scripts/setup-s3-migration.js
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to read the .env file
function readEnvFile() {
  const envPath = path.join(__dirname, '../.env');
  
  if (!fs.existsSync(envPath)) {
    console.error('Error: .env file not found');
    process.exit(1);
  }
  
  return fs.readFileSync(envPath, 'utf8');
}

// Function to extract S3 configuration from .env content
function extractS3Config(envContent) {
  const config = {};
  
  // Extract S3 configuration variables
  const regionMatch = envContent.match(/NEXT_PUBLIC_S3_REGION="([^"]+)"/);
  const endpointMatch = envContent.match(/NEXT_PUBLIC_S3_ENDPOINT="([^"]+)"/);
  const bucketMatch = envContent.match(/NEXT_PUBLIC_S3_BUCKET="([^"]+)"/);
  const accessKeyMatch = envContent.match(/NEXT_PUBLIC_S3_ACCESS_KEY="([^"]+)"/);
  const secretKeyMatch = envContent.match(/NEXT_PUBLIC_S3_SECRET_KEY="([^"]+)"/);
  const useS3Match = envContent.match(/NEXT_PUBLIC_USE_S3="([^"]+)"/);
  
  if (regionMatch) config.region = regionMatch[1];
  if (endpointMatch) config.endpoint = endpointMatch[1];
  if (bucketMatch) config.bucket = bucketMatch[1];
  if (accessKeyMatch) config.accessKey = accessKeyMatch[1];
  if (secretKeyMatch) config.secretKey = secretKeyMatch[1];
  if (useS3Match) config.useS3 = useS3Match[1];
  
  return config;
}

// Function to check if destination config already exists
function checkDestConfigExists(envContent) {
  return envContent.includes('NEXT_PUBLIC_S3_REGION_2=') || 
         envContent.includes('NEXT_PUBLIC_S3_ENDPOINT_2=');
}

// Function to create destination config section
function createDestConfigSection(sourceConfig) {
  return `
# S3 Configuration 2 (Destination)
NEXT_PUBLIC_S3_REGION_2="${sourceConfig.region}"
NEXT_PUBLIC_S3_ENDPOINT_2="${sourceConfig.endpoint}"
NEXT_PUBLIC_S3_BUCKET_2="${sourceConfig.bucket}"
NEXT_PUBLIC_S3_ACCESS_KEY_2="${sourceConfig.accessKey}"
NEXT_PUBLIC_S3_SECRET_KEY_2="${sourceConfig.secretKey}"
NEXT_PUBLIC_USE_S3_2="${sourceConfig.useS3}"
`;
}

// Function to update the .env file
function updateEnvFile(envContent, newSection) {
  const envPath = path.join(__dirname, '../.env');
  
  // Create a backup of the original .env file
  const backupPath = path.join(__dirname, '../.env.backup');
  fs.writeFileSync(backupPath, envContent);
  console.log(`✅ Created backup of .env file at ${backupPath}`);
  
  // Append the new section to the .env file
  fs.writeFileSync(envPath, envContent + newSection);
  console.log(`✅ Updated .env file with destination S3 configuration`);
}

// Function to prompt for destination configuration
function promptForDestConfig(sourceConfig) {
  return new Promise((resolve) => {
    const destConfig = { ...sourceConfig };
    
    console.log('\nPlease enter the destination S3 configuration:');
    
    rl.question(`Region [${sourceConfig.region}]: `, (region) => {
      destConfig.region = region || sourceConfig.region;
      
      rl.question(`Endpoint [${sourceConfig.endpoint}]: `, (endpoint) => {
        destConfig.endpoint = endpoint || sourceConfig.endpoint;
        
        rl.question(`Bucket [${sourceConfig.bucket}]: `, (bucket) => {
          destConfig.bucket = bucket || sourceConfig.bucket;
          
          rl.question(`Access Key [${sourceConfig.accessKey.substring(0, 3)}...${sourceConfig.accessKey.substring(sourceConfig.accessKey.length - 3)}]: `, (accessKey) => {
            destConfig.accessKey = accessKey || sourceConfig.accessKey;
            
            rl.question(`Secret Key [${sourceConfig.secretKey.substring(0, 3)}...${sourceConfig.secretKey.substring(sourceConfig.secretKey.length - 3)}]: `, (secretKey) => {
              destConfig.secretKey = secretKey || sourceConfig.secretKey;
              
              rl.question(`Use S3 [${sourceConfig.useS3}]: `, (useS3) => {
                destConfig.useS3 = useS3 || sourceConfig.useS3;
                
                resolve(destConfig);
              });
            });
          });
        });
      });
    });
  });
}

// Main function
async function main() {
  console.log('Setting up S3 migration configuration...');
  
  // Read the .env file
  const envContent = readEnvFile();
  
  // Extract source S3 configuration
  const sourceConfig = extractS3Config(envContent);
  
  if (!sourceConfig.region || !sourceConfig.endpoint || !sourceConfig.bucket) {
    console.error('Error: Could not find complete S3 configuration in .env file');
    process.exit(1);
  }
  
  console.log('\nSource S3 Configuration:');
  console.log(`Region: ${sourceConfig.region}`);
  console.log(`Endpoint: ${sourceConfig.endpoint}`);
  console.log(`Bucket: ${sourceConfig.bucket}`);
  console.log(`Access Key: ${sourceConfig.accessKey.substring(0, 3)}...${sourceConfig.accessKey.substring(sourceConfig.accessKey.length - 3)}`);
  console.log(`Secret Key: ${sourceConfig.secretKey.substring(0, 3)}...${sourceConfig.secretKey.substring(sourceConfig.secretKey.length - 3)}`);
  console.log(`Use S3: ${sourceConfig.useS3}`);
  
  // Check if destination config already exists
  if (checkDestConfigExists(envContent)) {
    console.log('\nDestination S3 configuration already exists in .env file.');
    rl.question('Do you want to update it? (y/n): ', async (answer) => {
      if (answer.toLowerCase() === 'y') {
        const destConfig = await promptForDestConfig(sourceConfig);
        const newSection = createDestConfigSection(destConfig);
        updateEnvFile(envContent, newSection);
      } else {
        console.log('Keeping existing destination configuration.');
      }
      rl.close();
    });
  } else {
    // Prompt for destination configuration
    const destConfig = await promptForDestConfig(sourceConfig);
    
    // Create destination config section
    const newSection = createDestConfigSection(destConfig);
    
    // Update the .env file
    updateEnvFile(envContent, newSection);
    
    rl.close();
  }
}

// Run the main function
main().catch(error => {
  console.error('Error:', error);
  rl.close();
  process.exit(1);
});

rl.on('close', () => {
  console.log('\nS3 migration configuration setup complete!');
  console.log('You can now run the migration script: node scripts/migrate-s3.js');
});
