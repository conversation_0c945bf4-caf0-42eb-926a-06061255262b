const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Listing all categories...');
    
    const categories = await prisma.category.findMany();
    
    console.log(`Found ${categories.length} categories:`);
    categories.forEach(category => {
      console.log(`- ${category.name} (${category.slug}): ${category.description || 'No description'}`);
    });
    
  } catch (error) {
    console.error('Error listing categories:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
