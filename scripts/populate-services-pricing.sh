#!/bin/bash

# Script to populate services pricing data
# Usage: ./scripts/populate-services-pricing.sh

# Exit on error
set -e

echo "🔄 Starting services pricing data population..."

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Run the seed script
echo "🌱 Seeding services pricing data..."
node scripts/seed-services-pricing.js

echo "✅ Services pricing data population completed successfully!"
