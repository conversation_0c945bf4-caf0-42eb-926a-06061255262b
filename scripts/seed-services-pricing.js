// Script to seed services pricing data
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

// Initialize Prisma client
const prisma = new PrismaClient();

// Define services with pricing relevant to the Kenyan market
// Excluding services with dedicated pricing pages like logo design and web development
const servicesPricing = [
  {
    service: 'Graphics Design (Hourly)',
    price: 2500,
    description: 'Professional graphic design services on an hourly basis',
    features: [
      'Custom graphics creation',
      'Marketing materials',
      'Social media graphics',
      'Print-ready files',
      'Commercial usage rights',
      'Quick turnaround'
    ],
    icon: 'paint-brush',
    popular: false
  },
  {
    service: 'Social Media Management (Monthly)',
    price: 15000,
    description: 'Complete handling of your social media presence across platforms',
    features: [
      'Daily post management',
      'Community engagement',
      'Content creation',
      'Analytics & reporting',
      'Crisis management',
      'Platform optimization'
    ],
    icon: 'hashtag',
    popular: true
  },
  {
    service: 'Content Creation (Package)',
    price: 8000,
    description: 'Engaging, platform-optimized content that resonates with your audience',
    features: [
      'Custom graphics & videos',
      'Copywriting & captions',
      'Content calendar',
      'Brand voice development',
      '10 posts per package',
      'Commercial usage rights'
    ],
    icon: 'pencil-alt',
    popular: false
  },
  {
    service: 'Paid Social Media Advertising',
    price: 10000,
    description: 'Strategic paid campaigns that drive results and ROI',
    features: [
      'Campaign strategy',
      'Ad creation & design',
      'Audience targeting',
      'Performance tracking',
      'Budget management',
      'Conversion optimization'
    ],
    icon: 'ad',
    popular: true
  },
  {
    service: 'Brand Strategy',
    price: 25000,
    description: 'Comprehensive brand strategy development for your business',
    features: [
      'Brand positioning',
      'Target audience analysis',
      'Competitive research',
      'Brand messaging',
      'Brand personality development',
      'Implementation roadmap'
    ],
    icon: 'bullseye',
    popular: true
  },
  {
    service: 'Visual Identity Package',
    price: 35000,
    description: 'Complete visual identity system for your brand',
    features: [
      'Logo design',
      'Color palette',
      'Typography selection',
      'Brand guidelines',
      'Stationery design',
      'Social media templates'
    ],
    icon: 'palette',
    popular: true
  },
  {
    service: 'Brand Guidelines',
    price: 15000,
    description: 'Comprehensive brand guidelines document',
    features: [
      'Logo usage rules',
      'Color specifications',
      'Typography guidelines',
      'Image style guide',
      'Voice & tone guidelines',
      'Digital & print applications'
    ],
    icon: 'book',
    popular: false
  },
  {
    service: 'VPS Server Setup',
    price: 12000,
    description: 'Professional VPS server setup for your web applications',
    features: [
      'Server configuration',
      'Security hardening',
      'Performance optimization',
      'SSL certificate installation',
      'Database setup',
      'Monitoring configuration'
    ],
    icon: 'server',
    popular: false
  },
  {
    service: 'Cloud Migration',
    price: 20000,
    description: 'Migrate your existing applications to cloud infrastructure',
    features: [
      'Migration planning',
      'Data transfer',
      'Application configuration',
      'Testing & validation',
      'DNS configuration',
      'Post-migration support'
    ],
    icon: 'cloud-upload-alt',
    popular: false
  },
  {
    service: 'Server Management (Monthly)',
    price: 8000,
    description: 'Ongoing server management and maintenance',
    features: [
      'Security updates',
      'Performance monitoring',
      'Backup management',
      'Issue resolution',
      '24/7 monitoring',
      'Monthly reporting'
    ],
    icon: 'cogs',
    popular: false
  },
  {
    service: 'Website Maintenance (Monthly)',
    price: 5000,
    description: 'Keep your website secure, updated, and performing at its best',
    features: [
      'Regular updates',
      'Security monitoring',
      'Performance optimization',
      'Content updates',
      'Technical support',
      'Monthly reporting'
    ],
    icon: 'tools',
    popular: true
  },
  {
    service: 'Marketing Materials Package',
    price: 18000,
    description: 'Comprehensive marketing materials for your business',
    features: [
      'Business cards',
      'Brochure design',
      'Flyer design',
      'Social media templates',
      'Email signature',
      'Letterhead design'
    ],
    icon: 'briefcase',
    popular: false
  }
];

// Main function to seed the database
async function seedServicesPricing() {
  try {
    console.log('Starting to seed services pricing data...');

    // Get existing pricing items
    const existingPricing = await prisma.pricing.findMany();
    console.log(`Found ${existingPricing.length} existing pricing items`);

    // Create a map of existing services (case-insensitive)
    const existingServicesMap = new Map();
    existingPricing.forEach(item => {
      existingServicesMap.set(item.service.toLowerCase(), item);
    });

    let created = 0;
    let updated = 0;

    // Process each service
    for (const service of servicesPricing) {
      // Check if service already exists (case-insensitive)
      const existingService = existingServicesMap.get(service.service.toLowerCase());

      if (existingService) {
        // Update existing service
        await prisma.pricing.update({
          where: { id: existingService.id },
          data: {
            price: service.price,
            description: service.description,
            features: service.features,
            icon: service.icon,
            popular: service.popular
          }
        });
        updated++;
        console.log(`Updated existing service: ${service.service}`);
      } else {
        // Create new service
        await prisma.pricing.create({
          data: {
            service: service.service,
            price: service.price,
            description: service.description,
            features: service.features,
            icon: service.icon,
            popular: service.popular
          }
        });
        created++;
        console.log(`Created new service: ${service.service}`);
      }
    }

    console.log(`Successfully processed services pricing data:`);
    console.log(`- Created: ${created} new services`);
    console.log(`- Updated: ${updated} existing services`);
    console.log(`- Total: ${servicesPricing.length} services processed`);

  } catch (error) {
    console.error('Error seeding services pricing data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedServicesPricing()
  .catch((error) => {
    console.error('Error running seed script:', error);
    process.exit(1);
  });
