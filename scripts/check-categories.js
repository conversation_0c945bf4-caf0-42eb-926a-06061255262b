const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // Fetch all categories
    const categories = await prisma.category.findMany({
      orderBy: {
        name: 'asc'
      }
    });
    
    console.log('All categories:');
    console.log(JSON.stringify(categories, null, 2));
    
    // Check for uncategorized category specifically
    const uncategorized = await prisma.category.findUnique({
      where: {
        slug: 'uncategorized'
      }
    });
    
    if (uncategorized) {
      console.log('\nFound uncategorized category:');
      console.log(JSON.stringify(uncategorized, null, 2));
    } else {
      console.log('\nNo dedicated uncategorized category found');
    }
    
    // Check blog posts with uncategorized
    const postsWithUncategorized = await prisma.blogPost.findMany({
      where: {
        category: 'uncategorized'
      }
    });
    
    console.log('\nBlog posts with uncategorized category:');
    console.log(`Count: ${postsWithUncategorized.length}`);
    if (postsWithUncategorized.length > 0) {
      console.log(JSON.stringify(postsWithUncategorized.map(post => ({
        id: post.id,
        title: post.title,
        category: post.category
      })), null, 2));
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main(); 