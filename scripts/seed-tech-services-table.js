// Script to seed tech services data for the Kenyan market
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

// Initialize Prisma client
const prisma = new PrismaClient();

// Define tech services with pricing relevant to the Kenyan market
const techServices = [
  // Web Development Services
  {
    name: 'Basic Website Package',
    description: 'Professional website for small businesses and startups',
    price: 25000,
    category: 'Web Development'
  },
  {
    name: 'Business Website Package',
    description: 'Comprehensive website solution for established businesses',
    price: 45000,
    category: 'Web Development'
  },
  {
    name: 'E-commerce Website',
    description: 'Full-featured online store for selling products or services',
    price: 85000,
    category: 'Web Development'
  },
  {
    name: 'Custom Web Application',
    description: 'Tailored web application development for specific business needs',
    price: 150000,
    category: 'Web Development'
  },
  {
    name: 'Website Maintenance (Monthly)',
    description: 'Ongoing website maintenance and support services',
    price: 5000,
    category: 'Web Development'
  },
  
  // Digital Marketing Services
  {
    name: 'SEO Package (Monthly)',
    description: 'Search engine optimization to improve website visibility',
    price: 15000,
    category: 'Digital Marketing'
  },
  {
    name: 'Social Media Management (Monthly)',
    description: 'Professional management of your social media presence',
    price: 12000,
    category: 'Digital Marketing'
  },
  {
    name: 'Google Ads Campaign',
    description: 'Targeted Google advertising campaigns',
    price: 20000,
    category: 'Digital Marketing'
  },
  {
    name: 'Facebook & Instagram Ads',
    description: 'Strategic social media advertising campaigns',
    price: 18000,
    category: 'Digital Marketing'
  },
  {
    name: 'Content Marketing Package',
    description: 'Comprehensive content creation and marketing strategy',
    price: 25000,
    category: 'Digital Marketing'
  },
  
  // IT Services & Consulting
  {
    name: 'IT Infrastructure Setup',
    description: 'Complete IT infrastructure setup for small to medium businesses',
    price: 35000,
    category: 'IT Services'
  },
  {
    name: 'IT Support (Monthly)',
    description: 'Ongoing IT support for business operations',
    price: 15000,
    category: 'IT Services'
  },
  {
    name: 'Cybersecurity Audit',
    description: 'Comprehensive assessment of your organization\'s security posture',
    price: 45000,
    category: 'IT Services'
  },
  {
    name: 'Data Recovery',
    description: 'Professional data recovery services for lost or corrupted data',
    price: 8000,
    category: 'IT Services'
  },
  {
    name: 'IT Consulting (Hourly)',
    description: 'Expert IT consultation for business technology needs',
    price: 3500,
    category: 'IT Services'
  },
  
  // Software Development
  {
    name: 'Mobile App Development (Android)',
    description: 'Custom Android application development',
    price: 120000,
    category: 'Software Development'
  },
  {
    name: 'Mobile App Development (iOS)',
    description: 'Custom iOS application development',
    price: 150000,
    category: 'Software Development'
  },
  {
    name: 'Cross-Platform Mobile App',
    description: 'Develop once, deploy on both Android and iOS platforms',
    price: 180000,
    category: 'Software Development'
  },
  {
    name: 'Custom Business Software',
    description: 'Tailored software solutions for specific business needs',
    price: 250000,
    category: 'Software Development'
  },
  {
    name: 'Software Integration',
    description: 'Integrate existing software systems for seamless operation',
    price: 75000,
    category: 'Software Development'
  },
  
  // Cloud & Hosting Services
  {
    name: 'Cloud Migration',
    description: 'Migrate your existing infrastructure to cloud platforms',
    price: 65000,
    category: 'Cloud Services'
  },
  {
    name: 'VPS Hosting (Monthly)',
    description: 'Virtual private server hosting for websites and applications',
    price: 5000,
    category: 'Cloud Services'
  },
  {
    name: 'Dedicated Server (Monthly)',
    description: 'Dedicated server hosting for high-performance applications',
    price: 15000,
    category: 'Cloud Services'
  },
  {
    name: 'Managed WordPress Hosting (Monthly)',
    description: 'Specialized hosting optimized for WordPress websites',
    price: 3500,
    category: 'Cloud Services'
  },
  {
    name: 'Domain Registration (.co.ke)',
    description: 'Register your .co.ke domain name for your Kenyan business',
    price: 1200,
    category: 'Cloud Services'
  }
];

// Main function to seed the database
async function seedTechServices() {
  try {
    console.log('Starting to seed tech services data...');

    // Get existing services
    const existingServices = await prisma.service.findMany();
    console.log(`Found ${existingServices.length} existing services`);

    // Create a map of existing services (case-insensitive)
    const existingServicesMap = new Map();
    existingServices.forEach(item => {
      existingServicesMap.set(item.name.toLowerCase(), item);
    });

    let created = 0;
    let updated = 0;

    // Process each service
    for (const service of techServices) {
      // Check if service already exists (case-insensitive)
      const existingService = existingServicesMap.get(service.name.toLowerCase());

      if (existingService) {
        // Update existing service
        await prisma.service.update({
          where: { id: existingService.id },
          data: {
            description: service.description,
            price: service.price,
            category: service.category
          }
        });
        updated++;
        console.log(`Updated existing service: ${service.name}`);
      } else {
        // Create new service
        await prisma.service.create({
          data: {
            name: service.name,
            description: service.description,
            price: service.price,
            category: service.category
          }
        });
        created++;
        console.log(`Created new service: ${service.name}`);
      }
    }

    console.log(`Successfully processed tech services data:`);
    console.log(`- Created: ${created} new services`);
    console.log(`- Updated: ${updated} existing services`);
    console.log(`- Total: ${techServices.length} services processed`);

  } catch (error) {
    console.error('Error seeding tech services data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedTechServices()
  .catch((error) => {
    console.error('Error running seed script:', error);
    process.exit(1);
  });
