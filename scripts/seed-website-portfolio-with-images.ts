import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import fetch from 'node-fetch';
import { WebsitePortfolioItem } from '@/types/portfolio';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
  endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
  credentials: {
    accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
    secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
  },
  forcePathStyle: true // Required for Linode Object Storage
});

// Define the website portfolio data from the image
const websitePortfolioData = [
  {
    title: 'Homestore.co.ke',
    description: 'Modern E-commerce Platform for Household Items',
    category: 'e-commerce',
    url: 'https://homestore.co.ke',
    imageUrl: 'https://placehold.co/600x400/orange/white?text=Homestore', // Placeholder image URL
  },
  {
    title: 'Galaidh Ltd',
    description: 'IT Solutions & Security Systems Provider',
    category: 'corporate',
    url: 'https://galaidh.com',
    imageUrl: 'https://placehold.co/600x400/blue/white?text=Galaidh',
  },
  {
    title: 'Top23 Security Ltd',
    description: 'Professional Security Services Website',
    category: 'security',
    url: 'https://top23security.com',
    imageUrl: 'https://placehold.co/600x400/red/white?text=Top23+Security',
  },
  {
    title: 'Top23 USA Outlet',
    description: 'E-commerce Store for American Products',
    category: 'e-commerce',
    url: 'https://top23usa.com',
    imageUrl: 'https://placehold.co/600x400/navy/white?text=Top23+USA',
  },
  {
    title: 'Reucher Africa Kenya Ltd',
    description: 'Industrial Chemicals & Water Treatment Solutions',
    category: 'corporate',
    url: 'https://reucherafrica.co.ke',
    imageUrl: 'https://placehold.co/600x400/green/white?text=Reucher+Africa',
  },
  {
    title: 'I4Food Organization',
    description: 'Institute For Food Systems & Climate Research Website',
    category: 'nonprofit',
    url: 'https://i4food.org',
    imageUrl: 'https://placehold.co/600x400/teal/white?text=I4Food',
  },
  {
    title: 'WeShop254',
    description: 'E-commerce Platform for Home & Living Products',
    category: 'e-commerce',
    url: 'https://weshop254.co.ke',
    imageUrl: 'https://placehold.co/600x400/purple/white?text=WeShop254',
  },
  {
    title: 'Knight Swift Logistics',
    description: 'Logistics & Transportation Company Website',
    category: 'logistics',
    url: 'https://knightswiftlogistics.com',
    imageUrl: 'https://placehold.co/600x400/brown/white?text=Knight+Swift',
  },
  {
    title: 'MRL Motors',
    description: 'Automotive Company Website with Modern Design',
    category: 'automotive',
    url: 'https://mrlmotors.com',
    imageUrl: 'https://placehold.co/600x400/gray/white?text=MRL+Motors',
  },
];

// Path to the website portfolio data file
const dataFilePath = path.join(process.cwd(), 'src', 'data', 'website-portfolio.json');

// Function to read existing website portfolio data
async function readWebsitePortfolioData(): Promise<WebsitePortfolioItem[]> {
  try {
    const fileData = await fs.promises.readFile(dataFilePath, 'utf8');
    return JSON.parse(fileData);
  } catch (error) {
    if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
      await fs.promises.mkdir(path.dirname(dataFilePath), { recursive: true });
      await fs.promises.writeFile(dataFilePath, JSON.stringify([], null, 2));
      return [];
    }
    console.error('Error reading website portfolio data:', error);
    return [];
  }
}

// Function to write website portfolio data
async function writeWebsitePortfolioData(data: WebsitePortfolioItem[]): Promise<void> {
  try {
    await fs.promises.mkdir(path.dirname(dataFilePath), { recursive: true });
    await fs.promises.writeFile(dataFilePath, JSON.stringify(data, null, 2));
    console.log('Website portfolio data written successfully');
  } catch (error) {
    console.error('Error writing website portfolio data:', error);
    throw new Error('Failed to write website portfolio data');
  }
}

// Function to check if a website with the same URL already exists
function websiteExists(websites: WebsitePortfolioItem[], url: string): boolean {
  return websites.some(website => website.url === url);
}

// Function to download an image from a URL
async function downloadImage(url: string): Promise<Buffer> {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Failed to download image from ${url}: ${response.statusText}`);
  }
  return Buffer.from(await response.arrayBuffer());
}

// Function to upload an image to S3
async function uploadImageToS3(imageBuffer: Buffer, filename: string): Promise<string> {
  if (!process.env.NEXT_PUBLIC_S3_BUCKET) {
    throw new Error('S3 bucket not configured');
  }

  const key = `images/portfolio/websites/${filename}`;
  
  const command = new PutObjectCommand({
    Bucket: process.env.NEXT_PUBLIC_S3_BUCKET,
    Key: key,
    Body: imageBuffer,
    ContentType: 'image/jpeg',
    ACL: 'public-read',
  });

  await s3Client.send(command);
  
  return `${process.env.NEXT_PUBLIC_S3_ENDPOINT}/${process.env.NEXT_PUBLIC_S3_BUCKET}/${key}`;
}

// Main function to seed the database
async function seedWebsitePortfolio() {
  try {
    console.log('Starting to seed website portfolio data...');
    
    // Read existing data
    const existingWebsites = await readWebsitePortfolioData();
    console.log(`Found ${existingWebsites.length} existing website portfolio items`);
    
    // Create new items for websites that don't already exist
    const newWebsites: WebsitePortfolioItem[] = [];
    
    for (const website of websitePortfolioData) {
      if (!websiteExists(existingWebsites, website.url)) {
        console.log(`Processing website: ${website.title}`);
        
        try {
          // Generate a unique filename
          const filename = `${website.title.toLowerCase().replace(/[^a-z0-9]/g, '-')}-${Date.now()}.jpg`;
          
          // Download the image
          console.log(`Downloading image from ${website.imageUrl}`);
          const imageBuffer = await downloadImage(website.imageUrl);
          
          // Upload the image to S3
          console.log(`Uploading image to S3: ${filename}`);
          const s3ImageUrl = await uploadImageToS3(imageBuffer, filename);
          
          const newWebsite: WebsitePortfolioItem = {
            id: uuidv4(),
            title: website.title,
            description: website.description,
            category: website.category,
            imageSrc: s3ImageUrl,
            url: website.url,
            featured: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };
          
          newWebsites.push(newWebsite);
          console.log(`Added new website: ${website.title} with image: ${s3ImageUrl}`);
        } catch (error) {
          console.error(`Error processing website ${website.title}:`, error);
        }
      } else {
        console.log(`Website already exists: ${website.title}`);
      }
    }
    
    // Combine existing and new websites
    const updatedWebsites = [...existingWebsites, ...newWebsites];
    
    // Write the updated data
    await writeWebsitePortfolioData(updatedWebsites);
    
    console.log(`Successfully added ${newWebsites.length} new website portfolio items`);
    console.log(`Total website portfolio items: ${updatedWebsites.length}`);
  } catch (error) {
    console.error('Error seeding website portfolio data:', error);
  }
}

// Run the seed function
seedWebsitePortfolio();
