const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Define the new website portfolio items
const newWebsites = [
  {
    title: 'Wasonga Law',
    description: 'Professional legal services and law firm website',
    category: 'corporate',
    url: 'https://wasongalaw.co.ke/',
    imageSrc: 'https://placehold.co/600x400/blue/white?text=Wasonga+Law',
    featured: false
  },
  {
    title: 'Pure Gift',
    description: 'Non-profit organization website for charitable giving',
    category: 'nonprofit',
    url: 'https://pure-gift.org/',
    imageSrc: 'https://placehold.co/600x400/green/white?text=Pure+Gift',
    featured: false
  }
];

async function main() {
  try {
    console.log('Starting to add new websites...');
    let created = 0;
    let skipped = 0;

    // Process each website
    for (const website of newWebsites) {
      // Check if website already exists
      const existing = await prisma.websitePortfolio.findFirst({
        where: {
          url: website.url
        }
      });

      if (existing) {
        console.log(`Website "${website.title}" already exists, skipping...`);
        skipped++;
        continue;
      }

      // Create the website
      const newWebsite = await prisma.websitePortfolio.create({
        data: website
      });

      console.log(`Added new website: ${newWebsite.title} with image: ${newWebsite.imageSrc}`);
      created++;
    }

    console.log('\nSummary:');
    console.log(`Total websites processed: ${newWebsites.length}`);
    console.log(`Websites created: ${created}`);
    console.log(`Websites skipped: ${skipped}`);

    // Fetch all websites after creation
    const allWebsites = await prisma.websitePortfolio.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log('\nAll websites in database:');
    allWebsites.forEach(website => {
      console.log(`- ${website.title} (${website.url})`);
    });

  } catch (error) {
    console.error('Error adding websites:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main(); 