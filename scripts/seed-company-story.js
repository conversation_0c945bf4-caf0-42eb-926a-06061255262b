// This script seeds the company_story table with initial data
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('Starting to seed company story data...');

  // First, check if there's already data in the table
  const existingStories = await prisma.companyStory.count();
  
  if (existingStories > 0) {
    console.log(`Found ${existingStories} existing company stories. Skipping seeding.`);
    console.log('If you want to reseed, please delete existing records first.');
    return;
  }

  // Create company story
  const story = await prisma.companyStory.create({
    data: {
      title: 'The Journey Behind Mocky Digital',
      subtitle: 'From humble beginnings to becoming a trusted digital partner for businesses across Kenya.',
      imageSrc: '/images/about/ceo.jpg',
      quote1: "When I started Mocky Digital, I had a simple goal - to help local businesses thrive in the digital world. Growing up in Kenya, I saw many talented entrepreneurs struggling to make their mark online. That's why we focus on creating practical, results-driven solutions that actually work for our clients.",
      quote2: "What makes us different is our hands-on approach. I personally ensure that each project gets the attention it deserves. Whether you're a small startup or an established business, we treat your digital presence with the same dedication and creativity.",
      founder<PERSON><PERSON>: '<PERSON>',
      founder<PERSON>ole: 'CEO',
      linkedinUrl: 'https://www.linkedin.com/in/don-omondi-*********/',
      twitterUrl: 'https://x.com/onyango__omondi',
      isActive: true
    }
  });

  console.log(`Created company story with ID: ${story.id}`);
  console.log('Company story data seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('Error seeding company story data:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
