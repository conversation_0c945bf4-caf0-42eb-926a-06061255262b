#!/usr/bin/env node

/**
 * SEO <PERSON>an <PERSON>
 *
 * This script scans the website for SEO issues and updates the database.
 * It can be run manually or scheduled as a cron job.
 *
 * Usage:
 *   node scripts/seo-scan.js [--all] [--url=https://example.com]
 *
 * Options:
 *   --all: Scan all pages in the sitemap
 *   --url=URL: Scan a specific URL
 */

require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const { JSDOM } = require('jsdom');
const fetch = require('node-fetch');
const { parseArgs } = require('node:util');

// Configure Prisma Client with logging options
const prisma = new PrismaClient({
  log: [
    {
      emit: 'event',
      level: 'error',
    },
  ],
});

// Add error handler for Prisma Client
prisma.$on('error', (e) => {
  console.error('Prisma Client error:', e);
});

// Parse command line arguments
const options = {
  all: { type: 'boolean' },
  url: { type: 'string' },
};

const { values } = parseArgs({ options });

// Main function
async function main() {
  console.log('Starting SEO scan...');

  try {
    // Create a new scan record
    const scan = await prisma.seoScan.create({
      data: {
        status: 'running',
      },
    });

    console.log(`Created scan record with ID: ${scan.id}`);

    let urls = [];

    // Determine which URLs to scan
    if (values.url) {
      // Scan a specific URL
      urls.push(values.url);
    } else if (values.all) {
      // Scan all pages in the sitemap
      console.log('Fetching sitemap...');
      const sitemapUrl = 'https://mocky.co.ke/sitemap.xml';
      const response = await fetch(sitemapUrl);
      const sitemapXml = await response.text();

      // Extract URLs from sitemap
      const urlRegex = /<loc>(.*?)<\/loc>/g;
      let match;
      while ((match = urlRegex.exec(sitemapXml)) !== null) {
        urls.push(match[1]);
      }

      console.log(`Found ${urls.length} URLs in sitemap`);
    } else {
      // Default: Scan the homepage
      urls.push('https://mocky.co.ke');
    }

    // Scan each URL
    const results = [];
    for (const url of urls) {
      console.log(`Scanning URL: ${url}`);
      try {
        const result = await scanUrl(url);
        results.push(result);
      } catch (error) {
        console.error(`Error scanning URL ${url}:`, error);
      }
    }

    // Update the scan record
    await prisma.seoScan.update({
      where: { id: scan.id },
      data: {
        status: 'completed',
        endedAt: new Date(),
        summary: {
          urlsScanned: urls.length,
          results: results.map(r => ({
            url: r.url,
            issuesCount: r.issues.length,
            brokenLinksCount: r.brokenLinks.length,
          })),
        },
      },
    });

    console.log('SEO scan completed successfully');
  } catch (error) {
    console.error('Error during SEO scan:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Scan a single URL
async function scanUrl(url) {
  try {
    // Fetch the page content
    const response = await fetch(url);
    const html = await response.text();

    // Parse the HTML
    const dom = new JSDOM(html);
    const document = dom.window.document;

    // Extract basic page data
    const pageData = {
      url,
      title: document.title,
      description: getMetaContent(document, 'description'),
      keywords: getMetaContent(document, 'keywords')?.split(',').map(k => k.trim()) || [],
    };

    // Analyze the page
    const issues = analyzeIssues(document, url);
    const metaTags = extractMetaTags(document);
    const structuredData = extractStructuredData(document);
    const brokenLinks = await checkBrokenLinks(document, url);

    // Extract keywords from content
    const keywords = extractKeywords(document);

    // Create or update the page record
    const page = await prisma.seoPage.upsert({
      where: { url },
      update: {
        title: pageData.title,
        description: pageData.description,
        keywords: pageData.keywords,
        lastScanned: new Date(),
        healthScore: calculateHealthScore({ issues, brokenLinks }),
        status: 'scanned',
      },
      create: {
        url,
        title: pageData.title,
        description: pageData.description,
        keywords: pageData.keywords,
        lastScanned: new Date(),
        healthScore: calculateHealthScore({ issues, brokenLinks }),
        status: 'scanned',
      },
    });

    // Clear existing data
    await prisma.$transaction([
      prisma.seoIssue.deleteMany({ where: { pageId: page.id } }),
      prisma.seoKeyword.deleteMany({ where: { pageId: page.id } }),
      prisma.seoMetaTag.deleteMany({ where: { pageId: page.id } }),
      prisma.seoStructuredData.deleteMany({ where: { pageId: page.id } }),
      prisma.brokenLink.deleteMany({ where: { pageId: page.id } }),
    ]);

    // Save new data
    if (issues.length > 0) {
      await prisma.seoIssue.createMany({
        data: issues.map(issue => ({
          pageId: page.id,
          type: issue.type,
          severity: issue.severity,
          description: issue.description,
        })),
      });
    }

    if (keywords.length > 0) {
      await prisma.seoKeyword.createMany({
        data: keywords.map(keyword => ({
          pageId: page.id,
          keyword: keyword.keyword,
          lastChecked: new Date(),
        })),
      });
    }

    if (metaTags.length > 0) {
      await prisma.seoMetaTag.createMany({
        data: metaTags.map(tag => ({
          pageId: page.id,
          name: tag.name,
          content: tag.content,
        })),
      });
    }

    if (structuredData.length > 0) {
      await prisma.seoStructuredData.createMany({
        data: structuredData.map(item => ({
          pageId: page.id,
          type: item.type,
          data: item.data,
        })),
      });
    }

    if (brokenLinks.length > 0) {
      await prisma.brokenLink.createMany({
        data: brokenLinks.map(link => ({
          pageId: page.id,
          url: link.url,
          statusCode: link.statusCode,
          description: link.description,
          fixed: false,
        })),
      });
    }

    console.log(`Completed scan for ${url} - Found ${issues.length} issues, ${brokenLinks.length} broken links`);

    return {
      url,
      issues,
      keywords,
      metaTags,
      structuredData,
      brokenLinks,
    };
  } catch (error) {
    console.error(`Error scanning URL ${url}:`, error);
    throw error;
  }
}

// Helper functions
function getMetaContent(document, name) {
  const meta = document.querySelector(`meta[name="${name}"]`) || document.querySelector(`meta[property="og:${name}"]`);
  return meta ? meta.getAttribute('content') : undefined;
}

function analyzeIssues(document, url) {
  const issues = [];

  // Check title
  const title = document.title;
  if (!title) {
    issues.push({
      type: 'missing_title',
      severity: 'critical',
      description: 'Page is missing a title tag',
    });
  } else if (title.length < 10) {
    issues.push({
      type: 'short_title',
      severity: 'high',
      description: 'Page title is too short (less than 10 characters)',
    });
  } else if (title.length > 60) {
    issues.push({
      type: 'long_title',
      severity: 'medium',
      description: 'Page title is too long (more than 60 characters)',
    });
  }

  // Check meta description
  const description = getMetaContent(document, 'description');
  if (!description) {
    issues.push({
      type: 'missing_description',
      severity: 'high',
      description: 'Page is missing a meta description',
    });
  } else if (description.length < 50) {
    issues.push({
      type: 'short_description',
      severity: 'medium',
      description: 'Meta description is too short (less than 50 characters)',
    });
  } else if (description.length > 160) {
    issues.push({
      type: 'long_description',
      severity: 'low',
      description: 'Meta description is too long (more than 160 characters)',
    });
  }

  // Check headings
  const h1Elements = document.querySelectorAll('h1');
  if (h1Elements.length === 0) {
    issues.push({
      type: 'missing_h1',
      severity: 'high',
      description: 'Page is missing an H1 heading',
    });
  } else if (h1Elements.length > 1) {
    issues.push({
      type: 'multiple_h1',
      severity: 'medium',
      description: `Page has multiple H1 headings (${h1Elements.length})`,
    });
  }

  // Check images
  const images = document.querySelectorAll('img');
  let missingAltCount = 0;

  images.forEach(img => {
    if (!img.hasAttribute('alt')) {
      missingAltCount++;
    }
  });

  if (missingAltCount > 0) {
    issues.push({
      type: 'missing_alt',
      severity: 'medium',
      description: `${missingAltCount} images are missing alt attributes`,
    });
  }

  // Check canonical URL
  const canonical = document.querySelector('link[rel="canonical"]');
  if (!canonical) {
    issues.push({
      type: 'missing_canonical',
      severity: 'medium',
      description: 'Page is missing a canonical URL',
    });
  }

  // Check mobile viewport
  const viewport = document.querySelector('meta[name="viewport"]');
  if (!viewport) {
    issues.push({
      type: 'missing_viewport',
      severity: 'high',
      description: 'Page is missing a viewport meta tag',
    });
  }

  return issues;
}

function extractMetaTags(document) {
  const metaTags = [];
  const tags = document.querySelectorAll('meta');

  tags.forEach(tag => {
    const name = tag.getAttribute('name') || tag.getAttribute('property') || '';
    const content = tag.getAttribute('content') || '';

    if (name && content) {
      metaTags.push({ name, content });
    }
  });

  return metaTags;
}

function extractStructuredData(document) {
  const structuredData = [];
  const scripts = document.querySelectorAll('script[type="application/ld+json"]');

  scripts.forEach(script => {
    try {
      const data = JSON.parse(script.textContent || '{}');
      const type = data['@type'] || 'Unknown';

      structuredData.push({
        type,
        data,
      });
    } catch (error) {
      console.error('Error parsing structured data:', error);
    }
  });

  return structuredData;
}

async function checkBrokenLinks(document, baseUrl) {
  const brokenLinks = [];
  const links = document.querySelectorAll('a');
  const checkedUrls = new Set();

  // Only check a limited number of links to avoid overloading
  const MAX_LINKS_TO_CHECK = 20;
  let checkedCount = 0;

  for (const link of links) {
    if (checkedCount >= MAX_LINKS_TO_CHECK) break;

    const href = link.getAttribute('href');
    if (!href || href.startsWith('#') || href.startsWith('mailto:') || href.startsWith('tel:')) {
      continue;
    }

    try {
      // Resolve relative URLs
      const absoluteUrl = new URL(href, baseUrl).toString();

      // Skip if already checked
      if (checkedUrls.has(absoluteUrl)) {
        continue;
      }

      checkedUrls.add(absoluteUrl);
      checkedCount++;

      const response = await fetch(absoluteUrl, { method: 'HEAD' });

      if (response.status >= 400) {
        brokenLinks.push({
          url: absoluteUrl,
          statusCode: response.status,
          description: `Broken link with status ${response.status}`,
        });
      }
    } catch (error) {
      brokenLinks.push({
        url: href,
        description: `Failed to fetch: ${error.message}`,
      });
    }
  }

  return brokenLinks;
}

function extractKeywords(document) {
  // Get the text content of the page
  const bodyText = document.body.textContent || '';
  const cleanText = bodyText.toLowerCase().replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim();

  // Split into words
  const words = cleanText.split(' ');

  // Count word frequency
  const wordCounts = {};
  words.forEach(word => {
    if (word.length > 3) { // Skip short words
      wordCounts[word] = (wordCounts[word] || 0) + 1;
    }
  });

  // Convert to array and sort by frequency
  const sortedWords = Object.entries(wordCounts)
    .filter(([word, count]) => count > 2) // Only include words that appear more than twice
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10) // Get top 10 keywords
    .map(([word]) => ({ keyword: word }));

  return sortedWords;
}

function calculateHealthScore(results) {
  let score = 100;

  // Deduct points for issues based on severity
  results.issues.forEach(issue => {
    if (issue.severity === 'critical') {
      score -= 20;
    } else if (issue.severity === 'high') {
      score -= 10;
    } else if (issue.severity === 'medium') {
      score -= 5;
    } else if (issue.severity === 'low') {
      score -= 2;
    }
  });

  // Deduct points for broken links
  score -= results.brokenLinks.length * 5;

  // Ensure score doesn't go below 0
  return Math.max(0, score);
}

// Run the main function
main().catch(e => {
  console.error(e);
  process.exit(1);
});
