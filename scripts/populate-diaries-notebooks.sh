#!/bin/bash

# <PERSON>ript to populate diaries and notebooks pricing data
# Usage: ./scripts/populate-diaries-notebooks.sh

# Exit on error
set -e

echo "🔄 Starting diaries and notebooks pricing data population..."

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Run the seed script
echo "🌱 Seeding diaries and notebooks pricing data..."
node scripts/seed-diaries-notebooks.js

echo "✅ Diaries and notebooks pricing data population completed successfully!"
