const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function verifyPassword(password, hashedPassword) {
  return bcrypt.compare(password, hashedPassword);
}

async function main() {
  try {
    console.log('Testing authentication...');
    
    // Test credentials
    const username = '<EMAIL>';
    const password = 'Jack75522r';
    
    // Find user
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { username },
          { email: username }
        ],
        active: true
      },
      include: {
        role: true
      }
    });
    
    if (!user) {
      console.error('User not found!');
      return;
    }
    
    console.log(`Found user: ${user.username} (${user.email})`);
    console.log(`Role: ${user.role.name}`);
    
    // Verify password
    const isPasswordValid = await verifyPassword(password, user.passwordHash);
    
    if (isPasswordValid) {
      console.log('Password verification successful!');
      
      // Log activity
      await prisma.activityLog.create({
        data: {
          userId: user.id,
          action: 'test_login',
          details: 'Test login from authentication script',
          ipAddress: '127.0.0.1',
          userAgent: 'Test Script',
        }
      });
      
      console.log('Activity log created successfully!');
    } else {
      console.error('Password verification failed!');
    }
    
    // Test permission check
    const hasAdminPermission = user.role.permissions.includes('*');
    console.log(`Has admin permission: ${hasAdminPermission}`);
    
    const hasReadBlogPermission = user.role.permissions.includes('blog:read') || user.role.permissions.includes('*');
    console.log(`Has blog:read permission: ${hasReadBlogPermission}`);
    
  } catch (error) {
    console.error('Error testing authentication:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
