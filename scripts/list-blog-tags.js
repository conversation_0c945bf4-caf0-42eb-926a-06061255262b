const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Listing all blog posts with their tags...');
    console.log('Connecting to database...');

    // First check if we can count blog posts
    const count = await prisma.blogPost.count();
    console.log(`Total blog posts in database: ${count}`);

    if (count === 0) {
      console.log('No blog posts found in the database.');
      return;
    }

    console.log('Fetching blog posts with tags...');
    const blogPosts = await prisma.blogPost.findMany({
      select: {
        id: true,
        title: true,
        category: true,
        tags: true
      }
    });

    console.log(`Found ${blogPosts.length} blog posts:`);

    blogPosts.forEach(post => {
      console.log(`\n- ${post.title}`);
      console.log(`  Category: ${post.category || 'None'}`);
      console.log(`  Tags (${post.tags.length}): ${post.tags.length > 0 ? post.tags.join(', ') : 'No tags'}`);
    });

    // Collect all unique tags
    const allTags = new Set();
    blogPosts.forEach(post => {
      post.tags.forEach(tag => allTags.add(tag));
    });

    console.log(`\nTotal unique tags used across all posts: ${allTags.size}`);
    if (allTags.size > 0) {
      console.log('All unique tags:');
      console.log(Array.from(allTags).sort().join(', '));
    }

  } catch (error) {
    console.error('Error listing blog posts and tags:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
