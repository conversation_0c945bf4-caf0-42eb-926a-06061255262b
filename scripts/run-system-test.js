/**
 * Command-line script to run system tests
 * 
 * This script provides a command-line interface to run the system tests
 * that are available in the admin interface.
 * 
 * Usage: node scripts/run-system-test.js [test-type]
 * 
 * Available test types:
 * - s3_connection: Test connection to S3 storage
 * - s3_upload: Test uploading a file to S3 storage
 * - database: Test database connection
 * - components: Test UI components
 * - api: Test API endpoints
 * - openai: Test OpenAI integration
 * 
 * If no test type is specified, it will run all tests.
 */

require('dotenv').config();
const { 
  TestType, 
  testS3Connection, 
  testS3Upload, 
  testDatabaseConnection, 
  testComponents,
  testApiEndpoints,
  testOpenAI
} = require('../src/utils/testUtils');

// Get the test type from the command line arguments
const testType = process.argv[2];

// Map of test types to test functions
const testFunctions = {
  [TestType.S3_CONNECTION]: testS3Connection,
  [TestType.S3_UPLOAD]: testS3Upload,
  [TestType.DATABASE]: testDatabaseConnection,
  [TestType.COMPONENTS]: testComponents,
  [TestType.API]: testApiEndpoints,
  [TestType.OPENAI]: testOpenAI,
};

// Function to run a test and display the results
async function runTest(type, func) {
  console.log(`\n🧪 Running ${type} test...`);
  
  try {
    const startTime = Date.now();
    const result = await func();
    const duration = Date.now() - startTime;
    
    if (result.success) {
      console.log(`✅ Test passed (${(duration / 1000).toFixed(2)}s): ${result.message}`);
    } else {
      console.log(`❌ Test failed (${(duration / 1000).toFixed(2)}s): ${result.message}`);
    }
    
    if (result.details) {
      console.log('\nDetails:');
      console.log(JSON.stringify(result.details, null, 2));
    }
    
    return result.success;
  } catch (error) {
    console.error(`❌ Error running test: ${error.message}`);
    return false;
  }
}

// Main function
async function main() {
  console.log('🔍 System Test Runner');
  console.log('====================');
  
  // If a specific test type is specified, run only that test
  if (testType && testFunctions[testType]) {
    const success = await runTest(testType, testFunctions[testType]);
    process.exit(success ? 0 : 1);
  } 
  // If "all" is specified or no test type is specified, run all tests
  else if (!testType || testType === 'all') {
    console.log('Running all tests...');
    
    let allPassed = true;
    
    for (const [type, func] of Object.entries(testFunctions)) {
      const success = await runTest(type, func);
      if (!success) {
        allPassed = false;
      }
    }
    
    console.log(`\n${allPassed ? '✅ All tests passed!' : '❌ Some tests failed!'}`);
    process.exit(allPassed ? 0 : 1);
  } 
  // If an invalid test type is specified, show an error
  else {
    console.error(`❌ Invalid test type: ${testType}`);
    console.log('\nAvailable test types:');
    Object.keys(testFunctions).forEach(type => {
      console.log(`- ${type}`);
    });
    process.exit(1);
  }
}

// Run the main function
main().catch(error => {
  console.error('Unexpected error:', error);
  process.exit(1);
});
