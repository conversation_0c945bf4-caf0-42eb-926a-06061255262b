#!/bin/bash

# Define paths
APP_DIR="/var/www/mocky.co.ke"
PUBLIC_DIR="$APP_DIR/public"
IMAGES_DIR="$PUBLIC_DIR/images"

echo "Setting up image directories and permissions..."

# Create image directories using the new structure
mkdir -p "$IMAGES_DIR/portfolio/logos"
mkdir -p "$IMAGES_DIR/portfolio/branding"
mkdir -p "$IMAGES_DIR/portfolio/fliers"
mkdir -p "$IMAGES_DIR/portfolio/cards"
mkdir -p "$IMAGES_DIR/portfolio/letterheads"
mkdir -p "$IMAGES_DIR/portfolio/profiles"
mkdir -p "$IMAGES_DIR/portfolio/websites"

# Create content directories
mkdir -p "$IMAGES_DIR/content/about"
mkdir -p "$IMAGES_DIR/content/services"
mkdir -p "$IMAGES_DIR/content/testimonials"
mkdir -p "$IMAGES_DIR/content/hero"
mkdir -p "$IMAGES_DIR/content/projects"

# Set ownership to deployer and www-data group
chown -R deployer:www-data "$IMAGES_DIR"

# Set directory permissions to 775 (rwxrwxr-x)
find "$IMAGES_DIR" -type d -exec chmod 775 {} \;

# Set file permissions to 664 (rw-rw-r--)
find "$IMAGES_DIR" -type f -exec chmod 664 {} \;

# Ensure nginx can read everything
chmod -R o+r "$IMAGES_DIR"

echo "Done! Directories created and permissions set."