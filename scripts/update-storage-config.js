const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function updateStorageConfig() {
  try {
    // Find the default storage config
    const existingConfig = await prisma.storageConfig.findFirst({
      where: { isDefault: true }
    });

    if (!existingConfig) {
      console.log('No default storage configuration found. Cannot update.');
      return;
    }
    
    console.log('Found existing configuration:', existingConfig.id);
    
    // Simulate changing the bucket name in the admin panel
    // This change simulates what would happen when a user updates the config in the UI
    const updatedConfig = await prisma.storageConfig.update({
      where: { id: existingConfig.id },
      data: {
        // Only changing the bucket name to simulate admin UI change
        bucketName: 'new-bucket-name',
      }
    });
    
    console.log('Updated storage configuration:');
    console.log({
      id: updatedConfig.id,
      provider: updatedConfig.provider,
      region: updatedConfig.region,
      endpoint: updatedConfig.endpoint,
      bucketName: updatedConfig.bucketName, // This should now be 'new-bucket-name'
      accessKeyId: updatedConfig.accessKeyId.substring(0, 4) + '...',
      isDefault: updatedConfig.isDefault
    });
    
  } catch (error) {
    console.error('Error updating storage configuration:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateStorageConfig(); 