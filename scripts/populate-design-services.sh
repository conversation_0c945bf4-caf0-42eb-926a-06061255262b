#!/bin/bash

# Script to populate design services pricing data
# Usage: ./scripts/populate-design-services.sh

# Exit on error
set -e

echo "🔄 Starting design services pricing data population..."

# Create a backup of the current database
echo "📦 Creating database backup..."
BACKUP_FILE="backups/backup_before_pricing_seed_$(date +%Y%m%d_%H%M%S).sql"
sudo -u postgres pg_dump mocky > "$BACKUP_FILE"
echo "✅ Database backup created at $BACKUP_FILE"

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Run the seed script
echo "🌱 Seeding design services pricing data..."
node scripts/seed-design-services.js

echo "✅ Design services pricing data population completed successfully!"
