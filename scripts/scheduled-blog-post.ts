import cron from 'node-cron';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import { PrismaClient } from '@prisma/client';
import { generateAndCreateBlogPost } from './utils/blogAutomation';

// Load environment variables
dotenv.config();

// Initialize Prisma client
const prisma = new PrismaClient();

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Log file path
const logFilePath = path.join(logsDir, 'scheduled-blog-posts.log');

/**
 * Log a message to the console and to a file
 * @param message The message to log
 */
function log(message: string) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;

  console.log(logMessage);

  // Append to log file
  fs.appendFileSync(logFilePath, logMessage + '\n');
}

/**
 * Generate and publish a blog post
 */
async function generateAndPublishBlogPost() {
  try {
    log('Starting automated blog post generation...');

    // Check if there's a configuration for today's post
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Check if there's a scheduled post configuration
    const scheduledPost = await prisma.scheduledBlogPost.findFirst({
      where: {
        scheduledDate: {
          gte: today,
          lt: tomorrow
        }
      }
    });

    // Generate and publish the blog post
    if (scheduledPost) {
      log(`Found scheduled post configuration: ${JSON.stringify(scheduledPost)}`);

      const blogPost = await generateAndCreateBlogPost({
        category: scheduledPost.category || undefined,
        tone: scheduledPost.tone as any || 'professional',
        length: scheduledPost.length as any || 'medium',
        targetAudience: scheduledPost.targetAudience || undefined,
        publishImmediately: true
      });

      if (blogPost) {
        log(`Successfully generated and published blog post: "${blogPost.title}" (ID: ${blogPost.id})`);

        // Update the scheduled post status
        await prisma.scheduledBlogPost.update({
          where: { id: scheduledPost.id },
          data: { status: 'completed', blogPostId: blogPost.id }
        });
      } else {
        log('Failed to generate and publish blog post');

        // Update the scheduled post status
        await prisma.scheduledBlogPost.update({
          where: { id: scheduledPost.id },
          data: { status: 'failed' }
        });
      }
    } else {
      // No scheduled post configuration, generate with default settings
      log('No scheduled post configuration found, using default settings');

      const blogPost = await generateAndCreateBlogPost({
        publishImmediately: true
      });

      if (blogPost) {
        log(`Successfully generated and published blog post: "${blogPost.title}" (ID: ${blogPost.id})`);
      } else {
        log('Failed to generate and publish blog post');
      }
    }

    log('Automated blog post generation completed');
  } catch (error) {
    log(`Error in automated blog post generation: ${error instanceof Error ? error.message : String(error)}`);
    log(`Stack trace: ${error instanceof Error ? error.stack : 'No stack trace available'}`);
  } finally {
    // Close Prisma client
    await prisma.$disconnect();
  }
}

// If this script is run directly (not imported)
if (require.main === module) {
  // Check if this is a one-time run or scheduled task
  const args = process.argv.slice(2);
  const runOnce = args.includes('--run-once');

  if (runOnce) {
    // Run once and exit
    log('Running blog post generation once...');
    generateAndPublishBlogPost()
      .then(() => {
        log('Blog post generation completed');
        process.exit(0);
      })
      .catch((error) => {
        log(`Error: ${error instanceof Error ? error.message : String(error)}`);
        process.exit(1);
      });
  } else {
    // Schedule the task to run daily at 6:00 AM
    log('Starting scheduled blog post generation...');
    log('Blog posts will be generated daily at 6:00 AM');

    cron.schedule('0 6 * * *', () => {
      generateAndPublishBlogPost()
        .catch((error) => {
          log(`Scheduled task error: ${error instanceof Error ? error.message : String(error)}`);
        });
    });

    // Keep the process running
    log('Scheduler is running. Press Ctrl+C to exit.');
  }
}

// Export for testing and importing in other files
export { generateAndPublishBlogPost };
