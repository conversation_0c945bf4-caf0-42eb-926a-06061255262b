// This script seeds the database with sample blog posts
require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Sample blog posts data
const sampleBlogPosts = [
  {
    title: 'Getting Started with Web Design',
    slug: 'getting-started-with-web-design',
    content: '<p>Learn the basics of web design and how to create your first website. This comprehensive guide covers everything from HTML and CSS to responsive design principles.</p><h2>Understanding the Fundamentals</h2><p>Before diving into web design, it\'s important to understand the core technologies that power the web:</p><ul><li>HTML - The structure of your website</li><li>CSS - The styling and layout</li><li>JavaScript - The interactivity and behavior</li></ul><p>By mastering these three technologies, you\'ll have the foundation needed to create beautiful and functional websites.</p>',
    excerpt: 'Learn the basics of web design and how to create your first website.',
    featuredImage: '/images/blog/web-design.jpg',
    author: '<PERSON>',
    category: 'web-design',
    tags: ['web design', 'html', 'css', 'beginners'],
    status: 'published',
    publishedAt: new Date()
  },
  {
    title: 'The Importance of SEO',
    slug: 'importance-of-seo',
    content: '<p>Discover why SEO is crucial for your website\'s success. This article explains the fundamentals of search engine optimization and provides actionable tips to improve your rankings.</p><h2>Why SEO Matters</h2><p>In today\'s digital landscape, having a website isn\'t enough. You need to ensure that your target audience can find you when they search online. That\'s where SEO comes in.</p><p>Search Engine Optimization (SEO) is the practice of optimizing your website to rank higher in search engine results pages (SERPs). The higher your website ranks, the more visible it is to potential visitors, which can lead to increased traffic and conversions.</p>',
    excerpt: 'Discover why SEO is crucial for your website\'s success.',
    featuredImage: '/images/blog/seo.jpg',
    author: 'John Doe',
    category: 'seo',
    tags: ['seo', 'digital marketing', 'website optimization'],
    status: 'draft',
    publishedAt: null
  },
  {
    title: '5 Logo Design Trends to Watch in 2023',
    slug: 'logo-design-trends-2023',
    content: '<p>Explore the top five logo design trends of 2023, from minimalism 2.0 to sustainable aesthetics, and learn how to keep your brand identity fresh and relevant.</p><h2>1. Minimalism 2.0</h2><p>While minimalism has been a trend for years, in 2023 we\'re seeing a new iteration that combines simplicity with subtle details and textures. This approach maintains the clean look of minimalist logos while adding just enough visual interest to make them memorable.</p><h2>2. Dynamic Logos</h2><p>With the rise of digital platforms, static logos are giving way to dynamic, adaptable designs that can change based on context or user interaction. These responsive logos maintain brand recognition while offering a more engaging experience.</p>',
    excerpt: 'Explore the top five logo design trends of 2023, from minimalism 2.0 to sustainable aesthetics.',
    featuredImage: '/images/blog/logo-design-trends.jpg',
    author: 'Michael Johnson',
    category: 'graphic-design',
    tags: ['logo design', 'branding', 'design trends', 'visual identity'],
    status: 'published',
    publishedAt: new Date()
  }
];

async function seedDatabase() {
  try {
    console.log('Starting database seeding...');

    // Check if we already have blog posts
    const existingPostsCount = await prisma.blogPost.count();
    
    if (existingPostsCount > 0) {
      console.log(`Database already has ${existingPostsCount} blog posts. Skipping seeding.`);
      return;
    }

    // Create sample blog posts
    for (const post of sampleBlogPosts) {
      await prisma.blogPost.create({
        data: {
          title: post.title,
          slug: post.slug,
          content: post.content,
          excerpt: post.excerpt,
          featuredImage: post.featuredImage,
          author: post.author,
          category: post.category,
          tags: post.tags,
          status: post.status,
          publishedAt: post.publishedAt
        }
      });
      console.log(`Created blog post: ${post.title}`);
    }

    console.log('Database seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding function
seedDatabase();
