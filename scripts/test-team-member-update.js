/**
 * Test script for team member update API
 * 
 * This script tests the team member update API by:
 * 1. Creating a test team member
 * 2. Updating the team member with a new image
 * 3. Verifying the update was successful
 * 4. Cleaning up the test team member
 * 
 * Usage: node scripts/test-team-member-update.js
 */

const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');
const FormData = require('form-data');
const { v4: uuidv4 } = require('uuid');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
const TEST_IMAGE_PATH = path.join(__dirname, '../public/images/default-profile.jpg');
const TEST_ID = uuidv4();

// Test data
const testTeamMember = {
  name: 'Test User',
  role: 'Test Role',
  bio: 'This is a test user created for automated testing.',
  order: 999, // High number to keep it at the end
  linkedinUrl: 'https://linkedin.com/test',
  twitterUrl: 'https://twitter.com/test',
  githubUrl: 'https://github.com/test',
  emailAddress: '<EMAIL>',
};

console.log('Testing Team Member Update API:');
console.log(`  API Base URL: ${API_BASE_URL}`);
console.log(`  Test Image: ${TEST_IMAGE_PATH}`);
console.log(`  Test ID: ${TEST_ID}`);
console.log('');

async function runTests() {
  try {
    // Check if test image exists
    if (!fs.existsSync(TEST_IMAGE_PATH)) {
      console.error(`❌ Test image not found: ${TEST_IMAGE_PATH}`);
      process.exit(1);
    }

    console.log('🔍 Starting team member update tests...');
    
    // Test 1: Create test team member
    console.log('\n🧪 TEST 1: Create test team member');
    let teamMemberId;
    
    try {
      const formData = new FormData();
      Object.entries(testTeamMember).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          formData.append(key, value);
        }
      });
      
      // Add test image
      const imageBuffer = fs.readFileSync(TEST_IMAGE_PATH);
      formData.append('image', imageBuffer, {
        filename: 'test-profile.jpg',
        contentType: 'image/jpeg',
      });
      
      console.log(`⏳ Creating test team member...`);
      const createResponse = await fetch(`${API_BASE_URL}/admin/team`, {
        method: 'POST',
        body: formData,
      });
      
      if (!createResponse.ok) {
        const errorData = await createResponse.json();
        throw new Error(`Failed to create team member: ${errorData.error || createResponse.statusText}`);
      }
      
      const createData = await createResponse.json();
      teamMemberId = createData.id;
      console.log(`✅ Created test team member with ID: ${teamMemberId}`);
    } catch (error) {
      console.error(`❌ Create test failed: ${error.message}`);
      process.exit(1);
    }
    
    // Test 2: Update team member
    console.log('\n🧪 TEST 2: Update team member');
    
    try {
      const formData = new FormData();
      
      // Update some fields
      formData.append('name', `${testTeamMember.name} (Updated)`);
      formData.append('role', testTeamMember.role);
      formData.append('bio', `${testTeamMember.bio} This bio was updated.`);
      formData.append('order', testTeamMember.order);
      
      // Add test image
      const imageBuffer = fs.readFileSync(TEST_IMAGE_PATH);
      formData.append('image', imageBuffer, {
        filename: 'updated-profile.jpg',
        contentType: 'image/jpeg',
      });
      
      console.log(`⏳ Updating team member ${teamMemberId}...`);
      const updateResponse = await fetch(`${API_BASE_URL}/admin/team/${teamMemberId}`, {
        method: 'PUT',
        body: formData,
      });
      
      const updateData = await updateResponse.json();
      
      if (!updateResponse.ok) {
        throw new Error(`Failed to update team member: ${updateData.error || updateResponse.statusText}`);
      }
      
      if (updateData.warning) {
        console.warn(`⚠️ Update warning: ${updateData.warning}`);
      }
      
      console.log(`✅ Updated team member successfully`);
    } catch (error) {
      console.error(`❌ Update test failed: ${error.message}`);
      // Continue to cleanup even if update fails
    }
    
    // Test 3: Clean up
    console.log('\n🧪 TEST 3: Clean up test team member');
    
    try {
      console.log(`⏳ Deleting team member ${teamMemberId}...`);
      const deleteResponse = await fetch(`${API_BASE_URL}/admin/team/${teamMemberId}`, {
        method: 'DELETE',
      });
      
      if (!deleteResponse.ok) {
        const errorData = await deleteResponse.json();
        throw new Error(`Failed to delete team member: ${errorData.error || deleteResponse.statusText}`);
      }
      
      console.log(`✅ Deleted test team member successfully`);
    } catch (error) {
      console.error(`❌ Cleanup failed: ${error.message}`);
      // Continue even if cleanup fails
    }

    console.log('\n✅ All tests completed!');
  } catch (error) {
    console.error(`\n❌ Tests failed with error: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run the tests
runTests();
