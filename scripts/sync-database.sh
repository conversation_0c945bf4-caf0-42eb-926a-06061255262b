#!/bin/bash

# Database synchronization script for <PERSON>cky
# This script ensures the database schema matches the Prisma schema
# Usage: ./scripts/sync-database.sh

# Exit on error
set -e

echo "🔄 Starting database synchronization..."

# Get current timestamp for backup naming
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="./backups"
mkdir -p $BACKUP_DIR

# Load environment variables from .env file
if [ -f .env ]; then
  export $(grep -v '^#' .env | grep DATABASE_URL | tail -n 1 | xargs)
fi

if [ -z "$DATABASE_URL" ]; then
  echo "⚠️  DATABASE_URL environment variable not set. Using default connection string."
  export DATABASE_URL="postgresql://don:password@localhost:5432/mocky?schema=public"
fi

# Extract connection details from the DATABASE_URL
DB_USER=$(echo $DATABASE_URL | sed -n 's/^postgresql:\/\/\([^:]*\):.*/\1/p')
DB_PASSWORD=$(echo $DATABASE_URL | sed -n 's/^postgresql:\/\/[^:]*:\([^@]*\)@.*/\1/p')
DB_HOST=$(echo $DATABASE_URL | sed -n 's/^postgresql:\/\/[^@]*@\([^:]*\):.*/\1/p')
DB_PORT=$(echo $DATABASE_URL | sed -n 's/^postgresql:\/\/[^:]*:[^@]*@[^:]*:\([0-9]*\)\/.*/\1/p')
DB_NAME=$(echo $DATABASE_URL | sed -n 's/^postgresql:\/\/[^:]*:[^@]*@[^:]*:[0-9]*\/\([^?]*\).*/\1/p')

echo "Using database connection:"
echo "  Host: $DB_HOST"
echo "  Port: $DB_PORT"
echo "  Database: $DB_NAME"
echo "  User: $DB_USER"

# 1. Create a backup of the database
echo "📦 Creating database backup..."
PGPASSWORD=$DB_PASSWORD pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -F c -f "$BACKUP_DIR/backup_$TIMESTAMP.dump"
echo "✅ Database backup created at $BACKUP_DIR/backup_$TIMESTAMP.dump"

# 2. Check for failed migrations and resolve them
echo "🔍 Checking for failed migrations..."
# First check if migration_steps_count column exists
COLUMN_EXISTS=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT column_name FROM information_schema.columns WHERE table_name='_prisma_migrations' AND column_name='migration_steps_count';")

if [ -n "$COLUMN_EXISTS" ]; then
  FAILED_MIGRATIONS=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT migration_name FROM _prisma_migrations WHERE applied_steps_count < migration_steps_count;")
else
  echo "⚠️  Column 'migration_steps_count' does not exist in _prisma_migrations table. Skipping failed migration check."
  FAILED_MIGRATIONS=""
fi

if [ -n "$FAILED_MIGRATIONS" ]; then
  echo "⚠️  Found failed migrations:"
  echo "$FAILED_MIGRATIONS"

  # In non-interactive mode, automatically mark failed migrations as rolled back
  echo "🔄 Automatically marking failed migrations as rolled back..."
  for migration in $FAILED_MIGRATIONS; do
    echo "🔄 Marking migration $migration as rolled back..."
    npx prisma migrate resolve --rolled-back $migration
  done
else
  echo "✅ No failed migrations found."
fi

# 3. Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# 4. Apply pending migrations
echo "🔄 Applying pending migrations..."
npx prisma migrate deploy

# 5. Verify database schema
echo "🔍 Verifying database schema..."
npx prisma db pull --print > schema_current.prisma
echo "✅ Current database schema saved to schema_current.prisma"

# 6. Print summary
echo ""
echo "✅ Database synchronization completed!"
echo "📋 Summary:"
echo "  - Database backup: $BACKUP_DIR/backup_$TIMESTAMP.dump"
echo "  - Prisma client generated"
echo "  - Migrations applied"
echo "  - Schema verification completed"
echo ""
echo "🔄 To rollback database (if needed): pg_restore -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME $BACKUP_DIR/backup_$TIMESTAMP.dump"
