const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Testing activity logging functionality...');

    // Get admin user
    const adminUser = await prisma.user.findFirst({
      where: { username: '<EMAIL>' }
    });

    if (!adminUser) {
      console.error('Admin user not found!');
      return;
    }

    console.log(`Found admin user: ${adminUser.username}`);

    // Create a test activity log
    const activityLog = await prisma.activityLog.create({
      data: {
        userId: adminUser.id,
        action: 'test_activity',
        details: 'Testing activity logging functionality',
        ipAddress: '127.0.0.1',
        userAgent: 'Test Script',
        resourceType: 'test',
        resourceId: 'test-123'
      }
    });

    console.log(`✅ Created activity log with ID: ${activityLog.id}`);

    // Retrieve the activity log
    const retrievedLog = await prisma.activityLog.findUnique({
      where: { id: activityLog.id },
      include: { user: true }
    });

    if (retrievedLog) {
      console.log(`✅ Retrieved activity log: ${retrievedLog.action} by ${retrievedLog.user.username}`);
      console.log(`✅ Details: ${retrievedLog.details}`);
      console.log(`✅ IP Address: ${retrievedLog.ipAddress}`);
      console.log(`✅ User Agent: ${retrievedLog.userAgent}`);
      console.log(`✅ Resource: ${retrievedLog.resourceType}:${retrievedLog.resourceId}`);
      console.log(`✅ Created At: ${retrievedLog.createdAt}`);
    } else {
      console.error('❌ Failed to retrieve activity log!');
    }

    // Get all activity logs
    const allLogs = await prisma.activityLog.findMany({
      orderBy: { createdAt: 'desc' },
      take: 5,
      include: { user: true }
    });

    console.log(`\nFound ${allLogs.length} recent activity logs:`);
    allLogs.forEach((log, index) => {
      console.log(`${index + 1}. ${log.action} by ${log.user.username} at ${log.createdAt}`);
    });

    console.log('\nActivity logging tests completed!');
  } catch (error) {
    console.error('Error testing activity logging:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
