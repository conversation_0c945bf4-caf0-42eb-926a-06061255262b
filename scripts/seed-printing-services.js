// Script to seed printing services pricing data
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

// Initialize Prisma client
const prisma = new PrismaClient();

// Define printing services with pricing
const printingServices = [
  // Basic printing services from user input
  {
    service: 'Large Format Printing',
    price: 600, // Starting price
    description: 'High-quality large format printing for banners, posters, and signage',
    features: [
      'Starting from Ksh. 600',
      'High-resolution printing',
      'Various material options',
      'Weather-resistant options',
      'Quick turnaround time',
      'Custom sizes available'
    ],
    icon: 'print',
    popular: false
  },
  {
    service: 'Rollup Banners',
    price: 7000, // Starting price
    description: 'Professional rollup banners for exhibitions, events, and promotions',
    features: [
      'Starting from Ksh. 7,000',
      'Durable aluminum stand',
      'High-quality print',
      'Portable carrying case',
      'Easy setup and dismantling',
      'Various sizes available'
    ],
    icon: 'scroll',
    popular: true
  },
  {
    service: 'Flyers',
    price: 1000, // Price for minimum order (50 flyers at Ksh. 20 each)
    description: 'Professional flyer printing for effective marketing campaigns',
    features: [
      'Ksh. 20 per flyer',
      'Minimum order: 50 flyers',
      'High-quality paper options',
      'Full-color printing',
      'Single or double-sided',
      'Fast turnaround time'
    ],
    icon: 'file-alt',
    popular: false
  },
  {
    service: 'Brochures (A4)',
    price: 2500, // Price for minimum order (50 brochures at Ksh. 50 each)
    description: 'Professional A4 brochure printing for detailed product or service information',
    features: [
      'Ksh. 50 per brochure',
      'Minimum order: 50 brochures',
      'High-quality paper stock',
      'Full-color printing',
      'Tri-fold or bi-fold options',
      'Professional finishing'
    ],
    icon: 'newspaper',
    popular: false
  },
  {
    service: 'Business Cards',
    price: 1500, // Price for minimum order (100 cards at Ksh. 15 each)
    description: 'Professional business card printing to make a lasting impression',
    features: [
      'Ksh. 15-25 per card',
      'Minimum order: 100 cards',
      'Premium card stock',
      'Full-color printing',
      'Various finishing options',
      'Standard or custom sizes'
    ],
    icon: 'id-card',
    popular: true
  },

  // Banner products from sawaprint.com
  {
    service: 'Broad-base Roll-up Banner',
    price: 7500,
    description: 'Professional broad-base roll-up banner for exhibitions and events',
    features: [
      'Stable & sturdy design',
      'High-resolution printing',
      'Durable & tear-resistant material',
      'Retractable & reusable',
      'Premium & professional look',
      'Includes carrying case'
    ],
    icon: 'scroll',
    popular: false
  },
  {
    service: 'Telescopic Banners',
    price: 19000,
    description: 'Adjustable height telescopic banners for outdoor events and promotions',
    features: [
      'Adjustable height mechanism',
      'Weather-resistant materials',
      'High visibility design',
      'Easy assembly and disassembly',
      'Ideal for outdoor events',
      'Portable and lightweight'
    ],
    icon: 'flag',
    popular: true
  },
  {
    service: 'Adjustable Backdrop Media Banners (3M x 2M)',
    price: 18500,
    description: 'Professional backdrop media banners for events, exhibitions, and photo shoots',
    features: [
      'Large 3M x 2M size',
      'Adjustable frame',
      'High-resolution printing',
      'Easy to transport and set up',
      'Perfect for event backdrops',
      'Durable construction'
    ],
    icon: 'expand',
    popular: true
  },
  {
    service: 'Custom Door Frame Banners',
    price: 7300,
    description: 'Custom door frame banners for retail stores, exhibitions, and events',
    features: [
      'Custom-fit for standard door frames',
      'High-quality printing',
      'Easy installation',
      'Attention-grabbing design',
      'Ideal for retail promotions',
      'Durable materials'
    ],
    icon: 'door-open',
    popular: false
  },
  {
    service: 'Broad Base Media Banner (2M × 2M)',
    price: 15600,
    description: 'Professional broad base media banner for trade shows and exhibitions',
    features: [
      '2M x 2M size',
      'Sturdy broad base for stability',
      'High-quality printing',
      'Easy setup and transport',
      'Ideal for indoor events',
      'Reusable design'
    ],
    icon: 'expand-arrows-alt',
    popular: false
  },
  {
    service: 'Collapsible Backdrop Stand & Media Banners',
    price: 17800,
    description: 'Collapsible backdrop stand with media banner for events and exhibitions',
    features: [
      'Collapsible design for easy transport',
      'Quick setup and dismantling',
      'High-quality printed banner',
      'Sturdy construction',
      'Ideal for trade shows',
      'Includes carrying case'
    ],
    icon: 'compress-arrows-alt',
    popular: false
  },
  {
    service: 'Narrow Base Roll-Up Banners',
    price: 5800,
    description: 'Compact narrow base roll-up banners for limited space environments',
    features: [
      'Space-saving narrow base',
      'High-quality printing',
      'Portable and lightweight',
      'Easy setup',
      'Includes carrying case',
      'Ideal for retail displays'
    ],
    icon: 'compress',
    popular: false
  },
  {
    service: 'X-Banner Stands',
    price: 5400,
    description: 'Lightweight X-banner stands for quick and easy display setup',
    features: [
      'X-shaped support structure',
      'Quick and easy assembly',
      'Lightweight and portable',
      'High-quality banner printing',
      'Affordable display solution',
      'Includes carrying bag'
    ],
    icon: 'times',
    popular: false
  },
  {
    service: 'Teardrop Banner',
    price: 12500,
    description: 'Eye-catching teardrop banners for outdoor events and promotions',
    features: [
      'Distinctive teardrop shape',
      'Weather-resistant materials',
      'Double-sided printing option',
      'Includes ground stake',
      'Durable fiberglass poles',
      'High visibility in wind'
    ],
    icon: 'tint',
    popular: true
  }
];

// Main function to seed the database
async function seedPrintingServices() {
  try {
    console.log('Starting to seed printing services pricing data...');

    // Get existing pricing items
    const existingPricing = await prisma.pricing.findMany();
    console.log(`Found ${existingPricing.length} existing pricing items`);

    // Create a map of existing services (case-insensitive)
    const existingServicesMap = new Map();
    existingPricing.forEach(item => {
      existingServicesMap.set(item.service.toLowerCase(), item);
    });

    let created = 0;
    let updated = 0;

    // Process each service
    for (const service of printingServices) {
      // Check if service already exists (case-insensitive)
      const existingService = existingServicesMap.get(service.service.toLowerCase());

      if (existingService) {
        // Update existing service
        await prisma.pricing.update({
          where: { id: existingService.id },
          data: {
            price: service.price,
            description: service.description,
            features: service.features,
            icon: service.icon,
            popular: service.popular
          }
        });
        updated++;
        console.log(`Updated existing service: ${service.service}`);
      } else {
        // Create new service
        await prisma.pricing.create({
          data: {
            service: service.service,
            price: service.price,
            description: service.description,
            features: service.features,
            icon: service.icon,
            popular: service.popular
          }
        });
        created++;
        console.log(`Created new service: ${service.service}`);
      }
    }

    console.log(`Successfully processed printing services pricing data:`);
    console.log(`- Created: ${created} new services`);
    console.log(`- Updated: ${updated} existing services`);
    console.log(`- Total: ${printingServices.length} services processed`);

  } catch (error) {
    console.error('Error seeding printing services pricing data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedPrintingServices()
  .catch((error) => {
    console.error('Error running seed script:', error);
    process.exit(1);
  });
