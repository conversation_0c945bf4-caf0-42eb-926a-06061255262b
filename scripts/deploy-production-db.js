/**
 * Production Database Deployment Script
 * This script deploys database migrations to production
 */
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

async function deployDatabase() {
  try {
    console.log('🚀 Deploying database migrations to production...');
    
    // Generate Prisma client
    console.log('📦 Generating Prisma client...');
    const { stdout: generateOutput, stderr: generateError } = await execAsync('npx prisma generate');
    if (generateError) {
      console.error('❌ Error generating Prisma client:', generateError);
      return;
    }
    console.log('✅ Prisma client generated successfully');
    
    // Deploy migrations
    console.log('🔄 Deploying migrations...');
    const { stdout: migrateOutput, stderr: migrateError } = await execAsync('npx prisma migrate deploy');
    if (migrateError) {
      console.error('❌ Error deploying migrations:', migrateError);
      return;
    }
    console.log('✅ Migrations deployed successfully');
    console.log(migrateOutput);
    
    // Verify deployment
    console.log('🔍 Verifying deployment...');
    const { exec: execSync } = require('child_process');
    execSync('node scripts/check-production-db.js', (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Verification failed:', error);
        return;
      }
      console.log(stdout);
    });
    
  } catch (error) {
    console.error('❌ Deployment failed:', error);
  }
}

deployDatabase();
