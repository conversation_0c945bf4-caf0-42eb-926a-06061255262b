// <PERSON>ript to check and fix StorageConfig in the database
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('Checking StorageConfig in the database...');

  try {
    // Check if there's any StorageConfig entry
    const existingConfig = await prisma.storageConfig.findFirst({
      where: { isDefault: true },
    });

    if (existingConfig) {
      console.log('Found existing default StorageConfig:', {
        id: existingConfig.id,
        provider: existingConfig.provider,
        region: existingConfig.region,
        bucketName: existingConfig.bucketName,
        endpoint: existingConfig.endpoint,
        isDefault: existingConfig.isDefault,
      });

      // Check if the config has valid values
      const hasValidCredentials = 
        existingConfig.accessKeyId && 
        existingConfig.accessKeyId.length > 5 &&
        existingConfig.secretAccessKey && 
        existingConfig.secretAccessKey.length > 5;

      if (!hasValidCredentials) {
        console.log('Existing config has invalid credentials. Updating...');
        
        // Update with environment variables
        const updatedConfig = await prisma.storageConfig.update({
          where: { id: existingConfig.id },
          data: {
            provider: 'S3',
            region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
            endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
            bucketName: process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky',
            accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
            secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
            isDefault: true,
          },
        });

        console.log('Updated StorageConfig with environment variables:', {
          id: updatedConfig.id,
          provider: updatedConfig.provider,
          region: updatedConfig.region,
          bucketName: updatedConfig.bucketName,
          endpoint: updatedConfig.endpoint,
          isDefault: updatedConfig.isDefault,
        });
      } else {
        console.log('Existing config has valid credentials. No update needed.');
      }
    } else {
      console.log('No default StorageConfig found. Creating new entry...');

      // Create new config from environment variables
      const newConfig = await prisma.storageConfig.create({
        data: {
          provider: 'S3',
          region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
          endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
          bucketName: process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky',
          accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
          secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
          isDefault: true,
        },
      });

      console.log('Created new StorageConfig:', {
        id: newConfig.id,
        provider: newConfig.provider,
        region: newConfig.region,
        bucketName: newConfig.bucketName,
        endpoint: newConfig.endpoint,
        isDefault: newConfig.isDefault,
      });
    }

    // Verify environment variables
    console.log('\nVerifying environment variables:');
    console.log('NEXT_PUBLIC_S3_REGION:', process.env.NEXT_PUBLIC_S3_REGION ? 'Set' : 'Not set');
    console.log('NEXT_PUBLIC_S3_ENDPOINT:', process.env.NEXT_PUBLIC_S3_ENDPOINT ? 'Set' : 'Not set');
    console.log('NEXT_PUBLIC_S3_BUCKET:', process.env.NEXT_PUBLIC_S3_BUCKET ? 'Set' : 'Not set');
    console.log('NEXT_PUBLIC_S3_ACCESS_KEY:', process.env.NEXT_PUBLIC_S3_ACCESS_KEY ? 'Set' : 'Not set');
    console.log('NEXT_PUBLIC_S3_SECRET_KEY:', process.env.NEXT_PUBLIC_S3_SECRET_KEY ? 'Set (length: ' + (process.env.NEXT_PUBLIC_S3_SECRET_KEY?.length || 0) + ')' : 'Not set');

    console.log('\nStorageConfig check completed successfully.');
  } catch (error) {
    console.error('Error checking/fixing StorageConfig:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
