// JavaScript version of the seed-website-portfolio-prisma script
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

// Initialize Prisma client
const prisma = new PrismaClient();

// Define the website portfolio data
const websitePortfolioData = [
  {
    title: 'Homestore.co.ke',
    description: 'Modern E-commerce Platform for Household Items',
    category: 'e-commerce',
    url: 'https://homestore.co.ke',
    imageUrl: 'https://placehold.co/600x400/orange/white?text=Homestore', // Placeholder image URL
  },
  {
    title: 'Galaidh Ltd',
    description: 'IT Solutions & Security Systems Provider',
    category: 'corporate',
    url: 'https://galaidh.com',
    imageUrl: 'https://placehold.co/600x400/blue/white?text=Galaidh',
  },
  {
    title: 'Top23 Security Ltd',
    description: 'Professional Security Services Website',
    category: 'security',
    url: 'https://top23security.com',
    imageUrl: 'https://placehold.co/600x400/red/white?text=Top23+Security',
  },
  {
    title: 'Top23 USA Outlet',
    description: 'E-commerce Store for American Products',
    category: 'e-commerce',
    url: 'https://top23usa.com',
    imageUrl: 'https://placehold.co/600x400/navy/white?text=Top23+USA',
  },
  {
    title: 'Reucher Africa Kenya Ltd',
    description: 'Industrial Chemicals & Water Treatment Solutions',
    category: 'corporate',
    url: 'https://reucherafrica.co.ke',
    imageUrl: 'https://placehold.co/600x400/green/white?text=Reucher+Africa',
  },
  {
    title: 'I4Food Organization',
    description: 'Institute For Food Systems & Climate Research Website',
    category: 'nonprofit',
    url: 'https://i4food.org',
    imageUrl: 'https://placehold.co/600x400/teal/white?text=I4Food',
  },
  {
    title: 'WeShop254',
    description: 'E-commerce Platform for Home & Living Products',
    category: 'e-commerce',
    url: 'https://weshop254.co.ke',
    imageUrl: 'https://placehold.co/600x400/purple/white?text=WeShop254',
  },
  {
    title: 'Knight Swift Logistics',
    description: 'Logistics & Transportation Company Website',
    category: 'logistics',
    url: 'https://knightswiftlogistics.com',
    imageUrl: 'https://placehold.co/600x400/brown/white?text=Knight+Swift',
  },
  {
    title: 'MRL Motors',
    description: 'Automotive Company Website with Modern Design',
    category: 'automotive',
    url: 'https://mrlmotors.com',
    imageUrl: 'https://placehold.co/600x400/gray/white?text=MRL+Motors',
  },
];

// Function to generate a placeholder image URL
function generatePlaceholderImageUrl(title, color = 'orange') {
  const formattedTitle = encodeURIComponent(title.replace(/\s+/g, '+'));
  return `https://placehold.co/600x400/${color}/white?text=${formattedTitle}`;
}

// Main function to seed the database
async function seedWebsitePortfolio() {
  try {
    console.log('Starting to seed website portfolio data...');

    for (const website of websitePortfolioData) {
      // Check if website already exists
      const existingWebsite = await prisma.websitePortfolio.findFirst({
        where: {
          url: website.url,
        },
      });

      if (!existingWebsite) {
        console.log(`Processing website: ${website.title}`);

        try {
          // Generate a placeholder image URL
          const imageSrc = generatePlaceholderImageUrl(website.title,
            website.category === 'e-commerce' ? 'orange' :
            website.category === 'corporate' ? 'blue' :
            website.category === 'security' ? 'red' :
            website.category === 'logistics' ? 'brown' :
            website.category === 'automotive' ? 'gray' :
            website.category === 'nonprofit' ? 'green' : 'purple');

          // Create the website portfolio item in the database
          const newWebsite = await prisma.websitePortfolio.create({
            data: {
              title: website.title,
              description: website.description,
              category: website.category,
              imageSrc: imageSrc,
              url: website.url,
              featured: false,
            },
          });

          console.log(`Added new website: ${newWebsite.title} with image: ${newWebsite.imageSrc}`);
        } catch (error) {
          console.error(`Error processing website ${website.title}:`, error);
        }
      } else {
        console.log(`Website already exists: ${website.title}`);
      }
    }

    const totalWebsites = await prisma.websitePortfolio.count();
    console.log(`Total website portfolio items in database: ${totalWebsites}`);

  } catch (error) {
    console.error('Error seeding website portfolio data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedWebsitePortfolio()
  .catch((error) => {
    console.error('Error running seed script:', error);
    process.exit(1);
  });
