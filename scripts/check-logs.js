const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Checking activity logs...');
    
    // Get all activity logs
    const logs = await prisma.activityLog.findMany({
      orderBy: { createdAt: 'desc' },
      include: { user: true }
    });
    
    console.log(`Found ${logs.length} activity logs:`);
    
    if (logs.length > 0) {
      logs.forEach((log, index) => {
        console.log(`${index + 1}. ${log.action} by ${log.user.username} at ${new Date(log.createdAt).toLocaleString()}`);
        console.log(`   Details: ${log.details || 'None'}`);
        console.log(`   IP: ${log.ipAddress || 'None'}, User Agent: ${log.userAgent ? log.userAgent.substring(0, 30) + '...' : 'None'}`);
        console.log(`   Resource: ${log.resourceType || 'None'}${log.resourceId ? `:${log.resourceId}` : ''}`);
        console.log('---');
      });
    } else {
      console.log('No activity logs found.');
    }
  } catch (error) {
    console.error('Error checking activity logs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
