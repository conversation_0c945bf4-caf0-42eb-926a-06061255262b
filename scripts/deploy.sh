#!/bin/bash

# Deployment script for VPS
# This script pulls the latest changes from GitHub and deploys the application
# Usage: ./scripts/deploy.sh

echo "🚀 Starting deployment process..."

# Pull the latest changes from GitHub
echo "📥 Pulling latest changes from GitHub..."
git pull

# Install dependencies (if needed)
echo "📦 Installing dependencies..."
npm ci

# Build the application
echo "🔨 Building the application..."
npm run build

# Restart the application using PM2
echo "🔄 Restarting the application..."
pm2 restart mocky-digital || pm2 start ecosystem.config.js

# Reload nginx (if configuration has changed)
echo "🔄 Reloading Nginx configuration..."
sudo systemctl reload nginx

echo "✅ Deployment completed successfully!"
echo "   Your application is now live at https://mocky.co.ke" 