# S3 Migration Guide

This guide explains how to migrate your Linode S3 contents from one bucket to another using the provided scripts.

## Prerequisites

- Node.js installed
- Access to both source and destination S3 buckets
- Proper credentials (access key and secret key) for both buckets

## Setup

1. First, run the setup script to configure your destination S3 bucket:

```bash
node scripts/setup-s3-migration.js
```

This script will:
- Read your current S3 configuration from the `.env` file
- Prompt you to enter the destination S3 configuration
- Update the `.env` file with the destination configuration

2. After running the setup script, your `.env` file will contain two sets of S3 configurations:
   - `NEXT_PUBLIC_S3_*` - Source configuration
   - `NEXT_PUBLIC_S3_*_2` - Destination configuration

3. Verify that both configurations are correct in your `.env` file.

## Running the Migration

Once you've set up the configurations, run the migration script:

```bash
node scripts/migrate-s3.js
```

This script will:
1. Connect to both source and destination S3 buckets
2. List all objects in the source bucket
3. Copy each object to the destination bucket
4. Preserve metadata and ACLs
5. Log the progress and results

The migration log will be saved to the `logs` directory.

## Monitoring Progress

The migration script provides real-time progress updates in the console, showing:
- Total number of objects found
- Current progress (percentage and object count)
- Success/failure status for each object
- Summary at the end of the migration

## Troubleshooting

If you encounter any issues during the migration:

1. Check the log file in the `logs` directory for detailed error messages
2. Verify that both S3 configurations are correct
3. Ensure you have proper permissions to read from the source bucket and write to the destination bucket
4. For large migrations, consider running the script in a screen or tmux session to prevent interruptions

## After Migration

After the migration is complete:

1. Verify that all objects were successfully migrated
2. Update your application to use the new S3 configuration
3. Consider keeping the old bucket as a backup for a period of time

## Switching to the New Configuration

To switch your application to use the new S3 bucket:

1. Update your `.env` file to use the new configuration:
   ```
   NEXT_PUBLIC_S3_REGION="your-new-region"
   NEXT_PUBLIC_S3_ENDPOINT="your-new-endpoint"
   NEXT_PUBLIC_S3_BUCKET="your-new-bucket"
   NEXT_PUBLIC_S3_ACCESS_KEY="your-new-access-key"
   NEXT_PUBLIC_S3_SECRET_KEY="your-new-secret-key"
   ```

2. Restart your application to apply the changes:
   ```bash
   pm2 restart all
   ```

## Notes

- The migration process does not delete any objects from the source bucket
- Large files may take longer to migrate
- The script handles pagination, so it can migrate any number of objects
- If the migration is interrupted, you can safely run it again - it will attempt to migrate all objects again
