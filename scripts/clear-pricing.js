// Script to clear all pricing data
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

// Initialize Prisma client
const prisma = new PrismaClient();

async function clearPricingData() {
  try {
    console.log('Starting to clear pricing data...');

    // Get count of existing pricing items
    const count = await prisma.pricing.count();
    console.log(`Found ${count} existing pricing items`);

    if (count === 0) {
      console.log('No pricing items to delete.');
      return;
    }

    // Delete all pricing items
    const result = await prisma.pricing.deleteMany({});
    console.log(`Deleted ${result.count} pricing items`);
    console.log('Pricing data cleared successfully');

  } catch (error) {
    console.error('Error clearing pricing data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the clear function
clearPricingData()
  .catch((error) => {
    console.error('Error running clear script:', error);
    process.exit(1);
  });
