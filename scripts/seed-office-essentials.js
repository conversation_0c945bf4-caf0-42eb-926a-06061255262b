// Script to seed office essentials pricing data
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

// Initialize Prisma client
const prisma = new PrismaClient();

// Define office essentials products with pricing
const officeEssentials = [
  {
    service: 'Rectangular Wall Clocks',
    price: 3780,
    description: 'Elegant rectangular wall clocks available in silver, white, black and gold (35×30 cm)',
    features: [
      'Premium quality materials',
      'Multiple color options',
      'Size: 35×30 cm',
      'Silent movement',
      'Ideal for offices and homes',
      'Customizable with your logo'
    ],
    icon: 'clock',
    popular: false
  },
  {
    service: 'Luxury Wooden Desk Organizer with Dual Clocks',
    price: 4900,
    description: 'Premium wooden desk organizer featuring dual clocks for elegant office organization',
    features: [
      'Dual time zone clocks',
      'High-quality wooden construction',
      'Multiple compartments',
      'Pen and card holders',
      'Professional executive design',
      'Perfect corporate gift'
    ],
    icon: 'clock',
    popular: true
  },
  {
    service: 'Elegant Wooden Desk Organizer with Clock',
    price: 3080,
    description: 'Stylish wooden desk organizer with integrated clock for professional office spaces',
    features: [
      'Integrated analog clock',
      'Premium wood construction',
      'Multiple storage compartments',
      'Pen and business card holders',
      'Elegant professional design',
      'Customizable with branding'
    ],
    icon: 'briefcase',
    popular: false
  },
  {
    service: 'Luxury Wooden & Glass Desk Organizer with Gold Accents',
    price: 5320,
    description: 'Premium desk organizer combining wood, glass, and gold accents for executive offices',
    features: [
      'Premium materials: wood, glass, and gold accents',
      'Multiple storage compartments',
      'Integrated clock',
      'Business card and pen holders',
      'Executive luxury design',
      'Perfect corporate gift'
    ],
    icon: 'briefcase',
    popular: true
  },
  {
    service: 'Premium Wooden Desk Organizer with Clock & Holders',
    price: 3080,
    description: 'Functional wooden desk organizer with integrated clock and multiple storage options',
    features: [
      'Integrated analog clock',
      'Multiple storage compartments',
      'Pen and card holders',
      'Premium wood construction',
      'Professional design',
      'Customizable with branding'
    ],
    icon: 'briefcase',
    popular: false
  },
  {
    service: 'Premium Glass Desk Organizer Set',
    price: 5320,
    description: 'Elegant glass desk organizer set for modern office environments',
    features: [
      'Premium glass construction',
      'Multiple compartments',
      'Modern transparent design',
      'Pen and card holders',
      'Executive professional look',
      'Customizable with branding'
    ],
    icon: 'glass-martini',
    popular: true
  },
  {
    service: 'Minimalist Glass/Acrylic Desk Organizer Set',
    price: 3920,
    description: 'Contemporary minimalist desk organizer set made from glass or acrylic',
    features: [
      'Sleek minimalist design',
      'Glass/acrylic construction',
      'Multiple storage compartments',
      'Modern office aesthetic',
      'Durable and elegant',
      'Customizable with branding'
    ],
    icon: 'glass-martini',
    popular: false
  },
  {
    service: 'Modern Acrylic Desk Organizer',
    price: 2800,
    description: 'Contemporary acrylic desk organizer for modern office spaces',
    features: [
      'Clear acrylic construction',
      'Multiple compartments',
      'Modern transparent design',
      'Pen and card holders',
      'Sleek professional look',
      'Customizable with branding'
    ],
    icon: 'glass-martini',
    popular: false
  },
  {
    service: 'Elegant Acrylic Desk Accessory Set',
    price: 3920,
    description: 'Complete acrylic desk accessory set for professional office organization',
    features: [
      'Premium acrylic construction',
      'Multiple pieces for complete organization',
      'Pen holders, card holders, and more',
      'Modern transparent design',
      'Professional executive look',
      'Customizable with branding'
    ],
    icon: 'glass-martini',
    popular: false
  },
  {
    service: 'Premium Metal Pens',
    price: 420,
    description: 'High-quality metal pens for professional use and corporate gifting',
    features: [
      'Premium metal construction',
      'Smooth writing experience',
      'Professional executive design',
      'Customizable with logo',
      'Ideal for corporate gifting',
      'Available in various finishes'
    ],
    icon: 'pen',
    popular: false
  },
  {
    service: 'Standard Metal Pens',
    price: 140,
    description: 'Quality metal pens for everyday professional use',
    features: [
      'Durable metal construction',
      'Smooth writing experience',
      'Professional design',
      'Customizable with logo',
      'Bulk ordering available',
      'Various color options'
    ],
    icon: 'pen',
    popular: true
  },
  {
    service: 'Plastic/Wooden Pens',
    price: 98,
    description: 'Affordable plastic or wooden pens for everyday office use',
    features: [
      'Eco-friendly wooden options',
      'Durable plastic construction',
      'Comfortable grip',
      'Customizable with logo',
      'Bulk ordering available',
      'Various color options'
    ],
    icon: 'pen',
    popular: false
  },
  {
    service: 'Multi-Color Metallic Clip Pens',
    price: 80,
    description: 'Colorful metallic clip pens for vibrant office stationery',
    features: [
      'Vibrant color options',
      'Metallic clip design',
      'Smooth writing experience',
      'Customizable with logo',
      'Bulk ordering available',
      'Affordable pricing'
    ],
    icon: 'pen',
    popular: false
  },
  {
    service: 'Secure Desk Pen with Base',
    price: 140,
    description: 'Secure pen with desk base, ideal for counters, reception areas, and public spaces',
    features: [
      'Secure attachment to base',
      'Prevents pen loss or theft',
      'Ideal for customer-facing areas',
      'Smooth writing experience',
      'Customizable with logo',
      'Professional appearance'
    ],
    icon: 'pen-fancy',
    popular: false
  },
  {
    service: 'Basic Plastic Pens',
    price: 63,
    description: 'Affordable plastic pens for everyday office and business use',
    features: [
      'Economical pricing',
      'Durable plastic construction',
      'Comfortable grip',
      'Customizable with logo',
      'Bulk ordering available',
      'Various color options'
    ],
    icon: 'pen',
    popular: true
  },
  {
    service: 'Premium Wooden Pens',
    price: 112,
    description: 'Eco-friendly wooden pens with premium feel for professional use',
    features: [
      'Eco-friendly wooden construction',
      'Natural elegant appearance',
      'Smooth writing experience',
      'Customizable with logo',
      'Ideal for corporate gifting',
      'Sustainable choice'
    ],
    icon: 'pen-fancy',
    popular: false
  },
  {
    service: 'Pen Holders',
    price: 420,
    description: 'Stylish pen holders for desk organization and professional office spaces',
    features: [
      'Premium materials',
      'Multiple design options',
      'Holds multiple pens',
      'Professional appearance',
      'Customizable with logo',
      'Complements office decor'
    ],
    icon: 'pencil-ruler',
    popular: false
  },
  {
    service: 'Business Card Holders (Metallic)',
    price: 182,
    description: 'Professional metallic business card holders for elegant card presentation',
    features: [
      'Premium metallic construction',
      'Sleek professional design',
      'Protects business cards',
      'Easy access mechanism',
      'Customizable with logo',
      'Ideal corporate gift'
    ],
    icon: 'address-card',
    popular: false
  }
];

// Main function to seed the database
async function seedOfficeEssentials() {
  try {
    console.log('Starting to seed office essentials pricing data...');

    // Get existing pricing items
    const existingPricing = await prisma.pricing.findMany();
    console.log(`Found ${existingPricing.length} existing pricing items`);

    // Create a map of existing services (case-insensitive)
    const existingServicesMap = new Map();
    existingPricing.forEach(item => {
      existingServicesMap.set(item.service.toLowerCase(), item);
    });

    let created = 0;
    let updated = 0;

    // Process each service
    for (const service of officeEssentials) {
      // Check if service already exists (case-insensitive)
      const existingService = existingServicesMap.get(service.service.toLowerCase());

      if (existingService) {
        // Update existing service
        await prisma.pricing.update({
          where: { id: existingService.id },
          data: {
            price: service.price,
            description: service.description,
            features: service.features,
            icon: service.icon,
            popular: service.popular
          }
        });
        updated++;
        console.log(`Updated existing service: ${service.service}`);
      } else {
        // Create new service
        await prisma.pricing.create({
          data: {
            service: service.service,
            price: service.price,
            description: service.description,
            features: service.features,
            icon: service.icon,
            popular: service.popular
          }
        });
        created++;
        console.log(`Created new service: ${service.service}`);
      }
    }

    console.log(`Successfully processed office essentials pricing data:`);
    console.log(`- Created: ${created} new services`);
    console.log(`- Updated: ${updated} existing services`);
    console.log(`- Total: ${officeEssentials.length} services processed`);

  } catch (error) {
    console.error('Error seeding office essentials pricing data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedOfficeEssentials()
  .catch((error) => {
    console.error('Error running seed script:', error);
    process.exit(1);
  });
