const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Counting blog posts...');
    
    const count = await prisma.blogPost.count();
    
    console.log(`Total blog posts in database: ${count}`);
    
    if (count > 0) {
      // Get a sample post
      const samplePost = await prisma.blogPost.findFirst();
      console.log('\nSample blog post:');
      console.log(JSON.stringify(samplePost, null, 2));
    }
    
  } catch (error) {
    console.error('Error counting blog posts:', error);
    console.error(error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

main();
