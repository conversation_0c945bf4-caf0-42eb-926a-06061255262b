#!/bin/bash

# Unified script to switch between development and production environments
# Handles both Node.js environment and nginx configuration
# Usage: sudo ./scripts/switch-complete.sh [dev|prod]

# Check if running as sudo/root for nginx config changes
if [ "$EUID" -ne 0 ]; then
  echo "Please run as root or with sudo"
  exit 1
fi

NGINX_SITES_AVAILABLE="/etc/nginx/sites-available"
NGINX_SITES_ENABLED="/etc/nginx/sites-enabled"
DEV_CONF="$NGINX_SITES_AVAILABLE/dev.mocky.co.ke"
PROD_CONF="$NGINX_SITES_AVAILABLE/mocky.co.ke"
ENABLED_CONF="$NGINX_SITES_ENABLED/mocky.co.ke"

# Function to switch nginx configuration
switch_nginx() {
  local env="$1"
  local src_file=""
  local dest_file=""
  
  if [ "$env" == "dev" ]; then
    src_file="$(pwd)/nginx/dev.mocky.co.ke.conf"
    dest_file="$DEV_CONF"
  else
    src_file="$(pwd)/nginx/prod.mocky.co.ke.conf"
    dest_file="$PROD_CONF"
  fi
  
  echo "Installing $src_file to $dest_file..."
  cp "$src_file" "$dest_file"
  
  # Handle symlink for sites-enabled
  if [ -L "$ENABLED_CONF" ]; then
    rm "$ENABLED_CONF"
  fi
  ln -s "$dest_file" "$ENABLED_CONF"
  
  # Test and reload nginx
  echo "Testing nginx configuration..."
  nginx -t && systemctl reload nginx
  
  if [ $? -eq 0 ]; then
    echo "Nginx configuration switched successfully"
  else
    echo "Error: Nginx configuration test failed"
    return 1
  fi
  
  return 0
}

# Function to switch Node.js environment
switch_node_env() {
  local env="$1"
  local current_user=$(logname)
  
  echo "Switching Node.js environment to $env as user $current_user..."
  
  # Run the environment switch as the actual user, not root
  if [ "$env" == "dev" ]; then
    sudo -u "$current_user" bash -c "cd $(pwd) && npm run dev:config"
    sudo -u "$current_user" bash -c "cd $(pwd) && pm2 stop mocky-digital || echo 'No production process to stop'"
    echo "Starting development server..."
    sudo -u "$current_user" bash -c "cd $(pwd) && npm run dev &"
  else
    sudo -u "$current_user" bash -c "cd $(pwd) && pkill -f 'next dev' || echo 'No development servers to kill'"
    echo "Building for production..."
    sudo -u "$current_user" bash -c "cd $(pwd) && npm run build"
    echo "Starting production server with PM2..."
    sudo -u "$current_user" bash -c "cd $(pwd) && pm2 start ecosystem.config.js"
  fi
}

if [ "$1" == "dev" ]; then
  echo "Switching to development environment..."
  
  # Switch nginx configuration
  switch_nginx "dev"
  
  # Switch Node.js environment
  switch_node_env "dev"
  
  echo "✅ Development environment is now active"
  echo "- Nginx is configured for dev.mocky.co.ke"
  echo "- Next.js is running in development mode on port 3000"
  
elif [ "$1" == "prod" ]; then
  echo "Switching to production environment..."
  
  # Switch nginx configuration
  switch_nginx "prod"
  
  # Switch Node.js environment
  switch_node_env "prod"
  
  echo "✅ Production environment is now active"
  echo "- Nginx is configured for mocky.co.ke"
  echo "- Next.js is running in production mode with PM2"
  
else
  echo "Usage: $0 [dev|prod]"
  echo "  dev  - Switch to development environment"
  echo "  prod - Switch to production environment"
  exit 1
fi 