#!/usr/bin/env node

/**
 * Test Upload Configuration Script
 * 
 * This script tests the upload configuration by checking S3 connectivity
 * and verifying that all required components are working.
 */

const { S3Client, ListObjectsV2Command, PutObjectCommand } = require('@aws-sdk/client-s3');
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient();

async function getStorageConfig() {
  try {
    const config = await prisma.storageConfig.findFirst({
      where: { isDefault: true }
    });
    
    if (!config) {
      // Fallback to environment variables
      return {
        region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
        endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT,
        bucketName: process.env.NEXT_PUBLIC_S3_BUCKET,
        accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY,
        secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY,
      };
    }
    
    return config;
  } catch (error) {
    console.error('Error getting storage config:', error);
    return null;
  }
}

async function testS3Connection() {
  try {
    console.log('🔗 Testing S3 connection...');
    
    const config = await getStorageConfig();
    if (!config) {
      throw new Error('No storage configuration found');
    }
    
    console.log('📋 Using configuration:');
    console.log(`   Bucket: ${config.bucketName}`);
    console.log(`   Region: ${config.region}`);
    console.log(`   Endpoint: ${config.endpoint}`);
    
    const s3Client = new S3Client({
      region: config.region,
      endpoint: config.endpoint,
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
      },
      forcePathStyle: true,
      maxAttempts: 3,
      retryMode: 'standard'
    });
    
    // Test connection by listing objects
    const command = new ListObjectsV2Command({
      Bucket: config.bucketName,
      MaxKeys: 5,
    });
    
    const response = await s3Client.send(command);
    
    console.log('✅ S3 connection successful!');
    console.log(`   Found ${response.Contents?.length || 0} objects in bucket`);
    
    if (response.Contents && response.Contents.length > 0) {
      console.log('   Recent objects:');
      response.Contents.slice(0, 3).forEach(obj => {
        console.log(`     - ${obj.Key} (${obj.Size} bytes)`);
      });
    }
    
    return { success: true, s3Client, config };
    
  } catch (error) {
    console.error('❌ S3 connection failed:', error.message);
    
    if (error.name === 'CredentialsProviderError') {
      console.log('💡 This looks like a credentials issue. Check your S3 access keys.');
    } else if (error.name === 'NetworkingError') {
      console.log('💡 This looks like a network issue. Check your internet connection and endpoint URL.');
    } else if (error.message.includes('NoSuchBucket')) {
      console.log('💡 The specified bucket does not exist. Check your bucket name.');
    } else if (error.message.includes('AccessDenied')) {
      console.log('💡 Access denied. Check your S3 credentials and bucket permissions.');
    }
    
    return { success: false, error };
  }
}

async function testUploadPermissions(s3Client, config) {
  try {
    console.log('\n📤 Testing upload permissions...');
    
    // Create a small test file
    const testContent = `Test upload from Mocky Digital at ${new Date().toISOString()}`;
    const testKey = `test-uploads/test-${Date.now()}.txt`;
    
    const command = new PutObjectCommand({
      Bucket: config.bucketName,
      Key: testKey,
      Body: Buffer.from(testContent),
      ContentType: 'text/plain',
      ACL: 'public-read'
    });
    
    await s3Client.send(command);
    
    // Generate the public URL
    const publicUrl = `${config.endpoint}/${config.bucketName}/${testKey}`;
    
    console.log('✅ Upload test successful!');
    console.log(`   Test file uploaded: ${testKey}`);
    console.log(`   Public URL: ${publicUrl}`);
    
    return { success: true, testKey, publicUrl };
    
  } catch (error) {
    console.error('❌ Upload test failed:', error.message);
    
    if (error.message.includes('AccessDenied')) {
      console.log('💡 Upload access denied. Check your S3 credentials and bucket write permissions.');
    } else if (error.message.includes('InvalidBucketName')) {
      console.log('💡 Invalid bucket name. Check your bucket configuration.');
    }
    
    return { success: false, error };
  }
}

async function testEnvironmentVariables() {
  console.log('🔍 Checking environment variables...');
  
  const requiredVars = [
    'NEXT_PUBLIC_S3_BUCKET',
    'NEXT_PUBLIC_S3_ACCESS_KEY',
    'NEXT_PUBLIC_S3_SECRET_KEY',
    'NEXT_PUBLIC_S3_ENDPOINT',
    'NEXT_PUBLIC_S3_REGION'
  ];
  
  const results = {};
  let allPresent = true;
  
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    results[varName] = {
      present: !!value,
      value: value ? (varName.includes('SECRET') || varName.includes('KEY') ? 
        `${value.substring(0, 8)}...` : value) : 'NOT SET'
    };
    
    if (!value) allPresent = false;
  });
  
  console.log('📋 Environment Variables:');
  Object.entries(results).forEach(([name, info]) => {
    const status = info.present ? '✅' : '❌';
    console.log(`   ${status} ${name}: ${info.value}`);
  });
  
  if (!allPresent) {
    console.log('\n❌ Some required environment variables are missing!');
    return false;
  }
  
  console.log('\n✅ All required environment variables are present!');
  return true;
}

async function main() {
  try {
    console.log('🧪 Testing Upload Configuration\n');
    console.log('=' .repeat(50));
    
    // Test 1: Environment Variables
    const envTest = await testEnvironmentVariables();
    if (!envTest) {
      console.log('\n❌ Environment variable test failed. Please check your .env file.');
      process.exit(1);
    }
    
    console.log('\n' + '=' .repeat(50));
    
    // Test 2: S3 Connection
    const connectionTest = await testS3Connection();
    if (!connectionTest.success) {
      console.log('\n❌ S3 connection test failed. Please check your S3 configuration.');
      process.exit(1);
    }
    
    console.log('\n' + '=' .repeat(50));
    
    // Test 3: Upload Permissions
    const uploadTest = await testUploadPermissions(connectionTest.s3Client, connectionTest.config);
    if (!uploadTest.success) {
      console.log('\n❌ Upload permissions test failed. Please check your S3 permissions.');
      process.exit(1);
    }
    
    console.log('\n' + '=' .repeat(50));
    console.log('\n🎉 All tests passed! Your upload configuration is working correctly.');
    console.log('\n✅ Summary:');
    console.log('   - Environment variables: OK');
    console.log('   - S3 connection: OK');
    console.log('   - Upload permissions: OK');
    console.log('   - Storage configuration in database: OK');
    
    console.log('\n🚀 You can now upload images through your application!');
    
  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
    console.log('\n💡 Please check the error details above and fix any issues.');
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { testS3Connection, testUploadPermissions, testEnvironmentVariables };
