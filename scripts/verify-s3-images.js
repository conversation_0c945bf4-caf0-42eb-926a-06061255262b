#!/usr/bin/env node

/**
 * This script verifies that all local images exist in S3.
 * Usage: node verify-s3-images.js <directory>
 * 
 * Example: node verify-s3-images.js logos
 */

const fs = require('fs').promises;
const path = require('path');
const { S3Client, ListObjectsV2Command, HeadObjectCommand } = require('@aws-sdk/client-s3');

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'eu-west-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

const BUCKET_NAME = 'mocky';

// Define category mapping
const CATEGORY_MAP = {
  'logos': 'images/portfolio/logos',
  'fliers': 'images/portfolio/fliers',
  'cards': 'images/portfolio/cards',
  'letterheads': 'images/portfolio/letterheads',
  'branding': 'images/portfolio/branding',
  'websites': 'images/portfolio/websites',
  'profiles': 'images/portfolio/profiles'
};

// Valid image extensions
const IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp', '.svg', '.gif'];

// Main function
async function main() {
  // Get the directory from command line arguments
  const category = process.argv[2];
  
  if (!category) {
    console.error('Please provide a category: logos, fliers, cards, letterheads, branding, websites, profiles');
    process.exit(1);
  }
  
  if (!CATEGORY_MAP[category]) {
    console.error(`Invalid category: ${category}. Valid options are: ${Object.keys(CATEGORY_MAP).join(', ')}`);
    process.exit(1);
  }
  
  const s3Path = CATEGORY_MAP[category];
  const localPath = path.join('public', s3Path);
  
  console.log(`Verifying images in category: ${category}`);
  console.log(`Local path: ${localPath}`);
  console.log(`S3 path: ${s3Path}`);
  
  // Check if local directory exists
  try {
    const stats = await fs.stat(localPath);
    if (!stats.isDirectory()) {
      console.error(`${localPath} is not a directory`);
      process.exit(1);
    }
  } catch (error) {
    console.error(`Directory ${localPath} does not exist`);
    process.exit(1);
  }
  
  // List files in local directory
  const files = await fs.readdir(localPath);
  
  // Filter for image files
  const imageFiles = files.filter(file => {
    const ext = path.extname(file).toLowerCase();
    return IMAGE_EXTENSIONS.includes(ext);
  });
  
  console.log(`Found ${imageFiles.length} image files in ${localPath}`);
  
  // Check which images exist in S3
  let existInS3 = 0;
  let missingInS3 = 0;
  let filesToCheck = imageFiles.length;
  
  // List of missing files
  const missingFiles = [];
  
  console.log('\nChecking each file in S3...');
  
  for (const file of imageFiles) {
    const s3Key = `${s3Path}/${file}`;
    
    try {
      // Check if file exists in S3
      const command = new HeadObjectCommand({
        Bucket: BUCKET_NAME,
        Key: s3Key
      });

      await s3Client.send(command);
      
      existInS3++;
      process.stdout.write(`\rProgress: ${existInS3 + missingInS3}/${filesToCheck} (${Math.round((existInS3 + missingInS3) / filesToCheck * 100)}%)`);
    } catch (error) {
      missingInS3++;
      missingFiles.push(file);
      process.stdout.write(`\rProgress: ${existInS3 + missingInS3}/${filesToCheck} (${Math.round((existInS3 + missingInS3) / filesToCheck * 100)}%)`);
    }
  }
  
  console.log('\n\nVerification complete!');
  console.log('\nSummary:');
  console.log(`Total images checked: ${imageFiles.length}`);
  console.log(`Images existing in S3: ${existInS3}`);
  console.log(`Images missing in S3: ${missingInS3}`);
  
  // List missing files if any
  if (missingInS3 > 0) {
    console.log('\nMissing files:');
    missingFiles.forEach(file => {
      console.log(`- ${file}`);
    });
    
    console.log('\nPlease upload these files to S3 before deleting local images.');
  } else {
    console.log('\nAll local images exist in S3. You can safely delete local images.');
  }
}

// Run the main function
main().catch(error => {
  console.error('Error:', error);
  process.exit(1);
}); 