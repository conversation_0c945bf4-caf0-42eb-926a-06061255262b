import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

async function main() {
  try {
    console.log('Checking database connection...');

    // Check pricing items
    const pricingItems = await prisma.pricing.findMany();
    console.log(`Found ${pricingItems.length} pricing items`);
    if (pricingItems.length > 0) {
      console.log('First pricing item:', JSON.stringify(pricingItems[0], null, 2));
    }

    // Check site settings
    const siteSettings = await prisma.siteSettings.findFirst();
    console.log('Site settings found:', !!siteSettings);

    // Check database schema
    const tables = await prisma.$queryRaw`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `;
    console.log('Database tables:', tables);

    console.log('Database connection successful!');
  } catch (error) {
    console.error('Error connecting to database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
