console.log('Lead scoring test script started');

const { PrismaClient } = require('@prisma/client');
console.log('PrismaClient imported successfully');

const prisma = new PrismaClient({
  log: ['info', 'warn', 'error'],
});
console.log('PrismaClient instance created');

// We'll implement the lead scoring logic directly in this script
// since we can't easily import the TypeScript service in a Node.js script

async function calculateLeadScore(leadId) {
  try {
    // Get the lead with interactions
    const lead = await prisma.lead.findUnique({
      where: { id: leadId },
      include: {
        interactions: true,
      },
    });

    if (!lead) {
      throw new Error(`Lead with ID ${leadId} not found`);
    }

    // Get event tracking data for this lead
    const events = await prisma.eventTracking.findMany({
      where: {
        leadId,
      },
    });

    // Calculate score factors
    let totalScore = 0;

    // Form completeness (0-20 points)
    let formCompletenessScore = 0;
    if (lead.name) formCompletenessScore += 2;
    if (lead.email) formCompletenessScore += 2;
    if (lead.phone) formCompletenessScore += 2;
    if (lead.company) formCompletenessScore += 2;
    if (lead.notes && lead.notes.length > 10) formCompletenessScore += 2;

    const fieldsCount = Object.keys(lead).filter(key =>
      lead[key] !== null &&
      lead[key] !== undefined &&
      !['id', 'createdAt', 'updatedAt', 'interactions'].includes(key)
    ).length;

    formCompletenessScore += Math.min(10, fieldsCount);
    formCompletenessScore = Math.min(20, formCompletenessScore);

    console.log(`Form completeness score: ${formCompletenessScore}`);
    totalScore += formCompletenessScore;

    // Interaction frequency (0-20 points)
    let interactionFrequencyScore = 0;
    if (lead.interactions && lead.interactions.length > 0) {
      interactionFrequencyScore = Math.min(20, lead.interactions.length * 5);
    }

    console.log(`Interaction frequency score: ${interactionFrequencyScore}`);
    totalScore += interactionFrequencyScore;

    // Page visits (0-20 points)
    const pageViewEvents = events.filter(event =>
      event.eventType === 'pageView'
    );

    const pageVisitsScore = Math.min(20, pageViewEvents.length);
    console.log(`Page visits score: ${pageVisitsScore}`);
    totalScore += pageVisitsScore;

    // High-value page visits (0-20 points)
    const highValuePages = [
      '/pricing',
      '/contact',
      '/request',
      '/web-development',
      '/logo-design',
      '/social-media',
      '/services',
    ];

    const highValuePageViews = events.filter(event =>
      event.eventType === 'pageView' &&
      highValuePages.some(page => event.url?.includes(page))
    );

    const highValuePageVisitsScore = Math.min(20, highValuePageViews.length * 4);
    console.log(`High-value page visits score: ${highValuePageVisitsScore}`);
    totalScore += highValuePageVisitsScore;

    // Conversion events (0-20 points)
    const conversionEventTypes = [
      'formSubmission',
      'buttonClick',
      'download',
      'Lead',
      'Contact',
    ];

    const conversionEvents = events.filter(event =>
      conversionEventTypes.includes(event.eventType) ||
      conversionEventTypes.includes(event.eventName)
    );

    const conversionEventsScore = Math.min(20, conversionEvents.length * 10);
    console.log(`Conversion events score: ${conversionEventsScore}`);
    totalScore += conversionEventsScore;

    // Update the lead's score
    await prisma.lead.update({
      where: { id: leadId },
      data: {
        score: totalScore,
      },
    });

    return totalScore;
  } catch (error) {
    console.error(`Error calculating lead score for ${leadId}:`, error);
    throw error;
  }
}

async function main() {
  try {
    console.log('Testing lead scoring functionality...');

    // Create a test lead
    console.log('Creating test lead...');
    const lead = await prisma.lead.create({
      data: {
        name: 'Test Lead for Scoring',
        email: '<EMAIL>',
        phone: '1234567890',
        company: 'Test Company',
        source: 'test_script',
        status: 'new',
        score: 0,
        notes: 'This is a test lead for scoring',
      },
    });

    console.log(`Created test lead with ID: ${lead.id}`);

    // Create multiple interactions
    console.log('Creating test interactions...');
    await prisma.interaction.createMany({
      data: [
        {
          leadId: lead.id,
          type: 'note',
          details: 'Initial contact',
        },
        {
          leadId: lead.id,
          type: 'call',
          details: 'Follow-up call',
        },
        {
          leadId: lead.id,
          type: 'email',
          details: 'Sent proposal',
        },
      ],
    });

    console.log('Created test interactions');

    // Create multiple events
    console.log('Creating test events...');
    await prisma.eventTracking.createMany({
      data: [
        {
          eventName: 'pageView',
          eventType: 'pageView',
          url: 'http://example.com/home',
          sessionId: 'test_session',
          leadId: lead.id,
        },
        {
          eventName: 'pageView',
          eventType: 'pageView',
          url: 'http://example.com/pricing',
          sessionId: 'test_session',
          leadId: lead.id,
        },
        {
          eventName: 'pageView',
          eventType: 'pageView',
          url: 'http://example.com/contact',
          sessionId: 'test_session',
          leadId: lead.id,
        },
        {
          eventName: 'buttonClick',
          eventType: 'buttonClick',
          url: 'http://example.com/pricing',
          sessionId: 'test_session',
          leadId: lead.id,
          metadata: {
            buttonName: 'Order Now',
          },
        },
        {
          eventName: 'contact_form',
          eventType: 'formSubmission',
          url: 'http://example.com/contact',
          sessionId: 'test_session',
          leadId: lead.id,
          metadata: {
            formName: 'Contact Form',
          },
        },
      ],
    });

    console.log('Created test events');

    // Calculate the lead score
    console.log('Calculating lead score...');
    const score = await calculateLeadScore(lead.id);
    console.log(`Lead score calculated: ${score}`);

    // Fetch the updated lead
    const updatedLead = await prisma.lead.findUnique({
      where: {
        id: lead.id,
      },
    });

    console.log(`Updated lead score: ${updatedLead.score}`);

    // Clean up
    console.log('Cleaning up...');
    await prisma.eventTracking.deleteMany({
      where: {
        leadId: lead.id,
      },
    });
    await prisma.interaction.deleteMany({
      where: {
        leadId: lead.id,
      },
    });
    await prisma.lead.delete({
      where: {
        id: lead.id,
      },
    });

    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Error testing lead scoring:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
