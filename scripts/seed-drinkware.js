// Script to seed drinkware pricing data
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

// Initialize Prisma client
const prisma = new PrismaClient();

// Define drinkware products with pricing
const drinkware = [
  // Flasks
  {
    service: 'Premium Thermal Flask (500ml)',
    price: 1400,
    description: 'High-quality thermal flask for hot and cold beverages',
    features: [
      'Double-wall vacuum insulation',
      'Keeps drinks hot/cold for hours',
      'Premium stainless steel construction',
      'Leak-proof design',
      'Custom branding options',
      'Ideal for corporate gifting'
    ],
    icon: 'flask',
    popular: true
  },
  {
    service: 'Magnetic Flask (750ml)',
    price: 1680,
    description: 'Innovative magnetic flask with premium insulation',
    features: [
      'Magnetic lid technology',
      'Double-wall vacuum insulation',
      'Premium stainless steel construction',
      'Keeps drinks hot/cold for hours',
      'Custom branding options',
      'Elegant design'
    ],
    icon: 'flask',
    popular: false
  },
  {
    service: 'LED Tumbler Flask (500ml)',
    price: 1365,
    description: 'Illuminated tumbler flask with LED technology',
    features: [
      'Built-in LED illumination',
      'Double-wall insulation',
      'Premium materials',
      'Keeps drinks at temperature',
      'Custom branding options',
      'Ideal for promotional events'
    ],
    icon: 'lightbulb',
    popular: true
  },
  {
    service: 'String Flask (620ml)',
    price: 1495,
    description: 'Stylish flask with string detail and premium insulation',
    features: [
      'Unique string detail design',
      'Double-wall vacuum insulation',
      'Premium stainless steel construction',
      'Keeps drinks hot/cold for hours',
      'Custom branding options',
      'Elegant appearance'
    ],
    icon: 'flask',
    popular: false
  },
  {
    service: 'Jug Flask (500ml)',
    price: 1330,
    description: 'Jug-style flask with premium insulation',
    features: [
      'Unique jug design',
      'Double-wall vacuum insulation',
      'Available in white and black',
      'Keeps drinks hot/cold for hours',
      'Custom branding options',
      'Stylish appearance'
    ],
    icon: 'flask',
    popular: false
  },
  
  // Tumblers
  {
    service: 'Long Tumbler (500ml)',
    price: 1050,
    description: 'Tall tumbler for hot and cold beverages with sublimation printing',
    features: [
      'Tall elegant design',
      'Double-wall insulation',
      'Full-color sublimation printing',
      'Keeps drinks at temperature',
      'Custom branding options',
      'Premium materials'
    ],
    icon: 'glass-whiskey',
    popular: true
  },
  {
    service: 'Stanley Mug with Handle (900ml)',
    price: 1680,
    description: 'Premium Stanley-style mug with handle and large capacity',
    features: [
      'Large 900ml capacity',
      'Comfortable handle',
      'Double-wall vacuum insulation',
      'Premium stainless steel construction',
      'Keeps drinks hot/cold for hours',
      'Durable design'
    ],
    icon: 'mug-hot',
    popular: true
  },
  {
    service: 'Non-LED Tumbler (500ml)',
    price: 1120,
    description: 'Standard tumbler with premium insulation',
    features: [
      'Double-wall insulation',
      'Premium materials',
      'Keeps drinks at temperature',
      'Custom branding options',
      'Multiple color options',
      'Elegant design'
    ],
    icon: 'glass-whiskey',
    popular: false
  },
  
  // Water Bottles
  {
    service: 'Plastic Water Bottle (800ml)',
    price: 910,
    description: 'Durable plastic water bottle for everyday use',
    features: [
      '800ml capacity',
      'BPA-free plastic',
      'Leak-proof design',
      'Custom branding options',
      'Multiple color options',
      'Lightweight and portable'
    ],
    icon: 'bottle-water',
    popular: true
  },
  {
    service: 'Wooden Water Bottle (550ml)',
    price: 1680,
    description: 'Premium water bottle with wooden accent design',
    features: [
      'Unique wooden accent design',
      'Double-wall insulation',
      'Premium materials',
      'Keeps drinks at temperature',
      'Custom branding options',
      'Elegant appearance'
    ],
    icon: 'bottle-water',
    popular: false
  },
  {
    service: 'Metallic Water Bottle (800ml)',
    price: 728,
    description: 'Durable metallic water bottle for everyday use',
    features: [
      '800ml capacity',
      'Premium stainless steel construction',
      'Leak-proof design',
      'Custom branding options',
      'Multiple color options',
      'Durable and long-lasting'
    ],
    icon: 'bottle-water',
    popular: true
  },
  
  // Mugs
  {
    service: 'Standard White Mug (11oz)',
    price: 224,
    description: 'Classic white ceramic mug for sublimation printing',
    features: [
      '11oz capacity',
      'High-quality ceramic',
      'Full-color sublimation printing',
      'Dishwasher safe',
      'Custom branding options',
      'Ideal for promotional gifts'
    ],
    icon: 'mug-hot',
    popular: true
  },
  {
    service: 'Magic Mug (350ml)',
    price: 322,
    description: 'Color-changing magic mug with heat-sensitive coating',
    features: [
      'Heat-sensitive color-changing coating',
      'Reveals design when hot liquid is added',
      'High-quality ceramic',
      'Full-color sublimation printing',
      'Custom branding options',
      'Unique promotional gift'
    ],
    icon: 'magic',
    popular: true
  },
  {
    service: 'Two-Tone Mug (350ml)',
    price: 266,
    description: 'Ceramic mug with contrasting interior and exterior colors',
    features: [
      'Two-tone color design',
      'High-quality ceramic',
      'Full-color sublimation printing',
      'Dishwasher safe',
      'Custom branding options',
      'Multiple color combinations'
    ],
    icon: 'mug-hot',
    popular: false
  },
  {
    service: 'Enamel Mug (18oz)',
    price: 602,
    description: 'Durable enamel mug with vintage appeal',
    features: [
      'Large 18oz capacity',
      'Durable enamel construction',
      'Vintage/rustic appearance',
      'Custom branding options',
      'Ideal for outdoor activities',
      'Unique promotional gift'
    ],
    icon: 'mug-hot',
    popular: false
  },
  {
    service: 'Bluetooth Travel Mug',
    price: 2665,
    description: 'Smart travel mug with Bluetooth connectivity and temperature control',
    features: [
      'Bluetooth connectivity',
      'Temperature control via app',
      'Double-wall vacuum insulation',
      'Premium materials',
      'Keeps drinks at desired temperature',
      'High-tech corporate gift'
    ],
    icon: 'bluetooth',
    popular: true
  }
];

// Main function to seed the database
async function seedDrinkware() {
  try {
    console.log('Starting to seed drinkware pricing data...');

    // Get existing pricing items
    const existingPricing = await prisma.pricing.findMany();
    console.log(`Found ${existingPricing.length} existing pricing items`);

    // Create a map of existing services (case-insensitive)
    const existingServicesMap = new Map();
    existingPricing.forEach(item => {
      existingServicesMap.set(item.service.toLowerCase(), item);
    });

    let created = 0;
    let updated = 0;

    // Process each service
    for (const service of drinkware) {
      // Check if service already exists (case-insensitive)
      const existingService = existingServicesMap.get(service.service.toLowerCase());

      if (existingService) {
        // Update existing service
        await prisma.pricing.update({
          where: { id: existingService.id },
          data: {
            price: service.price,
            description: service.description,
            features: service.features,
            icon: service.icon,
            popular: service.popular
          }
        });
        updated++;
        console.log(`Updated existing service: ${service.service}`);
      } else {
        // Create new service
        await prisma.pricing.create({
          data: {
            service: service.service,
            price: service.price,
            description: service.description,
            features: service.features,
            icon: service.icon,
            popular: service.popular
          }
        });
        created++;
        console.log(`Created new service: ${service.service}`);
      }
    }

    console.log(`Successfully processed drinkware pricing data:`);
    console.log(`- Created: ${created} new services`);
    console.log(`- Updated: ${updated} existing services`);
    console.log(`- Total: ${drinkware.length} services processed`);

  } catch (error) {
    console.error('Error seeding drinkware pricing data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedDrinkware()
  .catch((error) => {
    console.error('Error running seed script:', error);
    process.exit(1);
  });
