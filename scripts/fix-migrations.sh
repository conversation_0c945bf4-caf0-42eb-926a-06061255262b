#!/bin/bash

# Simple script to fix migrations
# Usage: ./scripts/fix-migrations.sh

# Exit on error
set -e

echo "🔄 Fixing database migrations..."

# Mark the failed migration as rolled back
echo "🔄 Marking failed migration as rolled back..."
npx prisma migrate resolve --rolled-back 20240101000000_add_pricing_fields

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Apply pending migrations
echo "🔄 Applying pending migrations..."
npx prisma migrate deploy

echo "✅ Migration fix completed!"
