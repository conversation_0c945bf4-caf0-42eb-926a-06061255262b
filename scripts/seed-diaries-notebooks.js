// Script to seed diaries and notebooks pricing data
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

// Initialize Prisma client
const prisma = new PrismaClient();

// Define diaries and notebooks products with pricing
const diariesNotebooks = [
  {
    service: 'A5 Notebook (Standard)',
    price: 490,
    description: 'Professional A5 notebook for business and personal use',
    features: [
      'A5 size (148 × 210 mm)',
      'Premium paper quality',
      'Hardcover design',
      'Customizable with logo',
      'Multiple color options',
      'Ideal for corporate gifting'
    ],
    icon: 'book',
    popular: true
  },
  {
    service: 'Executive Notebook',
    price: 1120,
    description: 'Premium executive notebook with luxury features for professionals',
    features: [
      'Premium leather-like cover',
      'High-quality paper',
      'Business card pocket',
      'Pen holder',
      'Ribbon bookmark',
      'Customizable with embossed logo'
    ],
    icon: 'book-open',
    popular: true
  },
  {
    service: 'A5 Semi-Executive Notebook',
    price: 770,
    description: 'Mid-range professional notebook with premium features',
    features: [
      'A5 size (148 × 210 mm)',
      'Semi-premium cover material',
      'High-quality paper',
      'Business card pocket',
      'Ribbon bookmark',
      'Customizable with logo'
    ],
    icon: 'book',
    popular: false
  },
  {
    service: 'A6 Pocket Notebook',
    price: 350,
    description: 'Compact A6 notebook for on-the-go note-taking',
    features: [
      'A6 size (105 × 148 mm)',
      'Pocket-friendly design',
      'Durable cover',
      'Quality paper',
      'Customizable with logo',
      'Ideal for field work'
    ],
    icon: 'book',
    popular: false
  },
  {
    service: 'B5 Notebook',
    price: 840,
    description: 'Larger B5 notebook for more writing space and detailed notes',
    features: [
      'B5 size (176 × 250 mm)',
      'Premium paper quality',
      'Hardcover design',
      'More writing space',
      'Customizable with logo',
      'Professional appearance'
    ],
    icon: 'book',
    popular: false
  },
  {
    service: 'Patterned Edge A5 Notebook',
    price: 845,
    description: 'Stylish A5 notebook with decorative patterned edges',
    features: [
      'A5 size (148 × 210 mm)',
      'Decorative patterned edges',
      'Premium paper quality',
      'Hardcover design',
      'Customizable with logo',
      'Elegant professional look'
    ],
    icon: 'book',
    popular: false
  },
  {
    service: 'Executive Double Cover A5 Notebook',
    price: 1040,
    description: 'Premium A5 notebook with innovative double cover design',
    features: [
      'A5 size (148 × 210 mm)',
      'Unique double cover design',
      'Premium materials',
      'Business card pocket',
      'Pen holder',
      'Customizable with embossed logo'
    ],
    icon: 'book-open',
    popular: true
  },
  {
    service: 'Custom Corporate Diaries (A5)',
    price: 630,
    description: 'Fully customizable corporate diaries with company branding',
    features: [
      'A5 size (148 × 210 mm)',
      'Full customization options',
      'Company logo and colors',
      'Premium paper quality',
      'Year planner and calendar',
      'Minimum order: 50 pieces'
    ],
    icon: 'calendar-alt',
    popular: true
  },
  {
    service: 'Custom Corporate Diaries (Executive)',
    price: 1200,
    description: 'Premium executive diaries with full corporate customization',
    features: [
      'Executive size',
      'Leather-like cover',
      'Full company branding',
      'Premium paper quality',
      'Year planner and calendar',
      'Minimum order: 25 pieces'
    ],
    icon: 'calendar-alt',
    popular: true
  },
  {
    service: 'Eco-Friendly Notebooks',
    price: 560,
    description: 'Environmentally friendly notebooks made from recycled materials',
    features: [
      'Recycled paper',
      'Eco-friendly cover materials',
      'A5 size (148 × 210 mm)',
      'Customizable with logo',
      'Sustainable choice',
      'Minimum order: 50 pieces'
    ],
    icon: 'leaf',
    popular: false
  },
  {
    service: 'Spiral-Bound Notebooks',
    price: 420,
    description: 'Practical spiral-bound notebooks for easy page turning',
    features: [
      'Spiral binding for flat opening',
      'A5 size (148 × 210 mm)',
      'Durable cover',
      'Quality paper',
      'Customizable with logo',
      'Ideal for meetings and note-taking'
    ],
    icon: 'book',
    popular: false
  },
  {
    service: 'Conference Notepads',
    price: 280,
    description: 'Professional notepads for conferences, seminars, and training sessions',
    features: [
      'A4/A5 size options',
      'Company branding',
      'Quality paper',
      'Perforated pages',
      'Minimum order: 100 pieces',
      'Bulk discounts available'
    ],
    icon: 'clipboard',
    popular: false
  }
];

// Main function to seed the database
async function seedDiariesNotebooks() {
  try {
    console.log('Starting to seed diaries and notebooks pricing data...');

    // Get existing pricing items
    const existingPricing = await prisma.pricing.findMany();
    console.log(`Found ${existingPricing.length} existing pricing items`);

    // Create a map of existing services (case-insensitive)
    const existingServicesMap = new Map();
    existingPricing.forEach(item => {
      existingServicesMap.set(item.service.toLowerCase(), item);
    });

    let created = 0;
    let updated = 0;

    // Process each service
    for (const service of diariesNotebooks) {
      // Check if service already exists (case-insensitive)
      const existingService = existingServicesMap.get(service.service.toLowerCase());

      if (existingService) {
        // Update existing service
        await prisma.pricing.update({
          where: { id: existingService.id },
          data: {
            price: service.price,
            description: service.description,
            features: service.features,
            icon: service.icon,
            popular: service.popular
          }
        });
        updated++;
        console.log(`Updated existing service: ${service.service}`);
      } else {
        // Create new service
        await prisma.pricing.create({
          data: {
            service: service.service,
            price: service.price,
            description: service.description,
            features: service.features,
            icon: service.icon,
            popular: service.popular
          }
        });
        created++;
        console.log(`Created new service: ${service.service}`);
      }
    }

    console.log(`Successfully processed diaries and notebooks pricing data:`);
    console.log(`- Created: ${created} new services`);
    console.log(`- Updated: ${updated} existing services`);
    console.log(`- Total: ${diariesNotebooks.length} services processed`);

  } catch (error) {
    console.error('Error seeding diaries and notebooks pricing data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedDiariesNotebooks()
  .catch((error) => {
    console.error('Error running seed script:', error);
    process.exit(1);
  });
