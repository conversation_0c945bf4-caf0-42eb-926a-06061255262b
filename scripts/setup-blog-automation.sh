#!/bin/bash

# <PERSON><PERSON>t to set up automated blog post generation
# This script sets up a cron job to run the blog post generation script daily
# Usage: ./scripts/setup-blog-automation.sh

# Exit on error
set -e

echo "🔄 Setting up automated blog post generation..."

# Get the absolute path to the project directory
PROJECT_DIR=$(pwd)

# Check if the project directory is correct
if [ ! -f "$PROJECT_DIR/package.json" ]; then
  echo "❌ Error: This script must be run from the project root directory"
  exit 1
fi

# Check if the scheduled-blog-post.ts script exists
if [ ! -f "$PROJECT_DIR/scripts/scheduled-blog-post.ts" ]; then
  echo "❌ Error: scheduled-blog-post.ts script not found"
  exit 1
fi

# Create a wrapper script that will be called by cron
WRAPPER_SCRIPT="$PROJECT_DIR/scripts/run-blog-automation.sh"

echo "📝 Creating wrapper script at $WRAPPER_SCRIPT"

cat > "$WRAPPER_SCRIPT" << EOL
#!/bin/bash

# Wrapper script for running the blog post automation
# This script is called by cron

# Change to the project directory
cd $PROJECT_DIR

# Load environment variables
if [ -f .env ]; then
  export \$(grep -v '^#' .env | xargs)
fi

# Run the blog post generation script
echo "Running blog post generation script at \$(date)"
npx ts-node $PROJECT_DIR/scripts/scheduled-blog-post.ts --run-once >> $PROJECT_DIR/logs/scheduled-blog-posts.log 2>&1

# Exit with the script's exit code
exit \$?
EOL

# Make the wrapper script executable
chmod +x "$WRAPPER_SCRIPT"

echo "✅ Wrapper script created"

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_DIR/logs"

# Set up the cron job to run daily at 6:00 AM
echo "🕒 Setting up cron job to run daily at 6:00 AM"

# Check if crontab is available
if ! command -v crontab &> /dev/null; then
  echo "❌ Error: crontab command not found. Please install cron."
  exit 1
fi

# Create a temporary file for the crontab
TEMP_CRONTAB=$(mktemp)

# Export current crontab
crontab -l > "$TEMP_CRONTAB" 2>/dev/null || echo "" > "$TEMP_CRONTAB"

# Check if the cron job already exists
if grep -q "run-blog-automation.sh" "$TEMP_CRONTAB"; then
  echo "⚠️ Cron job already exists. Updating..."
  # Remove the existing cron job
  sed -i '/run-blog-automation.sh/d' "$TEMP_CRONTAB"
fi

# Add the new cron job
echo "0 6 * * * $WRAPPER_SCRIPT" >> "$TEMP_CRONTAB"

# Install the new crontab
crontab "$TEMP_CRONTAB"

# Remove the temporary file
rm "$TEMP_CRONTAB"

echo "✅ Cron job set up successfully"
echo "📊 Blog posts will be generated daily at 6:00 AM"
echo "📝 Logs will be written to $PROJECT_DIR/logs/scheduled-blog-posts.log"

# Test the script
echo "🧪 Testing the blog post generation script..."
echo "This will not publish a post, just check if the script runs correctly"

# Run the script with --run-once flag
"$WRAPPER_SCRIPT"

echo "✅ Setup completed successfully"
echo "You can also manage scheduled blog posts from the admin interface at /admin/scheduled-blog-posts"
