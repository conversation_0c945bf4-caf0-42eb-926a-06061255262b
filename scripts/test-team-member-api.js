/**
 * Test script for team member API
 *
 * This script tests the team member API by:
 * 1. Fetching all team members
 * 2. Fetching a specific team member
 * 3. Updating a team member (without image)
 *
 * Usage: node scripts/test-team-member-api.js [team-member-id]
 */

const fetch = require('node-fetch');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Configuration
const API_BASE_URL = 'https://mocky.co.admin/api';
const TEST_IMAGE_PATH = path.join(__dirname, '../public/images/default-profile.jpg');

// Get team member ID from command line arguments or use a default
const teamMemberId = process.argv[2];

// Display configuration
console.log('Testing Team Member API:');
console.log(`  API Base URL: ${API_BASE_URL}`);
console.log(`  Team Member ID: ${teamMemberId || 'Not specified (will fetch first team member)'}`);
console.log('');

async function runTests() {
  try {
    console.log('🔍 Starting team member API tests...');

    // Test 1: Fetch all team members
    console.log('\n🧪 TEST 1: Fetch all team members');
    let firstTeamMemberId;

    try {
      console.log(`⏳ Fetching team members...`);
      const response = await fetch(`${API_BASE_URL}/admin/team`);

      if (!response.ok) {
        throw new Error(`Failed to fetch team members: ${response.statusText}`);
      }

      const teamMembers = await response.json();
      console.log(`✅ Found ${teamMembers.length} team members`);

      if (teamMembers.length > 0) {
        firstTeamMemberId = teamMembers[0].id;
        console.log(`First team member: ${teamMembers[0].name} (${firstTeamMemberId})`);
      } else {
        console.log('No team members found');
        process.exit(0);
      }
    } catch (error) {
      console.error(`❌ Test 1 failed: ${error.message}`);
      process.exit(1);
    }

    // Use provided ID or first team member ID
    const idToUse = teamMemberId || firstTeamMemberId;
    if (!idToUse) {
      console.error('❌ No team member ID available for testing');
      process.exit(1);
    }

    // Test 2: Fetch specific team member
    console.log(`\n🧪 TEST 2: Fetch team member with ID ${idToUse}`);
    let teamMember;

    try {
      console.log(`⏳ Fetching team member...`);
      const response = await fetch(`${API_BASE_URL}/admin/team/${idToUse}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch team member: ${response.statusText}`);
      }

      teamMember = await response.json();
      console.log(`✅ Found team member: ${teamMember.name}`);
      console.log(`  Role: ${teamMember.role}`);
      console.log(`  Image: ${teamMember.imageSrc}`);
    } catch (error) {
      console.error(`❌ Test 2 failed: ${error.message}`);
      process.exit(1);
    }

    // Test 3: Update team member (without image)
    console.log(`\n🧪 TEST 3: Update team member (without image)`);

    try {
      const formData = new FormData();

      // Use existing data with a small change to the name
      formData.append('name', `${teamMember.name} (Test)`);
      formData.append('role', teamMember.role);
      formData.append('bio', teamMember.bio);
      formData.append('order', teamMember.order);

      if (teamMember.linkedinUrl) formData.append('linkedinUrl', teamMember.linkedinUrl);
      if (teamMember.twitterUrl) formData.append('twitterUrl', teamMember.twitterUrl);
      if (teamMember.githubUrl) formData.append('githubUrl', teamMember.githubUrl);
      if (teamMember.emailAddress) formData.append('emailAddress', teamMember.emailAddress);

      console.log(`⏳ Updating team member...`);
      const response = await fetch(`${API_BASE_URL}/admin/team/${idToUse}`, {
        method: 'PUT',
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Failed to update team member: ${result.error || response.statusText}`);
      }

      console.log(`✅ Updated team member successfully`);

      // Restore original name
      formData.set('name', teamMember.name);

      console.log(`⏳ Restoring original name...`);
      const restoreResponse = await fetch(`${API_BASE_URL}/admin/team/${idToUse}`, {
        method: 'PUT',
        body: formData,
      });

      if (!restoreResponse.ok) {
        console.warn(`⚠️ Failed to restore original name: ${restoreResponse.statusText}`);
      } else {
        console.log(`✅ Restored original name`);
      }
    } catch (error) {
      console.error(`❌ Test 3 failed: ${error.message}`);
      process.exit(1);
    }

    // Test 4: Update team member with image
    console.log(`\n🧪 TEST 4: Update team member with image`);

    try {
      // Check if test image exists
      if (!fs.existsSync(TEST_IMAGE_PATH)) {
        console.error(`❌ Test image not found: ${TEST_IMAGE_PATH}`);
        process.exit(1);
      }

      const formData = new FormData();

      // Use existing data
      formData.append('name', teamMember.name);
      formData.append('role', teamMember.role);
      formData.append('bio', teamMember.bio);
      formData.append('order', teamMember.order);

      if (teamMember.linkedinUrl) formData.append('linkedinUrl', teamMember.linkedinUrl);
      if (teamMember.twitterUrl) formData.append('twitterUrl', teamMember.twitterUrl);
      if (teamMember.githubUrl) formData.append('githubUrl', teamMember.githubUrl);
      if (teamMember.emailAddress) formData.append('emailAddress', teamMember.emailAddress);

      // Add test image
      const imageBuffer = fs.readFileSync(TEST_IMAGE_PATH);
      formData.append('image', imageBuffer, {
        filename: 'test-profile.jpg',
        contentType: 'image/jpeg',
      });

      console.log(`⏳ Updating team member with image...`);
      const response = await fetch(`${API_BASE_URL}/admin/team/${idToUse}`, {
        method: 'PUT',
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Failed to update team member with image: ${result.error || response.statusText}`);
      }

      if (result.warning) {
        console.warn(`⚠️ Update warning: ${result.warning}`);
      }

      console.log(`✅ Updated team member with image successfully`);
      console.log(`  New image URL: ${result.imageSrc || (result.teamMember && result.teamMember.imageSrc) || 'N/A'}`);
    } catch (error) {
      console.error(`❌ Test 4 failed: ${error.message}`);
      // Continue to next test even if this one fails
    }

    console.log('\n✅ All tests completed!');
  } catch (error) {
    console.error(`\n❌ Tests failed with error: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run the tests
runTests();
