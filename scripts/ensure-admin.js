const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function hashPassword(password) {
  return bcrypt.hash(password, 12);
}

async function ensureAdminUser() {
  try {
    console.log('Ensuring admin user exists...');
    
    // Check if admin role exists
    let adminRole = await prisma.role.findFirst({
      where: { name: 'admin' }
    });
    
    // Create admin role if it doesn't exist
    if (!adminRole) {
      console.log('Creating admin role...');
      adminRole = await prisma.role.create({
        data: {
          name: 'admin',
          description: 'Administrator with full access',
          permissions: ['*']
        }
      });
      console.log('Admin role created successfully.');
    } else {
      console.log('Admin role already exists.');
    }
    
    // Check if admin user exists
    const adminUsername = '<EMAIL>';
    const adminEmail = '<EMAIL>';
    const adminPassword = 'Jack75522r';
    
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: adminUsername },
          { email: adminEmail }
        ]
      }
    });
    
    if (existingUser) {
      console.log('Admin user already exists.');
      
      // Update the admin role if needed
      if (existingUser.roleId !== adminRole.id) {
        await prisma.user.update({
          where: { id: existingUser.id },
          data: { roleId: adminRole.id }
        });
        console.log('Updated admin user role.');
      }
    } else {
      console.log('Creating admin user...');
      
      // Hash the password
      const passwordHash = await hashPassword(adminPassword);
      
      // Create the admin user
      await prisma.user.create({
        data: {
          username: adminUsername,
          email: adminEmail,
          name: 'Admin',
          passwordHash,
          roleId: adminRole.id,
          active: true
        }
      });
      
      console.log('Admin user created successfully.');
    }
    
    console.log('Admin user check completed.');
  } catch (error) {
    console.error('Error ensuring admin user exists:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
ensureAdminUser();
