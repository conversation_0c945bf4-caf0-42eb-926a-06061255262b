const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;
const { glob } = require('glob');

const QUALITY = 80;
const IMAGE_SIZES = [640, 750, 828, 1080, 1200, 1920, 2048];

async function optimizeImage(inputPath, outputPath, options = {}) {
  const { width, height, quality = QUALITY } = options;
  
  try {
    const image = sharp(inputPath);
    const metadata = await image.metadata();

    // Don't upscale images
    const targetWidth = width ? Math.min(width, metadata.width) : metadata.width;
    const targetHeight = height ? Math.min(height, metadata.height) : null;

    let pipeline = image.resize(targetWidth, targetHeight, {
      fit: 'inside',
      withoutEnlargement: true
    });

    // Optimize based on input format
    const ext = path.extname(inputPath).toLowerCase();
    switch (ext) {
      case '.jpg':
      case '.jpeg':
        pipeline = pipeline.jpeg({ quality, mozjpeg: true });
        break;
      case '.png':
        pipeline = pipeline.png({ quality, compressionLevel: 9 });
        break;
      case '.webp':
        pipeline = pipeline.webp({ quality });
        break;
      default:
        throw new Error(`Unsupported image format: ${ext}`);
    }

    await pipeline.toFile(outputPath);
    console.log(`✓ Optimized: ${path.basename(inputPath)}`);
  } catch (error) {
    console.error(`✗ Failed to optimize ${inputPath}:`, error.message);
  }
}

async function generateResponsiveImages(inputPath) {
  const dir = path.dirname(inputPath);
  const ext = path.extname(inputPath);
  const basename = path.basename(inputPath, ext);

  // Create responsive versions
  for (const width of IMAGE_SIZES) {
    const outputPath = path.join(dir, `${basename}-${width}w${ext}`);
    await optimizeImage(inputPath, outputPath, { width });
  }

  // Create WebP versions
  for (const width of IMAGE_SIZES) {
    const outputPath = path.join(dir, `${basename}-${width}w.webp`);
    await optimizeImage(inputPath, outputPath, { width });
  }
}

async function processDirectory(directory) {
  try {
    const files = await glob(`${directory}/**/*.{jpg,jpeg,png}`, { nodir: true });
    
    console.log(`Found ${files.length} images to process`);
    
    for (const file of files) {
      await generateResponsiveImages(file);
    }
    
    console.log('Image optimization complete!');
  } catch (error) {
    console.error('Error processing directory:', error);
  }
}

// Process public images directory
const PUBLIC_IMAGES = path.join(process.cwd(), 'public', 'images');
processDirectory(PUBLIC_IMAGES).catch(console.error); 