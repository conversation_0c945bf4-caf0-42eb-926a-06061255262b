#!/bin/bash

# Script to populate tech services data in the services table
# Usage: ./scripts/populate-tech-services-table.sh

# Exit on error
set -e

echo "🔄 Starting tech services data population in services table..."

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Run the seed script
echo "🌱 Seeding tech services data..."
node scripts/seed-tech-services-table.js

echo "✅ Tech services data population completed successfully!"
