/**
 * Seed script for the receipting system
 * This script will populate the database with sample data for:
 * - Transactions
 * - Services
 * - Receipts
 * - Receipt Items
 *
 * Run with: npx ts-node scripts/seed-receipting-system.ts
 */
import { PrismaClient } from '@prisma/client';
import { parseTransactionMessage } from '../src/utils/transactionParser';

const prisma = new PrismaClient();

// Sample M-PESA transaction messages
const sampleTransactionMessages = [
  "TEE5PIQ2H7 completed. You have received KES 1500 from Wesley Moturi ************ for account MOCKY GRAPHICS LIMITED 7934479 on 14/05/2025 at 05:00 PM. KCB Go Ahead.",
  "TED3JYV8SX completed. You have received KES 1500 from DAMARIS JUMA ************ for account MOCKY GRAPHICS LIMITED 7934479 on 13/05/2025 at 12:55 PM. KCB Go Ahead.",
  "TDF9TBL1BV completed. You have received KES 2500 from SHUKRI OMAR ************ for account MOCKY GRAPHICS LIMITED 7934479 on 15/04/2025 at 11:00 AM. KCB Go Ahead.",
  "TGH7KLM9NP completed. You have received KES 5000 from JOHN KAMAU ************ for account MOCKY GRAPHICS LIMITED 7934479 on 16/05/2025 at 09:30 AM. KCB Go Ahead.",
  "TJK4LMN5PQ completed. You have received KES 10000 from MARY WANJIKU ************ for account MOCKY GRAPHICS LIMITED 7934479 on 17/05/2025 at 02:15 PM. KCB Go Ahead.",
  "TLM7NPQ8RS completed. You have received KES 7500 from PETER OCHIENG ************ for account MOCKY GRAPHICS LIMITED 7934479 on 18/05/2025 at 10:45 AM. KCB Go Ahead.",
  "TPQ9RST1UV completed. You have received KES 3000 from JANE AKINYI ************ for account MOCKY GRAPHICS LIMITED 7934479 on 19/05/2025 at 04:20 PM. KCB Go Ahead.",
  "TST2UVW3XY completed. You have received KES 8500 from SAMUEL MAINA ************ for account MOCKY GRAPHICS LIMITED 7934479 on 20/05/2025 at 11:30 AM. KCB Go Ahead."
];

// Sample services
const sampleServices = [
  {
    name: 'Basic Logo Package',
    description: 'Professional logo design with 5 initial concepts and 2 revision rounds',
    price: 5000,
    category: 'Design'
  },
  {
    name: 'Professional Logo Package',
    description: 'Premium logo design with 7 initial concepts and 3 revision rounds',
    price: 10000,
    category: 'Design'
  },
  {
    name: 'Premium Logo Package',
    description: 'Comprehensive logo design with 9 initial concepts and unlimited revisions',
    price: 15000,
    category: 'Design'
  },
  {
    name: 'Business Card Design',
    description: 'Professional business card design with print-ready files',
    price: 2500,
    category: 'Design'
  },
  {
    name: 'Flyer Design',
    description: 'Eye-catching flyer design for your marketing needs',
    price: 3000,
    category: 'Design'
  },
  {
    name: 'Brochure Design',
    description: 'Professional brochure design with up to 4 pages',
    price: 5000,
    category: 'Design'
  },
  {
    name: 'Social Media Graphics',
    description: 'Set of 5 social media graphics for your brand',
    price: 3500,
    category: 'Design'
  },
  {
    name: 'Website Design',
    description: 'Custom website design with responsive layouts',
    price: 15000,
    category: 'Development'
  },
  {
    name: 'E-commerce Website',
    description: 'Full e-commerce website with product management and payment integration',
    price: 50000,
    category: 'Development'
  },
  {
    name: 'Website Maintenance',
    description: 'Monthly website maintenance and updates',
    price: 5000,
    category: 'Development'
  }
];

// Function to generate a unique receipt number
async function generateReceiptNumber(): Promise<string> {
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');

  // Get the count of receipts for today
  const todayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const todayEnd = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);

  const count = await prisma.receipt.count({
    where: {
      createdAt: {
        gte: todayStart,
        lt: todayEnd
      }
    }
  });

  // Generate receipt number in format: MG-YYMMDD-XXX
  const sequence = (count + 1).toString().padStart(3, '0');
  return `MG-${year}${month}${day}-${sequence}`;
}

// Main function to seed the database
async function main() {
  console.log('Starting to seed receipting system data...');

  // Step 1: Create services
  console.log('Creating services...');
  const createdServices = [];

  for (const service of sampleServices) {
    // Check if service already exists
    const existingService = await prisma.service.findFirst({
      where: {
        name: service.name
      }
    });

    if (!existingService) {
      const createdService = await prisma.service.create({
        data: service
      });
      console.log(`Created service: ${createdService.name}`);
      createdServices.push(createdService);
    } else {
      console.log(`Service already exists: ${existingService.name}`);
      createdServices.push(existingService);
    }
  }

  // Step 2: Create transactions
  console.log('\nCreating transactions...');
  const createdTransactions = [];

  for (const message of sampleTransactionMessages) {
    // Parse the message
    const parsedData = parseTransactionMessage(message);

    if (!parsedData) {
      console.log(`Failed to parse message: ${message}`);
      continue;
    }

    // Check if transaction already exists
    const existingTransaction = await prisma.transaction.findUnique({
      where: {
        transactionId: parsedData.transactionId
      }
    });

    if (existingTransaction) {
      console.log(`Transaction ${parsedData.transactionId} already exists`);
      createdTransactions.push(existingTransaction);
      continue;
    }

    // Create the transaction
    const transaction = await prisma.transaction.create({
      data: {
        transactionId: parsedData.transactionId,
        amount: parsedData.amount,
        customerName: parsedData.customerName,
        phoneNumber: parsedData.phoneNumber,
        transactionDate: parsedData.transactionDate,
        rawMessage: parsedData.rawMessage,
        status: 'pending'
      }
    });

    console.log(`Created transaction: ${transaction.transactionId} - ${transaction.customerName} - KES ${transaction.amount}`);
    createdTransactions.push(transaction);
  }

  // Step 3: Create receipts for some transactions
  console.log('\nCreating receipts...');

  // Create receipts for the first 3 transactions
  for (let i = 0; i < 3; i++) {
    if (i >= createdTransactions.length) break;

    const transaction = createdTransactions[i];

    // Skip if transaction already has a receipt
    const existingReceipt = await prisma.receipt.findUnique({
      where: {
        transactionId: transaction.id
      }
    });

    if (existingReceipt) {
      console.log(`Transaction ${transaction.transactionId} already has a receipt`);
      continue;
    }

    // Generate receipt number
    const receiptNumber = await generateReceiptNumber();

    // Select random services for this receipt
    const serviceCount = Math.floor(Math.random() * 3) + 1; // 1 to 3 services
    const selectedServices = [];

    for (let j = 0; j < serviceCount; j++) {
      const randomIndex = Math.floor(Math.random() * createdServices.length);
      selectedServices.push(createdServices[randomIndex]);
    }

    // Calculate total amount
    let totalAmount = 0;
    const receiptItems = [];

    for (const service of selectedServices) {
      const quantity = Math.floor(Math.random() * 2) + 1; // 1 or 2
      const unitPrice = Number(service.price);
      const totalPrice = unitPrice * quantity;
      totalAmount += totalPrice;

      receiptItems.push({
        serviceId: service.id,
        quantity,
        unitPrice,
        totalPrice,
        description: service.name
      });
    }

    // Calculate balance
    const amountPaid = Number(transaction.amount);
    const balance = totalAmount - amountPaid;
    const status = balance <= 0 ? 'paid' : 'issued';

    // Create the receipt
    const receipt = await prisma.receipt.create({
      data: {
        receiptNumber,
        totalAmount,
        amountPaid,
        balance,
        customerName: transaction.customerName,
        phoneNumber: transaction.phoneNumber,
        email: null,
        status,
        notes: `Sample receipt for ${transaction.customerName}`,
        issuedAt: new Date(),
        paidAt: status === 'paid' ? new Date() : null,
        transactionId: transaction.id,
        items: {
          create: receiptItems
        }
      },
      include: {
        items: true
      }
    });

    // Update transaction status
    await prisma.transaction.update({
      where: {
        id: transaction.id
      },
      data: {
        status: 'processed',
        processedAt: new Date()
      }
    });

    console.log(`Created receipt: ${receipt.receiptNumber} - ${receipt.customerName} - KES ${receipt.totalAmount}`);
    console.log(`  Items: ${receipt.items.length}`);
    console.log(`  Balance: KES ${receipt.balance}`);
    console.log(`  Status: ${receipt.status}`);
  }

  console.log('\nSeeding completed successfully!');
}

// Run the main function
main()
  .catch((e) => {
    console.error('Error seeding receipting system data:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
