#!/bin/bash

# <PERSON>ript to reorganize image directory structure
# Run from project root with: bash scripts/reorganize-images.sh

set -e
echo "Starting image directory reorganization..."

# Define paths
PUBLIC_DIR="/var/www/mocky.co.ke/public"
IMAGES_DIR="$PUBLIC_DIR/images"
BACKUP_DIR="/var/www/mocky.co.ke/image-backup-$(date +%Y%m%d-%H%M%S)"

# Create backup of existing images
echo "Creating backup of current images at $BACKUP_DIR"
mkdir -p $BACKUP_DIR
cp -r $IMAGES_DIR $BACKUP_DIR/

# Create new directory structure
echo "Creating new directory structure..."
mkdir -p $IMAGES_DIR/portfolio/{logos,cards,fliers,letterheads,websites,profiles,branding}
mkdir -p $IMAGES_DIR/content/{about,services,testimonials,hero,projects}
mkdir -p $IMAGES_DIR/uploads
mkdir -p $IMAGES_DIR/icons

# Move files to their proper locations
echo "Moving files to their new locations..."

# Move portfolio items to proper subdirectories
echo "Consolidating portfolio logos..."
find $IMAGES_DIR/logos -type f -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.webp" -o -name "*.gif" | xargs -I{} cp {} $IMAGES_DIR/portfolio/logos/ 2>/dev/null || true

echo "Consolidating web/websites..."
# Move web content to websites folder
if [ -d "$IMAGES_DIR/web" ]; then
  find $IMAGES_DIR/web -type f -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.webp" -o -name "*.gif" | xargs -I{} cp {} $IMAGES_DIR/portfolio/websites/ 2>/dev/null || true
fi

# Move other portfolio categories if needed
echo "Moving other portfolio categories..."
find $IMAGES_DIR/ -maxdepth 1 -type d | while read dir; do
  basename=$(basename "$dir")
  case "$basename" in
    "branding")
      find "$dir" -type f -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.webp" -o -name "*.gif" | xargs -I{} cp {} $IMAGES_DIR/portfolio/branding/ 2>/dev/null || true
      ;;
    "packaging")
      find "$dir" -type f -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.webp" -o -name "*.gif" | xargs -I{} cp {} $IMAGES_DIR/portfolio/branding/ 2>/dev/null || true
      ;;
  esac
done

# Move content images
echo "Organizing content images..."
if [ -d "$IMAGES_DIR/about" ]; then
  cp -r $IMAGES_DIR/about/* $IMAGES_DIR/content/about/ 2>/dev/null || true
fi

if [ -d "$IMAGES_DIR/hero" ]; then
  cp -r $IMAGES_DIR/hero/* $IMAGES_DIR/content/hero/ 2>/dev/null || true
fi

if [ -d "$IMAGES_DIR/services" ]; then
  cp -r $IMAGES_DIR/services/* $IMAGES_DIR/content/services/ 2>/dev/null || true
fi

if [ -d "$IMAGES_DIR/testimonials" ]; then
  cp -r $IMAGES_DIR/testimonials/* $IMAGES_DIR/content/testimonials/ 2>/dev/null || true
fi

if [ -d "$IMAGES_DIR/projects" ]; then
  cp -r $IMAGES_DIR/projects/* $IMAGES_DIR/content/projects/ 2>/dev/null || true
fi

# Move any SVG or small graphics to icons
echo "Moving icons and SVGs..."
find $IMAGES_DIR -name "*.svg" | xargs -I{} cp {} $IMAGES_DIR/icons/ 2>/dev/null || true

# Handle uploads symlink
echo "Handling uploads directory..."
if [ -L "$PUBLIC_DIR/uploads" ]; then
  # Get the target of the symlink
  UPLOAD_TARGET=$(readlink "$PUBLIC_DIR/uploads")
  echo "Found uploads symlink pointing to $UPLOAD_TARGET"
  
  # Remove the symlink
  rm "$PUBLIC_DIR/uploads"
  
  # Create the directory
  mkdir -p "$PUBLIC_DIR/uploads"
  
  # If the target exists, copy content
  if [ -d "$UPLOAD_TARGET" ]; then
    cp -r $UPLOAD_TARGET/* $PUBLIC_DIR/uploads/ 2>/dev/null || true
  fi
fi

# Handle web symlink
echo "Handling web directory..."
if [ -L "$PUBLIC_DIR/web" ]; then
  # Remove the symlink
  rm "$PUBLIC_DIR/web"
fi

# Add .gitkeep files to maintain directory structure
echo "Adding .gitkeep files..."
find $IMAGES_DIR -type d -empty | xargs -I{} touch "{}/.gitkeep"

# Set proper permissions
echo "Setting permissions..."
chown -R deployer:www-data $IMAGES_DIR
find $IMAGES_DIR -type d -exec chmod 775 {} \;
find $IMAGES_DIR -type f -exec chmod 664 {} \;

echo "Image reorganization complete!"
echo "Backup saved at: $BACKUP_DIR"
echo ""
echo "New structure:"
echo "- /public/images/portfolio/ - All portfolio work (logos, cards, etc.)"
echo "- /public/images/content/ - Content-specific images (about, hero, etc.)"
echo "- /public/images/uploads/ - User-uploaded images"
echo "- /public/images/icons/ - UI icons and small graphics"
echo ""
echo "Please update API routes and code references to use the new paths." 