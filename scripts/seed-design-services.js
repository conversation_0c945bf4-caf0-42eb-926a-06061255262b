// Script to seed design services pricing data for the Kenyan market
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

// Initialize Prisma client
const prisma = new PrismaClient();

// Define design services with pricing relevant to the Kenyan market
const designServices = [
  {
    service: 'Logo Design',
    price: 15000,
    description: 'Professional custom logo design for your brand identity',
    features: [
      '3 unique concepts',
      'Unlimited revisions',
      'All file formats (AI, EPS, PDF, PNG, JPG)',
      'Full copyright ownership',
      'Brand style guide',
      'Social media kit'
    ],
    icon: 'palette',
    popular: true
  },
  {
    service: 'Business Card Design',
    price: 5000,
    description: 'Professional business card design that makes a lasting impression',
    features: [
      '2 unique concepts',
      '3 revision rounds',
      'Print-ready files (CMYK)',
      'Digital formats (RGB)',
      'Double-sided design',
      'Delivery in 3 days'
    ],
    icon: 'id-card',
    popular: false
  },
  {
    service: 'Flyer Design',
    price: 7500,
    description: 'Eye-catching flyer designs for effective marketing campaigns',
    features: [
      '2 design concepts',
      '3 revision rounds',
      'Print-ready files',
      'Web-optimized versions',
      'Custom illustrations',
      'Delivery in 4 days'
    ],
    icon: 'file-image',
    popular: false
  },
  {
    service: 'Brochure Design',
    price: 12000,
    description: 'Professional multi-page brochure design for your business',
    features: [
      'Up to 8-page design',
      '3 revision rounds',
      'Print-ready files',
      'Digital formats',
      'Custom graphics',
      'Layout & typography',
      'Delivery in 7 days'
    ],
    icon: 'book-open',
    popular: true
  },
  {
    service: 'Social Media Graphics Package',
    price: 8500,
    description: 'Consistent and engaging social media graphics for your brand',
    features: [
      '10 social media templates',
      'Customized for Facebook, Instagram, Twitter',
      '3 revision rounds',
      'Source files included',
      'Sizing for all platforms',
      'Delivery in 5 days'
    ],
    icon: 'share-alt',
    popular: true
  },
  {
    service: 'Website UI Design',
    price: 35000,
    description: 'Custom website user interface design for your digital presence',
    features: [
      'Up to 5 page designs',
      'Mobile responsive layouts',
      'User experience optimization',
      'Interactive elements',
      'Source files included',
      'Delivery in 14 days'
    ],
    icon: 'desktop',
    popular: true
  },
  {
    service: 'Company Profile Design',
    price: 18000,
    description: 'Professional company profile design to showcase your business',
    features: [
      'Up to 12 pages',
      'Custom cover design',
      'Professional layout',
      'Image selection assistance',
      'Print & digital formats',
      'Delivery in 10 days'
    ],
    icon: 'building',
    popular: false
  },
  {
    service: 'Billboard Design',
    price: 20000,
    description: 'High-impact billboard design for maximum visibility',
    features: [
      '2 design concepts',
      '3 revision rounds',
      'Large format print-ready files',
      'Attention-grabbing layout',
      'Strategic message placement',
      'Delivery in 7 days'
    ],
    icon: 'map-signs',
    popular: false
  },
  {
    service: 'Packaging Design',
    price: 25000,
    description: 'Custom product packaging design that stands out on shelves',
    features: [
      '3 design concepts',
      'Unlimited revisions',
      'Dieline creation',
      'Print-ready files',
      '3D mockup visualization',
      'Delivery in 14 days'
    ],
    icon: 'box',
    popular: false
  },
  {
    service: 'T-shirt Design',
    price: 8000,
    description: 'Creative t-shirt designs for your brand, events, or merchandise',
    features: [
      '2 unique concepts',
      '3 revision rounds',
      'Print-ready files',
      'Vector source files',
      'Mockup visualization',
      'Delivery in 5 days'
    ],
    icon: 'tshirt',
    popular: false
  },
  {
    service: 'Banner Design',
    price: 6500,
    description: 'Eye-catching banner designs for events, trade shows, or online advertising',
    features: [
      '2 design concepts',
      '3 revision rounds',
      'Print & web formats',
      'Custom graphics',
      'Attention-grabbing layout',
      'Delivery in 4 days'
    ],
    icon: 'image',
    popular: false
  },
  {
    service: 'Email Newsletter Design',
    price: 9000,
    description: 'Professional email newsletter templates that convert',
    features: [
      '2 template designs',
      '3 revision rounds',
      'Responsive HTML templates',
      'Compatible with major email platforms',
      'Editable sections',
      'Delivery in 6 days'
    ],
    icon: 'envelope',
    popular: false
  }
];

// Main function to seed the database
async function seedDesignServices() {
  try {
    console.log('Starting to seed design services pricing data...');

    // Create a backup of existing pricing data
    const existingPricing = await prisma.pricing.findMany();
    console.log(`Found ${existingPricing.length} existing pricing items`);

    // Create a map of existing services (case-insensitive)
    const existingServicesMap = new Map();
    existingPricing.forEach(item => {
      existingServicesMap.set(item.service.toLowerCase(), item);
    });

    let created = 0;
    let updated = 0;

    // Process each design service
    for (const service of designServices) {
      // Check if service already exists (case-insensitive)
      const existingService = existingServicesMap.get(service.service.toLowerCase());

      if (existingService) {
        // Update existing service
        await prisma.pricing.update({
          where: { id: existingService.id },
          data: {
            price: service.price,
            description: service.description,
            features: service.features,
            icon: service.icon,
            popular: service.popular
          }
        });
        updated++;
        console.log(`Updated existing service: ${service.service}`);
      } else {
        // Create new service
        await prisma.pricing.create({
          data: {
            service: service.service,
            price: service.price,
            description: service.description,
            features: service.features,
            icon: service.icon,
            popular: service.popular
          }
        });
        created++;
        console.log(`Created new service: ${service.service}`);
      }
    }

    console.log(`Successfully processed design services pricing data:`);
    console.log(`- Created: ${created} new services`);
    console.log(`- Updated: ${updated} existing services`);
    console.log(`- Total: ${designServices.length} services processed`);

  } catch (error) {
    console.error('Error seeding design services pricing data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedDesignServices()
  .catch((error) => {
    console.error('Error running seed script:', error);
    process.exit(1);
  });
