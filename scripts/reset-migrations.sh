#!/bin/bash

# Script to reset Prisma migrations and create a new baseline
# Usage: ./scripts/reset-migrations.sh

# Exit on error
set -e

echo "🔄 Starting migration reset process..."

# Create a backup of the current database
echo "📦 Creating database backup..."
BACKUP_FILE="backups/backup_migration_reset_$(date +%Y%m%d_%H%M%S).sql"
sudo -u postgres pg_dump mocky > "$BACKUP_FILE"
echo "✅ Database backup created at $BACKUP_FILE"

# Clear the _prisma_migrations table
echo "🧹 Clearing _prisma_migrations table..."
sudo -u postgres psql -d mocky -c "TRUNCATE TABLE _prisma_migrations;"

# Backup the current migrations directory
echo "📦 Backing up current migrations..."
MIGRATIONS_BACKUP="prisma/migrations_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$MIGRATIONS_BACKUP"
cp -r prisma/migrations/* "$MIGRATIONS_BACKUP"
echo "✅ Migrations backed up to $MIGRATIONS_BACKUP"

# Remove old migrations
echo "🗑️ Removing old migrations..."
rm -rf prisma/migrations
mkdir -p prisma/migrations

# Copy the new baseline migration
echo "📋 Installing new baseline migration..."
cp -r prisma/migrations_new/20250513000000_baseline prisma/migrations/

# Mark the migration as applied
echo "✅ Marking baseline migration as applied..."
sudo -u postgres psql -d mocky -c "INSERT INTO _prisma_migrations (id, checksum, migration_name, applied_steps_count, finished_at) VALUES ('$(uuidgen)', 'baseline_migration_checksum', '20250513000000_baseline', 1, NOW());"

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

echo "✅ Migration reset completed successfully!"
echo "🔍 The database now has a clean migration history with a single baseline migration."
echo "⚠️ Note: For future schema changes, use 'npx prisma migrate dev' as usual."
