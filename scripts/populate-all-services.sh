#!/bin/bash

# Master script to populate all services pricing data
# Usage: ./scripts/populate-all-services.sh

# Exit on error
set -e

echo "🔄 Starting population of all services pricing data..."

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Run all the individual seeding scripts
echo "🌱 Seeding services pricing data..."
node scripts/seed-services-pricing.js

echo "🌱 Seeding printing services pricing data..."
node scripts/seed-printing-services.js

echo "🌱 Seeding office essentials pricing data..."
node scripts/seed-office-essentials.js

echo "🌱 Seeding diaries and notebooks pricing data..."
node scripts/seed-diaries-notebooks.js

echo "🌱 Seeding gift sets pricing data..."
node scripts/seed-gift-sets.js

echo "🌱 Seeding drinkware pricing data..."
node scripts/seed-drinkware.js

echo "🌱 Seeding tech services pricing data..."
node scripts/seed-tech-services.js

echo "✅ All services pricing data population completed successfully!"
echo "📊 Total number of services added:"
curl -s http://localhost:3000/api/catalogue | jq | grep -c "id"
