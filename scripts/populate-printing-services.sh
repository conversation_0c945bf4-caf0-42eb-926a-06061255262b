#!/bin/bash

# Script to populate printing services pricing data
# Usage: ./scripts/populate-printing-services.sh

# Exit on error
set -e

echo "🔄 Starting printing services pricing data population..."

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Run the seed script
echo "🌱 Seeding printing services pricing data..."
node scripts/seed-printing-services.js

echo "✅ Printing services pricing data population completed successfully!"
