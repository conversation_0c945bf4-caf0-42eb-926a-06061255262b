const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Define categories for the website
const categories = [
  {
    name: 'Web Development',
    slug: 'web-development',
    description: 'Custom website development services including responsive design, e-commerce solutions, and content management systems.'
  },
  {
    name: 'Mobile Development',
    slug: 'mobile-development',
    description: 'Native and cross-platform mobile app development for Android and iOS platforms.'
  },
  {
    name: 'UI/UX Design',
    slug: 'ui-ux-design',
    description: 'User interface and experience design services focused on creating intuitive, engaging digital experiences.'
  },
  {
    name: 'Graphic Design',
    slug: 'graphic-design',
    description: 'Professional graphic design services including logos, branding materials, and marketing collateral.'
  },
  {
    name: 'Digital Marketing',
    slug: 'digital-marketing',
    description: 'Comprehensive digital marketing strategies including SEO, social media, and content marketing.'
  }
];

// Define sample blog posts with tags
const samplePosts = [
  {
    title: 'The Importance of Responsive Web Design',
    slug: 'importance-of-responsive-web-design',
    content: '<p>Responsive web design is crucial for modern websites. This article explains why.</p>',
    excerpt: 'Learn why responsive web design is essential for your business website.',
    author: '<PERSON>',
    category: 'web-development',
    tags: ['web-development', 'responsive-design', 'mobile-friendly', 'user-experience'],
    status: 'published',
    publishedAt: new Date()
  },
  {
    title: 'Top 5 UI Design Trends for 2023',
    slug: 'top-ui-design-trends-2023',
    content: '<p>Discover the latest UI design trends that are dominating the industry in 2023.</p>',
    excerpt: 'Stay ahead of the curve with these cutting-edge UI design trends.',
    author: 'Jane Smith',
    category: 'ui-ux-design',
    tags: ['ui-ux-design', 'design-trends', 'user-interface', 'web-design'],
    status: 'published',
    publishedAt: new Date()
  },
  {
    title: 'Effective Digital Marketing Strategies for Small Businesses',
    slug: 'digital-marketing-strategies-small-businesses',
    content: '<p>Learn how small businesses can leverage digital marketing to grow their customer base.</p>',
    excerpt: 'Practical digital marketing strategies tailored for small business owners.',
    author: 'Mike Johnson',
    category: 'digital-marketing',
    tags: ['digital-marketing', 'small-business', 'social-media', 'content-marketing', 'seo'],
    status: 'published',
    publishedAt: new Date()
  }
];

// Function to seed categories
async function seedCategories() {
  console.log('Seeding categories...');
  
  for (const category of categories) {
    // Check if category already exists
    const existingCategory = await prisma.category.findUnique({
      where: { slug: category.slug }
    });
    
    if (existingCategory) {
      console.log(`Category "${category.name}" already exists, skipping...`);
      continue;
    }
    
    // Create category
    await prisma.category.create({
      data: {
        name: category.name,
        slug: category.slug,
        description: category.description
      }
    });
    
    console.log(`Created category: ${category.name}`);
  }
  
  console.log('Categories seeding completed!');
}

// Function to seed sample blog posts with tags
async function seedSamplePosts() {
  console.log('Seeding sample blog posts with tags...');
  
  for (const post of samplePosts) {
    // Check if post already exists
    const existingPost = await prisma.blogPost.findUnique({
      where: { slug: post.slug }
    });
    
    if (existingPost) {
      console.log(`Blog post "${post.title}" already exists, skipping...`);
      continue;
    }
    
    // Create blog post
    const createdPost = await prisma.blogPost.create({
      data: {
        title: post.title,
        slug: post.slug,
        content: post.content,
        excerpt: post.excerpt,
        author: post.author,
        category: post.category,
        tags: post.tags,
        status: post.status,
        publishedAt: post.publishedAt
      }
    });
    
    console.log(`Created blog post: ${post.title} with tags: ${post.tags.join(', ')}`);
  }
  
  console.log('Sample blog posts seeding completed!');
}

// Function to list all blog posts with their tags
async function listBlogPosts() {
  console.log('\nListing all blog posts with their tags:');
  
  const blogPosts = await prisma.blogPost.findMany({
    select: {
      id: true,
      title: true,
      category: true,
      tags: true
    }
  });
  
  console.log(`Found ${blogPosts.length} blog posts:`);
  
  blogPosts.forEach(post => {
    console.log(`- ${post.title} (Category: ${post.category || 'None'})`);
    console.log(`  Tags: ${post.tags.length > 0 ? post.tags.join(', ') : 'No tags'}`);
  });
}

// Main function to run the seeding
async function main() {
  try {
    console.log('Starting database seeding...');
    
    // Seed categories
    await seedCategories();
    
    // Seed sample blog posts with tags
    await seedSamplePosts();
    
    // List all blog posts with their tags
    await listBlogPosts();
    
    console.log('Database seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the main function
main();
