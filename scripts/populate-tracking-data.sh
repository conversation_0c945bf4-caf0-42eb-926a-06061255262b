#!/bin/bash

# Script to populate tracking data
# Usage: ./scripts/populate-tracking-data.sh [--force]

# Exit on error
set -e

echo "🔄 Starting tracking data population..."

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Run the seed script
echo "🌱 Seeding tracking data..."
node scripts/seed-tracking-data.js $@

echo "✅ Tracking data population completed successfully!"
