import { createLogger } from './logger';
import { OpenAI } from 'openai';
import { PrismaClient } from '@prisma/client';
import slugify from 'slugify';

// Initialize Prisma client
const prisma = new PrismaClient();

// Create a logger for blog automation
const logger = createLogger('BlogAutomation');

// Initialize OpenAI client
let openaiInstance: OpenAI | null = null;

/**
 * Get the OpenAI client instance
 * @returns OpenAI client instance
 */
function getOpenAIClient(): OpenAI {
  if (!openaiInstance) {
    const apiKey = process.env.OPENAI_API_KEY;

    if (!apiKey) {
      logger.error('OpenAI API key is not configured');
      throw new Error('OpenAI API key is not configured. Please add it to your environment variables.');
    }

    logger.info('Initializing OpenAI client');
    openaiInstance = new OpenAI({
      apiKey,
    });
  }

  return openaiInstance;
}

// Define topic categories for blog posts
const BLOG_CATEGORIES = [
  'web-design',
  'graphic-design',
  'branding',
  'marketing',
  'ui-ux',
  'design-trends',
  'business',
  'technology'
];

// Define company services to ensure comprehensive coverage in blog posts
const COMPANY_SERVICES = [
  {
    name: 'Web Design & Development',
    description: 'Custom website design and development services',
    keywords: ['responsive design', 'website development', 'custom websites', 'e-commerce', 'WordPress']
  },
  {
    name: 'Graphic Design',
    description: 'Professional graphic design for print and digital media',
    keywords: ['logo design', 'print design', 'digital graphics', 'visual identity', 'illustrations']
  },
  {
    name: 'Branding & Identity',
    description: 'Comprehensive branding solutions for businesses',
    keywords: ['brand strategy', 'visual identity', 'logo design', 'brand guidelines', 'rebranding']
  },
  {
    name: 'UI/UX Design',
    description: 'User interface and experience design for digital products',
    keywords: ['user experience', 'interface design', 'usability', 'wireframing', 'prototyping']
  },
  {
    name: 'Digital Marketing',
    description: 'Strategic digital marketing services to grow your online presence',
    keywords: ['SEO', 'content marketing', 'social media', 'email marketing', 'PPC advertising']
  },
  {
    name: 'Content Creation',
    description: 'Professional content creation for various platforms',
    keywords: ['copywriting', 'blog writing', 'social media content', 'video production', 'photography']
  },
  {
    name: 'E-commerce Solutions',
    description: 'Custom e-commerce website development and optimization',
    keywords: ['online store', 'e-commerce platform', 'payment integration', 'product catalog', 'shopping cart']
  },
  {
    name: 'Mobile App Design',
    description: 'Intuitive and engaging mobile application design',
    keywords: ['app UI design', 'mobile UX', 'app prototyping', 'iOS design', 'Android design']
  }
];

// Define blog post generation options
interface AutomatedBlogPostOptions {
  category?: string;
  tone?: 'professional' | 'casual' | 'humorous' | 'technical' | 'conversational';
  length?: 'short' | 'medium' | 'long';
  targetAudience?: string;
  publishImmediately?: boolean;
}

/**
 * Generate a topic for a blog post based on the category
 * @param category The category to generate a topic for
 * @returns A generated topic
 */
async function generateBlogTopic(category: string): Promise<string> {
  try {
    logger.info(`Generating blog topic for category: ${category}`);

    const openai = getOpenAIClient();

    const prompt = `Generate an interesting and specific blog post topic about ${category} that would be relevant for a design agency's blog.
    The topic should be specific, not generic, and should be something that would be valuable to potential clients.
    Return only the topic as a string, without quotes or additional text.`;

    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: 'You are a helpful assistant that generates blog topics.' },
        { role: 'user', content: prompt }
      ],
      temperature: 0.8,
      max_tokens: 100,
    });

    const topic = completion.choices[0].message.content?.trim() || `Latest Trends in ${category}`;
    logger.info(`Generated topic: ${topic}`);

    return topic;
  } catch (error) {
    logger.error('Error generating blog topic:', error);
    // Fallback to a default topic if generation fails
    return `Latest Trends in ${category} for ${new Date().getFullYear()}`;
  }
}

/**
 * Generate and create a blog post
 * @param options Options for blog post generation
 * @returns The created blog post
 */
export async function generateAndCreateBlogPost(options: AutomatedBlogPostOptions = {}): Promise<any> {
  try {
    // Select a random category if none provided
    const category = options.category ||
      BLOG_CATEGORIES[Math.floor(Math.random() * BLOG_CATEGORIES.length)];

    // Generate a topic based on the category
    const topic = await generateBlogTopic(category);

    // Generate the blog post content
    logger.info(`Generating blog post for topic: ${topic}`);

    const openai = getOpenAIClient();

    // Determine content length based on the length option
    // Ensure minimum word count is at least 800 words for better SEO impact
    const wordCount = options.length === 'short'
      ? '1000-1200'
      : options.length === 'medium'
        ? '1500-2000'
        : '2500-3500';

    // Determine the tone
    const tone = options.tone || 'professional';

    // Determine the target audience
    const targetAudience = options.targetAudience || 'potential clients interested in design services';

    // Get the website URL from environment or use a default
    const websiteUrl = process.env.WEBSITE_URL || 'https://mocky.digital';

    // Select 3-4 services to focus on in this blog post
    const shuffledServices = [...COMPANY_SERVICES].sort(() => 0.5 - Math.random());
    const selectedServices = shuffledServices.slice(0, 4);

    // Extract service names and keywords for the prompt
    const serviceNames = selectedServices.map(service => service.name).join(', ');
    const serviceKeywords = selectedServices.flatMap(service => service.keywords).join(', ');

    // Build the prompt
    const prompt = `Write a comprehensive, in-depth blog post about "${topic}" in a ${tone} tone for ${targetAudience}.
    The blog post should be approximately ${wordCount} words (MINIMUM 1000 words) and should be well-structured with:

    1. A compelling title that includes SEO-friendly keywords
    2. An engaging introduction that mentions the main keywords and sets up the topic
    3. Well-organized sections with appropriate headings (at least 4-5 main sections)
    4. Practical insights, actionable advice, and real-world examples
    5. A detailed exploration of how this topic relates to different services we offer
    6. A strong conclusion that summarizes the key points and includes a call-to-action

    SERVICES TO HIGHLIGHT:
    This blog post should specifically mention and discuss the following services we offer:
    ${serviceNames}

    For each service, explain how it relates to the main topic and provide specific benefits or applications.

    SEO REQUIREMENTS:
    - Include at least 4-5 natural links to the website ${websiteUrl} in the content
    - Use these relevant keywords throughout the content: ${serviceKeywords}
    - Create a comprehensive resource that covers the topic from multiple angles
    - Optimize headings (H2, H3) with important keywords
    - Include statistics, examples, or case studies where appropriate
    - Add a strong call-to-action that encourages readers to visit our website or contact us for services
    - Ensure content is valuable, informative, and shareable

    Format the response with:
    TITLE: [The blog post title with SEO keywords]
    EXCERPT: [A 2-3 sentence summary of the blog post with main keywords]
    CONTENT: [The full blog post content with proper formatting, links to the website, and SEO optimization]`;

    // Generate the content
    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are a professional SEO-focused blog writer for a full-service digital agency specializing in web design, graphic design, branding, UI/UX, digital marketing, content creation, e-commerce, and mobile app design. Create comprehensive, in-depth content that demonstrates expertise across all our service areas and provides exceptional value to readers. Your content should be optimized for search engines with relevant keywords, proper heading structure, and strategic internal linking. Always include at least 4-5 links to the website in a natural way and ensure content is at least 1000 words. Focus on creating content that thoroughly covers the topic from multiple angles, mentions specific services we offer, and provides detailed explanations of how these services benefit clients. Your goal is to create authoritative content that positions our agency as experts in multiple service areas while providing genuine value to readers.'
        },
        { role: 'user', content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 4000,
    });

    const generatedContent = completion.choices[0].message.content || '';

    // Parse the generated content
    const titleMatch = generatedContent.match(/TITLE:\s*(.*?)(?=\n|EXCERPT:|CONTENT:|$)/);
    const excerptMatch = generatedContent.match(/EXCERPT:\s*(.*?)(?=\n|CONTENT:|$)/);
    const contentMatch = generatedContent.match(/CONTENT:\s*(.*)/);

    const title = titleMatch && titleMatch[1] ? titleMatch[1].trim() : `Blog Post about ${topic}`;
    const excerpt = excerptMatch && excerptMatch[1] ? excerptMatch[1].trim() : '';
    let content = contentMatch && contentMatch[1] ? contentMatch[1].trim() : generatedContent;

    // Validate content meets minimum requirements
    function validateContent(content: string): boolean {
      // Check word count (minimum 1000 words)
      const wordCount = content.split(/\s+/).length;
      if (wordCount < 1000) {
        logger.warn(`Generated content has only ${wordCount} words, which is less than the minimum 1000 words`);
        return false;
      }

      // Check for website links
      const websiteUrl = process.env.WEBSITE_URL || 'https://mocky.digital';
      const websiteDomain = websiteUrl.replace(/^https?:\/\//, '').replace(/\/.*$/, '');

      // Count links to the website
      const linkRegex = new RegExp(`href=["']https?://(www\\.)?${websiteDomain}`, 'gi');
      const linkMatches = content.match(linkRegex) || [];

      if (linkMatches.length < 4) {
        logger.warn(`Generated content has only ${linkMatches.length} links to the website, which is less than the minimum 4 links`);
        return false;
      }

      // Check for service mentions
      let servicesMentioned = 0;
      COMPANY_SERVICES.forEach(service => {
        // Check if service name is mentioned
        if (content.toLowerCase().includes(service.name.toLowerCase())) {
          servicesMentioned++;
          return;
        }

        // Check if at least 2 keywords for this service are mentioned
        let keywordsMentioned = 0;
        service.keywords.forEach(keyword => {
          if (content.toLowerCase().includes(keyword.toLowerCase())) {
            keywordsMentioned++;
          }
        });

        if (keywordsMentioned >= 2) {
          servicesMentioned++;
        }
      });

      if (servicesMentioned < 3) {
        logger.warn(`Generated content mentions only ${servicesMentioned} services, which is less than the minimum 3 services`);
        return false;
      }

      // Check for heading structure (at least 4 headings)
      const headingMatches = content.match(/<h[2-3][^>]*>.*?<\/h[2-3]>/gi) || [];
      if (headingMatches.length < 4) {
        logger.warn(`Generated content has only ${headingMatches.length} headings, which is less than the minimum 4 headings`);
        return false;
      }

      return true;
    }

    // If content doesn't meet requirements, regenerate or enhance it
    if (!validateContent(content)) {
      logger.info('Content does not meet requirements, enhancing it');

      // Add website link if missing
      const websiteUrl = process.env.WEBSITE_URL || 'https://mocky.digital';

      // Check word count
      const wordCount = content.split(/\s+/).length;
      if (wordCount < 1000) {
        logger.info(`Content has only ${wordCount} words, adding more comprehensive content`);

        // Add substantial content about our services to meet the minimum word count
        content += `
          <h2>Comprehensive Solutions for Modern Businesses</h2>
          <p>In today's competitive digital landscape, businesses need comprehensive solutions that address multiple aspects of their online presence. At <a href="${websiteUrl}">Mocky Digital</a>, we provide integrated services that work together to create a cohesive and effective digital strategy.</p>

          <h3>Web Design & Development Excellence</h3>
          <p>Your website is the cornerstone of your digital presence. At <a href="${websiteUrl}/services/web-design">Mocky Digital</a>, our web design and development team creates responsive, user-friendly websites that not only look stunning but also perform exceptionally well. We focus on:</p>
          <ul>
            <li>Custom website design tailored to your brand identity</li>
            <li>Responsive layouts that work flawlessly on all devices</li>
            <li>Optimized page loading speed for better user experience</li>
            <li>SEO-friendly structure to improve search engine rankings</li>
            <li>Intuitive navigation and user flows to increase conversions</li>
          </ul>

          <p>Our development process at <a href="${websiteUrl}/about">Mocky Digital</a> involves close collaboration with clients to ensure that every aspect of your website aligns with your business goals and meets the needs of your target audience.</p>

          <h3>Strategic Branding & Identity Development</h3>
          <p>Your brand is more than just a logo—it's the complete experience customers have with your business. Our <a href="${websiteUrl}/services/branding">branding specialists</a> develop comprehensive brand identities that communicate your values and resonate with your audience:</p>
          <ul>
            <li>Brand strategy development based on market research</li>
            <li>Logo design and visual identity systems</li>
            <li>Brand guidelines to ensure consistency across all platforms</li>
            <li>Voice and tone development for written communications</li>
            <li>Brand positioning to differentiate you from competitors</li>
          </ul>

          <p>A strong brand identity from <a href="${websiteUrl}/services/branding">Mocky Digital</a> creates recognition, builds trust, and establishes a foundation for all your marketing efforts.</p>

          <h3>UI/UX Design That Drives Engagement</h3>
          <p>User experience is critical to the success of any digital product. Our <a href="${websiteUrl}/services/ui-ux">UI/UX design team</a> creates intuitive, engaging interfaces that keep users coming back:</p>
          <ul>
            <li>User research and persona development</li>
            <li>Information architecture and user flow mapping</li>
            <li>Wireframing and prototyping</li>
            <li>Interactive design elements that enhance engagement</li>
            <li>Usability testing and iterative improvements</li>
          </ul>

          <p>By focusing on the user's needs and behaviors, our <a href="${websiteUrl}/services/ui-ux">UI/UX design services</a> create digital experiences that are both enjoyable to use and effective at achieving business objectives.</p>

          <h3>Digital Marketing Strategies That Deliver Results</h3>
          <p>Even the best website won't generate business without effective marketing. Our <a href="${websiteUrl}/services/digital-marketing">digital marketing team</a> develops comprehensive strategies to attract and convert your ideal customers:</p>
          <ul>
            <li>Search engine optimization (SEO) to improve organic visibility</li>
            <li>Content marketing that establishes your expertise</li>
            <li>Social media management to build community and engagement</li>
            <li>Email marketing campaigns that nurture leads</li>
            <li>Pay-per-click advertising for immediate traffic and conversions</li>
          </ul>

          <p>We measure and analyze all marketing efforts to continuously refine our approach and maximize your return on investment.</p>

          <h3>E-commerce Solutions for Online Sellers</h3>
          <p>For businesses selling products online, our <a href="${websiteUrl}/services/e-commerce">e-commerce solutions</a> provide everything needed for a successful online store:</p>
          <ul>
            <li>Custom e-commerce website development</li>
            <li>Product catalog and inventory management</li>
            <li>Secure payment gateway integration</li>
            <li>Order fulfillment and shipping integration</li>
            <li>Customer account management and loyalty programs</li>
          </ul>

          <p>Our e-commerce platforms are designed to provide a seamless shopping experience while giving you the tools to manage your online business effectively.</p>

          <h3>Mobile App Design for Connected Customers</h3>
          <p>With more users accessing content via mobile devices, a dedicated app can provide significant advantages. Our <a href="${websiteUrl}/services/mobile-app-design">mobile app design team</a> creates applications that extend your digital presence:</p>
          <ul>
            <li>Native and cross-platform app development</li>
            <li>Intuitive mobile interfaces designed for small screens</li>
            <li>Push notification strategies for engagement</li>
            <li>Offline functionality for uninterrupted use</li>
            <li>App Store optimization for maximum downloads</li>
          </ul>

          <h3>The Integrated Advantage</h3>
          <p>What sets <a href="${websiteUrl}">Mocky Digital</a> apart is our integrated approach. By offering all these services under one roof, we ensure that every aspect of your digital presence works together seamlessly. This integration leads to:</p>
          <ul>
            <li>Consistent messaging and branding across all platforms</li>
            <li>Streamlined project management and communication</li>
            <li>Cost efficiencies through coordinated strategies</li>
            <li>Comprehensive data analysis across all digital touchpoints</li>
            <li>Faster implementation of multi-channel campaigns</li>
          </ul>

          <h3>Taking the Next Step</h3>
          <p>Ready to transform your digital presence with our comprehensive services? <a href="${websiteUrl}/contact">Contact our team</a> today for a consultation. We'll discuss your specific needs and develop a tailored strategy to help you achieve your business objectives.</p>

          <p>At <a href="${websiteUrl}">Mocky Digital</a>, we're committed to your success. Let us show you how our integrated approach can take your business to new heights in the digital landscape.</p>
        `;
      }

      // Ensure there are at least 4 links to the website
      const websiteDomain = websiteUrl.replace(/^https?:\/\//, '').replace(/\/.*$/, '');
      const linkRegex = new RegExp(`href=["']https?://(www\\.)?${websiteDomain}`, 'gi');
      const linkMatches = content.match(linkRegex) || [];

      if (linkMatches.length < 4) {
        logger.info(`Content has only ${linkMatches.length} links, adding more links`);

        // Add call to action with links to all services
        content += `
          <h3>Our Full Range of Services</h3>
          <p>At <a href="${websiteUrl}">Mocky Digital</a>, we offer a comprehensive suite of digital services to meet all your business needs:</p>
          <ul>
            <li><a href="${websiteUrl}/services/web-design">Web Design & Development</a> - Custom websites that convert visitors into customers</li>
            <li><a href="${websiteUrl}/services/graphic-design">Graphic Design</a> - Visual assets that communicate your brand message</li>
            <li><a href="${websiteUrl}/services/branding">Branding & Identity</a> - Comprehensive brand development and strategy</li>
            <li><a href="${websiteUrl}/services/ui-ux">UI/UX Design</a> - User-centered design that enhances engagement</li>
            <li><a href="${websiteUrl}/services/digital-marketing">Digital Marketing</a> - Strategies that drive traffic and conversions</li>
            <li><a href="${websiteUrl}/services/content-creation">Content Creation</a> - Compelling content that tells your story</li>
            <li><a href="${websiteUrl}/services/e-commerce">E-commerce Solutions</a> - Online stores that drive sales</li>
            <li><a href="${websiteUrl}/services/mobile-app-design">Mobile App Design</a> - Applications that extend your digital reach</li>
          </ul>

          <p>Ready to elevate your digital presence? <a href="${websiteUrl}/contact">Contact us today</a> to discuss how our services can help you achieve your business goals.</p>
        `;
      }

      // Check for service mentions and add content about missing services
      let servicesMentioned: string[] = [];
      COMPANY_SERVICES.forEach(service => {
        // Check if service name is mentioned
        if (content.toLowerCase().includes(service.name.toLowerCase())) {
          servicesMentioned.push(service.name);
          return;
        }

        // Check if at least 2 keywords for this service are mentioned
        let keywordsMentioned = 0;
        service.keywords.forEach(keyword => {
          if (content.toLowerCase().includes(keyword.toLowerCase())) {
            keywordsMentioned++;
          }
        });

        if (keywordsMentioned >= 2) {
          servicesMentioned.push(service.name);
        }
      });

      // If fewer than 3 services are mentioned, add content about missing services
      if (servicesMentioned.length < 3) {
        logger.info(`Content mentions only ${servicesMentioned.length} services, adding content about more services`);

        // Get services that aren't mentioned
        const missingServices = COMPANY_SERVICES.filter(service => !servicesMentioned.includes(service.name));

        // Add content about up to 3 missing services
        const servicesToAdd = missingServices.slice(0, 3);

        content += `
          <h2>Additional Services to Consider</h2>
          <p>While the focus of this article has been on ${servicesMentioned.join(', ')}, there are several other services that can complement your strategy:</p>
        `;

        servicesToAdd.forEach(service => {
          content += `
            <h3>${service.name}</h3>
            <p>${service.description}. At <a href="${websiteUrl}/services/${service.name.toLowerCase().replace(/\s+&?\s+/g, '-')}">Mocky Digital</a>, our ${service.name.toLowerCase()} services include:</p>
            <ul>
              ${service.keywords.map(keyword => `<li>${keyword.charAt(0).toUpperCase() + keyword.slice(1)}</li>`).join('\n')}
            </ul>
          `;
        });
      }

      // Check for heading structure and add more headings if needed
      const headingMatches = content.match(/<h[2-3][^>]*>.*?<\/h[2-3]>/gi) || [];
      if (headingMatches.length < 4) {
        logger.info(`Content has only ${headingMatches.length} headings, adding more structured content`);

        content += `
          <h2>Best Practices for Implementing These Strategies</h2>
          <p>To get the most out of the approaches discussed in this article, consider these best practices:</p>

          <h3>Start with a Comprehensive Strategy</h3>
          <p>Before implementing any digital solution, develop a comprehensive strategy that aligns with your business goals. This ensures that all efforts work together toward common objectives.</p>

          <h3>Focus on User-Centered Design</h3>
          <p>Always prioritize the needs and preferences of your users. User-centered design leads to better engagement, higher conversion rates, and improved customer satisfaction.</p>

          <h3>Maintain Consistency Across Channels</h3>
          <p>Ensure that your branding, messaging, and user experience remain consistent across all digital touchpoints. This builds trust and reinforces your brand identity.</p>

          <h3>Measure and Iterate</h3>
          <p>Implement analytics to track the performance of your digital assets. Use this data to make informed decisions and continuously improve your approach.</p>
        `;
      }
    }

    // Format the content properly for HTML rendering
    // Process markdown-like content into HTML
    if (!content.includes('<p>') && !content.includes('<h')) {
      // Process headings (# Heading)
      content = content.replace(/^###\s+(.*)$/gm, '<h3>$1</h3>');
      content = content.replace(/^##\s+(.*)$/gm, '<h2>$1</h2>');
      content = content.replace(/^#\s+(.*)$/gm, '<h1>$1</h1>');

      // Process bold (**text**)
      content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

      // Process italic (*text*)
      content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');

      // Process lists
      content = content.replace(/^-\s+(.*)$/gm, '<li>$1</li>');
      content = content.replace(/(<li>.*<\/li>\n)+/g, '<ul>$&</ul>');

      // Process numbered lists
      content = content.replace(/^\d+\.\s+(.*)$/gm, '<li>$1</li>');
      content = content.replace(/(<li>.*<\/li>\n)+/g, '<ol>$&</ol>');

      // Split by double newlines and wrap remaining text in paragraph tags
      content = content.split('\n\n')
        .map(para => para.trim())
        .filter(para => para.length > 0)
        .map(para => {
          // Skip if it's already an HTML element
          if (para.startsWith('<') && para.endsWith('>')) {
            return para;
          }
          return `<p>${para}</p>`;
        })
        .join('\n\n');
    }

    logger.info(`Successfully generated blog post: "${title}"`);

    // Generate slug from title
    const slug = slugify(title, {
      lower: true,
      strict: true,
      trim: true,
    });

    // Determine status based on options
    const status = options.publishImmediately ? 'published' : 'draft';

    // Create the blog post
    const blogPost = await prisma.blogPost.create({
      data: {
        title,
        slug,
        content,
        excerpt: excerpt || content.substring(0, 150) + '...',
        author: 'AI Writer',
        category,
        tags: [category, 'automated'],
        status,
        publishedAt: status === 'published' ? new Date() : null,
      }
    });

    logger.info(`Blog post created with ID: ${blogPost.id}, Status: ${status}`);

    return blogPost;
  } catch (error) {
    logger.error('Error generating and creating blog post:', error);
    return null;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Publish a draft blog post
 * @param id The ID of the blog post to publish
 * @returns The published blog post
 */
export async function publishDraftBlogPost(id: string): Promise<any> {
  try {
    logger.info(`Publishing draft blog post with ID: ${id}`);

    // Get the blog post
    const blogPost = await prisma.blogPost.findUnique({
      where: { id: parseInt(id) }
    });

    if (!blogPost) {
      logger.error(`Blog post with ID ${id} not found`);
      return null;
    }

    if (blogPost.status === 'published') {
      logger.info(`Blog post with ID ${id} is already published`);
      return blogPost;
    }

    // Update the blog post status to published
    const updatedBlogPost = await prisma.blogPost.update({
      where: { id: parseInt(id) },
      data: {
        status: 'published',
        publishedAt: new Date()
      }
    });

    logger.info(`Blog post with ID ${id} published successfully`);

    return updatedBlogPost;
  } catch (error) {
    logger.error(`Error publishing blog post with ID ${id}:`, error);
    return null;
  } finally {
    await prisma.$disconnect();
  }
}
