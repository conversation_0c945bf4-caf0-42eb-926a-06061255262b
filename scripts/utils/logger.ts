/**
 * Simple logger utility for scripts
 */
export function createLogger(namespace: string) {
  return {
    info: (message: string, ...args: any[]) => {
      console.log(`[${new Date().toISOString()}] [INFO] [${namespace}] ${message}`, ...args);
    },
    error: (message: string, ...args: any[]) => {
      console.error(`[${new Date().toISOString()}] [ERROR] [${namespace}] ${message}`, ...args);
    },
    warn: (message: string, ...args: any[]) => {
      console.warn(`[${new Date().toISOString()}] [WARN] [${namespace}] ${message}`, ...args);
    },
    debug: (message: string, ...args: any[]) => {
      console.debug(`[${new Date().toISOString()}] [DEBUG] [${namespace}] ${message}`, ...args);
    }
  };
}
