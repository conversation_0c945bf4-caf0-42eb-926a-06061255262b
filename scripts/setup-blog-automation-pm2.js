// <PERSON><PERSON>t to set up automated blog post generation using PM2
// This script creates a PM2 process that runs the blog post generation script daily
// Usage: node scripts/setup-blog-automation-pm2.js

const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get the absolute path to the project directory
const PROJECT_DIR = process.cwd();

// Check if the project directory is correct
if (!fs.existsSync(path.join(PROJECT_DIR, 'package.json'))) {
  console.error('❌ Error: This script must be run from the project root directory');
  process.exit(1);
}

// Check if the scheduled-blog-post.ts script exists
if (!fs.existsSync(path.join(PROJECT_DIR, 'scripts', 'scheduled-blog-post.ts'))) {
  console.error('❌ Error: scheduled-blog-post.ts script not found');
  process.exit(1);
}

// Create logs directory if it doesn't exist
const logsDir = path.join(PROJECT_DIR, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
  console.log('✅ Created logs directory');
}

// Create a wrapper script that will be called by PM2
const wrapperScriptPath = path.join(PROJECT_DIR, 'scripts', 'blog-automation-wrapper.js');
const wrapperScriptContent = `
// Wrapper script for running the blog post automation with PM2
// This script is called by PM2 and runs the scheduled-blog-post.ts script

const cron = require('node-cron');
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Path to the log file
const logFile = path.join('${PROJECT_DIR}', 'logs', 'scheduled-blog-posts.log');

// Function to log messages
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = \`[\${timestamp}] \${message}\`;
  
  console.log(logMessage);
  
  // Append to log file
  fs.appendFileSync(logFile, logMessage + '\\n');
}

// Function to run the blog post generation script
function runBlogPostGeneration() {
  try {
    log('Running scheduled blog post generation...');
    
    // Run the script with ts-node
    execSync(\`npx ts-node \${path.join('${PROJECT_DIR}', 'scripts', 'scheduled-blog-post.ts')} --run-once\`, {
      stdio: 'inherit'
    });
    
    log('Scheduled blog post generation completed successfully');
  } catch (error) {
    log(\`Error running scheduled blog post generation: \${error.message}\`);
  }
}

// Schedule the task to run daily at 6:00 AM
log('Starting scheduled blog post generation...');
log('Blog posts will be generated daily at 6:00 AM');

// Schedule the cron job
cron.schedule('0 6 * * *', runBlogPostGeneration);

// Also run once when the process starts (for testing)
// Comment this out in production if you don't want it to run immediately
// runBlogPostGeneration();

// Keep the process alive
log('Scheduler is running. Process will stay alive until stopped.');
`;

// Write the wrapper script
fs.writeFileSync(wrapperScriptPath, wrapperScriptContent);
console.log(`✅ Created wrapper script at ${wrapperScriptPath}`);

// Create a PM2 ecosystem config file
const pm2ConfigPath = path.join(PROJECT_DIR, 'ecosystem.blog-automation.config.js');
const pm2ConfigContent = `
module.exports = {
  apps: [
    {
      name: 'blog-automation',
      script: '${wrapperScriptPath}',
      watch: false,
      instances: 1,
      autorestart: true,
      max_memory_restart: '200M',
      env: {
        NODE_ENV: 'production',
      },
      log_date_format: 'YYYY-MM-DD HH:mm:ss',
      error_file: '${path.join(logsDir, 'blog-automation-error.log')}',
      out_file: '${path.join(logsDir, 'blog-automation-out.log')}',
    },
  ],
};
`;

// Write the PM2 config file
fs.writeFileSync(pm2ConfigPath, pm2ConfigContent);
console.log(`✅ Created PM2 config file at ${pm2ConfigPath}`);

// Check if PM2 is installed
exec('pm2 --version', (error) => {
  if (error) {
    console.log('⚠️ PM2 is not installed. Installing PM2 globally...');
    exec('npm install -g pm2', (installError) => {
      if (installError) {
        console.error('❌ Error installing PM2:', installError);
        console.log('Please install PM2 manually: npm install -g pm2');
        process.exit(1);
      } else {
        startPM2Process();
      }
    });
  } else {
    startPM2Process();
  }
});

// Function to start the PM2 process
function startPM2Process() {
  // Check if the process is already running
  exec('pm2 list', (error, stdout) => {
    if (error) {
      console.error('❌ Error checking PM2 processes:', error);
      process.exit(1);
    }
    
    // If the process is already running, stop it
    if (stdout.includes('blog-automation')) {
      console.log('⚠️ Blog automation process is already running. Stopping it...');
      exec('pm2 delete blog-automation', (deleteError) => {
        if (deleteError) {
          console.error('❌ Error stopping existing process:', deleteError);
        } else {
          console.log('✅ Existing process stopped');
          startNewProcess();
        }
      });
    } else {
      startNewProcess();
    }
  });
}

// Function to start a new PM2 process
function startNewProcess() {
  console.log('🚀 Starting blog automation process with PM2...');
  exec(`pm2 start ${pm2ConfigPath}`, (error) => {
    if (error) {
      console.error('❌ Error starting PM2 process:', error);
      process.exit(1);
    } else {
      console.log('✅ Blog automation process started successfully');
      console.log('📊 Blog posts will be generated daily at 6:00 AM');
      console.log('📝 Logs will be written to:');
      console.log(`   - ${path.join(logsDir, 'scheduled-blog-posts.log')}`);
      console.log(`   - ${path.join(logsDir, 'blog-automation-out.log')}`);
      console.log(`   - ${path.join(logsDir, 'blog-automation-error.log')}`);
      console.log('');
      console.log('You can also manage scheduled blog posts from the admin interface at /admin/scheduled-blog-posts');
      console.log('');
      console.log('To check the status of the process: pm2 status blog-automation');
      console.log('To view logs: pm2 logs blog-automation');
      console.log('To stop the process: pm2 stop blog-automation');
      console.log('To restart the process: pm2 restart blog-automation');
    }
  });
}
