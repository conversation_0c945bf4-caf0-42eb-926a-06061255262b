const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    // Create editor role if it doesn't exist
    let editorRole = await prisma.role.findFirst({
      where: { name: 'editor' }
    });

    if (!editorRole) {
      console.log('Creating editor role...');
      editorRole = await prisma.role.create({
        data: {
          name: 'editor',
          description: 'Content editor with limited access',
          permissions: [
            'dashboard:read',
            'blog:read', 'blog:write',
            'portfolio:read', 'portfolio:write',
            'team:read', 'team:write',
            'transactions:read',
            'receipts:read',
            'invoices:read',
            'quotes:read',
            'services:read',
            'pricing:read',
            'categories:read', 'categories:write',
            'tags:read', 'tags:write',
            'authors:read', 'authors:write'
          ]
        }
      });
      console.log('Editor role created successfully.');
    } else {
      console.log('Editor role already exists.');
    }

    // Create viewer role if it doesn't exist
    let viewerRole = await prisma.role.findFirst({
      where: { name: 'viewer' }
    });

    if (!viewerRole) {
      console.log('Creating viewer role...');
      viewerRole = await prisma.role.create({
        data: {
          name: 'viewer',
          description: 'Read-only access to content',
          permissions: [
            'dashboard:read',
            'blog:read',
            'portfolio:read',
            'team:read',
            'transactions:read',
            'receipts:read',
            'invoices:read',
            'quotes:read',
            'services:read',
            'pricing:read',
            'categories:read',
            'tags:read',
            'authors:read'
          ]
        }
      });
      console.log('Viewer role created successfully.');
    } else {
      console.log('Viewer role already exists.');
    }

    console.log('Default roles setup completed.');
  } catch (error) {
    console.error('Error creating default roles:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
