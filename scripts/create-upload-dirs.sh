#!/bin/bash

# Script to create upload directories for images
# Usage: ./scripts/create-upload-dirs.sh

# Exit on error
set -e

echo "🔄 Creating upload directories..."

# Create main uploads directory
mkdir -p public/uploads

# Create specific upload directories
mkdir -p public/uploads/pricing
mkdir -p public/uploads/blog
mkdir -p public/uploads/portfolio

# Set permissions
chmod -R 755 public/uploads

echo "✅ Upload directories created successfully!"
echo "📁 Created directories:"
echo "  - public/uploads"
echo "  - public/uploads/pricing"
echo "  - public/uploads/blog"
echo "  - public/uploads/portfolio"
