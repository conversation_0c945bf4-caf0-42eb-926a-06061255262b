#!/bin/bash

# Setup SEO scanning cron job
# This script sets up a cron job to run the SEO scan weekly

# Get the absolute path to the project directory
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# Create the cron job command
CRON_COMMAND="cd $PROJECT_DIR && node scripts/seo-scan.js --all >> $PROJECT_DIR/logs/seo-scan.log 2>&1"

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_DIR/logs"

# Check if the cron job already exists
EXISTING_CRON=$(crontab -l 2>/dev/null | grep -F "$CRON_COMMAND")

if [ -z "$EXISTING_CRON" ]; then
  # Add the cron job to run every Sunday at 2:00 AM
  (crontab -l 2>/dev/null; echo "0 2 * * 0 $CRON_COMMAND") | crontab -

  echo "✅ SEO scan cron job has been set up to run weekly on Sundays at 2:00 AM"
  echo "   Logs will be written to: $PROJECT_DIR/logs/seo-scan.log"
else
  echo "ℹ️ SEO scan cron job already exists"
fi

# Display the current crontab
echo "Current crontab:"
crontab -l
