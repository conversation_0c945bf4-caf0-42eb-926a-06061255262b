// Script to seed tech services pricing data for the Kenyan market
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

// Initialize Prisma client
const prisma = new PrismaClient();

// Define tech services with pricing relevant to the Kenyan market
const techServices = [
  // Web Development Services
  {
    service: 'Basic Website Package',
    price: 25000,
    description: 'Professional website for small businesses and startups',
    features: [
      'Up to 5 pages',
      'Mobile responsive design',
      'Contact form',
      'Social media integration',
      'Basic SEO setup',
      '1 year domain & hosting'
    ],
    icon: 'globe',
    popular: true,
    category: 'Web Development'
  },
  {
    service: 'Business Website Package',
    price: 45000,
    description: 'Comprehensive website solution for established businesses',
    features: [
      'Up to 10 pages',
      'Mobile responsive design',
      'Contact form with custom fields',
      'Blog/news section',
      'Google Analytics integration',
      'Advanced SEO optimization',
      '1 year domain & hosting'
    ],
    icon: 'building',
    popular: true,
    category: 'Web Development'
  },
  {
    service: 'E-commerce Website',
    price: 85000,
    description: 'Full-featured online store for selling products or services',
    features: [
      'Up to 100 products',
      'Secure payment gateway integration',
      'Inventory management',
      'Order tracking system',
      'Customer accounts',
      'Mobile responsive design',
      '1 year domain & hosting'
    ],
    icon: 'shopping-cart',
    popular: true,
    category: 'Web Development'
  },
  {
    service: 'Custom Web Application',
    price: 150000,
    description: 'Tailored web application development for specific business needs',
    features: [
      'Custom functionality',
      'User authentication system',
      'Database integration',
      'API development',
      'Scalable architecture',
      'Comprehensive documentation',
      '3 months of support'
    ],
    icon: 'code',
    popular: false,
    category: 'Web Development'
  },
  {
    service: 'Website Maintenance (Monthly)',
    price: 5000,
    description: 'Ongoing website maintenance and support services',
    features: [
      'Regular updates & backups',
      'Security monitoring',
      'Content updates (2 per month)',
      'Technical support',
      'Performance optimization',
      'Monthly analytics report'
    ],
    icon: 'tools',
    popular: false,
    category: 'Web Development'
  },
  
  // Digital Marketing Services
  {
    service: 'SEO Package (Monthly)',
    price: 15000,
    description: 'Search engine optimization to improve website visibility',
    features: [
      'Keyword research & analysis',
      'On-page SEO optimization',
      'Content optimization',
      'Technical SEO improvements',
      'Monthly performance reports',
      'Local SEO for Kenyan businesses'
    ],
    icon: 'search',
    popular: true,
    category: 'Digital Marketing'
  },
  {
    service: 'Social Media Management (Monthly)',
    price: 12000,
    description: 'Professional management of your social media presence',
    features: [
      'Content creation (12 posts/month)',
      'Community management',
      'Engagement monitoring',
      'Platform optimization',
      'Monthly performance reports',
      'Strategy development'
    ],
    icon: 'hashtag',
    popular: true,
    category: 'Digital Marketing'
  },
  {
    service: 'Google Ads Campaign',
    price: 20000,
    description: 'Targeted Google advertising campaigns',
    features: [
      'Campaign setup & management',
      'Keyword research',
      'Ad copywriting',
      'Landing page optimization',
      'Conversion tracking',
      'Monthly performance reports'
    ],
    icon: 'ad',
    popular: false,
    category: 'Digital Marketing'
  },
  {
    service: 'Facebook & Instagram Ads',
    price: 18000,
    description: 'Strategic social media advertising campaigns',
    features: [
      'Campaign setup & management',
      'Audience targeting',
      'Ad creative development',
      'A/B testing',
      'Conversion tracking',
      'Monthly performance reports'
    ],
    icon: 'facebook',
    popular: false,
    category: 'Digital Marketing'
  },
  {
    service: 'Content Marketing Package',
    price: 25000,
    description: 'Comprehensive content creation and marketing strategy',
    features: [
      '4 blog posts per month',
      'SEO optimization',
      'Social media promotion',
      'Email newsletter',
      'Content strategy development',
      'Performance analytics'
    ],
    icon: 'file-alt',
    popular: false,
    category: 'Digital Marketing'
  },
  
  // IT Services & Consulting
  {
    service: 'IT Infrastructure Setup',
    price: 35000,
    description: 'Complete IT infrastructure setup for small to medium businesses',
    features: [
      'Network design & implementation',
      'Hardware procurement assistance',
      'Software installation & configuration',
      'Security setup',
      'Data backup solutions',
      'Staff training'
    ],
    icon: 'network-wired',
    popular: false,
    category: 'IT Services'
  },
  {
    service: 'IT Support (Monthly)',
    price: 15000,
    description: 'Ongoing IT support for business operations',
    features: [
      'Remote technical support',
      'On-site support (8 hours/month)',
      'Hardware & software troubleshooting',
      'Network monitoring',
      'Security updates',
      'Monthly system health reports'
    ],
    icon: 'headset',
    popular: true,
    category: 'IT Services'
  },
  {
    service: 'Cybersecurity Audit',
    price: 45000,
    description: 'Comprehensive assessment of your organization\'s security posture',
    features: [
      'Vulnerability assessment',
      'Security policy review',
      'Network security testing',
      'Data protection assessment',
      'Detailed report with recommendations',
      'Post-audit consultation'
    ],
    icon: 'shield-alt',
    popular: false,
    category: 'IT Services'
  },
  {
    service: 'Data Recovery',
    price: 8000,
    description: 'Professional data recovery services for lost or corrupted data',
    features: [
      'Free initial assessment',
      'Recovery from various storage media',
      'Secure data handling',
      'Quick turnaround time',
      'Confidentiality guarantee',
      'No recovery, no fee policy'
    ],
    icon: 'database',
    popular: false,
    category: 'IT Services'
  },
  {
    service: 'IT Consulting (Hourly)',
    price: 3500,
    description: 'Expert IT consultation for business technology needs',
    features: [
      'Technology strategy development',
      'IT infrastructure planning',
      'Software & hardware recommendations',
      'Digital transformation guidance',
      'Vendor selection assistance',
      'Implementation oversight'
    ],
    icon: 'user-tie',
    popular: false,
    category: 'IT Services'
  },
  
  // Software Development
  {
    service: 'Mobile App Development (Android)',
    price: 120000,
    description: 'Custom Android application development',
    features: [
      'UI/UX design',
      'Native Android development',
      'API integration',
      'Testing & quality assurance',
      'Google Play Store submission',
      '3 months of support & updates'
    ],
    icon: 'android',
    popular: true,
    category: 'Software Development'
  },
  {
    service: 'Mobile App Development (iOS)',
    price: 150000,
    description: 'Custom iOS application development',
    features: [
      'UI/UX design',
      'Native iOS development',
      'API integration',
      'Testing & quality assurance',
      'App Store submission',
      '3 months of support & updates'
    ],
    icon: 'apple',
    popular: false,
    category: 'Software Development'
  },
  {
    service: 'Cross-Platform Mobile App',
    price: 180000,
    description: 'Develop once, deploy on both Android and iOS platforms',
    features: [
      'UI/UX design',
      'React Native/Flutter development',
      'API integration',
      'Testing on multiple devices',
      'App store submissions',
      '3 months of support & updates'
    ],
    icon: 'mobile-alt',
    popular: true,
    category: 'Software Development'
  },
  {
    service: 'Custom Business Software',
    price: 250000,
    description: 'Tailored software solutions for specific business needs',
    features: [
      'Requirements analysis',
      'System architecture design',
      'Full-stack development',
      'Database design & implementation',
      'User training',
      '6 months of support & updates'
    ],
    icon: 'laptop-code',
    popular: false,
    category: 'Software Development'
  },
  {
    service: 'Software Integration',
    price: 75000,
    description: 'Integrate existing software systems for seamless operation',
    features: [
      'System analysis',
      'API development',
      'Data migration',
      'Testing & validation',
      'Documentation',
      '3 months of support'
    ],
    icon: 'plug',
    popular: false,
    category: 'Software Development'
  },
  
  // Cloud & Hosting Services
  {
    service: 'Cloud Migration',
    price: 65000,
    description: 'Migrate your existing infrastructure to cloud platforms',
    features: [
      'Migration assessment & planning',
      'Data transfer',
      'Application configuration',
      'Testing & validation',
      'Staff training',
      'Post-migration support'
    ],
    icon: 'cloud-upload-alt',
    popular: false,
    category: 'Cloud Services'
  },
  {
    service: 'VPS Hosting (Monthly)',
    price: 5000,
    description: 'Virtual private server hosting for websites and applications',
    features: [
      '4 vCPU cores',
      '8GB RAM',
      '100GB SSD storage',
      'Unlimited bandwidth',
      'cPanel/WHM',
      '24/7 technical support'
    ],
    icon: 'server',
    popular: true,
    category: 'Cloud Services'
  },
  {
    service: 'Dedicated Server (Monthly)',
    price: 15000,
    description: 'Dedicated server hosting for high-performance applications',
    features: [
      '8 CPU cores',
      '16GB RAM',
      '500GB SSD storage',
      'Unlimited bandwidth',
      'Full root access',
      '24/7 technical support'
    ],
    icon: 'hdd',
    popular: false,
    category: 'Cloud Services'
  },
  {
    service: 'Managed WordPress Hosting (Monthly)',
    price: 3500,
    description: 'Specialized hosting optimized for WordPress websites',
    features: [
      'Automatic WordPress updates',
      'Daily backups',
      'Security monitoring',
      'Performance optimization',
      'CDN integration',
      '24/7 technical support'
    ],
    icon: 'wordpress',
    popular: true,
    category: 'Cloud Services'
  },
  {
    service: 'Domain Registration (.co.ke)',
    price: 1200,
    description: 'Register your .co.ke domain name for your Kenyan business',
    features: [
      '1 year registration',
      'DNS management',
      'Email forwarding',
      'Domain lock',
      'Auto-renewal option',
      'Control panel access'
    ],
    icon: 'globe-africa',
    popular: false,
    category: 'Cloud Services'
  }
];

// Main function to seed the database
async function seedTechServices() {
  try {
    console.log('Starting to seed tech services pricing data...');

    // Get existing pricing items
    const existingPricing = await prisma.pricing.findMany();
    console.log(`Found ${existingPricing.length} existing pricing items`);

    // Create a map of existing services (case-insensitive)
    const existingServicesMap = new Map();
    existingPricing.forEach(item => {
      existingServicesMap.set(item.service.toLowerCase(), item);
    });

    let created = 0;
    let updated = 0;

    // Process each service
    for (const service of techServices) {
      // Check if service already exists (case-insensitive)
      const existingService = existingServicesMap.get(service.service.toLowerCase());

      if (existingService) {
        // Update existing service
        await prisma.pricing.update({
          where: { id: existingService.id },
          data: {
            price: service.price,
            description: service.description,
            features: service.features,
            icon: service.icon,
            popular: service.popular,
            category: service.category
          }
        });
        updated++;
        console.log(`Updated existing service: ${service.service}`);
      } else {
        // Create new service
        await prisma.pricing.create({
          data: {
            service: service.service,
            price: service.price,
            description: service.description,
            features: service.features,
            icon: service.icon,
            popular: service.popular,
            category: service.category
          }
        });
        created++;
        console.log(`Created new service: ${service.service}`);
      }
    }

    console.log(`Successfully processed tech services pricing data:`);
    console.log(`- Created: ${created} new services`);
    console.log(`- Updated: ${updated} existing services`);
    console.log(`- Total: ${techServices.length} services processed`);

  } catch (error) {
    console.error('Error seeding tech services pricing data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedTechServices()
  .catch((error) => {
    console.error('Error running seed script:', error);
    process.exit(1);
  });
