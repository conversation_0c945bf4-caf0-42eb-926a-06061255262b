#!/bin/bash

# Script to populate drinkware pricing data
# Usage: ./scripts/populate-drinkware.sh

# Exit on error
set -e

echo "🔄 Starting drinkware pricing data population..."

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Run the seed script
echo "🌱 Seeding drinkware pricing data..."
node scripts/seed-drinkware.js

echo "✅ Drinkware pricing data population completed successfully!"
