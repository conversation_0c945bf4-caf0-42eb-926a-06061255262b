
// Wrapper script for running the blog post automation with PM2
// This script is called by PM2 and runs the scheduled-blog-post.ts script

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Path to the log file
const logFile = path.join(process.cwd(), 'logs', 'scheduled-blog-posts.log');

// Function to log messages
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;

  console.log(logMessage);

  // Append to log file
  fs.appendFileSync(logFile, logMessage + '\n');
}

// Function to run the blog post generation script
function runBlogPostGeneration() {
  try {
    log('Running scheduled blog post generation...');

    // Run the script with ts-node
    execSync(`npx ts-node ${path.join(process.cwd(), 'scripts', 'scheduled-blog-post.ts')} --run-once`, {
      stdio: 'inherit'
    });

    log('Scheduled blog post generation completed successfully');
  } catch (error) {
    log(`Error running scheduled blog post generation: ${error.message}`);
  }
}

// Schedule the task to run daily at 6:00 AM
log('Starting scheduled blog post generation...');
log('Blog posts will be generated daily at 6:00 AM');

// Instead of using node-cron, we'll use a simple setInterval
// Calculate time until 6:00 AM
function getTimeUntil6AM() {
  const now = new Date();
  const target = new Date(now);
  target.setHours(6, 0, 0, 0);

  // If it's already past 6 AM, schedule for tomorrow
  if (now >= target) {
    target.setDate(target.getDate() + 1);
  }

  return target.getTime() - now.getTime();
}

// Function to schedule the next run
function scheduleNextRun() {
  const timeUntil6AM = getTimeUntil6AM();
  log(`Next run scheduled in ${Math.floor(timeUntil6AM / (1000 * 60 * 60))} hours and ${Math.floor((timeUntil6AM % (1000 * 60 * 60)) / (1000 * 60))} minutes`);

  setTimeout(() => {
    runBlogPostGeneration();
    scheduleNextRun(); // Schedule the next run after execution
  }, timeUntil6AM);
}

// Schedule the first run
scheduleNextRun();

// Also run once when the process starts (for testing)
runBlogPostGeneration();

// Keep the process alive
log('Scheduler is running. Process will stay alive until stopped.');
