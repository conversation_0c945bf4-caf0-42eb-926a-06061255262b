#!/bin/bash

# Production deployment script with database synchronization
# This script handles deployment and ensures database is in sync
# Usage: ./scripts/production-deploy.sh

# Exit on error
set -e

echo "🚀 Starting production deployment..."

# Get current timestamp for backup naming
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="./backups"
mkdir -p $BACKUP_DIR

# Load environment variables from .env file
if [ -f .env ]; then
  export $(grep -v '^#' .env | grep DATABASE_URL | tail -n 1 | xargs)
fi

if [ -z "$DATABASE_URL" ]; then
  echo "⚠️  DATABASE_URL environment variable not set. Using default connection string."
  export DATABASE_URL="postgresql://don:password@localhost:5432/mocky?schema=public"
fi

# Extract connection details from the DATABASE_URL
DB_USER=$(echo $DATABASE_URL | sed -n 's/^postgresql:\/\/\([^:]*\):.*/\1/p')
DB_PASSWORD=$(echo $DATABASE_URL | sed -n 's/^postgresql:\/\/[^:]*:\([^@]*\)@.*/\1/p')
DB_HOST=$(echo $DATABASE_URL | sed -n 's/^postgresql:\/\/[^@]*@\([^:]*\):.*/\1/p')
DB_PORT=$(echo $DATABASE_URL | sed -n 's/^postgresql:\/\/[^:]*:[^@]*@[^:]*:\([0-9]*\)\/.*/\1/p')
DB_NAME=$(echo $DATABASE_URL | sed -n 's/^postgresql:\/\/[^:]*:[^@]*@[^:]*:[0-9]*\/\([^?]*\).*/\1/p')

echo "Using database connection:"
echo "  Host: $DB_HOST"
echo "  Port: $DB_PORT"
echo "  Database: $DB_NAME"
echo "  User: $DB_USER"

# 1. Create a backup of the database
echo "📦 Creating database backup..."
PGPASSWORD=$DB_PASSWORD pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -F c -f "$BACKUP_DIR/backup_$TIMESTAMP.dump"
echo "✅ Database backup created at $BACKUP_DIR/backup_$TIMESTAMP.dump"

# 2. Pull the latest changes from GitHub
echo "📥 Pulling latest changes from GitHub..."
git pull

# 3. Install dependencies
echo "📦 Installing dependencies..."
npm ci

# 4. Synchronize the database schema using db push with data preservation
echo "🔄 Synchronizing database schema (preserving all data)..."
# Use --skip-generate to avoid generating the client twice
npx prisma db push --accept-data-loss=false --skip-generate

# Verify if the schema update was successful
if [ $? -ne 0 ]; then
  echo "❌ Schema update failed. Restoring from backup..."
  PGPASSWORD=$DB_PASSWORD pg_restore -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME --clean --if-exists "$BACKUP_DIR/backup_$TIMESTAMP.dump"
  echo "✅ Database restored from backup."
  echo "⚠️ Please review your schema changes and try again."
  exit 1
fi

# Count records in key tables before and after to verify data preservation
echo "🔍 Verifying data integrity..."
BLOG_POSTS_COUNT_BEFORE=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM blog_posts;")
WEBSITE_PORTFOLIO_COUNT_BEFORE=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM website_portfolio;")
STORAGE_CONFIG_COUNT_BEFORE=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM storage_config;")

# Store counts for later comparison
echo "  - Blog posts: $BLOG_POSTS_COUNT_BEFORE"
echo "  - Website portfolio items: $WEBSITE_PORTFOLIO_COUNT_BEFORE"
echo "  - Storage configurations: $STORAGE_CONFIG_COUNT_BEFORE"

# 5. Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# 6. Build the application
echo "🔨 Building the application..."
npm run build

# 7. Restart the application using PM2
echo "🔄 Restarting the application..."
pm2 restart mocky-digital || pm2 start ecosystem.config.js

# 8. Reload nginx (if configuration has changed)
echo "🔄 Reloading Nginx configuration..."
if command -v nginx > /dev/null; then
  sudo systemctl reload nginx
else
  echo "⚠️  Nginx not found. Skipping Nginx reload."
fi

# 9. Verify deployment and data integrity
echo "✅ Verifying deployment..."
if curl -s http://localhost:3000 > /dev/null; then
  echo "✅ Application is responding on port 3000"
else
  echo "⚠️  Application is not responding on port 3000. Check logs with: pm2 logs mocky-digital"
fi

# Verify data integrity after deployment
echo "🔍 Verifying data integrity after deployment..."
BLOG_POSTS_COUNT_AFTER=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM blog_posts;")
WEBSITE_PORTFOLIO_COUNT_AFTER=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM website_portfolio;")
STORAGE_CONFIG_COUNT_AFTER=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM storage_config;")

echo "Data integrity check:"
echo "  - Blog posts: $BLOG_POSTS_COUNT_BEFORE → $BLOG_POSTS_COUNT_AFTER"
echo "  - Website portfolio items: $WEBSITE_PORTFOLIO_COUNT_BEFORE → $WEBSITE_PORTFOLIO_COUNT_AFTER"
echo "  - Storage configurations: $STORAGE_CONFIG_COUNT_BEFORE → $STORAGE_CONFIG_COUNT_AFTER"

# Check if any data was lost
if [ "$BLOG_POSTS_COUNT_BEFORE" -gt "$BLOG_POSTS_COUNT_AFTER" ] ||
   [ "$WEBSITE_PORTFOLIO_COUNT_BEFORE" -gt "$WEBSITE_PORTFOLIO_COUNT_AFTER" ] ||
   [ "$STORAGE_CONFIG_COUNT_BEFORE" -gt "$STORAGE_CONFIG_COUNT_AFTER" ]; then
  echo "⚠️  WARNING: Some data may have been lost during deployment!"
  echo "⚠️  Consider restoring from backup: $BACKUP_DIR/backup_$TIMESTAMP.dump"

  # In a non-interactive environment, automatically restore from backup
  echo "🔄 Automatically restoring database from backup to preserve data..."
  PGPASSWORD=$DB_PASSWORD pg_restore -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME --clean --if-exists "$BACKUP_DIR/backup_$TIMESTAMP.dump"
  echo "✅ Database restored from backup."

  # Restart the application after restore
  echo "🔄 Restarting the application after database restore..."
  pm2 restart mocky-digital
else
  echo "✅ Data integrity verified: No data loss detected"
fi

# 10. Print summary
echo ""
echo "✅ Deployment completed!"
echo "📋 Summary:"
echo "  - Database backup: $BACKUP_DIR/backup_$TIMESTAMP.dump"
echo "  - Database schema synchronized with data preservation"
echo "  - Data integrity verified"
echo "  - Application restarted with PM2"
echo "  - Nginx reloaded"
echo ""
echo "📊 Data counts:"
echo "  - Blog posts: $BLOG_POSTS_COUNT_AFTER"
echo "  - Website portfolio items: $WEBSITE_PORTFOLIO_COUNT_AFTER"
echo "  - Storage configurations: $STORAGE_CONFIG_COUNT_AFTER"
echo ""
echo "🔍 To check application logs: pm2 logs mocky-digital"
echo "🔄 To rollback database (if needed): pg_restore -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME $BACKUP_DIR/backup_$TIMESTAMP.dump"
