const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // Check if uncategorized category already exists
    const existingCategory = await prisma.category.findUnique({
      where: {
        slug: 'uncategorized'
      }
    });
    
    if (existingCategory) {
      console.log('Uncategorized category already exists:', existingCategory);
      return;
    }
    
    // Create the uncategorized category
    const uncategorizedCategory = await prisma.category.create({
      data: {
        name: 'Uncategorized',
        slug: 'uncategorized',
        description: 'Default category for unclassified content'
      }
    });
    
    console.log('Created uncategorized category successfully:');
    console.log(JSON.stringify(uncategorizedCategory, null, 2));
  } catch (error) {
    console.error('Error creating uncategorized category:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main(); 