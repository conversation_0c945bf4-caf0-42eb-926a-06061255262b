const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Checking database for categories and tags...');

    // Fetch all categories
    console.log('Fetching categories...');
    const categories = await prisma.category.findMany({
      orderBy: {
        name: 'asc'
      }
    });

    console.log('All categories:');
    console.log(JSON.stringify(categories, null, 2));
    console.log(`Total categories: ${categories.length}`);

    // Fetch all blog posts with their tags
    console.log('Fetching blog posts...');
    const blogPosts = await prisma.blogPost.findMany({
      select: {
        id: true,
        title: true,
        category: true,
        tags: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`Found ${blogPosts.length} blog posts`);

    console.log('\nBlog posts with tags:');
    blogPosts.forEach(post => {
      console.log(`- ${post.title} (Category: ${post.category || 'None'})`);
      console.log(`  Tags: ${post.tags.length > 0 ? post.tags.join(', ') : 'No tags'}`);
    });

    // Count unique tags across all blog posts
    const allTags = new Set();
    blogPosts.forEach(post => {
      post.tags.forEach(tag => allTags.add(tag));
    });

    console.log(`\nTotal unique tags used: ${allTags.size}`);
    console.log('All unique tags:');
    console.log(Array.from(allTags).sort().join(', '));

  } catch (error) {
    console.error('Error checking categories and tags:', error);
    console.error(error.stack);
  } finally {
    console.log('Disconnecting from database...');
    await prisma.$disconnect();
    console.log('Done!');
  }
}

// Run the main function
main();
